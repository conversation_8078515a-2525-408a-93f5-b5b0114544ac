include: package:flutter_lints/flutter.yaml

formatter:
  trailing_commas: preserve

analyzer:
  exclude: [build/**, lib/l10n/**, tools/**, lib/caching/database.g.dart, lib/generated_plugin_registrant.dart]
  errors:
    missing_return: error
    dead_code: error
    always_declare_return_types: error
    avoid_web_libraries_in_flutter: error
    missing_required_param: error
    file_names: error
    camel_case_types: error
    empty_statements: error
    iterable_contains_unrelated_type: error
    list_remove_unrelated_type: error
    no_duplicate_case_values: error
    unrelated_type_equality_checks: error
    close_sinks: error
    cancel_subscriptions: error

linter:
  rules:
    - use_super_parameters
    - always_declare_return_types
    - avoid_web_libraries_in_flutter
    - file_names
    - camel_case_types
    - empty_statements
    - collection_methods_unrelated_type
    - no_duplicate_case_values
    - unrelated_type_equality_checks
    - omit_local_variable_types
    - prefer_const_constructors
    - prefer_const_constructors_in_immutables
    - prefer_const_declarations
    - prefer_final_fields
    - prefer_final_locals
    - prefer_final_in_for_each
    - await_only_futures
    - hash_and_equals
    - avoid_annotating_with_dynamic
    - avoid_unnecessary_containers
    - prefer_if_null_operators
    - prefer_null_aware_operators
    - unnecessary_this
    - unnecessary_const
    - unnecessary_new
    - avoid_empty_else
    - cancel_subscriptions
    - close_sinks
    - no_logic_in_create_state
    - prefer_void_to_null
    - unnecessary_statements
    - annotate_overrides
    - avoid_bool_literals_in_conditional_expressions
    - only_throw_errors
    - sized_box_for_whitespace
    - unnecessary_await_in_return
    - prefer_relative_imports
