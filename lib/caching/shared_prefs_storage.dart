import 'dart:convert';

import 'package:shared_preferences/shared_preferences.dart';

import 'storage.dart';

class SharedPrefsStorage implements Storage {
  const SharedPrefsStorage();

  @override
  Future<String?> get(String key) async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getString(key);
  }

  @override
  Future<bool> put(String key, String value) async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.setString(key, value);
  }

  @override
  Future<void> delete(String key) async {
    final prefs = await SharedPreferences.getInstance();
    prefs.remove(key);
  }

  @override
  Future<Object?> getJson(String key) async {
    final text = await get(key);
    if (text == null) {
      return null;
    }
    return jsonDecode(text);
  }

  @override
  Future<bool> putJson(String key, jsonableObject) {
    final text = jsonEncode(jsonableObject);
    return put(key, text);
  }

  @override
  Future<bool> getBool(String key) async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getBool(key) ?? false;
  }

  @override
  Future<bool> putBool(String key, bool value) async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.setBool(key, value);
  }
}
