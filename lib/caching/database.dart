// Rebuild the generated code with this command
// dart run build_runner build --delete-conflicting-outputs
import 'dart:io';

import 'package:drift/drift.dart';
import 'package:drift/native.dart';
import 'package:path/path.dart';
import 'package:path_provider/path_provider.dart';

import '../features/cities/city_database.dart';
import '../features/map/tiles/internal/tile_repository.dart';
import '../models/geo_area.dart';

part 'database.g.dart';

@DataClassName('AreaDto')
class Areas extends Table {
  IntColumn get id => integer()();
  TextColumn get isoCode => text().withLength(max: 12)();
  IntColumn get parentId => integer().nullable()();
  IntColumn get continentId => integer().nullable()();
  TextColumn get json => text()();

  @override
  Set<Column> get primaryKey => {id, isoCode};
}

@DataClassName('ContinentDto')
class Continents extends Table {
  IntColumn get id => integer()();
  TextColumn get name => text()();
  TextColumn get translations => text()();

  @override
  Set<Column> get primaryKey => {id};
}

@DataClassName('LastModifiedDto')
class LastModified extends Table {
  IntColumn get id => integer().autoIncrement()();
  TextColumn get type => text().withLength(max: 16)();
  DateTimeColumn get lastModified => dateTime()();
}

@DataClassName('AreaSelectionDto')
class AreaSelections extends Table {
  IntColumn get areaId => integer()();
  TextColumn get isoCode => text().withLength(max: 16)();
  TextColumn get type => text().withLength(max: 16)();
  BoolColumn get synced => boolean().withDefault(const Constant(false))();

  @override
  Set<Column> get primaryKey => {areaId, isoCode};
}

@DataClassName('InspirationDto')
class Inspirations extends Table {
  IntColumn get id => integer()();
  TextColumn get name => text().withLength(max: 128)();
  TextColumn get url => text().withLength(max: 200)();
  IntColumn get areaId => integer()();
  TextColumn get selection => text().withLength(max: 10)();
  BoolColumn get synced => boolean().withDefault(const Constant(false))();

  @override
  Set<Column> get primaryKey => {id};
}

@DataClassName('CityDto')
class Cities extends Table {
  IntColumn get id => integer()();
  TextColumn get name => text().withLength(max: 128)();
  RealColumn get lat => real()();
  RealColumn get long => real()();
  IntColumn get areaId => integer()();
  IntColumn get secondLevelAreaId => integer().nullable()();

  @override
  Set<Column<Object>>? get primaryKey => {id};
}

class CitySelections extends Table {
  IntColumn get id => integer()();
  TextColumn get selection => text().withLength(max: 10)();
  BoolColumn get synced => boolean().withDefault(const Constant(false))();

  @override
  Set<Column<Object>>? get primaryKey => {id};
}

@DriftDatabase(
  tables: [
    Areas,
    Continents,
    LastModified,
    AreaSelections,
    Inspirations,
    Cities,
    CitySelections,
  ],
  queries: {
    'countInspirations': 'SELECT COUNT(id) FROM inspirations;',
  },
  daos: [
    CityDatabase,
  ],
)
class VisitedDatabase extends _$VisitedDatabase with GridSizeCalculable {
  static const _kAreaLastModifiedId = 1;
  // static const _kMapTilesLastModifierId = 3;

  VisitedDatabase(super.e);

  @override
  int get schemaVersion => 5;

  @override
  MigrationStrategy get migration => MigrationStrategy(
    beforeOpen: (details) {
      return customStatement(
        'PRAGMA foreign_keys = ON; PRAGMA journal_mode=WAL; PRAGMA busy_timeout=5000;',
      );
    },
    onUpgrade: (Migrator m, int from, int to) async {
      if (from < 3) {
        await m.createTable(cities);
        await m.createTable(citySelections);
      }

      // if (from < 4) {
      //   await m.createTable(mapTiles);
      //   await m.createTable(mapTileGeometryXref);
      // }

      if (from < 5) {
        try {
          await m.deleteTable('map_tiles');
          await m.deleteTable('map_tile_geometry_xref');
        } catch (_) {}
      }
    },
  );

  Future<AreaDto?> fetchAreaByIsoCode(String isoCode) {
    return (select(
      areas,
    )..where((tbl) => tbl.isoCode.equals(isoCode))).getSingleOrNull();
  }

  Future<List<AreaDto>> fetchTopLevelAreas() {
    return (select(areas)..where((tbl) => tbl.parentId.isNull())).get();
  }

  Future<AreaDto?> fetchAreaById(int id) {
    return (select(areas)..where((tbl) => tbl.id.equals(id))).getSingleOrNull();
  }

  Future<List<ContinentDto>> fetchAllContinents() {
    return (select(continents)).get();
  }

  Future<List<AreaDto>> fetchAllAreas() {
    return (select(areas)).get();
  }

  Future<List<AreaDto>> fetchAreasFromContinent(ContinentDto continent) {
    return (select(
      areas,
    )..where((tbl) => tbl.continentId.equals(continent.id))).get();
  }

  Future<List<AreaDto>> fetchSubdivisions(GeoArea area) {
    return (select(areas)..where((tbl) => tbl.parentId.equals(area.id))).get();
  }

  Future<AreaDto?> fetchParent(GeoArea area) async {
    final parentId = area.parentId;
    if (parentId == null) {
      return null;
    }
    return (select(
      areas,
    )..where((tbl) => tbl.id.equals(parentId))).getSingleOrNull();
  }

  Future<bool> updateData(
    DateTime lastModifiedDate, {
    Set<ContinentDto>? continentDtos,
    Set<AreaDto>? areaDtos,
  }) async {
    try {
      await batch((batch) async {
        final companion = LastModifiedDto(
          id: _kAreaLastModifiedId,
          type: 'geoareas',
          lastModified: lastModifiedDate,
        ).toCompanion(false);

        await into(lastModified).insertOnConflictUpdate(companion);

        if (continentDtos != null) {
          batch.insertAllOnConflictUpdate(continents, continentDtos);
        }

        if (areaDtos != null) {
          batch.insertAllOnConflictUpdate(areas, areaDtos);
        }
      });
      return true;
    } catch (e) {
      return false;
    }
  }

  Future<DateTime?> fetchLastTimeAreasWereModified() =>
      _fetchLastModifiedTime(_kAreaLastModifiedId);

  Future<DateTime?> _fetchLastModifiedTime(int lastModifiedId) async {
    final query = select(lastModified)
      ..where((tbl) => tbl.id.equals(lastModifiedId));
    final dto = await query.getSingleOrNull();
    return dto?.lastModified;
  }

  Future<InspirationDto?> fetchCachedInspiration(int id) {
    return (select(
      inspirations,
    )..where((tbl) => tbl.id.equals(id))).getSingleOrNull();
  }

  Future<void> cacheInspiration(InspirationDto dto) {
    return into(inspirations).insertOnConflictUpdate(dto.toCompanion(false));
  }

  Future<List<InspirationDto>> fetchInspirations({required String type}) {
    return (select(
      inspirations,
    )..where((tbl) => tbl.selection.equals(type))).get();
  }

  Future<void> clearInspirations() {
    return delete(inspirations).go();
  }

  Future<int> fetchInspirationCount() async {
    return countInspirations().getSingle();
  }

  Future<void> clearGeoAreasAndContinents() async {
    await delete(areas).go();
    await delete(continents).go();
  }

  Future<void> clearUserData() async {
    await delete(areaSelections).go();
    await delete(inspirations).go();
    await delete(citySelections).go();
  }

  Future clearSelections() => delete(areaSelections).go();
}

LazyDatabase openConnection() {
  return LazyDatabase(() async {
    final dbFolder = await getApplicationDocumentsDirectory();
    final file = File(join(dbFolder.path, 'visited-flutter.db'));
    return NativeDatabase.createInBackground(
      file,
      logStatements: false,
    );
  });
}
