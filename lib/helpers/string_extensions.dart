extension Helpers on String {
  String get capitalized {
    if (isEmpty) {
      return this;
    }

    if (length == 1) {
      return toUpperCase();
    }

    return this[0].toUpperCase() + substring(1).toLowerCase();
  }

  String get capitalizedWords => split(' ').map((e) {
        if (e == 'U.S.' || e == 'D.C.') {
          return e;
        }
        return e.capitalized;
      }).join(' ');

  String get initials =>
      split(' ').map((e) => e.isNotEmpty ? e[0] : '').join().toUpperCase();

  String get unlocalized => this;
}
