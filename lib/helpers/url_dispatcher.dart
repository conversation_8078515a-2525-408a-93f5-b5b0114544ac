import 'dart:io';

import 'package:url_launcher/url_launcher.dart';

class UrlDispatcher {
  static Future<bool> launch(String? url) async {
    if (url == null) {
      return false;
    }

    late Uri uri;
    try {
      uri = Uri.parse(url);
    } catch (e) {
      return false;
    }

    return launchUri(uri);
  }

  static Future<bool> launchUri(Uri uri, {bool useInAppBrowser = true}) async {
    if (!(await canLaunchUrl(uri))) {
      return false;
    }

    final mode = Platform.isIOS && useInAppBrowser
        ? LaunchMode.inAppWebView
        : LaunchMode.externalApplication;
    return launchUrl(
      uri,
      mode: mode,
    );
  }
}
