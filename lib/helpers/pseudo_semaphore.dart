import 'dart:async';

/// When multiple parts of the app want to call the came procedure,
/// this will only allow the first first request to do the work and
/// all subsequent requests will wait (kind of like a Kirkland Brand Semaphore)
/// and get the result of the first request.
class PseudoSemaphore<T> {
  PseudoSemaphore({
    required this.expensiveOperation,
  });

  final _requests = <Completer<T>>{};
  final Future<T> Function() expensiveOperation;

  bool _isProcessing = false;

  Future<T> request() {
    if (_isProcessing) {
      return _addRequest();
    }

    _isProcessing = true;
    expensiveOperation().then(_onCompleted).onError((error, stackTrace) {
      for (final request in _requests) {
        request.completeError(
          error ?? Exception('Unknown Error'),
          stackTrace,
        );
      }
      _requests.clear();
    }).whenComplete(() => _isProcessing = false);

    return _addRequest();
  }

  Future<T> _addRequest() {
    final completer = Completer<T>();
    _requests.add(completer);
    return completer.future;
  }

  void _onCompleted(T result) {
    for (final request in _requests) {
      request.complete(result);
    }
    _requests.clear();
  }
}
