import 'dart:io';

import 'package:flutter/cupertino.dart';
import 'package:package_info_plus/package_info_plus.dart';

import '../networking/environment.dart';

class EnvironmentFetcher {
  static Locale get systemLocale {
    final languageComponents = Platform.localeName.split('_');
    return Locale.fromSubtags(
      languageCode: languageComponents.first,
      scriptCode: languageComponents.length == 3 ? languageComponents[1] : null,
      countryCode: languageComponents.length == 3
          ? languageComponents[2]
          : languageComponents.length == 2
              ? languageComponents[1]
              : null,
    );
  }

  Future<EnvironmentData> fetch(BuildContext context) async {
    final pixelDensity = _pixelDensity(context);

    const flavour = String.fromEnvironment(
      'flavour',
      defaultValue: 'prod',
    );

    final package = await PackageInfo.fromPlatform();
    final version = package.version;

    // // Only take the first two components of the locale (chinese can have 3)
    // final languageComponents = Platform.localeName.split('_');
    // final language = languageComponents.first == 'zh'
    //     ? languageComponents.take(2).join('-')
    //     : languageComponents.first;

    const apiVersion = 'v2';

    const ngrokDev =
        'https://yearly-complete-hornet.ngrok-free.app/$apiVersion';

    const baseUrl = flavour == 'dev'
        ? ngrokDev
        : 'https://visitedapi.arrivinginhighheels.com/$apiVersion';

    const baseUrlFallback = flavour == 'dev'
        ? ngrokDev
        : 'https://visitedbackend.arrivinginhighheels.com/$apiVersion';

    const stripeKey = flavour == 'dev'
        ? 'pk_test_51LP6J9JYxEcRrjMrsBK0Bj6yWeqE8SJUlVW254pwYwcyp1DCx9IRqkT1hhg74KZAYkwXdWiSNNcWYlQ87IEBPugL00goWyRbe9'
        : 'pk_live_51LP6J9JYxEcRrjMr0oWDWNidmQnGBkNSLDJOLxz6XJFYhhS9LVidPwmiRjkbGG7vuIhLpJ6vSXVsSQ93um1w8Fag00USBySHFx';

    return EnvironmentData(
      baseUrl: baseUrl,
      baseUrlFallback: baseUrlFallback,
      name: flavour,
      version: version,
      pixelDensity: pixelDensity,
      stripePublishKey: stripeKey,
    );
  }

  double _pixelDensity(BuildContext context) {
    try {
      final window = View.of(context);
      final pixelRatio = window.devicePixelRatio;

      if (pixelRatio < 1.3) {
        return 1.0;
      } else if (pixelRatio >= 1.3 && pixelRatio < 1.7) {
        return 1.5;
      } else if (pixelRatio >= 1.7 && pixelRatio < 2.5) {
        return 2.0;
      } else if (pixelRatio >= 2.5 && pixelRatio < 3.5) {
        return 3.0;
      } else {
        return 4.0;
      }
    } catch (e) {
      return 1.0;
    }
  }
}

class Environment extends InheritedWidget {
  final EnvironmentData environment;

  static EnvironmentData? of(BuildContext context) =>
      context.dependOnInheritedWidgetOfExactType<Environment>()?.environment;

  const Environment({
    super.key,
    required this.environment,
    required super.child,
  });

  @override
  bool updateShouldNotify(covariant InheritedWidget oldWidget) => false;
}
