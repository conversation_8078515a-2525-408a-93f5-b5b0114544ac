import 'package:firebase_core/firebase_core.dart';
import 'package:firebase_in_app_messaging/firebase_in_app_messaging.dart'; // Don't remove
import 'package:flutter/cupertino.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:sentry_flutter/sentry_flutter.dart';

import 'dependency_injection/dependency_injector.dart';
import 'features/ads/ad_manager.dart';
import 'features/areas/geometry_bloc.dart';
import 'visited_app.dart';

void main() async {
  SentryWidgetsFlutterBinding.ensureInitialized();
  await Firebase.initializeApp();
  DependencyInjector.register(() => GeometryBloc());

  FirebaseInAppMessaging.instance.setAutomaticDataCollectionEnabled(true);
  AdManager.init();

  LicenseRegistry.addLicense(() async* {
    final arimo = await rootBundle.loadString('assets/fonts/Arimo-LICENSE.txt');
    yield LicenseEntryWithLineBreaks(['google_fonts'], arimo);
  });

  await SentryFlutter.init((options) {
    options.dsn =
        'https://<EMAIL>/5861200';
    options.tracesSampleRate = 1.0;
    options.tracesSampler = (samplingContext) => 0.5;
    options.enableMemoryPressureBreadcrumbs = true;

    options.replay.quality = SentryReplayQuality.medium;
    options.replay.sessionSampleRate = 1.0;
    options.replay.onErrorSampleRate = 0.9;
  }, appRunner: () => runApp(SentryScreenshotWidget(child: const Visited())));
}
