import 'geo_area.dart';

class Continent {
  final int id;
  final String name;
  final Set<GeoArea> countries;

  const Continent({
    required this.id,
    required this.countries,
    required this.name,
  });

  Continent.fromJson(Map json)
      : id = json['id'],
        name = json['name'],
        countries = <GeoArea>{};

  @override
  String toString() {
    return 'Continent{name: $name}';
  }

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is Continent && runtimeType == other.runtimeType && id == other.id;

  @override
  int get hashCode => id.hashCode;
}
