class Etags {
  final String? subdivisions;
  final String? info;
  final String? experiences;
  final String? deals;

  const Etags({
    this.subdivisions,
    this.info,
    this.experiences,
    this.deals,
  });

  Etags.fromJson(Map json)
      : subdivisions = json['subdivisions'],
        info = json['info'],
        experiences = json['experiences'],
        deals = json['deals'];

  Map toJson() {
    return {
      'subdivisions': subdivisions,
      'info': info,
      'experiences': experiences,
      'deals': deals
    };
  }
}
