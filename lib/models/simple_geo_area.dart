class SimpleGeoArea {
  final int id;
  final String isoCode;
  final String name;

  SimpleGeoArea({
    required this.id,
    required this.isoCode,
    required this.name,
  });

  SimpleGeoArea.fromJson(Map json)
      : id = json['id'],
        isoCode = json['isoCode'],
        name = json['name'];

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is SimpleGeoArea &&
          runtimeType == other.runtimeType &&
          id == other.id;

  @override
  int get hashCode => id.hashCode;

  @override
  String toString() {
    return 'SimpleGeoArea{isoCode: $isoCode, name: $name}';
  }

  Map toJson() => {
        'id': id,
        'isoCode': isoCode,
        'name': name,
      };
}
