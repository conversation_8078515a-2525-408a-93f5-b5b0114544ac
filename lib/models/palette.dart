import 'dart:ui';

import 'package:flutter/material.dart';

import 'color_extensions.dart';
import 'selection.dart';

class Palette {
  final String id;
  final Color water;
  final Color land;
  final Color want;
  final Color been;
  final Color live;
  final Color border;
  final Color label;
  final Color lived;

  final bool defaultLight;
  final bool defaultDark;

  const Palette({
    required this.id,
    required this.water,
    required this.land,
    required this.want,
    required this.been,
    required this.live,
    required this.lived,
    required this.border,
    required this.label,
    this.defaultLight = false,
    this.defaultDark = false,
  });

  Palette.fromJson(Map json)
      : id = json['name'],
        water = _parseColour(json['water']),
        land = _parseColour(json['land']),
        want = _parseColour(json['want']),
        been = _parseColour(json['been']),
        live = _parseColour(json['live']),
        lived = _parseColour(json['lived'] ?? json['live']),
        border = _parseColour(json['border']),
        label = _parseColour(json['label']),
        defaultLight = json['defaultLight'] ?? false,
        defaultDark = json['defaultDark'] ?? false;

  Palette.fromIsolate(Map json)
      : id = json['name'],
        water = ColorSerialization.fromJson(json['water']),
        land = ColorSerialization.fromJson(json['land']),
        want = ColorSerialization.fromJson(json['want']),
        been = ColorSerialization.fromJson(json['been']),
        live = ColorSerialization.fromJson(json['live']),
        lived = ColorSerialization.fromJson(json['lived'] ?? json['live']),
        border = ColorSerialization.fromJson(json['border']),
        label = ColorSerialization.fromJson(json['label']),
        defaultLight = json['defaultLight'] ?? false,
        defaultDark = json['defaultDark'] ?? false;

  static Color _parseColour(Object colorData) {
    if (colorData is Map) {
      return ColorSerialization.fromJson(colorData);
    }

    if (colorData is List) {
      return Color.fromRGBO(
        colorData[0],
        colorData[1],
        colorData[2],
        1,
      );
    }

    throw Exception('unsupported color type');
  }

  static Palette defaultPalette(BuildContext context) =>
      Theme.of(context).brightness == Brightness.light
          ? stockPalettes[0]
          : stockPalettes[1];
  static Palette get standard => stockPalettes.first;

  static const stockPalettes = [
    Palette(
        id: 'traveller',
        water: Color(0xffffffff),
        land: Color(0xfff2eae7),
        want: Color(0xff6999f2),
        been: Color(0xff092a68),
        live: Color(0xffffd710),
        border: Color(0xffffffff),
        label: Color(0xff2e2a2a),
        lived: Color(0xffffaf00),
        defaultLight: true,
        defaultDark: false),
    Palette(
        id: 'nightTraveller',
        water: Color(0xff202020),
        land: Color(0xff423c39),
        want: Color(0xffff5964),
        been: Color(0xff0a4dc2),
        live: Color(0xffffe74c),
        border: Color(0xff0c0c0c),
        label: Color(0xffd7d7d7),
        lived: Color(0xfff5c256),
        defaultLight: false,
        defaultDark: true),
    Palette(
        id: 'naturalist',
        water: Color(0xffA9B18E),
        land: Color(0xffE3C1A1),
        want: Color(0xffFFDB58),
        been: Color(0xff8B4513),
        live: Color(0xff556B2F),
        border: Color(0xff3D2B1F),
        label: Color(0xffffffff),
        lived: Color(0xff38471F),
        defaultLight: false,
        defaultDark: false),
    Palette(
        id: 'explorer',
        water: Color(0xff211B22),
        land: Color(0xffC9A000),
        want: Color(0xffA989A8),
        been: Color(0xffD5C3D5),
        live: Color(0xff533C53),
        border: Color(0xff53472D),
        label: Color(0xffc0c0c0),
        lived: Color(0xff998888),
        defaultLight: false,
        defaultDark: false),
    Palette(
        id: 'original',
        water: Color(0xffd4ecf6),
        land: Color(0xfff2eae7),
        want: Color(0xffc4a5ec),
        been: Color(0xff66cb80),
        live: Color(0xffe99235),
        border: Color(0xff525252),
        label: Color(0xff2e2a2a),
        lived: Color(0xfff5bc71),
        defaultLight: false,
        defaultDark: false),
    Palette(
        id: 'weekender',
        water: Color(0xff090d10),
        land: Color(0xff03131b),
        want: Color(0xff38506f),
        been: Color(0xff2cb4db),
        live: Color(0xff043a4a),
        border: Color(0xff869eac),
        label: Color(0xfffafafa),
        lived: Color(0xff106f82),
        defaultLight: false,
        defaultDark: false),
    Palette(
        id: 'historian',
        water: Color(0xfff3eeeb),
        land: Color(0xffe2d2ba),
        want: Color(0xffb3a29c),
        been: Color(0xff9ea69c),
        live: Color(0xffb5ba8f),
        border: Color(0xff6d6249),
        label: Color(0xff2e2a2a),
        lived: Color(0xffcbd1b4),
        defaultLight: false,
        defaultDark: false),
    Palette(
        id: 'thrill_seeker',
        water: Color(0xfffafafa),
        land: Color(0xffcdcdcd),
        want: Color(0xffb991ce),
        been: Color(0xff82d570),
        live: Color(0xff5ab846),
        border: Color(0xffcccccc),
        label: Color(0xff2e2a2a),
        lived: Color(0xff9dd67a),
        defaultLight: false,
        defaultDark: false),
    Palette(
        id: 'cultural_buff',
        water: Color(0xfff7f7f7),
        land: Color(0xfff2e3dd),
        want: Color(0xffe17e8e),
        been: Color(0xff92c5de),
        live: Color(0xff0571b0),
        border: Color(0xffffffff),
        label: Color(0xff2e2a2a),
        lived: Color(0xff3496e0),
        defaultLight: false,
        defaultDark: false)
  ];

  Color colorForSelection(Selection selection) {
    return switch (selection) {
      Selection.live => live,
      Selection.lived => lived,
      Selection.been => been,
      Selection.want => want,
      _ => land,
    };
  }

  Color labelColorForSelection(Selection selection) {
    final background = colorForSelection(selection);
    return background.legibleForegroundColor();
  }

  Palette copyWith({
    String? id,
    Color? water,
    Color? land,
    Color? want,
    Color? been,
    Color? live,
    Color? lived,
    Color? border,
    Color? label,
    Color? cityLive,
    Color? cityBeen,
    Color? cityWant,
  }) =>
      Palette(
        id: id ?? this.id,
        water: water ?? this.water,
        land: land ?? this.land,
        want: want ?? this.want,
        been: been ?? this.been,
        live: live ?? this.live,
        lived: lived ?? this.lived,
        border: border ?? this.border,
        label: label ?? this.label,
        defaultLight: defaultLight,
        defaultDark: defaultDark,
      );

  Map toJson({bool eightBitColourArrays = false}) => {
        'name': id,
        'water': eightBitColourArrays ? water.toEightBitList() : water.toJson(),
        'land': eightBitColourArrays ? land.toEightBitList() : land.toJson(),
        'live': eightBitColourArrays ? live.toEightBitList() : live.toJson(),
        'lived': eightBitColourArrays ? lived.toEightBitList() : lived.toJson(),
        'been': eightBitColourArrays ? been.toEightBitList() : been.toJson(),
        'want': eightBitColourArrays ? want.toEightBitList() : want.toJson(),
        'border': eightBitColourArrays ? border.toEightBitList() : border.toJson(),
        'label': eightBitColourArrays ? label.toEightBitList() : label.toJson(),
      };

  @override
  String toString() {
    return 'Palette{id: $id, water: $water, land: $land, want: $want, been: $been, live: $live, border: $border, label: $label, lived: $lived, defaultLight: $defaultLight, defaultDark: $defaultDark}';
  }

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is Palette && runtimeType == other.runtimeType && id == other.id;

  @override
  int get hashCode => id.hashCode;

  static Palette outlined(BuildContext context) {
    final theme = Theme.of(context);
    return Palette(
      water: Colors.transparent,
      land: theme.scaffoldBackgroundColor,
      want: theme.colorScheme.primary.withValues(alpha: 0.15),
      been: theme.colorScheme.primary,
      live: theme.colorScheme.primary,
      lived: theme.colorScheme.primary,
      border: theme.colorScheme.primary,
      label: Colors.transparent,
      id: 'outlined',
    );
  }
}

/// To provide backwards compatibility with the old 256bit way of modeling colours
extension EightBitConversion on double {
  int to8Bits() => (this * 255).round();
}

extension ColorSerialization on Color {
  List toEightBitList() => [
    r.to8Bits(),
    g.to8Bits(),
    b.to8Bits()
  ];

  Map toJson() => {
        'r': r,
        'g': g,
        'b': b,
        'a': a,
        'colourSpace': colorSpace.name,
      };

  static Color fromJson(Map data) {
    return Color.from(
      red: data['r'],
      green: data['g'],
      blue: data['b'],
      alpha: data['a'],
      colorSpace: ColorSpace.values.firstWhere(
        (space) => space.name == data['colourSpace'],
      ),
    );
  }
}
