import 'dart:math';
import 'dart:ui';

class Coordinate {
  static const max = Coordinate(latitude: 90.0, longitude: 180.0);
  static const min = Coordinate(latitude: -90.0, longitude: -180.0);

  final num latitude;
  final num longitude;

  const Coordinate({
    required this.latitude,
    required this.longitude,
  });

  Coordinate.fromList(List json)
      : longitude = json[0],
        latitude = json[1];

  factory Coordinate.fromScreenPosition(Offset position, Size mapSize) {
    final relative = Point(
      position.dx / mapSize.width,
      position.dy / mapSize.height,
    );

    return Coordinate.fromRelativePosition(relative);
  }

  factory Coordinate.fromRelativePosition(Point<num> relative) {
    final screenX = relative.x * (max.longitude - min.longitude);
    final screenY = relative.y * (max.latitude - min.latitude);

    final longitude = screenX - max.longitude;
    final latitude = -screenY + max.latitude;

    return Coordinate(
      latitude: latitude,
      longitude: longitude,
    );
  }

  List toList() => [longitude, latitude];

  Point get screenPoint {
    final screenX = longitude + max.longitude;
    final screenY = -latitude + max.latitude;

    final relativeX = screenX / (max.longitude - min.longitude);
    final relativeY = screenY / (max.latitude - min.latitude);

    return Point(relativeX, relativeY);
  }

  Point<double> renderablePoint(Size size, {Offset offset = Offset.zero}) {
    final point = screenPoint;
    return Point(
      (point.x * size.width) - offset.dx,
      (point.y * size.height) - offset.dy,
    );
  }

  bool get isCenter => latitude == 0 && longitude == 0;

  @override
  String toString() {
    const precision = 4;
    return 'Coordinate{latitude: ${latitude.toStringAsFixed(precision)}, longitude: ${longitude.toStringAsFixed(precision)}';
  }

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is Coordinate &&
          runtimeType == other.runtimeType &&
          latitude == other.latitude &&
          longitude == other.longitude;

  @override
  int get hashCode => latitude.hashCode ^ longitude.hashCode;
}
