import 'package:flutter/widgets.dart';

/// Heavily inspired <PERSON><PERSON>'s TTTOrdinalNumberFormatter for iOS
/// http://matt.me
enum Gender { male, female, neuter }

class OrdinalNumberFormatter {
  static const defaultOrdinalIndicator = '.';

  Gender gender = Gender.neuter;

  String localizedFormat(BuildContext context, int number) {
    final language = Localizations.localeOf(context).languageCode.toLowerCase();
    var ordinalIndicator = defaultOrdinalIndicator;

    if (language == 'en') {
      ordinalIndicator = _englishOrdinalIndicator(number);
    } else if (language == 'fr') {
      ordinalIndicator = _frenchOrdinalIndicator(number);
    } else if (language == 'es') {
      ordinalIndicator = _spanishNumberIndicator(number);
    } else if (language == 'nl') {
      ordinalIndicator = _nlNumberIndicator(number);
    } else if (language == 'it') {
      ordinalIndicator = _itNumberIndicator(number);
    } else if (language == 'pt') {
      ordinalIndicator = _ptOrdinalIndicator(number);
    } else if (language == 'ga') {
      ordinalIndicator = _gaOrdinalIndicator(number);
    } else if (language == 'ja') {
      ordinalIndicator = _jaOrdinalIndicator(number);
    } else if (language == 'zh') {
      ordinalIndicator = _zhOrdinalIndicator(number);
    } else if (language == 'ca') {
      ordinalIndicator = _caOrdinalIndicator(number);
    } else if (language == 'sv') {
      ordinalIndicator = _svOrdinalIndicator(number);
    }

    return '$number$ordinalIndicator';
  }

  String _englishOrdinalIndicator(int number) {
    final lastTwoDigits = number % 100;
    if (lastTwoDigits == 11 || lastTwoDigits == 12 || lastTwoDigits == 13) {
      return 'th';
    }

    switch (number % 10) {
      case 1:
        return 'st';
      case 2:
        return 'nd';
      case 3:
        return 'rd';
      default:
        return 'th';
    }
  }

  String _frenchOrdinalIndicator(int number) {
    if (number == 1) {
      switch (gender) {
        case Gender.male:
          return 'er';
        case Gender.female:
          return 're';
        case Gender.neuter:
          return 'er';
      }
    }
    return 'e';
  }

  String _spanishNumberIndicator(int number) {
    if (gender == Gender.female) {
      return '\u00AA';
    } else {
      return '\u00BA';
    }
  }

  String _nlNumberIndicator(final int number) {
    return 'e';
  }

  String _itNumberIndicator(final int number) {
    switch (gender) {
      case Gender.male:
        return '\u00BA';
      case Gender.female:
        return '\u00AA';
      default:
        return defaultOrdinalIndicator;
    }
  }

  String _ptOrdinalIndicator(final int number) {
    return _itNumberIndicator(number);
  }

  String _gaOrdinalIndicator(final int number) {
    return '\u00FA';
  }

  String _jaOrdinalIndicator(final int number) {
    return '\u756A';
  }

  String _zhOrdinalIndicator(final int number) {
    return '\u7B2C';
  }

  String _caOrdinalIndicator(final int number) {
    if (gender == Gender.female) {
      return 'es';
    }

    switch (number) {
      case 1:
        return 'rs';
      case 2:
        return 'ns';
      case 3:
        return 'rs';
      case 4:
        return 'ts';
      default:
        return 'ns';
    }
  }

  String _svOrdinalIndicator(final int number) {
    // If number % 100 is 11 or 12, ordinals are 11:e and 12:e.
    final tens = number % 100;
    if (tens == 11 || tens == 12) {
      return ':e';
    }

    // 1:a, 2:a, 3:e, 4:e and so on. Also, 21:a, 22:a, 23:e ...
    final ones = number % 10;
    if (ones == 1 || ones == 2) {
      return ':a';
    } else {
      return ':e';
    }
  }
}
