import 'package:flutter/material.dart';

extension ColorLegibiliy on Color {
  Color legibleForegroundColor({
    Color lightColor = Colors.white,
    Color darkColor = Colors.black,
  }) {
    final luminance = computeLuminance();
    return luminance < 0.30 ? lightColor : darkColor;
  }

  Color withBrightness(double factor) {
    final hsl = HSLColor.fromColor(this);
    final adjusted = hsl.withLightness(factor.clamp(0, 1.0));
    return adjusted.toColor();
  }

  Color withSaturation(double factor) {
    final hsl = HSLColor.fromColor(this);
    final adjusted = hsl.withSaturation(factor.clamp(0, 1.0));
    return adjusted.toColor();
  }
}
