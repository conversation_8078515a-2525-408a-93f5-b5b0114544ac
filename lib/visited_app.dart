import 'dart:async';

import 'package:firebase_analytics/firebase_analytics.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:flutter_stripe/flutter_stripe.dart';
import 'package:sentry_flutter/sentry_flutter.dart';

import 'bridges/environment_fetcher.dart';
import 'caching/database.dart';
import 'dependency_injection/dependency_injector.dart';
import 'features/authentication/loading_screen.dart';
import 'features/authentication/login_background.dart';
import 'features/authentication/login_screen.dart';
import 'features/authentication/session_bloc.dart';
import 'features/first_time/first_time_city_search_screen.dart';
import 'features/home/<USER>';
import 'features/localization/supported_language.dart';
import 'features/privacy_agreement/privacy_agreement.dart';
import 'features/privacy_agreement/privacy_form.dart';
import 'generic_widgets/alert.dart';
import 'generic_widgets/navigation_extensions.dart';
import 'generic_widgets/spinner.dart';
import 'l10n/generated/app_localizations.dart';
import 'networking/client.dart';
import 'networking/network_client.dart';

class Visited extends StatefulWidget {
  const Visited({super.key});
  @override
  State createState() => _VisitedState();
}

const visitedPrimaryFont = 'Arimo';

class _VisitedState extends State<Visited> {
  VisitedDatabase? _database;
  Client? _client;
  final navigatorKey = GlobalKey<NavigatorState>();
  final analytics = FirebaseAnalytics.instance;
  StreamSubscription? _authListener;

  @override
  void initState() {
    super.initState();
    _database = VisitedDatabase(openConnection());
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    if (_client == null) {
      _initDependencies();
    }
  }

  Future<void> _initDependencies() async {
    final brightness = View.of(context).platformDispatcher.platformBrightness;
    await _fetchEnvironment();
    assert(_client != null && _database != null);
    DependencyInjector.configure(_client!, _database!, brightness);
    final auth = DependencyInjector.sessionBloc;
    _authListener ??= auth.status.listen(_onSessionStatusUpdated);
  }

  @override
  Widget build(BuildContext context) {
    if (_client == null) {
      return const MaterialApp(home: LoginBackground());
    }

    return Environment(
      environment: _client!.environment,
      child: StreamBuilder<SupportedLanguage?>(
        stream: DependencyInjector.settingsBloc.language,
        builder: (context, snapshot) {
          return MaterialApp(
            title: 'Visited',
            navigatorKey: navigatorKey,
            darkTheme: _buildDarkTheme(),
            theme: _buildLightTheme(),
            supportedLocales: AppLocalizations.supportedLocales,
            locale: snapshot.data?.locale,
            localizationsDelegates: const [
              GlobalMaterialLocalizations.delegate,
              GlobalWidgetsLocalizations.delegate,
              GlobalCupertinoLocalizations.delegate,
              DefaultCupertinoLocalizations.delegate,
              AppLocalizations.delegate,
            ],
            navigatorObservers: [
              SentryNavigatorObserver(
                autoFinishAfter: const Duration(seconds: 5),
                setRouteNameAsTransaction: true,
              ),
              FirebaseAnalyticsObserver(analytics: analytics),
            ],
            home: const LoadingScreen(),
          );
        },
      ),
    );
  }

  ThemeData _buildLightTheme() {
    const visitedBlue = Color(0xFF092a68);
    return ThemeData(
      fontFamily: visitedPrimaryFont,
      primaryColor: visitedBlue,
      visualDensity: VisualDensity.adaptivePlatformDensity,
      textTheme: _buildTextTheme(Brightness.light),
      dividerColor: const Color(0xFFCADCF9),
      secondaryHeaderColor: const Color(0xFFEDF4FF),
      colorScheme: ColorScheme.fromSeed(
        seedColor: visitedBlue,
        secondary: const Color(0xFF6999F2),
      ).copyWith(error: const Color(0xFFC2277B)),
    );
  }

  ThemeData _buildDarkTheme() {
    const background = Color(0xFF14161C);
    const visitedBlue = Color(0xFF6999F2);
    const divider = Color(0xFF2F4A63);

    return ThemeData(
      brightness: Brightness.dark,
      fontFamily: visitedPrimaryFont,
      appBarTheme: const AppBarTheme(backgroundColor: background),
      visualDensity: VisualDensity.adaptivePlatformDensity,
      primaryColor: Colors.white,
      colorScheme: const ColorScheme.dark(
        primary: visitedBlue,
        secondary: Color(0xFFF5F6F9),
      ),
      highlightColor: visitedBlue,
      scaffoldBackgroundColor: background,
      cardTheme: const CardThemeData(color: Color(0xff202945), elevation: 1),
      secondaryHeaderColor: divider,
      bottomNavigationBarTheme: const BottomNavigationBarThemeData(
        backgroundColor: background,
        selectedLabelStyle: TextStyle(
          color: Colors.white,
          fontFamily: visitedPrimaryFont,
        ),
      ),
      cupertinoOverrideTheme: const NoDefaultCupertinoThemeData(
        barBackgroundColor: background,
        scaffoldBackgroundColor: background,
      ),
      textTheme: _buildTextTheme(Brightness.dark),
      dividerColor: divider,
      dividerTheme: const DividerThemeData(color: visitedBlue),
      floatingActionButtonTheme: const FloatingActionButtonThemeData(
        backgroundColor: visitedBlue,
        foregroundColor: Colors.white,
      ),
    );
  }

  TextTheme _buildTextTheme(Brightness brightness) {
    final primaryTextColour = brightness == Brightness.light
        ? const Color(0xFF092C69)
        : const Color(0xFFe9ebf2);

    final baseTheme = TextTheme(
      titleLarge: TextStyle(
        fontFamily: visitedPrimaryFont,
        color: primaryTextColour,
        fontSize: 22,
        fontWeight: FontWeight.bold,
      ),
      titleMedium: TextStyle(
        fontFamily: visitedPrimaryFont,
        color: primaryTextColour,
        fontSize: 14,
        fontWeight: FontWeight.w400,
      ),
      titleSmall: TextStyle(
        fontFamily: visitedPrimaryFont,
        color: primaryTextColour,
        fontSize: 13,
        fontWeight: FontWeight.w400,
      ),
      bodyLarge: const TextStyle(fontFamily: visitedPrimaryFont, fontSize: 13),
    );

    return baseTheme.apply(
      fontFamily: visitedPrimaryFont,
      displayColor: primaryTextColour,
    );
  }

  Future<void> _fetchEnvironment() async {
    try {
      if (_client != null) {
        return;
      }

      final environment = await EnvironmentFetcher().fetch(context);
      Stripe.publishableKey = environment.stripePublishKey;
      Stripe.merchantIdentifier = 'merchant.com.highheels.visited';
      await Stripe.instance.applySettings();

      _client = NetworkClient(environment, _onNoNetwork);
      setState(() {});
    } catch (e) {
      rethrow;
    }
  }

  Future<void> _onNoNetwork() async {
    final context = navigatorKey.currentContext!;
    final localizations = AppLocalizations.of(context)!;
    final alert = Alert(
      title: localizations.errorTitle,
      message: localizations.requiresOnline,
    );
    return alert.show(context, barrierDismissible: false);
  }

  bool _removeEveryRoutePredicate(Route _) => false;

  void _onSessionStatusUpdated(SessionStatus status) {
    final navigator = navigatorKey.currentState;

    late final WidgetBuilder builder;
    late final String name;

    switch (status) {
      case SessionStatus.pending:
        builder = (_) => const LoadingScreen();
        name = 'loading';

      case SessionStatus.needPrivacyAgreement:
        builder = (context) {
          final agreement = PrivacyAgreement();
          final bloc = DependencyInjector.sessionBloc;
          return PrivacyForm(
            agreement: agreement,
            onCancelled: bloc.cancelOnboarding,
            onSubmitTapped: () {
              if (agreement.accepted) {
                SpinnerDialog.showDuringLongProcess(
                  context,
                  job: () => bloc.createAccount(privacyAgreement: agreement),
                );
                return;
              }

              final localizations = AppLocalizations.of(context)!;
              Alert(
                title: localizations.errorTitle,
                message: localizations.privacyAgreementRequired,
              ).show(context);
            },
          );
        };
        name = 'privacyAgreement';

      case SessionStatus.firstTime:
      case SessionStatus.requiresLiveCity:
        builder = (_) => const FirstTimeCitySearchScreen();
        name = 'firstTime';

      case SessionStatus.loggedIn:
        _fadeToScreen(
          navigator: navigator,
          screenName: 'home',
          screen: HomeScreen(analytics: analytics),
        );
        return;

      case SessionStatus.noUser:
        _fadeToScreen(
          navigator: navigator,
          screenName: 'loginScreen',
          screen: const LoginScreen(),
        );
        return;
    }

    navigator?.pushMaterialAndRemoveUntil(
      builder: builder,
      name: name,
      predicate: _removeEveryRoutePredicate,
    );
  }

  void _fadeToScreen({
    required NavigatorState? navigator,
    required String screenName,
    required Widget screen,
  }) {
    navigator?.pushAndRemoveUntil(
      PageRouteBuilder(
        settings: RouteSettings(name: screenName),
        pageBuilder: (context, anim, secondAnim) => screen,
        transitionsBuilder: (context, anim, secondAnim, child) =>
            FadeTransition(opacity: anim, child: child),
      ),
      _removeEveryRoutePredicate,
    );
  }

  @override
  void dispose() {
    _client?.dispose();
    _database?.close();
    _authListener?.cancel();
    DependencyInjector.disposeBlocs();
    super.dispose();
  }
}
