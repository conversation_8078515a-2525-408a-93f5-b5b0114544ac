// ignore: unused_import
import 'package:intl/intl.dart' as intl;
import 'app_localizations.dart';

// ignore_for_file: type=lint

/// The translations for Romanian Moldavian Moldovan (`ro`).
class AppLocalizationsRo extends AppLocalizations {
  AppLocalizationsRo([String locale = 'ro']) : super(locale);

  @override
  String get appName => 'Visited';

  @override
  String get language => 'Limbă';

  @override
  String get pickEmailApp => 'Alegeți aplicația de e-mail';

  @override
  String numberOfCountriesShareMessage(int amount) {
    return 'Am vizitat $amount țări! Câte ai vizitat tu? www.visitedapp.com';
  }

  @override
  String numberOfCitiesShareMessage(int amount) {
    return 'Am vizitat $amount orașe! Câte ai vizitat tu? www.visitedapp.com';
  }

  @override
  String numberOfItemsInTodoListShareMessage(int amount, String listName) {
    return 'Am vizitat $amount $listName! Câte ai vizitat tu? www.visitedapp.com';
  }

  @override
  String get clear => 'Elimină';

  @override
  String get been => 'Vizitat';

  @override
  String get want => 'Doresc';

  @override
  String get live => 'Locuiesc';

  @override
  String get lived => 'Locuiam';

  @override
  String get water => 'Maritim';

  @override
  String get land => 'Terestru';

  @override
  String get borders => 'Granițe';

  @override
  String get labels => 'Etichete';

  @override
  String get legend => 'Legendă';

  @override
  String get inspiration => 'Inspirație';

  @override
  String get inspirations => 'Inspirații';

  @override
  String get delete => 'Șterge';

  @override
  String get unlockVisitedUpsellTitle => 'Vrei mai multe?';

  @override
  String get unlockVisitedUpsellSubtitle =>
      'Deblochează toate funcțiile și bucură-te de Visited în totalitate';

  @override
  String get checkTheDetails => 'Verifica detaliile';

  @override
  String get moreInspirationsComingSoon =>
      'Încercăm să aducem mai multe imagini. Verifică din nou cât de curând!';

  @override
  String get unlockPremiumFeatures => 'Deblochează funcțiile premium';

  @override
  String get purchased => 'Cumpărat!';

  @override
  String get buy => 'Cumpără';

  @override
  String get restorePurchases => 'Ramburzează achiziția';

  @override
  String get ok => 'Ok';

  @override
  String get areYouSure => 'Ești sigur?';

  @override
  String get deleteInspirationConfirmMessage =>
      'Ștergerea acestei cărți este permanentă. Nu puteți recupera această imagine.';

  @override
  String get cancel => 'Anulează';

  @override
  String get map => 'Hartă';

  @override
  String get progress => 'Progres';

  @override
  String get myTravelGoal => 'Destinația mea de călătorie';

  @override
  String goalRemaining(int remaining) {
    return 'Mai sunt încă $remaining!';
  }

  @override
  String get top => 'TOP';

  @override
  String get ofTheWorld => 'din lume!';

  @override
  String get countries => 'țări';

  @override
  String topPlacesVisitedFromCountry(String country) {
    return 'Top cele mai vizitate țări din $country:';
  }

  @override
  String get login => 'Conectare';

  @override
  String get logout => 'Deconectare';

  @override
  String get enterYourEmail => 'Introdu email-ul tău:';

  @override
  String get privacyPolicy => 'Politică de confidențialitate';

  @override
  String get privatePolicyUrl =>
      'https://www.arrivinginhighheels.com/privacy-policy-romanian/';

  @override
  String get termsOfUse => 'Termeni de utilizare';

  @override
  String get termsOfUserUrl =>
      'https://www.arrivinginhighheels.com/terms-of-use-romanian/';

  @override
  String get errorTitle => 'Hopa!';

  @override
  String get enterValidEmail => 'Te rugăm să introduci un email valid';

  @override
  String get settings => 'Setări';

  @override
  String get whereDoYouLive => 'Unde locuiești?';

  @override
  String get whereHaveYouBeen => 'Pe unde ai fost?';

  @override
  String get whereDoYouFlyFrom => 'De unde faci următorul zbor?';

  @override
  String get next => 'Mai departe';

  @override
  String get missingAirports =>
      'Nu găsești ceea ce cauți? Trimite-ne un email <NAME_EMAIL>';

  @override
  String get missingAirportsEmailTitle => 'Aeroporturile lipsesc!';

  @override
  String get supportEmailAddress => '<EMAIL>';

  @override
  String get welcomeTitle => 'Bine ai venit la Visited';

  @override
  String get welcomeSubtitle => 'Te așteaptă aventura vieții';

  @override
  String get getStarted => 'Începe';

  @override
  String get privacyAgreement => 'Acord de confidențialitate';

  @override
  String get privacyAgreementSubtitle =>
      'Te rugăm să accepți următorii termeni înainte de a continua să folosești Visited.';

  @override
  String get privacyAgreementTermsMarkdown =>
      'Bifând această casetă, recunoașteți că ați citit și sunteți de acord să respectați [Privacy Policy] (https://www.arrivinginhighheels.com/privacy-policy) și [Terms of Use](https://www.arrivinginhighheels.com/terms-of-use) Arriving in High Heels.';

  @override
  String get privacyAgreementOptIn =>
      'Sunt de acord să primesc mesaje electronice de la Arriving in High Heels care conțin informații și oferte cu privire la produse, aplicații și servicii care ar putea fi de interes pentru mine, inclusiv notificări de vânzări, promoții, oferte și buletine informative. Pot retrage acest consimțământ în orice moment, așa cum este descris în Politica de confidențialitate sau făcând clic pe linkul „dezabonare” din mesajele electronice.';

  @override
  String get submit => 'Trimite';

  @override
  String get companyName => 'Arriving In High Heels Corporation';

  @override
  String get companyAddress =>
      '31 Claudview Street\nKing City, Ontario\nCanada\nL7B 0C6';

  @override
  String get privacyAgreementRequired =>
      'Trebuie să fiți de acord atât cu termenii noștri, cât și să optați pentru a continua să utilizați aplicația Visited.';

  @override
  String get deleteAccount => 'Sterge contul';

  @override
  String get removeAdsUpsell =>
      'Dorești să nu mai primești reclame și să te dezabonezi de la mesajele electronice?';

  @override
  String get deleteAccountWarning =>
      'Prin ștergerea contului ți se vor ștege toate informațiile de pe serverele noastre. Acest proces este permanent.';

  @override
  String get about => 'Despre';

  @override
  String get popularity => 'Popularitate';

  @override
  String get regions => 'Regiuni';

  @override
  String get population => 'Populație';

  @override
  String get size => 'Dimensiune';

  @override
  String get coverage => 'Acoperire';

  @override
  String get percentOfCountryVisited => '% vizitat din țară';

  @override
  String get visited => 'vizitat';

  @override
  String get notes => 'Note';

  @override
  String get kmSquared => 'km²';

  @override
  String get customize => 'Personalizează';

  @override
  String get onlyCountSovereign => 'Suveran recunoscut de ONU';

  @override
  String get countUkSeparately => 'Arată țările din Regatul Unit separat';

  @override
  String get showLegend => 'Arată legenda';

  @override
  String get showLivedPin => 'Arată orașele cu eticheta locuit';

  @override
  String get useMyColours => 'Folosește culorile mele';

  @override
  String get mapColors => 'Culorile hărții';

  @override
  String get traveller => 'Călătorul';

  @override
  String get nightTraveller => 'Călătorul nocturn';

  @override
  String get original => 'Independentul';

  @override
  String get explorer => 'Exploratorul';

  @override
  String get weekender => 'Călătorul de weekend';

  @override
  String get naturalist => 'Naturistul';

  @override
  String get historian => 'Istoricianul';

  @override
  String get thrillSeeker => 'Căutătorul de senzații';

  @override
  String get culturalBuff => 'Pasionatul de cultură';

  @override
  String get myColors => 'Culorile mele';

  @override
  String get experiences => 'Experiențe';

  @override
  String get done => 'Gata';

  @override
  String get experiencesInstructions => 'Apasă pe butonul + pentru a începe!';

  @override
  String get continueText => 'Continuă';

  @override
  String get experiencesDescription => 'Ce îți place să faci când călătorești?';

  @override
  String get visitedWebsiteShortLink => 'https://www.visitedapp.com';

  @override
  String get sharingHashtag => '#Visited';

  @override
  String get myTravelMap => 'Harta mea de călătorie';

  @override
  String percentOfWorldSeen(int percentage) {
    return 'Am văzut $percentage% din lume';
  }

  @override
  String get requiresOnline =>
      'Ne pare rău, Visited are nevoie de conexiune la internet. Te rugăm să accesezi setările aplicației și să te asiguri că \\r Wi-Fi sau Datele movile sunt activate, iar modul Avion este dezactivat.';

  @override
  String get list => 'Listă';

  @override
  String get more => 'Mai multe';

  @override
  String get myCountrySelections => 'Țările pe care le-am ales';

  @override
  String get cities => 'Orașe';

  @override
  String get citiesInstructions =>
      'Apasă pe o țară pentru a putea selecta orașele.';

  @override
  String get missingCitiesEmailTitle => 'Lipsesc orașele!';

  @override
  String get lists => 'Liste';

  @override
  String get disputedTerritories => 'Teritorii disputate';

  @override
  String get sponsored => 'Sponsorizat';

  @override
  String get places => 'Locuri';

  @override
  String get noListsError =>
      'Hopa, nu sunt disponibile liste în acest moment, te rugăm să înerci mai târziu';

  @override
  String get noInspirationsError =>
      'Hopa, nu sunt disponibile fotografii momentan, te rugăm să înerci mai târziu';

  @override
  String get mostFrequentlyVisitedCountries =>
      'Țările dvs. cele mai frecvent vizitate:';

  @override
  String get update => 'Actualizați';

  @override
  String get signup => 'Inscrie-te';

  @override
  String get loginWallSubtitle =>
      'Creați un cont gratuit pentru a experimenta versiunea completă a Visited';

  @override
  String get loseAllSelectionsWarning =>
      'Veți pierde toate selecțiile după închiderea aplicației.';

  @override
  String get createAccount => 'Creați un cont';

  @override
  String get continueWithoutAccount => 'Continuați fără un cont';

  @override
  String get inspirationPromotion =>
      'Inspiră-te din fotografia frumoasă de călătorie';

  @override
  String get saveStatsPromotion => 'Salvați-vă statisticile de călătorie!';

  @override
  String get selectRegionsPromotion => 'Selectați state și provincii';

  @override
  String get experiencesPromotion => 'Urmăriți experiențele din întreaga lume';

  @override
  String get missingListItem =>
      'Ne -a fost dor de ceva? Atingeți aici pentru a ne trimite un e -mail pentru a vă adăuga locul preferat.';

  @override
  String missingListItemEmailTitle(String list) {
    return 'Element lipsă din $list';
  }

  @override
  String listShareMessage(Object amount, Object listName) {
    return 'Am vizitat $amount $listName';
  }

  @override
  String get orderPoster => 'Poster';

  @override
  String get shareMap => 'Partajează harta';

  @override
  String get posterLandingPageTitle => 'Obține posterul tău';

  @override
  String get posterNotAvailableError =>
      'Achiziția de afiș nu este disponibilă chiar acum. Vă rugăm să încercați din nou mai târziu.';

  @override
  String posterPricePlusShipping(Object price, Object shipping) {
    return '$price + $shipping Livrare';
  }

  @override
  String get posterDescriptionMarkdownApple =>
      '## Despre hărțile noastre de imprimare personalizate\nTipăriți harta lumii personalizate. Personalizați -l cu culorile proprii și să -l livrați direct acasă.\n \n### Specificații:\n- 16\" x 20\" (40.64cm x 50.8cm)\n- Orientarea peisajului.\n- Micro -cerneală, picături pentru imprimeuri precise, culoare de 8 biți, aproape o calitate a imprimării foto,\n- Hârtie din satin gros de 0,22 mm\n\n### Detalii de expediere:\nTransport de la Toronto, Canada în oriunde în lume folosind Canada Post. Vă rugăm să permiteți 2 până la 4 săptămâni pentru livrare către majoritatea destinațiilor. Toate comenzile sunt expediate rulate într -o cutie de tub de carton la adresa de expediere trimisă. Toată plata este gestionată de Apple Pay, sau Stripe.\n\n\n### Anulare/rambursare:\nComenzile sunt procesate imediat după ce au fost depuse pentru cea mai rapidă schimbare posibilă. Prin urmare, nu există nicio rambursare/anulare disponibilă.';

  @override
  String get posterDescriptionMarkdown =>
      '## Despre hărțile noastre de imprimare personalizate\nTipăriți harta lumii personalizate. Personalizați -l cu culorile proprii și să -l livrați direct acasă.\n \n### Specificații:\n- 16\" x 20\" (40.64cm x 50.8cm)\n- Orientarea peisajului.\n- Micro -cerneală, picături pentru imprimeuri precise, culoare de 8 biți, aproape o calitate a imprimării foto,\n- Hârtie din satin gros de 0,22 mm\n\n### Detalii de expediere:\nTransport de la Toronto, Canada în oriunde în lume folosind Canada Post. Vă rugăm să permiteți 2 până la 4 săptămâni pentru livrare către majoritatea destinațiilor. Toate comenzile sunt expediate rulate într -o cutie de tub de carton la adresa de expediere trimisă. Toată plata este gestionată de Google Pay sau Stripe.\n\n\n### Anulare/rambursare:\nComenzile sunt procesate imediat după ce au fost depuse pentru cea mai rapidă schimbare posibilă. Prin urmare, nu există nicio rambursare/anulare disponibilă.';

  @override
  String get posterCustomizeTitle => 'Personalizați poster';

  @override
  String get enterShippingAddress => 'Introduceți adresa de expediere';

  @override
  String get price => 'Preț';

  @override
  String formattedPlusTax(Object formattedPrice) {
    return '$formattedPrice + taxă';
  }

  @override
  String get showSelections => 'Selecția arată';

  @override
  String get posterNoRefunds =>
      'Nu sunt disponibile rambursări după ce posterul dvs. a fost tipărit.';

  @override
  String get posterReviewOrder => 'Verifica-ti comanda';

  @override
  String get email => 'E-mail';

  @override
  String get emailEmptyError => 'te rog introdu e-mailul tău';

  @override
  String get fullName => 'Numele complet';

  @override
  String get fullNameEmptyError => 'Te rog sa introduci numele complet';

  @override
  String get streetAddressEmptyError =>
      'Vă rugăm să introduceți adresa dvs. de stradă';

  @override
  String get cityEmptyError => 'Vă rugăm să intrați în orașul dvs.';

  @override
  String fieldEmptyError(Object fieldName) {
    return 'Vă rugăm să introduceți $fieldName';
  }

  @override
  String get country => 'Țară';

  @override
  String get countryEmptyError => 'Vă rugăm să intrați în țara dvs.';

  @override
  String get posterReviewOrderTitle => 'Verifica-ti comanda';

  @override
  String get buyNow => 'Cumpără acum';

  @override
  String get secureCheckoutDisclaimer => 'Checkout sigur furnizat de Stripe';

  @override
  String get total => 'Total';

  @override
  String get tax => 'Impozit';

  @override
  String get subtotal => 'Subtotal';

  @override
  String get posterProductName => 'Poster de hartă vizitat personalizat';

  @override
  String get shipping => 'Transport';

  @override
  String get posterOrderReceivedTitle => 'Comanda Primita';

  @override
  String get posterOrderReceivedSubtitle => 'Am primit comanda ta!';

  @override
  String get posterOrderReceivedInstructionsMarkdown =>
      'Verificați e-mailul pentru mai multe actualizări. \nVă rugăm să așteptați până la 4 săptămâni pentru ca posterul să sosească. \nDacă aveți întrebări, vă rugăm să ne trimiteți un e-mail la [<EMAIL>](<EMAIL>)';

  @override
  String get posterOrderReceivedEmailSubject =>
      'Starea comenzii de afiș tipărit';

  @override
  String get moreInfo => 'Mai multe informatii';

  @override
  String get logoutConfirm => 'Doriți să vă deconectați din aplicație?';

  @override
  String get emailNotAvailable => 'Acest e -mail a fost luat.';

  @override
  String get alphabetical => 'Alfabetic';

  @override
  String get firstTimeLiveTutorial =>
      'Furnizarea țării și orașului de origine va personaliza experiența dumneavoastră în aplicație.';

  @override
  String get firstTimeBeenTutorial =>
      'Selectarea locului în care ați fost vă permite să vizualizați harta cu toate țările în care ați fost și să vedeți statisticile personale.';

  @override
  String get progressTooltipGoal =>
      'Obiectivele dvs. de călătorie se bazează pe numărul de țări pe care \"Doriți\" să le călătoriți în comparație cu țările în care ați \"Fost\".';

  @override
  String get progressTooltipRank =>
      'Acest număr arată cum vă situați în comparație cu călătorii din întreaga lume.  Puteți să vă măriți rangul dacă călătoriți în mai multe țări.';

  @override
  String get progressTooltipPercentageOfWorld =>
      'Acest grafic se bazează pe numărul de țări în care ați fost în comparație cu numărul total de țări din lume.';

  @override
  String get sortBy => 'Sortează după';

  @override
  String get updateWishlist => 'Actualizați lista de dorințe';

  @override
  String get mapInfo =>
      'Faceți clic pe țară pentru a selecta fost, dorit sau locuit. De asemenea, puteți face clic pe pictograma care se găsește în colțul din stânga sus pentru vizualizarea listei.';

  @override
  String get oneTimePurchase => 'Totul este o achiziție unică!';

  @override
  String get contact => 'Contactați';

  @override
  String get contactUs => 'Contactați-ne';

  @override
  String get noCitiesSelected => 'Nu ați selectat încă niciun oraș...';

  @override
  String get updateTravelGoal => 'Actualizați obiectivul de călătorie';

  @override
  String get travelGoalComplete =>
      'Felicitări! \n\n -ați finalizat obiectivul de călătorie! \n\ntap butonul + pentru a adăuga mai multe țări.';

  @override
  String loginEmailNotFoundError(String email) {
    return 'Nu există un cont asociat e -mailul $email. Doriți să -l creați acum?';
  }

  @override
  String get tryAgain => 'Încearcă din nou';

  @override
  String get itineraries => 'Planuri de călătorie';

  @override
  String get itinerary => 'Plan de călătorie';

  @override
  String get place => 'Loc';

  @override
  String get itinerariesDescription =>
      'Acestea sunt locurile pentru care v-ați exprimat interesul.\nFolosiți acest ghid pentru a vă ajuta să vă planificați următoarea vacanță.';

  @override
  String get addMore => 'Adăugați mai multe';

  @override
  String get interests => 'Interese';

  @override
  String get selection => 'Selecţie';

  @override
  String get goal => 'Poartă';

  @override
  String get noItineraries => 'Fără Itinerarii';

  @override
  String get noItinerariesExplanation =>
      'Vă rugăm să adăugați câteva locuri, inspirații sau experiențe pentru a vedea cum se generează automat itinerariile dvs.';

  @override
  String get clusterPins => 'Grupare Marcatori Hartă';

  @override
  String get toggleRegions => 'Afișați regiunile la zoom';

  @override
  String get mapProjection => 'Proiecție hartă';

  @override
  String get mercator => 'Mercator';

  @override
  String get equirectangular => 'Echirarectangular';

  @override
  String get yourTravellerType => 'Tipul tău de călător:';

  @override
  String get yourHotelPreferences => 'Preferințele dumneavoastră de hotel:';

  @override
  String get budget => 'Buget';

  @override
  String get midScale => 'Clasă medie';

  @override
  String get luxury => 'Lux';

  @override
  String get noTravellerType =>
      'Adăugați elemente la lista de dorințe pentru a descoperi ce tip de călător sunteți.';

  @override
  String get unlockLived => 'Deblochează Trăit';

  @override
  String get unlockLivedDescription =>
      'Selectați pe hartă unde ați locuit anterior!';

  @override
  String get futureFeaturesDescription => '...și toate funcțiile viitoare';

  @override
  String get yourMostFrequentlyVisitedCountry =>
      'Țara dvs. cel mai frecvent vizitată:';

  @override
  String get departureDate => 'Data plecării';

  @override
  String get returnDate => 'Data întoarcerii';

  @override
  String get hotels => 'Hoteluri';

  @override
  String get food => 'Mâncare';

  @override
  String get travelDates => 'Datele călătoriei';

  @override
  String get posterForMe => 'Pentru mine';

  @override
  String get posterSendGift => 'Trimite un cadou';

  @override
  String get addSelections => 'Adaugă selecții';

  @override
  String get posterType => 'Tip poster';

  @override
  String get help => 'Ajutor';

  @override
  String get tutorialMap =>
      'Atingeți o țară pentru a selecta: fost, dorit și trăit.';

  @override
  String get tutorialMapList =>
      'Atingeți pictograma listă (colțul din stânga sus) pentru a selecta după listă.';

  @override
  String get tutorialCountryDetails =>
      'Atingeți țara și apoi „mai multe” pentru a selecta în funcție de regiune.';

  @override
  String get tutorialItems =>
      'Glisați comutatorul pentru a alege cum doriți să selectați elementele.';

  @override
  String get tutorialInspirations =>
      'Glisați spre dreapta sau spre stânga pentru a trece la următorul card.';

  @override
  String get lifetime => 'Pe viață';

  @override
  String get chooseYourPlan => 'Alege-ți planul';

  @override
  String get requestARefund => 'Solicită o rambursare';

  @override
  String get noPurchasesFound => 'Nicio achiziție găsită.';

  @override
  String get noProductsAvailable => 'Niciun produs disponibil';

  @override
  String get posterLandingAppBar => 'Adu-ți poveștile acasă';

  @override
  String get posterLandingSubHeading => 'Călătoria ta, povestea ta';

  @override
  String get posterLandingSubDescription =>
      'Călătoriile tale sunt mai mult decât călătorii, sunt povești, amintiri și repere. Transformă acele momente de neuitat într-o hartă personalizată a lumii, la fel de unică ca aventurile tale.';

  @override
  String get posterLandingPromoBullet1 =>
      '• O hartă a realizărilor tale: evidențiază fiecare destinație, de la prima ta excursie mare până la cea mai îndrăzneață aventură.';

  @override
  String get posterLandingPromoBullet2 =>
      '• Sărbătorește fiecare călătorie: retrăiește-ți călătoriile zilnic cu o afișă frumos realizată, menită să inspire.';

  @override
  String get posterLandingPromoBullet3 =>
      '• Un cadou pe care îl vor prețui: surprindeți un coleg de călătorie cu o hartă personalizată care prezintă călătoria lor, perfectă pentru zile de naștere, etape sau doar pentru că.';

  @override
  String get posterLandingHowItWorks => 'Cum funcționează!';

  @override
  String get posterLandingHowItWorksStep1 =>
      '1. Personalizați-vă designul: alegeți culori, stiluri și marcați-vă călătoriile (sau ale lor!)';

  @override
  String get posterLandingHowItWorksStep2 =>
      '2. Previzualizează-ți harta: vezi-o să prindă viață înainte de comandă.';

  @override
  String get posterLandingHowItWorksStep3_iOS =>
      '3. Plată sigură: rapidă și sigură cu Apple Pay sau Stripe.';

  @override
  String get posterLandingHowItWorksStep3_android =>
      '3. Plată sigură: rapidă și sigură cu Google Pay sau Stripe.';

  @override
  String get posterLandingHowItWorksStep4 =>
      '4. Gata pentru afișare: Îl vom expedia direct la ușa dvs. (sau a lor).';

  @override
  String get posterLandingCustomerReviewsHeader =>
      'Experiențe de la alți călători';

  @override
  String get posterLandingCustomerReview1 =>
      '„Această hartă este o modalitate excelentă de a ține evidența peste tot unde am călătorit și de a planifica călătoriile noastre viitoare.  Calitatea este solidă și arată minunat agățat în biroul meu.  Am luat una chiar și pentru fratele meu și el nu a putut, să nu mai vorbească despre cât de cool este!” - John C.';

  @override
  String get posterLandingCustomerReview2 =>
      '„Am călătorit în peste 150 de porturi în timp ce lucram pe croazieră. Această hartă este un mare plus pentru camera mea de zi ca o amintire pentru toți anii pe mare.” - Betty K. ';

  @override
  String get posterLandingCustomerReview3 =>
      '„Cadou minunat pentru ziua mamei. Mama mea a fost super emoționată!” Samantha W.';

  @override
  String get posterLandingCustomerReview4 =>
      '„Am imprimat o hartă a locurilor pe care am vrut să le vizitez cu gf-ul meu. A fost un cadou minunat de Crăciun. Și de înaltă calitate.” Brad J.';

  @override
  String get posterLandingSpecifications => 'Specificații';

  @override
  String get posterLandingSpecification1 =>
      '- Dimensiuni: 16„ x 20” (40,64cm x 50,8cm)';

  @override
  String get posterLandingSpecification2 => '- Orientare: Peisaj';

  @override
  String get posterLandingSpecification3 =>
      '- Calitatea imprimării: Micro picături de cerneală, pentru imprimări precise.  Culoare de 8 biți, calitate de imprimare aproape foto.';

  @override
  String get posterLandingSpecification4 =>
      '- Hârtie: Hârtie satinată groasă de 0,22 mm';

  @override
  String get posterLandingShippingHeader => 'Detalii de livrare';

  @override
  String get posterLandingShipping1 =>
      '- Livrare din Toronto, Canada oriunde în lume folosind Canada Post.';

  @override
  String get posterLandingShipping2 =>
      '- Așteptați 2-4 săptămâni pentru livrarea către majoritatea destinațiilor.';

  @override
  String get posterLandingShipping3 =>
      '- Toate comenzile sunt rulate într-o cutie tubulară de carton la adresa de expediere pe care o trimiteți.';

  @override
  String get posterLandingCancellationHeader => 'Anulare/Reambursare:';

  @override
  String get posterLandingCancellationBody =>
      'Restituirile sunt disponibile înainte ca posterul dvs. să fie trimis la tipografie, ceea ce poate dura până la 24 de ore.  După ce comanda dvs. a fost procesată, nu este posibilă nicio rambursare/anulare.  Veți primi un e-mail atunci când comanda dvs. a fost imprimată.';

  @override
  String get unsubscribe => 'Dezabonare';

  @override
  String get unsubscribeConfirmMessage =>
      'Ești sigur că vrei să te dezabonezi? Vei pierde ofertele exclusive și actualizările!';

  @override
  String get updateLive => 'Actualizează Reședința';

  @override
  String get updateLiveDescription =>
      'Pentru a schimba țara în care locuiești, trebuie să selectezi mai întâi o țară nouă care să o înlocuiască.';

  @override
  String get underOneThousand => 'Sub 1.000';

  @override
  String get oneThousandToTenThousand => '1.000 – 10.000';

  @override
  String get overTenThousand => 'Peste 10.000';

  @override
  String get becomeABrandAmbassador => 'Devino ambasador al mărcii';

  @override
  String get instagram => 'Instagram';

  @override
  String get tiktok => 'TikTok';

  @override
  String get youtube => 'YouTube';

  @override
  String get website => 'Website';

  @override
  String get handle => 'Utilizator';

  @override
  String get followers => 'Urmăritori';

  @override
  String get joinBrandAmbassadorProgram =>
      'Alătură-te programului de ambasadori';

  @override
  String get brandAmbassadorProgramDescription =>
      'Îți place Visited? Ca ambasador al mărcii, vei reprezenta comunitatea noastră de călătorie, îți vei prezenta harta și listele de călătorii și îi vei ajuta pe alții să descopere destinații noi și funcții ale aplicației. În schimb, vei primi recompense, reduceri, articole promoționale și multe altele!';

  @override
  String get fillOutTheFormToGetStarted =>
      'Completează formularul de mai jos pentru a începe:';

  @override
  String get yourName => 'Numele tău';

  @override
  String get yourNameEmptyError => 'Te rugăm să introduci numele';

  @override
  String get fillInWhereApplicable => 'Completează unde este cazul:';

  @override
  String get otherNetworks => 'Alte rețele';

  @override
  String get anythingElse => 'Altceva ce ai dori să ne spui?';

  @override
  String get yourTravelsByContinent => 'Călătoriile tale pe continente';

  @override
  String get territories => 'Teritorii';

  @override
  String get couponCode => 'Cod cupon';

  @override
  String get apply => 'Aplică';

  @override
  String get discount => 'Reducere';

  @override
  String get noCouponCode => 'Te rugăm să introduci un cod cupon';

  @override
  String get invalidCouponCode => 'Cod cupon invalid';

  @override
  String get couponApplied => 'Cupon aplicat';

  @override
  String discountPercentage(double percentage) {
    return '$percentage% reducere!';
  }

  @override
  String discountAmount(double amount) {
    return '$amount reducere!';
  }

  @override
  String get thankYou => 'Mulțumim!';

  @override
  String get formSubmitted =>
      'Am primit cererea ta de a deveni ambasador al mărcii. Te vom contacta în curând!';
}
