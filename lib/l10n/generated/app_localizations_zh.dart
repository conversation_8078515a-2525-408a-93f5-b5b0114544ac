// ignore: unused_import
import 'package:intl/intl.dart' as intl;
import 'app_localizations.dart';

// ignore_for_file: type=lint

/// The translations for Chinese (`zh`).
class AppLocalizationsZh extends AppLocalizations {
  AppLocalizationsZh([String locale = 'zh']) : super(locale);

  @override
  String get appName => 'Visited';

  @override
  String get language => '语言';

  @override
  String get pickEmailApp => '选择您的电子邮件应用';

  @override
  String numberOfCountriesShareMessage(int amount) {
    return '我已经去过 $amount 个国家！你去过多少个？ www.visitedapp.com';
  }

  @override
  String numberOfCitiesShareMessage(int amount) {
    return '我已经去过 $amount 个城市！你去过多少个？ www.visitedapp.com';
  }

  @override
  String numberOfItemsInTodoListShareMessage(int amount, String listName) {
    return '我已经去过 $amount 个$listName！你去过多少个？ www.visitedapp.com';
  }

  @override
  String get clear => '清除';

  @override
  String get been => '到过';

  @override
  String get want => '想';

  @override
  String get live => '居住';

  @override
  String get lived => '住过';

  @override
  String get water => '水';

  @override
  String get land => '土地';

  @override
  String get borders => '边境';

  @override
  String get labels => '标签';

  @override
  String get legend => '传奇';

  @override
  String get inspiration => '灵感';

  @override
  String get inspirations => '灵感';

  @override
  String get delete => '删除';

  @override
  String get unlockVisitedUpsellTitle => '想看更多？';

  @override
  String get unlockVisitedUpsellSubtitle => '解锁所有功能并充分享受 Visited';

  @override
  String get checkTheDetails => '查看详情';

  @override
  String get moreInspirationsComingSoon => '我们正在努力获取更多图像。请稍后再看！';

  @override
  String get unlockPremiumFeatures => '解锁高级功能';

  @override
  String get purchased => '已购买！';

  @override
  String get buy => '购买';

  @override
  String get restorePurchases => '恢复购买';

  @override
  String get ok => '好的';

  @override
  String get areYouSure => '你确定吗？';

  @override
  String get deleteInspirationConfirmMessage => '删除此卡是永久性的。之后没有办法恢复这个图像。';

  @override
  String get cancel => '取消';

  @override
  String get map => '地图';

  @override
  String get progress => '进步';

  @override
  String get myTravelGoal => '我的旅行目标';

  @override
  String goalRemaining(int remaining) {
    return '$remaining 还有！';
  }

  @override
  String get top => '最佳';

  @override
  String get ofTheWorld => '世界的！';

  @override
  String get countries => '国家';

  @override
  String topPlacesVisitedFromCountry(String country) {
    return '从$country访问的主要国家/地区：';
  }

  @override
  String get login => '登录';

  @override
  String get logout => '登出';

  @override
  String get enterYourEmail => '输入你的电子邮箱';

  @override
  String get privacyPolicy => '隐私政策';

  @override
  String get privatePolicyUrl =>
      'https://www.arrivinginhighheels.com/privacy-policy-simplified-chinese/';

  @override
  String get termsOfUse => '使用条款';

  @override
  String get termsOfUserUrl =>
      'https://www.arrivinginhighheels.com/terms-of-use-simplified-chinese/';

  @override
  String get errorTitle => '哎呀！';

  @override
  String get enterValidEmail => '请输入有效电子邮件';

  @override
  String get settings => '设置';

  @override
  String get whereDoYouLive => '你住在哪里？';

  @override
  String get whereHaveYouBeen => '你去哪儿了？';

  @override
  String get whereDoYouFlyFrom => '你从哪里飞出去？';

  @override
  String get next => '下一个';

  @override
  String get missingAirports =>
      '没有看到您在寻找什么？ 给我们发送电子邮件至 <EMAIL>';

  @override
  String get missingAirportsEmailTitle => '缺少的机场！';

  @override
  String get supportEmailAddress => '';

  @override
  String get welcomeTitle => '欢迎访问Visited';

  @override
  String get welcomeSubtitle => '一生的冒险等待着';

  @override
  String get getStarted => '开始吧';

  @override
  String get privacyAgreement => '隐私协议';

  @override
  String get privacyAgreementSubtitle => '在继续使用Visited之前，请同意以下条款。';

  @override
  String get privacyAgreementTermsMarkdown =>
      '选中此框，即表示您已阅读并同意受 Arriving in High Heels 的[隐私政策](https://www.arrivinginhighheels.com/privacy-policy) 和[使用条款](http: //www.arrivinginhighheels.com/terms-of-use）。';

  @override
  String get privacyAgreementOptIn =>
      '我同意接收来自 Arriving in High Heels 的电子消息，其中包含有关我可能感兴趣的产品、应用程序和服务的信息和优惠，包括销售通知、促销、优惠和新闻通讯。 我可以按照隐私政策中的说明或通过单击电子消息中的“取消订阅”链接随时撤回此同意。';

  @override
  String get submit => '提交';

  @override
  String get companyName => 'Arriving In High Heels Corporation';

  @override
  String get companyAddress =>
      '31 Claudview Street\nKing City, Ontario\nCanada\nL7B 0C6';

  @override
  String get privacyAgreementRequired => '您必须同意我们的条款并选择继续使用 Visited。';

  @override
  String get deleteAccount => '删除帐户';

  @override
  String get removeAdsUpsell => '您是否希望选择退出广告并取消订阅电子邮件营销？';

  @override
  String get deleteAccountWarning => '删除您的帐户将从我们的服务器中删除您的所有信息。';

  @override
  String get about => '关于';

  @override
  String get popularity => '受欢迎程度';

  @override
  String get regions => '地区';

  @override
  String get population => '人口';

  @override
  String get size => '尺寸';

  @override
  String get coverage => '覆盖范围';

  @override
  String get percentOfCountryVisited => '访问过的国家的百分比';

  @override
  String get visited => '访问过';

  @override
  String get notes => '笔记';

  @override
  String get kmSquared => '平方公里';

  @override
  String get customize => '定制';

  @override
  String get onlyCountSovereign => '联合国承认的主权国家';

  @override
  String get countUkSeparately => '分别计算英国国家';

  @override
  String get showLegend => '显示图例';

  @override
  String get showLivedPin => '显示现场的 Pin';

  @override
  String get useMyColours => '使用我的颜色';

  @override
  String get mapColors => '地图颜色';

  @override
  String get traveller => '旅行者';

  @override
  String get nightTraveller => '夜行者';

  @override
  String get original => '原本的';

  @override
  String get explorer => '探险家';

  @override
  String get weekender => '周末';

  @override
  String get naturalist => '博物学家';

  @override
  String get historian => '历史学家';

  @override
  String get thrillSeeker => '寻求刺激的人';

  @override
  String get culturalBuff => '文化爱好者';

  @override
  String get myColors => '我的颜色';

  @override
  String get experiences => '经验';

  @override
  String get done => '完毕';

  @override
  String get experiencesInstructions => '点击 + 按钮开始！';

  @override
  String get continueText => '继续';

  @override
  String get experiencesDescription => '你旅行时喜欢做什么？';

  @override
  String get visitedWebsiteShortLink =>
      'https://visitedapp.com/visited%e6%97%85%e6%b8%b8%e5%ba%94%e7%94%a8-%e7%a8%8b%e5%bc%8f/';

  @override
  String get sharingHashtag => '#Visited';

  @override
  String get myTravelMap => '我的旅行地图';

  @override
  String percentOfWorldSeen(int percentage) {
    return '我已经看到了 $percentage% 的世界';
  }

  @override
  String get requiresOnline =>
      '抱歉，已访问需要有效的网络连接。 请打开您的设置应用程序并确保\\r Wi-Fi 或蜂窝数据已启用且飞行模式已禁用';

  @override
  String get list => '列表';

  @override
  String get more => '更多的';

  @override
  String get myCountrySelections => '我的国家选择';

  @override
  String get cities => '城市';

  @override
  String get citiesInstructions => '点击任何国家/地区以开始选择城市。';

  @override
  String get missingCitiesEmailTitle => '失踪的城市！';

  @override
  String get lists => '列表';

  @override
  String get disputedTerritories => '受争议的领土';

  @override
  String get sponsored => '已赞助的';

  @override
  String get places => '地点';

  @override
  String get noListsError => '哎呀，目前没有可用的列表，请稍后再试';

  @override
  String get noInspirationsError => '哎呀，现在没有可用的照片，请稍后再试';

  @override
  String get mostFrequentlyVisitedCountries => '您最常访问的国家/地区：';

  @override
  String get update => '更新';

  @override
  String get signup => '报名';

  @override
  String get loginWallSubtitle => '创建一个免费帐户来体验 Visited 的完整版本';

  @override
  String get loseAllSelectionsWarning => '关闭应用程序后，您将失去所有选择。';

  @override
  String get createAccount => '创建帐户';

  @override
  String get continueWithoutAccount => '继续无帐户';

  @override
  String get inspirationPromotion => '获得美丽的旅游摄影灵感';

  @override
  String get saveStatsPromotion => '保存您的旅行统计数据！';

  @override
  String get selectRegionsPromotion => '选择州和省';

  @override
  String get experiencesPromotion => '世界各地的跟踪体验';

  @override
  String get missingListItem => '我们错过了什么吗？点击此处，向我们发送一封电子邮件，以获取您喜欢的地方。';

  @override
  String missingListItemEmailTitle(String list) {
    return '从$list中丢失项目';
  }

  @override
  String listShareMessage(Object amount, Object listName) {
    return '我访问了$amount $listName';
  }

  @override
  String get orderPoster => '海报';

  @override
  String get shareMap => '共享地图';

  @override
  String get posterLandingPageTitle => '获取您的海报';

  @override
  String get posterNotAvailableError => '海报购买目前不可用。请稍后再试。';

  @override
  String posterPricePlusShipping(Object price, Object shipping) {
    return '$price + $shipping运输';
  }

  @override
  String get posterDescriptionMarkdownApple =>
      '## 关于我们的自定义打印地图\n打印您的个性化世界地图。用自己的颜色自定义，并将其直接交付到您的家中。\n \n### 规格：\n- 16\" x 20\" (40.64cm x 50.8cm)\n- 景观取向。\n- 微墨水，精确印花的液滴，8位颜色，几乎是照片打印质量，\n- 0.22毫米厚的缎面纸\n\n### 送货细节：\n使用加拿大邮政运送从加拿大多伦多到世界任何地方。请允许2到4周的时间到达大多数目的地。所有订单均在纸板管盒中卷起，以提交的运输地址。所有付款均由Apple Pay，Google Pay或Stripe处理。\n\n\n### 取消/退款：\n提交最快的周转后，立即处理订单。因此，没有可用的退款/取消。';

  @override
  String get posterDescriptionMarkdown =>
      '## 关于我们的自定义打印地图\n打印您的个性化世界地图。用自己的颜色自定义，并将其直接交付到您的家中。\n \n### 规格：\n- 16\" x 20\" (40.64cm x 50.8cm)\n- 景观取向。\n- 微墨水，精确印花的液滴，8位颜色，几乎是照片打印质量，\n- 0.22毫米厚的缎面纸\n\n### 送货细节：\n使用加拿大邮政运送从加拿大多伦多到世界任何地方。请允许2到4周的时间到达大多数目的地。所有订单均在纸板管盒中卷起，以提交的运输地址。所有付款均由Apple Pay，Google Pay或Stripe处理。\n\n\n### 取消/退款：\n提交最快的周转后，立即处理订单。因此，没有可用的退款/取消。';

  @override
  String get posterCustomizeTitle => '自定义海报';

  @override
  String get enterShippingAddress => '输入运输地址';

  @override
  String get price => '价格';

  @override
  String formattedPlusTax(Object formattedPrice) {
    return '$formattedPrice +税';
  }

  @override
  String get showSelections => '显示选择';

  @override
  String get posterNoRefunds => '打印海报后，没有退款。';

  @override
  String get posterReviewOrder => '查看你的订单';

  @override
  String get email => '电子邮件';

  @override
  String get emailEmptyError => '请输入您的电子邮件';

  @override
  String get fullName => '全名';

  @override
  String get fullNameEmptyError => '请输入您的全名';

  @override
  String get streetAddressEmptyError => '请输入您的街道地址';

  @override
  String get cityEmptyError => '请输入您的城市';

  @override
  String fieldEmptyError(Object fieldName) {
    return '请输入您的$fieldName';
  }

  @override
  String get country => '国家';

  @override
  String get countryEmptyError => '请输入您的国家';

  @override
  String get posterReviewOrderTitle => '查看你的订单';

  @override
  String get buyNow => '立即购买';

  @override
  String get secureCheckoutDisclaimer => '条纹提供的安全结帐';

  @override
  String get total => '全部的';

  @override
  String get tax => '税';

  @override
  String get subtotal => '小计';

  @override
  String get posterProductName => '自定义访问的地图海报';

  @override
  String get shipping => '船运';

  @override
  String get posterOrderReceivedTitle => '收到订单';

  @override
  String get posterOrderReceivedSubtitle => '我们收到了您的订单！';

  @override
  String get posterOrderReceivedInstructionsMarkdown =>
      '检查您的电子邮件以获取更多更新。\n请允许最多 4 周的时间让您的海报到达。\n如果您有任何问题，请发送电子邮件至 [<EMAIL>](<EMAIL>)';

  @override
  String get posterOrderReceivedEmailSubject => '印刷海报订单状态';

  @override
  String get moreInfo => '更多信息';

  @override
  String get logoutConfirm => '您想注销该应用程序吗？';

  @override
  String get emailNotAvailable => '那封电子邮件已被收集。';

  @override
  String get alphabetical => '字母顺序';

  @override
  String get firstTimeLiveTutorial => '提供你的祖国和城市将使你的应用体验个性化。';

  @override
  String get firstTimeBeenTutorial => '选择你去过的地方，你就可以查看你去过的所有国家的地图，并看到个人统计数据。';

  @override
  String get progressTooltipGoal =>
      '你的旅行目标是基于你 \"想要 \"旅行的国家与你 \"去过 \"的国家之间的数量。';

  @override
  String get progressTooltipRank =>
      '这个数字显示了你与世界各地的旅行者相比的排名情况。 你可以通过去更多国家旅行来提高你的排名。';

  @override
  String get progressTooltipPercentageOfWorld => '这张图是基于你已经去过的国家数量与世界上所有国家的对比。';

  @override
  String get sortBy => '排序方式';

  @override
  String get updateWishlist => '更新愿望清单';

  @override
  String get mapInfo => '点击国家，选择去过、想要或居住的国家。你也可以点击左上角的图标来查看列表。';

  @override
  String get oneTimePurchase => '所有东西都是一次性购买!';

  @override
  String get contact => '联系我们';

  @override
  String get contactUs => '联系我们';

  @override
  String get noCitiesSelected => '您还没有选择任何城市，但...';

  @override
  String get updateTravelGoal => '更新旅行目标';

  @override
  String get travelGoalComplete =>
      '恭喜！\\ n \\，您已经完成了您的旅行目标！ \n\ntap +按钮以添加更多国家。';

  @override
  String loginEmailNotFoundError(String email) {
    return '没有帐户关联电子邮件$email。您想现在创建它吗？';
  }

  @override
  String get tryAgain => '再次尝试';

  @override
  String get itineraries => '旅行计划';

  @override
  String get itinerary => '旅行计划';

  @override
  String get place => '地点';

  @override
  String get itinerariesDescription => '这些是你表示有兴趣的地方\n使用这个指南来帮助计划你的下一个假期。';

  @override
  String get addMore => '添加更多';

  @override
  String get interests => '利益';

  @override
  String get selection => '选择';

  @override
  String get goal => '目标';

  @override
  String get noItineraries => '没有行程';

  @override
  String get noItinerariesExplanation => '请添加一些地点、灵感或体验，以查看您的行程自动生成。';

  @override
  String get clusterPins => '群集图钉';

  @override
  String get toggleRegions => '缩放时显示区域';

  @override
  String get mapProjection => '地图投影';

  @override
  String get mercator => '墨卡托';

  @override
  String get equirectangular => '等矩圆柱';

  @override
  String get yourTravellerType => '您的旅行者类型:';

  @override
  String get yourHotelPreferences => '您的酒店偏好：';

  @override
  String get budget => '经济型';

  @override
  String get midScale => '中档';

  @override
  String get luxury => '豪华型';

  @override
  String get noTravellerType => '将项目添加到您的愿望清单中，以发现您是哪种旅行者。';

  @override
  String get unlockLived => '解锁居住';

  @override
  String get unlockLivedDescription => '在地图上选择您以前居住过的地方！';

  @override
  String get futureFeaturesDescription => '...以及所有未来的功能';

  @override
  String get yourMostFrequentlyVisitedCountry => '您最常访问的国家：';

  @override
  String get departureDate => '出发日期';

  @override
  String get returnDate => '返回日期';

  @override
  String get hotels => '酒店';

  @override
  String get food => '食物';

  @override
  String get travelDates => '旅行日期';

  @override
  String get posterForMe => '为我';

  @override
  String get posterSendGift => '送礼物';

  @override
  String get addSelections => '添加选择';

  @override
  String get posterType => '海报类型';

  @override
  String get help => '帮助';

  @override
  String get tutorialMap => '点击一个国家可以选择：去过、想要和居住过。';

  @override
  String get tutorialMapList => '点击列表图标（左上角）以按列表进行选择。';

  @override
  String get tutorialCountryDetails => '点击国家/地区，然后点击“更多”以按地区进行选择。';

  @override
  String get tutorialItems => '滑动切换开关以选择您想要选择项目的方式。';

  @override
  String get tutorialInspirations => '向右或向左滑动即可移至下一张卡片。';

  @override
  String get lifetime => '终身';

  @override
  String get chooseYourPlan => '选择您的计划';

  @override
  String get requestARefund => '申请退款';

  @override
  String get noPurchasesFound => '未找到购买记录。';

  @override
  String get noProductsAvailable => '没有可用的产品';

  @override
  String get posterLandingAppBar => '把你的故事带回家';

  @override
  String get posterLandingSubHeading => '你的旅行，你的故事';

  @override
  String get posterLandingSubDescription =>
      '你的旅行不仅仅是一次旅行，更是一段段故事、一段段回忆和一段段里程碑。将这些难忘的时刻变成一张个性化的世界地图，就像你的冒险经历一样独一无二。';

  @override
  String get posterLandingPromoBullet1 => '• 成就地图：从你的第一次远行到你最大胆的冒险，突出显示每一个目的地。';

  @override
  String get posterLandingPromoBullet2 => '• 庆祝每一次旅程：用精美的海报重温你的每日旅程，激发你的灵感。';

  @override
  String get posterLandingPromoBullet3 =>
      '• 一份他们会珍藏的礼物：用一张定制的地图展示他们的旅程，给同行的旅伴一个惊喜，非常适合生日、里程碑或其他场合。';

  @override
  String get posterLandingHowItWorks => '使用方法！';

  @override
  String get posterLandingHowItWorksStep1 => '1. 定制你的设计：选择颜色、样式，标记你的（或他们的）旅程！';

  @override
  String get posterLandingHowItWorksStep2 => '2. 预览你的地图：在下单前预览它的效果。';

  @override
  String get posterLandingHowItWorksStep3_iOS =>
      '3. 安全支付：使用 Apple Pay 或 Stripe 快速安全。';

  @override
  String get posterLandingHowItWorksStep3_android =>
      '3. 安全支付：使用 Google Pay 或 Stripe 快速安全。';

  @override
  String get posterLandingHowItWorksStep4 => '4. 准备展示：我们将直接运送到您家门口（或他们家门口）。';

  @override
  String get posterLandingCustomerReviewsHeader => '同行者的经验';

  @override
  String get posterLandingCustomerReview1 =>
      '“这张地图是记录我所有旅行记录和规划未来行程的绝佳方式。质量上乘，挂在办公室里看起来棒极了。我甚至还给我弟弟买了一张，他赞不绝口，说它太酷了！” - John C.';

  @override
  String get posterLandingCustomerReview2 =>
      '“我在邮轮上工作期间，去过超过150个港口。这张地图挂在我客厅里，是我在海上航行多年的美好回忆。” - Betty K.';

  @override
  String get posterLandingCustomerReview3 => '“母亲节的超棒礼物。我妈妈超级感动！” Samantha W.';

  @override
  String get posterLandingCustomerReview4 =>
      '“我打印了一张我想和女朋友一起去的地方的地图。这真是一份很棒的圣诞礼物。质量也很好。” Brad J.';

  @override
  String get posterLandingSpecifications => '规格';

  @override
  String get posterLandingSpecification1 =>
      '• 尺寸：16英寸 x 20英寸 (40.64厘米 x 50.8厘米)';

  @override
  String get posterLandingSpecification2 => '• 方向：横向';

  @override
  String get posterLandingSpecification3 =>
      '• 打印质量：微墨滴，打印精​​准。8位色彩，几乎达到照片打印的质量。';

  @override
  String get posterLandingSpecification4 => '• 纸张：0.22 毫米厚缎纹纸';

  @override
  String get posterLandingShippingHeader => '配送详情';

  @override
  String get posterLandingShipping1 => '• 使用加拿大邮政从加拿大多伦多运送至世界各地。';

  @override
  String get posterLandingShipping2 => '• 大多数目的地需要 2-4 周才能送达。';

  @override
  String get posterLandingShipping3 => '• 所有订单均卷入纸筒包装盒中，寄送至您提交的收货地址。';

  @override
  String get posterLandingCancellationHeader => '取消/退款：';

  @override
  String get posterLandingCancellationBody =>
      '海报送至打印机前可退款，打印机最多可能需要 24 小时。订单处理完毕后，将无法退款/取消。订单打印完成后，您将收到一封电子邮件。';

  @override
  String get unsubscribe => '取消订阅';

  @override
  String get unsubscribeConfirmMessage => '您确定要取消订阅吗？您将错过独家优惠和更新！';

  @override
  String get updateLive => '更新居住地';

  @override
  String get updateLiveDescription => '要更改您居住的国家，您必须首先选择一个新国家来替换它。';

  @override
  String get underOneThousand => '少于1,000';

  @override
  String get oneThousandToTenThousand => '1,000 - 10,000';

  @override
  String get overTenThousand => '超过10,000';

  @override
  String get becomeABrandAmbassador => '成为品牌大使';

  @override
  String get instagram => 'Instagram';

  @override
  String get tiktok => 'TikTok';

  @override
  String get youtube => 'YouTube';

  @override
  String get website => '网站';

  @override
  String get handle => '用户名';

  @override
  String get followers => '关注者';

  @override
  String get joinBrandAmbassadorProgram => '加入品牌大使计划';

  @override
  String get brandAmbassadorProgramDescription =>
      '喜欢Visited吗？作为品牌大使，您将代表我们的旅行社区，展示您的地图和旅行清单，并帮助他人发现新的目的地和应用功能。作为回报，您将获得奖励、折扣、周边产品等等！';

  @override
  String get fillOutTheFormToGetStarted => '填写下面的表格开始：';

  @override
  String get yourName => '您的姓名';

  @override
  String get yourNameEmptyError => '请输入您的姓名';

  @override
  String get fillInWhereApplicable => '在适用的地方填写：';

  @override
  String get otherNetworks => '其他网络';

  @override
  String get anythingElse => '还有什么您希望我们知道的吗？';

  @override
  String get yourTravelsByContinent => '您按大洲的旅行';

  @override
  String get territories => '领土';

  @override
  String get couponCode => '优惠券代码';

  @override
  String get apply => '应用';

  @override
  String get discount => '折扣';

  @override
  String get noCouponCode => '请输入优惠券代码';

  @override
  String get invalidCouponCode => '无效的优惠券代码';

  @override
  String get couponApplied => '优惠券已应用';

  @override
  String discountPercentage(double percentage) {
    return '$percentage% 折扣！';
  }

  @override
  String discountAmount(double amount) {
    return '$amount 折扣！';
  }

  @override
  String get thankYou => '谢谢！';

  @override
  String get formSubmitted => '我们已收到您申请成为品牌大使的请求。我们将尽快与您联系！';
}

/// The translations for Chinese, as used in Taiwan (`zh_TW`).
class AppLocalizationsZhTw extends AppLocalizationsZh {
  AppLocalizationsZhTw() : super('zh_TW');

  @override
  String get appName => 'Visited';

  @override
  String get language => '語言';

  @override
  String get pickEmailApp => '選擇您的電子郵件應用程式';

  @override
  String numberOfCountriesShareMessage(int amount) {
    return '我已經去過 $amount 個國家！你去過多少個？ www.visitedapp.com';
  }

  @override
  String numberOfCitiesShareMessage(int amount) {
    return '我已經去過 $amount 個城市！你去過多少個？ www.visitedapp.com';
  }

  @override
  String numberOfItemsInTodoListShareMessage(int amount, String listName) {
    return '我已經去過 $amount 個$listName！你去過多少個？ www.visitedapp.com';
  }

  @override
  String get clear => '清除';

  @override
  String get been => '到過';

  @override
  String get want => '想';

  @override
  String get live => '居住';

  @override
  String get lived => '住過';

  @override
  String get water => '水';

  @override
  String get land => '土地';

  @override
  String get borders => '邊境';

  @override
  String get labels => '標籤';

  @override
  String get legend => '傳奇';

  @override
  String get inspiration => '靈dis感';

  @override
  String get inspirations => '靈感';

  @override
  String get delete => '刪除';

  @override
  String get unlockVisitedUpsellTitle => '想看更多？';

  @override
  String get unlockVisitedUpsellSubtitle => '解鎖所有功能並充分享受 Visited';

  @override
  String get checkTheDetails => '查看詳情';

  @override
  String get moreInspirationsComingSoon => '我們正在努力獲取更多圖像。請稍後再看！';

  @override
  String get unlockPremiumFeatures => '解鎖高級功能';

  @override
  String get purchased => '已購買！';

  @override
  String get buy => '購買';

  @override
  String get restorePurchases => '恢復購買';

  @override
  String get ok => '好的';

  @override
  String get areYouSure => '你確定嗎？';

  @override
  String get deleteInspirationConfirmMessage => '刪除此卡是永久性的。之後沒有辦法恢復這個圖像。';

  @override
  String get cancel => '取消';

  @override
  String get map => '地圖';

  @override
  String get progress => '進步';

  @override
  String get myTravelGoal => '我的旅行目標';

  @override
  String goalRemaining(int remaining) {
    return '$remaining 還有！';
  }

  @override
  String get top => '最佳';

  @override
  String get ofTheWorld => '世界的！';

  @override
  String get countries => '國家';

  @override
  String topPlacesVisitedFromCountry(String country) {
    return '從$country訪問的主要國家/地區：';
  }

  @override
  String get login => '登錄';

  @override
  String get logout => '登出';

  @override
  String get enterYourEmail => '輸入你的電子郵箱';

  @override
  String get privacyPolicy => '隱私政策';

  @override
  String get privatePolicyUrl =>
      'https://www.arrivinginhighheels.com/privacy-policy-traditional-chinese/';

  @override
  String get termsOfUse => '使用條款';

  @override
  String get termsOfUserUrl =>
      'https://www.arrivinginhighheels.com/terms-of-use-traditional-chinese/';

  @override
  String get errorTitle => '哎呀！';

  @override
  String get enterValidEmail => '請輸入有效電子郵件';

  @override
  String get settings => '設置';

  @override
  String get whereDoYouLive => '你住在哪裡？';

  @override
  String get whereHaveYouBeen => '你去哪兒了？';

  @override
  String get whereDoYouFlyFrom => '你從哪裡飛出去？';

  @override
  String get next => '下一個';

  @override
  String get missingAirports =>
      '沒有看到您在尋找什麼？給我們發送電子郵件至 <EMAIL>';

  @override
  String get missingAirportsEmailTitle => '缺少的機場！';

  @override
  String get supportEmailAddress => '';

  @override
  String get welcomeTitle => '歡迎訪問Visited';

  @override
  String get welcomeSubtitle => '一生的冒險等待著';

  @override
  String get getStarted => '開始吧';

  @override
  String get privacyAgreement => '隱私協議';

  @override
  String get privacyAgreementSubtitle => '在繼續使用Visited之前，請同意以下條款。';

  @override
  String get privacyAgreementTermsMarkdown =>
      '選中此框，即表示您已閱讀並同意受Arriving in High Heels 的[隱私政策](https://www.arrivinginhighheels.com/privacy-policy) 和[使用條款](http: //www.arrivinginhighheels. com/terms-of-use）。';

  @override
  String get privacyAgreementOptIn =>
      '我同意接收來自 Arriving in High Heels 的電子消息，其中包含有關我可能感興趣的產品、應用程序和服務的信息和優惠，包括銷售通知、促銷、優惠和新聞通訊。我可以按照隱私政策中的說明或通過單擊電子消息中的“取消訂閱”鏈接隨時撤回此同意。';

  @override
  String get submit => '提交';

  @override
  String get companyName => 'Arriving In High Heels Corporation';

  @override
  String get companyAddress =>
      '31 Claudview Street\nKing City, Ontario\nCanada\nL7B 0C6';

  @override
  String get privacyAgreementRequired => '您必須同意我們的條款並選擇繼續使用 Visited。';

  @override
  String get deleteAccount => '刪除帳戶';

  @override
  String get removeAdsUpsell => '您是否希望選擇退出廣告並取消訂閱電子郵件營銷？';

  @override
  String get deleteAccountWarning => '刪除您的帳戶將從我們的服務器中刪除您的所有信息。';

  @override
  String get about => '關於';

  @override
  String get popularity => '受歡迎程度';

  @override
  String get regions => '地區';

  @override
  String get population => '人口';

  @override
  String get size => '尺寸';

  @override
  String get coverage => '覆蓋範圍';

  @override
  String get percentOfCountryVisited => '訪問過的國家的百分比';

  @override
  String get visited => '訪問過';

  @override
  String get notes => '筆記';

  @override
  String get kmSquared => '平方公里';

  @override
  String get customize => '定制';

  @override
  String get onlyCountSovereign => '聯合國承認的主權國家';

  @override
  String get countUkSeparately => '分別計算英國國家';

  @override
  String get showLegend => '顯示圖例';

  @override
  String get showLivedPin => '顯示現場的 Pin';

  @override
  String get useMyColours => '使用我的顏色';

  @override
  String get mapColors => '地圖顏色';

  @override
  String get traveller => '旅行者';

  @override
  String get nightTraveller => '夜行者';

  @override
  String get original => '原本的';

  @override
  String get explorer => '探險家';

  @override
  String get weekender => '週末';

  @override
  String get naturalist => '博物學家';

  @override
  String get historian => '歷史學家';

  @override
  String get thrillSeeker => '尋求刺激的人';

  @override
  String get culturalBuff => '文化愛好者';

  @override
  String get myColors => '我的顏色';

  @override
  String get experiences => '經驗';

  @override
  String get done => '完畢';

  @override
  String get experiencesInstructions => '點擊 + 按鈕開始！';

  @override
  String get continueText => '繼續';

  @override
  String get experiencesDescription => '你旅行時喜歡做什麼？';

  @override
  String get visitedWebsiteShortLink =>
      'https://visitedapp.com/visited%e6%97%85%e9%81%8a%e6%87%89%e7%94%a8-%e7%a8%8b%e5%bc%8f/#';

  @override
  String get sharingHashtag => '#Visited';

  @override
  String get myTravelMap => '我的旅行地圖';

  @override
  String percentOfWorldSeen(int percentage) {
    return '我已經看到了 $percentage% 的世界';
  }

  @override
  String get requiresOnline =>
      '抱歉，已訪問需要有效的網絡連接。請打開您的設置應用程序並確保\\r Wi-Fi 或蜂窩數據已啟用且飛行模式已禁用';

  @override
  String get list => '列表';

  @override
  String get more => '更多的';

  @override
  String get myCountrySelections => '我的國家選擇';

  @override
  String get cities => '城市';

  @override
  String get citiesInstructions => '點擊任何國家/地區以開始選擇城市。';

  @override
  String get missingCitiesEmailTitle => '失踪的城市！';

  @override
  String get lists => '列表';

  @override
  String get disputedTerritories => '受爭議的領土';

  @override
  String get sponsored => '已贊助的';

  @override
  String get places => '地點';

  @override
  String get noListsError => '哎呀，目前沒有可用的列表，請稍後再試';

  @override
  String get noInspirationsError => '哎呀，現在沒有可用的照片，請稍後再試';

  @override
  String get mostFrequentlyVisitedCountries => '您最常訪問的國家/地區：';

  @override
  String get update => '更新';

  @override
  String get signup => '報名';

  @override
  String get loginWallSubtitle => '創建一個免費帳戶來體驗 Visited 的完整版本';

  @override
  String get loseAllSelectionsWarning => '關閉應用程式後，您將失去所有選擇。';

  @override
  String get createAccount => '創建帳戶';

  @override
  String get continueWithoutAccount => '繼續無帳戶';

  @override
  String get inspirationPromotion => '獲得美麗的旅遊攝影靈感';

  @override
  String get saveStatsPromotion => '保存您的旅行統計數據！';

  @override
  String get selectRegionsPromotion => '選擇州和省';

  @override
  String get experiencesPromotion => '世界各地的跟蹤體驗';

  @override
  String get missingListItem => '我們錯過了什麼嗎？點擊此處，向我們發送一封電子郵件，以獲取您喜歡的地方。';

  @override
  String missingListItemEmailTitle(String list) {
    return '從$list中丟失項目';
  }

  @override
  String listShareMessage(Object amount, Object listName) {
    return '我訪問了$amount $listName';
  }

  @override
  String get orderPoster => '海報';

  @override
  String get shareMap => '共享地圖';

  @override
  String get posterLandingPageTitle => '獲取您的海報';

  @override
  String get posterNotAvailableError => '海報購買目前不可用。請稍後再試。';

  @override
  String posterPricePlusShipping(Object price, Object shipping) {
    return '$price + $shipping運輸';
  }

  @override
  String get posterDescriptionMarkdownApple =>
      '## 關於我們的自定義打印地圖\n打印您的個性化世界地圖。用自己的顏色自定義，並將其直接交付到您的家中。\n \n### 規格：\n- 16\" x 20\" (40.64cm x 50.8cm)\n- 景觀取向。\n- 微墨水，精確印花的液滴，8位顏色，幾乎是照片打印質量，\n- 0.22毫米厚的緞面紙\n\n### 送貨細節：\n使用加拿大郵政運送從加拿大多倫多到世界任何地方。請允許2到4週的時間到達大多數目的地。所有訂單均在紙板管盒中捲起，以提交的運輸地址。所有付款均由Apple Pay，Google Pay或Stripe處理。\n\n\n### 取消/退款：\n提交最快的周轉後，立即處理訂單。因此，沒有可用的退款/取消。';

  @override
  String get posterDescriptionMarkdown =>
      '## 關於我們的自定義打印地圖\n打印您的個性化世界地圖。用自己的顏色自定義，並將其直接交付到您的家中。\n \n### 規格：\n- 16\" x 20\" (40.64cm x 50.8cm)\n- 景觀取向。\n- 微墨水，精確印花的液滴，8位顏色，幾乎是照片打印質量，\n- 0.22毫米厚的緞面紙\n\n### 送貨細節：\n使用加拿大郵政運送從加拿大多倫多到世界任何地方。請允許2到4週的時間到達大多數目的地。所有訂單均在紙板管盒中捲起，以提交的運輸地址。所有付款均由Apple Pay，Google Pay或Stripe處理。\n\n\n### 取消/退款：\n提交最快的周轉後，立即處理訂單。因此，沒有可用的退款/取消。';

  @override
  String get posterCustomizeTitle => '自定義海報';

  @override
  String get enterShippingAddress => '輸入運輸地址';

  @override
  String get price => '價格';

  @override
  String formattedPlusTax(Object formattedPrice) {
    return '$formattedPrice +稅';
  }

  @override
  String get showSelections => '顯示選擇';

  @override
  String get posterNoRefunds => '打印海報後，沒有退款。';

  @override
  String get posterReviewOrder => '查看你的訂單';

  @override
  String get email => '電子郵件';

  @override
  String get emailEmptyError => '請輸入您的電子郵件';

  @override
  String get fullName => '全名';

  @override
  String get fullNameEmptyError => '請輸入您的全名';

  @override
  String get streetAddressEmptyError => '請輸入您的街道地址';

  @override
  String get cityEmptyError => '請輸入您的城市';

  @override
  String fieldEmptyError(Object fieldName) {
    return '請輸入您的$fieldName';
  }

  @override
  String get country => '國家';

  @override
  String get countryEmptyError => '請輸入您的國家';

  @override
  String get posterReviewOrderTitle => '查看你的訂單';

  @override
  String get buyNow => '立即購買';

  @override
  String get secureCheckoutDisclaimer => '條紋提供的安全結帳';

  @override
  String get total => '全部的';

  @override
  String get tax => '稅';

  @override
  String get subtotal => '小計';

  @override
  String get posterProductName => '自定義訪問的地圖海報';

  @override
  String get shipping => '船運';

  @override
  String get posterOrderReceivedTitle => '收到訂單';

  @override
  String get posterOrderReceivedSubtitle => '我們收到了您的訂單！';

  @override
  String get posterOrderReceivedInstructionsMarkdown =>
      '檢查您的電子郵件以獲取更多更新。\n請允許最多 4 週的時間讓您的海報到達。\n如果您有任何問題，請發送電子郵件至 [<EMAIL>](<EMAIL>)';

  @override
  String get posterOrderReceivedEmailSubject => '印刷海報訂單狀態';

  @override
  String get moreInfo => '更多信息';

  @override
  String get logoutConfirm => '您想註銷該應用程序嗎？';

  @override
  String get emailNotAvailable => '那封電子郵件已被收集。';

  @override
  String get alphabetical => '字母順序';

  @override
  String get firstTimeLiveTutorial => '提供您的祖國和城市將個性化您的應用程序體驗。';

  @override
  String get firstTimeBeenTutorial => '選擇您所處的位置，可以查看您去過的所有國家 /地區的地圖並查看個人統計數據。';

  @override
  String get progressTooltipGoal => '您的旅行目標是基於與“曾經”的國家相比，您“想要”旅行的國家數量。';

  @override
  String get progressTooltipRank => '這個數字顯示了您與世界各地的旅行者相比的排名。您可以通過前往更多國家來提高排名。';

  @override
  String get progressTooltipPercentageOfWorld => '該圖基於您曾經與世界總國家進行比較的國家數量。';

  @override
  String get sortBy => '排序方式';

  @override
  String get updateWishlist => '更新願望清單';

  @override
  String get mapInfo => '單擊該國選擇，想要或直播。您也可以單擊左上角的圖標以獲取列表視圖。';

  @override
  String get oneTimePurchase => '一切都是一次購買！';

  @override
  String get contact => '接觸';

  @override
  String get contactUs => '聯繫我們';

  @override
  String get noCitiesSelected => '您尚未選擇任何城市...';

  @override
  String get updateTravelGoal => '更新旅行目標';

  @override
  String get travelGoalComplete =>
      '恭喜！\\ n \\，您已經完成了您的旅行目標！ \n\ntap +按鈕以添加更多國家。';

  @override
  String loginEmailNotFoundError(String email) {
    return '沒有帳戶關聯電子郵件$email。您想現在創建它嗎？';
  }

  @override
  String get tryAgain => '再試一次';

  @override
  String get itineraries => '旅行計劃';

  @override
  String get itinerary => '旅行計劃';

  @override
  String get place => '地方';

  @override
  String get itinerariesDescription => '這些是您表示感興趣的地方使\n用本指南來幫助您計劃下一個假期。';

  @override
  String get addMore => '添加更多';

  @override
  String get interests => '利益';

  @override
  String get selection => '選擇';

  @override
  String get goal => '目標';

  @override
  String get noItineraries => '沒有行程';

  @override
  String get noItinerariesExplanation => '請添加一些地點、靈感或體驗，以查看您的行程自動生成。';

  @override
  String get clusterPins => '群集圖釘';

  @override
  String get toggleRegions => '放大時顯示區域';

  @override
  String get mapProjection => '地圖投影';

  @override
  String get mercator => '墨卡托';

  @override
  String get equirectangular => '等矩圓柱';

  @override
  String get yourTravellerType => '您的旅行者類型:';

  @override
  String get yourHotelPreferences => '您的酒店偏好：';

  @override
  String get budget => '經濟型';

  @override
  String get midScale => '中檔';

  @override
  String get luxury => '豪華型';

  @override
  String get noTravellerType => '將項目添加到您的願望清單中，以發現您是哪種類型的旅行者。';

  @override
  String get unlockLived => '解鎖居住';

  @override
  String get unlockLivedDescription => '在地圖上選擇您以前居住過的地方！';

  @override
  String get futureFeaturesDescription => '...以及所有未來的功能';

  @override
  String get yourMostFrequentlyVisitedCountry => '您最常造訪的國家：';

  @override
  String get departureDate => '出發日期';

  @override
  String get returnDate => '返回日期';

  @override
  String get hotels => '酒店';

  @override
  String get food => '食物';

  @override
  String get travelDates => '旅行日期';

  @override
  String get posterForMe => '為我';

  @override
  String get posterSendGift => '送禮物';

  @override
  String get addSelections => '添加選擇';

  @override
  String get posterType => '海報類型';

  @override
  String get help => '幫助';

  @override
  String get tutorialMap => '點擊一個國家可以選擇：去過、想要和居住過。';

  @override
  String get tutorialMapList => '點擊清單圖示（左上角）以按清單進行選擇。';

  @override
  String get tutorialCountryDetails => '點擊國家/地區，然後點擊“更多”以按地區進行選擇。';

  @override
  String get tutorialItems => '滑動切換開關以選擇您想要選擇項目的方式。';

  @override
  String get tutorialInspirations => '向右或向左滑動即可移至下一張卡片。';

  @override
  String get lifetime => '終身';

  @override
  String get chooseYourPlan => '選擇您的計劃';

  @override
  String get requestARefund => '申請退款';

  @override
  String get noPurchasesFound => '未找到購買記錄。';

  @override
  String get noProductsAvailable => '沒有可用的產品';

  @override
  String get posterLandingAppBar => '把你的故事帶回家';

  @override
  String get posterLandingSubHeading => '你的旅行，你的故事';

  @override
  String get posterLandingSubDescription =>
      '您的旅行不僅僅是一次旅行，它還是一個個故事、一個個回憶和里程碑。將那些難忘的時刻變成一張個人化的世界地圖，就像您的冒險一樣獨特。';

  @override
  String get posterLandingPromoBullet1 =>
      '• 您的成就地圖：突出顯示每個目的地，從您的第一次長途旅行到最大膽的冒險。';

  @override
  String get posterLandingPromoBullet2 => '• 慶祝每一次旅程：透過精心製作、旨在激發靈感的海報每天重溫您的旅程。';

  @override
  String get posterLandingPromoBullet3 =>
      '• 一份他們會珍惜的禮物：用一張展示他們旅程的自訂地圖給同行的旅伴一個驚喜，非常適合生日、里程碑或僅僅因為。';

  @override
  String get posterLandingHowItWorks => '它是如何工作的！';

  @override
  String get posterLandingHowItWorksStep1 =>
      '1. 客製化您的設計：選擇顏色、樣式並標記您的旅程（或他們的旅程！）';

  @override
  String get posterLandingHowItWorksStep2 => '2. 預覽您的地圖：在下訂單之前查看它的顯示效果。';

  @override
  String get posterLandingHowItWorksStep3_iOS =>
      '3.安全支付：使用Apple Pay或Stripe快速且安全。';

  @override
  String get posterLandingHowItWorksStep3_android =>
      '3.安全付款：使用Google Pay或Stripe快速且安全。';

  @override
  String get posterLandingHowItWorksStep4 => '4. 準備展示：我們將直接送到您家門口（或他們家門口）。';

  @override
  String get posterLandingCustomerReviewsHeader => '同行旅客的經驗';

  @override
  String get posterLandingCustomerReview1 =>
      '「這張地圖可以讓我記錄旅行過的每個地方，並規劃我們未來的旅行。 它的品質非常穩固，掛在我的辦公室裡看起來棒極了。 我甚至幫我弟弟也買了一張，他一直在說這有多酷！」 - 約翰 C.';

  @override
  String get posterLandingCustomerReview2 =>
      '「在郵輪工作期間，我曾到過 150 多個港口。這張地圖是我客廳的一大亮點，是我多年來在海上工作的回憶\"。- Betty K. ';

  @override
  String get posterLandingCustomerReview3 => '「母親節的好禮物。我媽媽超級感動！」 Samantha W.';

  @override
  String get posterLandingCustomerReview4 =>
      '「打印了我想和我女朋友去的地方的地圖。這是一份很棒的聖誕禮物。品質也很高\"。Brad J.';

  @override
  String get posterLandingSpecifications => '規格';

  @override
  String get posterLandingSpecification1 =>
      '- 尺寸： 16「 x 20」 (40.64cm x 50.8cm)';

  @override
  String get posterLandingSpecification2 => '- 方向： 景觀';

  @override
  String get posterLandingSpecification3 =>
      '- 列印品質： 微墨水、墨滴可提供精確的列印效果。8 位元色彩，近乎攝影的列印品質。';

  @override
  String get posterLandingSpecification4 => '- 紙張： 0.22mm 厚的緞面紙';

  @override
  String get posterLandingShippingHeader => '運送細節';

  @override
  String get posterLandingShipping1 => '- 使用加拿大郵政從加拿大多倫多運送至世界任何地方。';

  @override
  String get posterLandingShipping2 => '- 運送到大多數目的地需要 2-4 週時間。';

  @override
  String get posterLandingShipping3 => '- 所有訂單都會用紙板筒箱捲好，送到您提交的送貨地址。';

  @override
  String get posterLandingCancellationHeader => '取消/退款：';

  @override
  String get posterLandingCancellationBody =>
      '在您的海報寄送至印刷廠之前（可能需要 24 小時），可以辦理退款。 在您的訂單被處理之後，將無法退款/取消訂單。 您的訂單印刷完成後，您會收到一封电子邮件。';

  @override
  String get unsubscribe => '取消訂閱';

  @override
  String get unsubscribeConfirmMessage => '您確定要取消訂閱嗎？您將錯過獨家優惠和更新！';

  @override
  String get updateLive => '更新居住地';

  @override
  String get updateLiveDescription => '要更改您居住的國家，您必須首先選擇一個新國家來替換它。';

  @override
  String get underOneThousand => '低於1,000';

  @override
  String get oneThousandToTenThousand => '1,000 – 10,000';

  @override
  String get overTenThousand => '超過10,000';

  @override
  String get becomeABrandAmbassador => '成為品牌大使';

  @override
  String get instagram => 'Instagram';

  @override
  String get tiktok => 'TikTok';

  @override
  String get youtube => 'YouTube';

  @override
  String get website => '網站';

  @override
  String get handle => '帳號名稱';

  @override
  String get followers => '粉絲數';

  @override
  String get joinBrandAmbassadorProgram => '加入品牌大使計劃';

  @override
  String get brandAmbassadorProgramDescription =>
      '喜歡使用 Visited 嗎？成為品牌大使後，您將代表我們的旅遊社群，展示您的地圖和旅遊清單，並幫助他人發現新目的地和應用功能。作為回報，您將獲得獎勵、折扣、贈品等好康！';

  @override
  String get fillOutTheFormToGetStarted => '請填寫下方表單以開始：';

  @override
  String get yourName => '您的姓名';

  @override
  String get yourNameEmptyError => '請輸入您的姓名';

  @override
  String get fillInWhereApplicable => '請在適用處填寫：';

  @override
  String get otherNetworks => '其他平台';

  @override
  String get anythingElse => '還有什麼想讓我們知道的嗎？';

  @override
  String get yourTravelsByContinent => '您的洲別旅行紀錄';

  @override
  String get territories => '地區';

  @override
  String get couponCode => '優惠代碼';

  @override
  String get apply => '套用';

  @override
  String get discount => '折扣';

  @override
  String get noCouponCode => '請輸入優惠代碼';

  @override
  String get invalidCouponCode => '無效的優惠代碼';

  @override
  String get couponApplied => '已套用優惠';

  @override
  String discountPercentage(double percentage) {
    return '折扣 $percentage%';
  }

  @override
  String discountAmount(double amount) {
    return '已折扣 $amount！';
  }

  @override
  String get thankYou => '感謝您！';

  @override
  String get formSubmitted => '我們已收到您成為品牌大使的申請。我們將盡快與您聯繫！';
}
