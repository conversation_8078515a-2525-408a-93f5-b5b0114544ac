// ignore: unused_import
import 'package:intl/intl.dart' as intl;
import 'app_localizations.dart';

// ignore_for_file: type=lint

/// The translations for Vietnamese (`vi`).
class AppLocalizationsVi extends AppLocalizations {
  AppLocalizationsVi([String locale = 'vi']) : super(locale);

  @override
  String get appName => 'Visited';

  @override
  String get language => 'Ngôn ngữ';

  @override
  String get pickEmailApp => 'Chọn ứng dụng email của bạn';

  @override
  String numberOfCountriesShareMessage(int amount) {
    return 'Tôi đã đến thăm $amount quốc gia! Bạn đã đến thăm bao nhiêu quốc gia? www.visitedapp.com';
  }

  @override
  String numberOfCitiesShareMessage(int amount) {
    return 'Tôi đã đến thăm $amount thành phố! Bạn đã đến thăm bao nhiêu thành phố? www.visitedapp.com';
  }

  @override
  String numberOfItemsInTodoListShareMessage(int amount, String listName) {
    return 'Tôi đã đến thăm $amount $listName! Bạn đã đến thăm bao nhiêu? www.visitedapp.com';
  }

  @override
  String get clear => 'Xóa';

  @override
  String get been => 'Đã đến';

  @override
  String get want => 'Muốn đến';

  @override
  String get live => 'Đang ở';

  @override
  String get lived => 'Từng Sống';

  @override
  String get water => 'Nước';

  @override
  String get land => 'Đất liền';

  @override
  String get borders => 'Biên giới';

  @override
  String get labels => 'Nhãn';

  @override
  String get legend => 'Chú giải';

  @override
  String get inspiration => 'Nguồn cảm hứng';

  @override
  String get inspirations => 'Nguồn cảm hứng';

  @override
  String get delete => 'Xóa';

  @override
  String get unlockVisitedUpsellTitle => 'Bạn muốn xem thêm?';

  @override
  String get unlockVisitedUpsellSubtitle =>
      'Mở khóa toàn bộ tính năng và tận hưởng trọn vẹn ứng dụng Visited';

  @override
  String get checkTheDetails => 'Xem chi tiết';

  @override
  String get moreInspirationsComingSoon =>
      'Chúng tôi đang nỗ lực để thu thập nhiều hình ảnh hơn. Hãy quay lại sau ít lâu nữa nhé!';

  @override
  String get unlockPremiumFeatures => 'Mở khóa các tính năng cao cấp';

  @override
  String get purchased => 'Đã mua!';

  @override
  String get buy => 'Mua';

  @override
  String get restorePurchases => 'Khôi phục giao dịch mua hàng';

  @override
  String get ok => 'OK';

  @override
  String get areYouSure => 'Bạn chắc chứ?';

  @override
  String get deleteInspirationConfirmMessage =>
      'Thao tác này sẽ có tác dụng vĩnh viễn. Bạn sẽ không thể khôi phục lại được hình ảnh này.';

  @override
  String get cancel => 'Huỷ';

  @override
  String get map => 'Bản đồ';

  @override
  String get progress => 'Tiến độ';

  @override
  String get myTravelGoal => 'Mục tiêu Du lịch';

  @override
  String goalRemaining(int remaining) {
    return '$remaining mục tiêu nữa!';
  }

  @override
  String get top => 'TOP';

  @override
  String get ofTheWorld => 'trên thế giới!';

  @override
  String get countries => 'các quốc gia';

  @override
  String topPlacesVisitedFromCountry(String country) {
    return 'Các quốc gia được người dân $country ghé thăm nhiều nhắt:';
  }

  @override
  String get login => 'Đăng nhập';

  @override
  String get logout => 'Đăng xuất';

  @override
  String get enterYourEmail => 'Nhập email của bạn';

  @override
  String get privacyPolicy => 'Chính sách bảo mật';

  @override
  String get privatePolicyUrl =>
      'https://www.arrivinginhighheels.com/privacy-policy-vietnamese/';

  @override
  String get termsOfUse => 'Điều khoản sử dụng';

  @override
  String get termsOfUserUrl =>
      'https://www.arrivinginhighheels.com/terms-of-use-vietnamese/';

  @override
  String get errorTitle => 'Rất tiếc!';

  @override
  String get enterValidEmail => 'Vui lòng nhập địa chỉ email hợp lệ';

  @override
  String get settings => 'Cài đặt';

  @override
  String get whereDoYouLive => 'Bạn đang sống tại đâu?';

  @override
  String get whereHaveYouBeen => 'Bạn đã từng đặt chân tới những nơi nào?';

  @override
  String get whereDoYouFlyFrom =>
      'Bạn đã từng xuất ngoại bằng máy bay từ địa điểm nào?';

  @override
  String get next => 'Tiếp';

  @override
  String get missingAirports =>
      'Bạn không tìm thấy nơi mình muốn? Gửi email cho chúng tôi theo địa chỉ <EMAIL>';

  @override
  String get missingAirportsEmailTitle => 'Thiếu sân bay trong app!';

  @override
  String get supportEmailAddress => '<EMAIL>';

  @override
  String get welcomeTitle => 'Chào mừng đến với Visited';

  @override
  String get welcomeSubtitle =>
      'Những chuyến phiêu lưu đáng nhớ đang chờ đón bạn';

  @override
  String get getStarted => 'Bắt đầu';

  @override
  String get privacyAgreement => 'Thỏa thuận quyền riêng tư';

  @override
  String get privacyAgreementSubtitle =>
      'Trước khi tiếp tục sử dụng Visited, mời bạn đồng ý với các mục sau đây.';

  @override
  String get privacyAgreementTermsMarkdown =>
      'Khi đánh dấu vào ô này, bạn xác nhận rằng bạn đã đọc và đồng ý chịu sự ràng buộc của [Chính sách Bảo mật] (https://www.arrivinginhighheels.com/privacy-policy) và [Điều khoản Sử dụng] (https://www.arrivinginhighheels.com/terms-of-use) của Arriving in High Heels.';

  @override
  String get privacyAgreementOptIn =>
      'Tôi đồng ý nhận các tin nhắn chứa thông tin và ưu đãi liên quan đến các sản phẩm, ứng dụng và dịch vụ từ phía Arriving in High Heels mà tôi có thể quan tâm, bao gồm các thông báo chào bán, khuyến mại, ưu đãi và bản tin. Tôi có thể rút lại sự đồng ý này bất kỳ lúc nào theo quy định trong Chính sách Bảo mật hoặc bằng cách nhấp vào liên kết “hủy đăng ký” trong các tin nhắn được gửi tới cho tôi.';

  @override
  String get submit => 'Gửi';

  @override
  String get companyName => 'Arriving In High Heels Corporation';

  @override
  String get companyAddress =>
      '31 Claudview Street\nKing City, Ontario\nCanada\nL7B 0C6';

  @override
  String get privacyAgreementRequired =>
      'Bạn phải đồng ý với các điều khoản của chúng tôi và chọn đồng ý nhận mail để tiếp tục sử dụng Visited.';

  @override
  String get deleteAccount => 'Xóa tài khoản';

  @override
  String get removeAdsUpsell =>
      'Bạn có muốn chọn không nhận quảng cáo và hủy đăng ký nhận thông tin marketing qua email không?';

  @override
  String get deleteAccountWarning =>
      'Tất cả thông tin của bạn sẽ bị xóa khỏi máy chủ của chúng tôi sau khi bạn chọn thao tác xóa tài khoản.\n Thao tác này sẽ không thể vãn hồi.';

  @override
  String get about => 'Giới thiệu';

  @override
  String get popularity => 'Nổi bật';

  @override
  String get regions => 'Khu vực';

  @override
  String get population => 'Dân số';

  @override
  String get size => 'Kích thước';

  @override
  String get coverage => 'Chiếm';

  @override
  String get percentOfCountryVisited => '% quốc gia đã ghé thăm';

  @override
  String get visited => 'đã đến thăm';

  @override
  String get notes => 'Ghi chú';

  @override
  String get kmSquared => 'km²';

  @override
  String get customize => 'Tùy chỉnh';

  @override
  String get onlyCountSovereign => 'Chủ quyền được LHQ công nhận';

  @override
  String get countUkSeparately =>
      'Tính riêng các quốc gia trong Vương quốc Anh';

  @override
  String get showLegend => 'Hiển thị Chú giải';

  @override
  String get showLivedPin => 'Hiển thị Nơi đã sống';

  @override
  String get useMyColours => 'Sử dụng Màu sắc của Tôi';

  @override
  String get mapColors => 'Màu bản đồ';

  @override
  String get traveller => 'Lữ khách';

  @override
  String get nightTraveller => 'Lữ khách đi đêm';

  @override
  String get original => 'Người đặc biệt';

  @override
  String get explorer => 'Người khám phá';

  @override
  String get weekender => 'Người chọn dịp cuối tuần';

  @override
  String get naturalist => 'Nhà tự nhiên học';

  @override
  String get historian => 'Nhà sử học';

  @override
  String get thrillSeeker => 'Người săn cảm giác mạnh';

  @override
  String get culturalBuff => 'Người ủng hộ văn hóa';

  @override
  String get myColors => 'Màu sắc của Tôi';

  @override
  String get experiences => 'Trải nghiệm';

  @override
  String get done => 'Xong';

  @override
  String get experiencesInstructions => 'Nhấn vào nút + để bắt đầu!';

  @override
  String get continueText => 'Tiếp tục';

  @override
  String get experiencesDescription => 'Bạn thích làm gì khi đi du lịch?';

  @override
  String get visitedWebsiteShortLink => 'https://www.visitedapp.com';

  @override
  String get sharingHashtag => '#Visited';

  @override
  String get myTravelMap => 'Bản đồ du lịch của tôi';

  @override
  String percentOfWorldSeen(int percentage) {
    return 'Tôi đã đi qua $percentage% nơi trên thế giới';
  }

  @override
  String get requiresOnline =>
      'Xin lỗi, bạn cần kết nối mạng để sử dụng Visited. Vui lòng mở phần cài đặt và đảm bảo rằng bạn đã\\rbật Wi-Fi/Dữ liệu di động hoặc tắt Chế độ máy bay';

  @override
  String get list => 'Danh sách';

  @override
  String get more => 'Xem thêm';

  @override
  String get myCountrySelections => 'Những quốc gia tôi chọn';

  @override
  String get cities => 'Thành phố';

  @override
  String get citiesInstructions =>
      'Nhấn vào quốc gia bất kỳ để bắt đầu chọn thành phố.';

  @override
  String get missingCitiesEmailTitle => 'Thiếu thành phố!';

  @override
  String get lists => 'Danh sách';

  @override
  String get disputedTerritories => 'Vùng có tranh chấp';

  @override
  String get sponsored => 'Được tài trợ';

  @override
  String get places => 'Địa điểm';

  @override
  String get noListsError =>
      'Rất tiếc, không có danh sách nào vào thời điểm này, vui lòng thử lại sau';

  @override
  String get noInspirationsError =>
      'Rất tiếc, hiện nay vẫn chưa có ảnh liên quan, vui lòng thử lại sau';

  @override
  String get mostFrequentlyVisitedCountries =>
      'Các quốc gia bạn thường ghé thăm nhất:';

  @override
  String get update => 'Cập nhật';

  @override
  String get signup => 'Đăng ký';

  @override
  String get loginWallSubtitle =>
      'Tạo tài khoản miễn phí để trải nghiệm phiên bản đầy đủ của Visited';

  @override
  String get loseAllSelectionsWarning =>
      'Bạn sẽ mất tất cả các lựa chọn của mình sau khi đóng ứng dụng.';

  @override
  String get createAccount => 'Tạo Tài khoản';

  @override
  String get continueWithoutAccount => 'Tiếp tục mà không có Tài khoản';

  @override
  String get inspirationPromotion => 'Lấy cảm hứng với nhiếp ảnh du lịch đẹp';

  @override
  String get saveStatsPromotion => 'Lưu số liệu thống kê du lịch của bạn!';

  @override
  String get selectRegionsPromotion => 'Chọn Tiểu bang và Tỉnh';

  @override
  String get experiencesPromotion => 'Theo dõi trải nghiệm trên toàn thế giới';

  @override
  String get missingListItem =>
      'Chúng ta đã bỏ lỡ điều gì đó? Nhấn vào đây để gửi cho chúng tôi một email để thêm địa điểm yêu thích của bạn.';

  @override
  String missingListItemEmailTitle(String list) {
    return 'Mục bị thiếu từ $list';
  }

  @override
  String listShareMessage(Object amount, Object listName) {
    return 'Tôi đã truy cập $amount $listName';
  }

  @override
  String get orderPoster => 'Poster';

  @override
  String get shareMap => 'Bản đồ chia sẻ';

  @override
  String get posterLandingPageTitle => 'Nhận poster của bạn';

  @override
  String get posterNotAvailableError =>
      'Mua poster không có sẵn ngay bây giờ. Vui lòng thử lại sau.';

  @override
  String posterPricePlusShipping(Object price, Object shipping) {
    return '$price + $shipping Vận chuyển';
  }

  @override
  String get posterDescriptionMarkdownApple =>
      '## Về bản đồ in tùy chỉnh của chúng tôi\nIn bản đồ thế giới cá nhân của bạn. Tùy chỉnh nó với màu sắc rất riêng của bạn và được chuyển thẳng đến nhà của bạn.\n \n### Thông số kỹ thuật:\n- 16\" x 20\" (40.64cm x 50.8cm)\n- Định hướng phong cảnh.\n- Micro Ink, Droplets cho các bản in chính xác, màu 8 bit, chất lượng in gần như ảnh,\n- Giấy satin dày 0,22mm\n\n### Chi tiết vận chuyển:\nVận chuyển từ Toronto, Canada đến bất cứ nơi nào trên thế giới sử dụng Canada Post. Vui lòng cho phép 2 đến 4 tuần để giao hàng đến hầu hết các điểm đến. Tất cả các đơn đặt hàng được vận chuyển được cuộn lên trong một hộp ống các tông đến địa chỉ vận chuyển được gửi. Tất cả các khoản thanh toán được xử lý bởi Apple Pay, hoặc Stripe.\n\n\n### Hủy/hoàn tiền:\nĐơn đặt hàng được xử lý ngay sau khi được gửi cho vòng quay nhanh nhất có thể. Do đó, không có hoàn lại tiền/hủy bỏ.';

  @override
  String get posterDescriptionMarkdown =>
      '## Về bản đồ in tùy chỉnh của chúng tôi\nIn bản đồ thế giới cá nhân của bạn. Tùy chỉnh nó với màu sắc rất riêng của bạn và được chuyển thẳng đến nhà của bạn.\n \n### Thông số kỹ thuật:\n- 16\" x 20\" (40.64cm x 50.8cm)\n- Định hướng phong cảnh.\n- Micro Ink, Droplets cho các bản in chính xác, màu 8 bit, chất lượng in gần như ảnh,\n- Giấy satin dày 0,22mm\n\n### Chi tiết vận chuyển:\nVận chuyển từ Toronto, Canada đến bất cứ nơi nào trên thế giới sử dụng Canada Post. Vui lòng cho phép 2 đến 4 tuần để giao hàng đến hầu hết các điểm đến. Tất cả các đơn đặt hàng được vận chuyển được cuộn lên trong một hộp ống các tông đến địa chỉ vận chuyển được gửi. Tất cả các khoản thanh toán được xử lý bởi Google Pay hoặc Stripe.\n\n\n### Hủy/hoàn tiền:\nĐơn đặt hàng được xử lý ngay sau khi được gửi cho vòng quay nhanh nhất có thể. Do đó, không có hoàn lại tiền/hủy bỏ.';

  @override
  String get posterCustomizeTitle => 'Tùy chỉnh poster';

  @override
  String get enterShippingAddress => 'Nhập địa chỉ vận chuyển';

  @override
  String get price => 'Giá bán';

  @override
  String formattedPlusTax(Object formattedPrice) {
    return '$formattedPrice + thuế';
  }

  @override
  String get showSelections => 'Hiển thị lựa chọn';

  @override
  String get posterNoRefunds =>
      'Không hoàn lại tiền có sẵn sau khi poster của bạn đã được in.';

  @override
  String get posterReviewOrder => 'Xem lại đơn hàng của bạn';

  @override
  String get email => 'E-mail';

  @override
  String get emailEmptyError => 'Vui lòng nhập email của bạn';

  @override
  String get fullName => 'Họ và tên';

  @override
  String get fullNameEmptyError => 'Vui lòng nhập tên đầy đủ của bạn';

  @override
  String get streetAddressEmptyError =>
      'Vui lòng nhập địa chỉ đường phố của bạn';

  @override
  String get cityEmptyError => 'Làm ơn nhập tên thành phố bạn đang sinh sống';

  @override
  String fieldEmptyError(Object fieldName) {
    return 'Vui lòng nhập $fieldName';
  }

  @override
  String get country => 'Quốc gia';

  @override
  String get countryEmptyError => 'Vui lòng vào quốc gia của bạn';

  @override
  String get posterReviewOrderTitle => 'Xem lại đơn hàng của bạn';

  @override
  String get buyNow => 'Mua ngay';

  @override
  String get secureCheckoutDisclaimer =>
      'Thanh toán an toàn được cung cấp bởi Stripe';

  @override
  String get total => 'Tổng cộng';

  @override
  String get tax => 'Thuế';

  @override
  String get subtotal => 'Tổng phụ';

  @override
  String get posterProductName => 'Áp phích bản đồ được truy cập tùy chỉnh';

  @override
  String get shipping => 'Đang chuyển hàng';

  @override
  String get posterOrderReceivedTitle => 'Đơn hang đa nhận';

  @override
  String get posterOrderReceivedSubtitle =>
      'Chúng tôi đã nhận được đơn đặt hàng của bạn!';

  @override
  String get posterOrderReceivedInstructionsMarkdown =>
      'Kiểm tra email của bạn để biết thêm thông tin cập nhật. \nXin vui lòng cho phép tối đa 4 tuần cho poster của bạn đến. \nNếu bạn có bất kỳ câu hỏi nào, vui lòng gửi email cho chúng tôi tại [<EMAIL>] (<EMAIL>)';

  @override
  String get posterOrderReceivedEmailSubject =>
      'Tình trạng đơn đặt hàng áp phích in';

  @override
  String get moreInfo => 'Thêm thông tin';

  @override
  String get logoutConfirm => 'Bạn có muốn đăng xuất khỏi ứng dụng không?';

  @override
  String get emailNotAvailable => 'Email đó đã được lấy.';

  @override
  String get alphabetical => 'Bảng chữ cái';

  @override
  String get firstTimeLiveTutorial =>
      'Cung cấp đất nước và thành phố của bạn sẽ cá nhân hóa trải nghiệm ứng dụng của bạn.';

  @override
  String get firstTimeBeenTutorial =>
      'Chọn nơi bạn đã cho phép bạn xem bản đồ của bạn về tất cả các quốc gia bạn đã đến và xem các số liệu thống kê cá nhân.';

  @override
  String get progressTooltipGoal =>
      'Mục tiêu du lịch của bạn dựa trên số lượng quốc gia bạn \"muốn\" đi du lịch so với các quốc gia nơi bạn đã.';

  @override
  String get progressTooltipRank =>
      'Con số này cho thấy cách bạn xếp hạng so với khách du lịch trên khắp thế giới. Bạn có thể tăng thứ hạng của mình bằng cách đi du lịch đến nhiều quốc gia hơn.';

  @override
  String get progressTooltipPercentageOfWorld =>
      'Biểu đồ này dựa trên số lượng quốc gia bạn đã so sánh với tổng số quốc gia trên thế giới.';

  @override
  String get sortBy => 'Sắp xếp theo';

  @override
  String get updateWishlist => 'Cập nhật danh sách mong muốn';

  @override
  String get mapInfo =>
      'Nhấp vào quốc gia để chọn, muốn hoặc sống. Bạn cũng có thể nhấp vào biểu tượng được tìm thấy ở góc trên cùng bên trái để xem danh sách.';

  @override
  String get oneTimePurchase => 'Mọi thứ là mua một lần!';

  @override
  String get contact => 'Liên hệ';

  @override
  String get contactUs => 'Liên hệ chúng tôi';

  @override
  String get noCitiesSelected =>
      'Bạn chưa chọn bất kỳ thành phố nào, nhưng ...';

  @override
  String get updateTravelGoal => 'Cập nhật mục tiêu du lịch';

  @override
  String get travelGoalComplete =>
      'Xin chúc mừng! \n\nyou đã hoàn thành mục tiêu du lịch của bạn! \n\ntap nút + để thêm nhiều quốc gia.';

  @override
  String loginEmailNotFoundError(String email) {
    return 'Không có tài khoản liên quan đến email $email. Bạn có muốn tạo nó ngay bây giờ không?';
  }

  @override
  String get tryAgain => 'Thử lại';

  @override
  String get itineraries => 'kế hoạch du lịch';

  @override
  String get itinerary => 'Kế hoạch du lịch';

  @override
  String get place => 'Địa điểm';

  @override
  String get itinerariesDescription =>
      'Đây là những nơi bạn đã bày tỏ sự quan tâm.\nHãy sử dụng hướng dẫn này để lên kế hoạch cho kỳ nghỉ tiếp theo của bạn.';

  @override
  String get addMore => 'Bổ sung thêm';

  @override
  String get interests => 'Lợi ích';

  @override
  String get selection => 'Lựa chọn';

  @override
  String get goal => 'Mục tiêu';

  @override
  String get noItineraries => 'Không Có Lộ Trình';

  @override
  String get noItinerariesExplanation =>
      'Vui lòng thêm một số địa điểm, nguồn cảm hứng hoặc trải nghiệm để xem lịch trình của bạn tự động tạo ra.';

  @override
  String get clusterPins => 'Ghim Cụm Bản đồ';

  @override
  String get toggleRegions => 'Hiển thị vùng';

  @override
  String get mapProjection => 'Phép chiếu bản đồ';

  @override
  String get mercator => 'Mercator';

  @override
  String get equirectangular => 'Hình chữ nhật đẳng tích';

  @override
  String get yourTravellerType => 'Loại khách du lịch của bạn:';

  @override
  String get yourHotelPreferences => 'Sở thích khách sạn của bạn:';

  @override
  String get budget => 'Ngân sách';

  @override
  String get midScale => 'Trung bình';

  @override
  String get luxury => 'Sang trọng';

  @override
  String get noTravellerType =>
      'Thêm các mục vào danh sách mong muốn của bạn để khám phá bạn thuộc kiểu du khách nào.';

  @override
  String get unlockLived => 'Mở Khóa Đã Sống';

  @override
  String get unlockLivedDescription => 'Chọn nơi bạn đã từng sống trên bản đồ!';

  @override
  String get futureFeaturesDescription =>
      '...và tất cả các tính năng trong tương lai';

  @override
  String get yourMostFrequentlyVisitedCountry =>
      'Quốc gia bạn ghé thăm thường xuyên nhất:';

  @override
  String get departureDate => 'Ngày khởi hành';

  @override
  String get returnDate => 'Ngày trở về';

  @override
  String get hotels => 'Khách sạn';

  @override
  String get food => 'Thực phẩm';

  @override
  String get travelDates => 'Ngày đi';

  @override
  String get posterForMe => 'Cho tôi';

  @override
  String get posterSendGift => 'Gửi quà';

  @override
  String get addSelections => 'Thêm lựa chọn';

  @override
  String get posterType => 'Loại poster';

  @override
  String get help => 'Giúp đỡ';

  @override
  String get tutorialMap =>
      'Nhấn vào quốc gia để chọn: đã từng, muốn và đã sống.';

  @override
  String get tutorialMapList =>
      'Nhấn vào biểu tượng danh sách (góc trên bên trái) để chọn theo danh sách.';

  @override
  String get tutorialCountryDetails =>
      'Nhấn vào quốc gia rồi nhấn vào “thêm” để chọn theo khu vực.';

  @override
  String get tutorialItems =>
      'Trượt nút chuyển đổi để chọn cách bạn muốn chọn các mục.';

  @override
  String get tutorialInspirations =>
      'Vuốt sang phải hoặc trái để chuyển sang thẻ tiếp theo.';

  @override
  String get lifetime => 'Trọn đời';

  @override
  String get chooseYourPlan => 'Chọn gói của bạn';

  @override
  String get requestARefund => 'Yêu cầu hoàn tiền';

  @override
  String get noPurchasesFound => 'Không tìm thấy giao dịch mua.';

  @override
  String get noProductsAvailable => 'Không có sản phẩm nào có sẵn';

  @override
  String get posterLandingAppBar => 'Mang câu chuyện của bạn về nhà';

  @override
  String get posterLandingSubHeading =>
      'Chuyến du lịch của bạn, câu chuyện của bạn';

  @override
  String get posterLandingSubDescription =>
      'Những chuyến du lịch của bạn không chỉ là những chuyến đi, mà còn là những câu chuyện, kỷ niệm và cột mốc. Biến những khoảnh khắc khó quên đó thành một bản đồ thế giới được cá nhân hóa, độc đáo như những cuộc phiêu lưu của bạn.';

  @override
  String get posterLandingPromoBullet1 =>
      '• Bản đồ thành tích của bạn: Làm nổi bật mọi điểm đến, từ chuyến đi lớn đầu tiên đến cuộc phiêu lưu táo bạo nhất của bạn.';

  @override
  String get posterLandingPromoBullet2 =>
      '• Kỷ niệm mọi hành trình: Sống lại những chuyến du lịch của bạn hàng ngày với một tấm áp phích được thiết kế đẹp mắt để truyền cảm hứng.';

  @override
  String get posterLandingPromoBullet3 =>
      '• Một món quà mà họ sẽ trân trọng: Làm bạn đồng hành ngạc nhiên với một bản đồ tùy chỉnh giới thiệu hành trình của họ, hoàn hảo cho sinh nhật, cột mốc hoặc chỉ vì lý do nào đó.';

  @override
  String get posterLandingHowItWorks => 'Cách thức hoạt động!';

  @override
  String get posterLandingHowItWorksStep1 =>
      '1. Tùy chỉnh thiết kế của bạn: Chọn màu sắc, kiểu dáng và đánh dấu chuyến du lịch của bạn (hoặc của họ!)';

  @override
  String get posterLandingHowItWorksStep2 =>
      '2. Xem trước bản đồ của bạn: Xem nó trở nên sống động trước khi bạn đặt hàng.';

  @override
  String get posterLandingHowItWorksStep3_iOS =>
      '3. Thanh toán an toàn: Nhanh chóng và an toàn với Apple Pay hoặc Stripe.';

  @override
  String get posterLandingHowItWorksStep3_android =>
      '3. Thanh toán an toàn: Nhanh chóng và an toàn với Google Pay hoặc Stripe.';

  @override
  String get posterLandingHowItWorksStep4 =>
      '4. Sẵn sàng trưng bày: Chúng tôi sẽ giao hàng thẳng đến tận nhà bạn (hoặc nhà họ).';

  @override
  String get posterLandingCustomerReviewsHeader =>
      'Kinh nghiệm từ những người bạn đồng hành';

  @override
  String get posterLandingCustomerReview1 =>
      '\"Bản đồ này là một cách tuyệt vời để theo dõi mọi nơi tôi đã đi qua và lập kế hoạch cho những chuyến đi trong tương lai của chúng tôi. Chất lượng bản đồ rất tốt và trông thật tuyệt khi treo trong văn phòng của tôi. Tôi thậm chí còn mua một tấm cho anh trai mình và anh ấy không thể ngừng nói về sự tuyệt vời của nó!\" - John C.';

  @override
  String get posterLandingCustomerReview2 =>
      '\"Tôi đã đi qua hơn 150 cảng trong khi làm việc trên du thuyền. Bản đồ này là vật trang trí tuyệt vời cho phòng khách của tôi như một kỷ niệm về tất cả những năm tháng trên biển.\" - Betty K.';

  @override
  String get posterLandingCustomerReview3 =>
      '\"Món quà tuyệt vời cho Ngày của Mẹ. Mẹ tôi vô cùng cảm động!\" Samantha W.';

  @override
  String get posterLandingCustomerReview4 =>
      '\"In bản đồ những nơi tôi muốn đến thăm cùng bạn gái. Đây là món quà Giáng sinh tuyệt vời. Chất lượng cao nữa.\" Brad J.';

  @override
  String get posterLandingSpecifications => 'Thông số kỹ thuật';

  @override
  String get posterLandingSpecification1 =>
      '• Kích thước: 16\" x 20\" (40,64cm x 50,8cm)';

  @override
  String get posterLandingSpecification2 => '• Định hướng: Phong cảnh';

  @override
  String get posterLandingSpecification3 =>
      '• Chất lượng in: Mực siêu nhỏ, giọt mực cho bản in chính xác. Màu 8 bit, chất lượng in gần như ảnh chụp.';

  @override
  String get posterLandingSpecification4 => '• Giấy: Giấy satin dày 0,22mm';

  @override
  String get posterLandingShippingHeader => 'Chi tiết vận chuyển';

  @override
  String get posterLandingShipping1 =>
      '• Vận chuyển từ Toronto, Canada đến bất kỳ nơi nào trên thế giới bằng Bưu điện Canada.';

  @override
  String get posterLandingShipping2 =>
      '• Thời gian giao hàng đến hầu hết các địa điểm là 2-4 tuần.';

  @override
  String get posterLandingShipping3 =>
      '• Tất cả các đơn hàng được cuộn lại trong hộp các tông và gửi đến địa chỉ giao hàng mà bạn cung cấp.';

  @override
  String get posterLandingCancellationHeader => 'Hủy/Hoàn tiền:';

  @override
  String get posterLandingCancellationBody =>
      'Bạn có thể hoàn tiền trước khi áp phích của bạn được gửi đến máy in, có thể mất tới 24 giờ. Sau khi đơn hàng của bạn được xử lý, sẽ không có hoàn tiền/hủy đơn hàng nào được hoàn lại. Bạn sẽ nhận được email khi đơn hàng của bạn đã được in.';

  @override
  String get unsubscribe => 'Hủy đăng ký';

  @override
  String get unsubscribeConfirmMessage =>
      'Bạn có chắc chắn muốn hủy đăng ký không? Bạn sẽ bỏ lỡ các ưu đãi độc quyền và cập nhật!';

  @override
  String get updateLive => 'Cập Nhật Nơi Cư Trú';

  @override
  String get updateLiveDescription =>
      'Để thay đổi quốc gia bạn đang sống, trước tiên bạn phải chọn một quốc gia mới để thay thế.';

  @override
  String get underOneThousand => 'Dưới 1.000';

  @override
  String get oneThousandToTenThousand => '1.000 – 10.000';

  @override
  String get overTenThousand => 'Trên 10.000';

  @override
  String get becomeABrandAmbassador => 'Trở thành Đại sứ Thương hiệu';

  @override
  String get instagram => 'Instagram';

  @override
  String get tiktok => 'TikTok';

  @override
  String get youtube => 'YouTube';

  @override
  String get website => 'Trang web';

  @override
  String get handle => 'Tên tài khoản';

  @override
  String get followers => 'Người theo dõi';

  @override
  String get joinBrandAmbassadorProgram =>
      'Tham gia chương trình Đại sứ Thương hiệu';

  @override
  String get brandAmbassadorProgramDescription =>
      'Yêu thích Visited? Là đại sứ thương hiệu, bạn sẽ đại diện cho cộng đồng du lịch của chúng tôi, chia sẻ bản đồ và danh sách chuyến đi của bạn, đồng thời giúp người khác khám phá điểm đến và tính năng mới. Đổi lại, bạn sẽ nhận được phần thưởng, giảm giá, quà tặng và nhiều hơn nữa!';

  @override
  String get fillOutTheFormToGetStarted =>
      'Điền vào biểu mẫu bên dưới để bắt đầu:';

  @override
  String get yourName => 'Tên của bạn';

  @override
  String get yourNameEmptyError => 'Vui lòng nhập tên của bạn';

  @override
  String get fillInWhereApplicable => 'Điền thông tin nếu có:';

  @override
  String get otherNetworks => 'Mạng khác';

  @override
  String get anythingElse => 'Có điều gì khác bạn muốn chúng tôi biết không?';

  @override
  String get yourTravelsByContinent => 'Chuyến đi của bạn theo châu lục';

  @override
  String get territories => 'Lãnh thổ';

  @override
  String get couponCode => 'Mã giảm giá';

  @override
  String get apply => 'Áp dụng';

  @override
  String get discount => 'Giảm giá';

  @override
  String get noCouponCode => 'Vui lòng nhập mã giảm giá';

  @override
  String get invalidCouponCode => 'Mã giảm giá không hợp lệ';

  @override
  String get couponApplied => 'Đã áp dụng mã';

  @override
  String discountPercentage(double percentage) {
    return 'Giảm $percentage%';
  }

  @override
  String discountAmount(double amount) {
    return 'Đã giảm $amount!';
  }

  @override
  String get thankYou => 'Cảm ơn bạn!';

  @override
  String get formSubmitted =>
      'Chúng tôi đã nhận được yêu cầu trở thành đại sứ thương hiệu của bạn. Chúng tôi sẽ liên hệ sớm!';
}
