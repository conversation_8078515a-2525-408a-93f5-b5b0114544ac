// ignore: unused_import
import 'package:intl/intl.dart' as intl;
import 'app_localizations.dart';

// ignore_for_file: type=lint

/// The translations for Thai (`th`).
class AppLocalizationsTh extends AppLocalizations {
  AppLocalizationsTh([String locale = 'th']) : super(locale);

  @override
  String get appName => 'Visited';

  @override
  String get language => 'ภาษา';

  @override
  String get pickEmailApp => 'เลือกแอปอีเมลของคุณ';

  @override
  String numberOfCountriesShareMessage(int amount) {
    return 'ฉันได้ไปเยือน $amount ประเทศ! คุณไปเยือนมาแล้วกี่ประเทศ? www.visitedapp.com';
  }

  @override
  String numberOfCitiesShareMessage(int amount) {
    return 'ฉันได้ไปเยือน $amount เมือง! คุณไปเยือนมาแล้วกี่เมือง? www.visitedapp.com';
  }

  @override
  String numberOfItemsInTodoListShareMessage(int amount, String listName) {
    return 'ฉันได้ไปเยือน $amount $listName! คุณไปเยือนมาแล้วกี่แห่ง? www.visitedapp.com';
  }

  @override
  String get clear => 'เคลียร์';

  @override
  String get been => 'เคย';

  @override
  String get want => 'ต้องการ';

  @override
  String get live => 'อาศัย';

  @override
  String get lived => 'เคยอยู่';

  @override
  String get water => 'ทางน้ำ';

  @override
  String get land => 'ทางบก';

  @override
  String get borders => 'พรมแดน';

  @override
  String get labels => 'ป้าย';

  @override
  String get legend => 'ตำนาน';

  @override
  String get inspiration => 'แรงบันดาลใจ';

  @override
  String get inspirations => 'หลายแรงบันดาลใจ';

  @override
  String get delete => 'ลบ';

  @override
  String get unlockVisitedUpsellTitle => 'ต้องการดูเพิ่มเติมหรือไม่';

  @override
  String get unlockVisitedUpsellSubtitle =>
      'ปลดล็อคคุณสมบัติทั้งหมดและเพลิดเพลินกับ Visited อย่างเต็มที่';

  @override
  String get checkTheDetails => 'ตรวจสอบรายละเอียด';

  @override
  String get moreInspirationsComingSoon =>
      'เรากำลังดำเนินการเพื่อให้ได้ภาพมากขึ้น กลับมาดูใหม่เร็ว ๆ นี้!';

  @override
  String get unlockPremiumFeatures => 'ปลดล็อคคุณสมบัติพิเศษ';

  @override
  String get purchased => 'ชำระแล้ว!';

  @override
  String get buy => 'ซื้อ';

  @override
  String get restorePurchases => 'เรียกคืนการชำระ';

  @override
  String get ok => 'ตกลง';

  @override
  String get areYouSure => 'คุณแน่ใจไหม';

  @override
  String get deleteInspirationConfirmMessage =>
      'การลบบัตรนี้จะมีผลถาวร ไม่มีวิธีกู้คืนรูปภาพนี้';

  @override
  String get cancel => 'ยกเลิก';

  @override
  String get map => 'แผนที่';

  @override
  String get progress => 'ความคืบหน้า';

  @override
  String get myTravelGoal => 'เป้าหมายการเดินทางของฉัน';

  @override
  String goalRemaining(int remaining) {
    return 'เหลืออีก $remaining ที่จะไป!';
  }

  @override
  String get top => 'อันดับสูงสุด';

  @override
  String get ofTheWorld => 'ของโลก!';

  @override
  String get countries => 'หลายประเทศ';

  @override
  String topPlacesVisitedFromCountry(String country) {
    return 'ประเทศยอดนิยมที่ไปเยี่ยมชมจาก $country:';
  }

  @override
  String get login => 'เข้าสู่ระบบ';

  @override
  String get logout => 'ออกจากระบบ';

  @override
  String get enterYourEmail => 'กรอกอีเมล์ของคุณ';

  @override
  String get privacyPolicy => 'นโยบายความเป็นส่วนตัว';

  @override
  String get privatePolicyUrl =>
      'https://www.arrivinginhighheels.com/privacy-policy-thai/';

  @override
  String get termsOfUse => 'ข้อกำหนดการใช้งาน';

  @override
  String get termsOfUserUrl =>
      'https://www.arrivinginhighheels.com/terms-of-use-thai/';

  @override
  String get errorTitle => 'อุ๊ป!';

  @override
  String get enterValidEmail => 'กรุณาใส่อีเมลที่ถูกต้อง';

  @override
  String get settings => 'การตั้งค่า';

  @override
  String get whereDoYouLive => 'คุณอาศัยอยู่ที่ไหน?';

  @override
  String get whereHaveYouBeen => 'คุณเคยไปที่ไหนมาบ้าง';

  @override
  String get whereDoYouFlyFrom => 'คุณบินจากที่ไหน';

  @override
  String get next => 'ถัดไป';

  @override
  String get missingAirports =>
      'ไม่เห็นสิ่งที่คุณกำลังมองหาใช่ไหม ส่งอีเมลถึงเราที่ <EMAIL>';

  @override
  String get missingAirportsEmailTitle => 'สนามบินหายไป!';

  @override
  String get supportEmailAddress => '<EMAIL>';

  @override
  String get welcomeTitle => 'ยินดีต้อนรับเข้าสู่ Visited';

  @override
  String get welcomeSubtitle => 'การผจญภัยของชีวิตกำลังรออยู่';

  @override
  String get getStarted => 'เริ่มกันเลย';

  @override
  String get privacyAgreement => 'ข้อตกลงความเป็นส่วนตัว';

  @override
  String get privacyAgreementSubtitle =>
      'โปรดยอมรับรายการต่อไปนี้ก่อนที่จะใช้ Visited ต่อไป';

  @override
  String get privacyAgreementTermsMarkdown =>
      'โดยการทำเครื่องหมายที่ช่องนี้แสดงว่าคุณรับทราบว่าคุณได้อ่านและตกลงที่จะผูกพันตาม [นโยบายความเป็นส่วนตัว](https://www.arrivinginhighheels.com/privacy-policy) และ [ข้อกำหนดการใช้งาน](https://www.arrivinginhighheels.com/terms-of-use)';

  @override
  String get privacyAgreementOptIn =>
      'ฉันตกลงที่จะรับข้อความอิเล็กทรอนิกส์จาก Arriving in High Heels ที่มีข้อมูลและข้อเสนอเกี่ยวกับผลิตภัณฑ์ แอปพลิเคชัน และบริการที่อาจเป็นที่สนใจของฉัน รวมถึงการแจ้งการขาย การส่งเสริมการขาย ข้อเสนอและจดหมายข่าว ฉันสามารถเพิกถอนความยินยอมนี้ได้ทุกเมื่อตามที่อธิบายไว้ในนโยบายความเป็นส่วนตัว หรือโดยการคลิกลิงค์ \"ยกเลิกการสมัคร\" ในข้อความอิเล็กทรอนิกส์';

  @override
  String get submit => 'ส่ง';

  @override
  String get companyName => 'Arriving In High Heels Corporation';

  @override
  String get companyAddress =>
      '31 Claudview Street\nKing City, Ontario\nCanada\nL7B 0C6';

  @override
  String get privacyAgreementRequired =>
      'คุณต้องยอมรับทั้งข้อกำหนดและการเลือกของเราเพื่อใช้งาน Visited ต่อไป';

  @override
  String get deleteAccount => 'ลบบัญชี';

  @override
  String get removeAdsUpsell =>
      'คุณต้องการยกเลิกโฆษณาและยกเลิกการสมัครรับจากการอีเมลการตลาดหรือไม่';

  @override
  String get deleteAccountWarning =>
      'การลบบัญชีของคุณจะเป็นการลบข้อมูลทั้งหมดของคุณออกจากเซิร์ฟเวอร์ของเรา กระบวนการนี้ไม่สามารถยกเลิกได้';

  @override
  String get about => 'เกี่ยวกับ';

  @override
  String get popularity => 'ความนิยม';

  @override
  String get regions => 'ภูมิภาค';

  @override
  String get population => 'ประชากร';

  @override
  String get size => 'ขนาด';

  @override
  String get coverage => 'ความคุ้มครอง';

  @override
  String get percentOfCountryVisited => '% ของประเทศที่เยี่ยมชม';

  @override
  String get visited => 'เยี่ยมชม';

  @override
  String get notes => 'หมายเหตุ';

  @override
  String get kmSquared => 'กม²';

  @override
  String get customize => 'ปรับแต่ง';

  @override
  String get onlyCountSovereign => 'มีอธิปไตยตามที่สหประชาชาติรับรอง';

  @override
  String get countUkSeparately => 'นับประเทศในอังกฤษแยกกัน';

  @override
  String get showLegend => 'แสดงตำนาน';

  @override
  String get showLivedPin => 'แสดงพินที่อยู่อาศัย';

  @override
  String get useMyColours => 'ใช้สีของฉัน';

  @override
  String get mapColors => 'สีแผนที่';

  @override
  String get traveller => 'นักเดินทาง';

  @override
  String get nightTraveller => 'นักเดินทางกลางคืน';

  @override
  String get original => 'เจ้าเก่า';

  @override
  String get explorer => 'นักสำรวจ';

  @override
  String get weekender => 'วันหยุดสุดสัปดาห์';

  @override
  String get naturalist => 'นักธรรมชาติวิทยา';

  @override
  String get historian => 'นักประวัติศาสตร์';

  @override
  String get thrillSeeker => 'ผู้แสวงหาความตื่นเต้น';

  @override
  String get culturalBuff => 'ผู้คลั่งไคล้วัฒนธรรม';

  @override
  String get myColors => 'สีของฉัน';

  @override
  String get experiences => 'ประสบการณ์';

  @override
  String get done => 'เสร็จสิ้น';

  @override
  String get experiencesInstructions => 'แตะปุ่ม + เพื่อเริ่มต้น!';

  @override
  String get continueText => 'ดำเนินต่อ';

  @override
  String get experiencesDescription => 'คุณชอบทำอะไรเมื่อคุณเดินทาง?';

  @override
  String get visitedWebsiteShortLink => 'https://www.visitedapp.com';

  @override
  String get sharingHashtag => '#Visited';

  @override
  String get myTravelMap => 'แผนที่การเดินทางของฉัน';

  @override
  String percentOfWorldSeen(int percentage) {
    return 'ฉันเคยเห็น $percentage% ของโลก';
  }

  @override
  String get requiresOnline =>
      'ขออภัย Visited ต้องการการเชื่อมต่อเครือข่ายที่ใช้งานได้ โปรดเปิดแอปการตั้งค่าของคุณและตรวจสอบให้แน่ใจว่าได้เปิดใช้งาน \\r Wi-Fi หรือข้อมูลเซลลูลาร์ และปิดใช้งานโหมดเครื่องบินแล้ว';

  @override
  String get list => 'รายการ';

  @override
  String get more => 'เพิ่มเติม';

  @override
  String get myCountrySelections => 'การเลือกประเทศของฉัน';

  @override
  String get cities => 'เมือง';

  @override
  String get citiesInstructions => 'แตะที่ประเทศใดก็ได้เพื่อเริ่มเลือกเมือง';

  @override
  String get missingCitiesEmailTitle => 'เมืองหายไป!';

  @override
  String get lists => 'รายการ';

  @override
  String get disputedTerritories => 'ดินแดนพิพาท';

  @override
  String get sponsored => 'สปอนเซอร์';

  @override
  String get places => 'สถานที่';

  @override
  String get noListsError =>
      'อุ๊ปส์ ไม่มีรายการในขณะนี้ โปรดลองอีกครั้งในภายหลัง';

  @override
  String get noInspirationsError =>
      'อุ๊ปส์ ไม่มีรูปภาพในขณะนี้ โปรดลองอีกครั้งในภายหลัง';

  @override
  String get mostFrequentlyVisitedCountries => 'ประเทศที่คุณเข้าชมบ่อยที่สุด:';

  @override
  String get update => 'อัพเดท';

  @override
  String get signup => 'ลงชื่อ';

  @override
  String get loginWallSubtitle =>
      'สร้างบัญชีฟรีเพื่อสัมผัสกับ Visited เวอร์ชันเต็ม';

  @override
  String get loseAllSelectionsWarning =>
      'คุณจะสูญเสียการเลือกทั้งหมดหลังจากปิดแอป';

  @override
  String get createAccount => 'สร้างบัญชี';

  @override
  String get continueWithoutAccount => 'ดําเนินการต่อโดยไม่มีบัญชี';

  @override
  String get inspirationPromotion =>
      'รับแรงบันดาลใจจากการถ่ายภาพท่องเที่ยวที่สวยงาม';

  @override
  String get saveStatsPromotion => 'บันทึกสถิติการเดินทางของคุณ!';

  @override
  String get selectRegionsPromotion => 'เลือกรัฐและจังหวัด';

  @override
  String get experiencesPromotion => 'ติดตามประสบการณ์ทั่วโลก';

  @override
  String get missingListItem =>
      'เราพลาดอะไรไปหรือเปล่า? แตะที่นี่เพื่อส่งอีเมลถึงเราเพื่อเพิ่มสถานที่โปรดของคุณ';

  @override
  String missingListItemEmailTitle(String list) {
    return 'รายการที่หายไปจาก $list';
  }

  @override
  String listShareMessage(Object amount, Object listName) {
    return 'ฉันได้เยี่ยมชม $amount $listName';
  }

  @override
  String get orderPoster => 'โปสเตอร์';

  @override
  String get shareMap => 'แชร์แผนที่';

  @override
  String get posterLandingPageTitle => 'รับโปสเตอร์ของคุณ';

  @override
  String get posterNotAvailableError =>
      'การซื้อโปสเตอร์ยังไม่มีให้บริการในขณะนี้ โปรดลองอีกครั้งในภายหลัง.';

  @override
  String posterPricePlusShipping(Object price, Object shipping) {
    return '$price + $shipping การจัดส่ง';
  }

  @override
  String get posterDescriptionMarkdownApple =>
      '## เกี่ยวกับแผนที่พิมพ์ที่กำหนดเองของเรา\nพิมพ์แผนที่โลกส่วนบุคคลของคุณ ปรับแต่งด้วยสีของคุณเองและส่งตรงไปที่บ้านของคุณ\n \n### ข้อมูลจำเพาะ:\n- 16\" x 20\" (40.64cm x 50.8cm)\n- การวางแนวนอน\n- Micro Ink, หยดสำหรับการพิมพ์ที่แม่นยำ, สี 8 บิต, คุณภาพการพิมพ์เกือบ\n- กระดาษซาตินหนา 0.22 มม.\n\n### รายละเอียดการจัดส่ง:\nการจัดส่งจากโตรอนโตแคนาดาไปยังที่ใดก็ได้ในโลกโดยใช้แคนาดาโพสต์ โปรดรอ 2 ถึง 4 สัปดาห์สำหรับการจัดส่งไปยังจุดหมายปลายทางส่วนใหญ่ คำสั่งซื้อทั้งหมดจะถูกจัดส่งในกล่องกระดาษแข็งไปยังที่อยู่จัดส่งที่ส่งไป \nการชำระเงินทั้งหมดได้รับการจัดการโดย Apple Pay, หรือ Stripe\n\n\n### การยกเลิก/คืนเงิน:\nคำสั่งซื้อจะถูกประมวลผลทันทีหลังจากส่งเพื่อการตอบสนองที่เร็วที่สุดเท่าที่จะเป็นไปได้ ดังนั้นจึงไม่มีเงินคืน/ยกเลิก';

  @override
  String get posterDescriptionMarkdown =>
      '## เกี่ยวกับแผนที่พิมพ์ที่กำหนดเองของเรา\nพิมพ์แผนที่โลกส่วนบุคคลของคุณ ปรับแต่งด้วยสีของคุณเองและส่งตรงไปที่บ้านของคุณ\n \n### ข้อมูลจำเพาะ:\n- 16\" x 20\" (40.64cm x 50.8cm)\n- การวางแนวนอน\n- Micro Ink, หยดสำหรับการพิมพ์ที่แม่นยำ, สี 8 บิต, คุณภาพการพิมพ์เกือบ\n- กระดาษซาตินหนา 0.22 มม.\n\n### รายละเอียดการจัดส่ง:\nการจัดส่งจากโตรอนโตแคนาดาไปยังที่ใดก็ได้ในโลกโดยใช้แคนาดาโพสต์ โปรดรอ 2 ถึง 4 สัปดาห์สำหรับการจัดส่งไปยังจุดหมายปลายทางส่วนใหญ่ คำสั่งซื้อทั้งหมดจะถูกจัดส่งในกล่องกระดาษแข็งไปยังที่อยู่จัดส่งที่ส่งไป \nการชำระเงินทั้งหมดได้รับการจัดการโดย Google Pay หรือ Stripe\n\n\n### การยกเลิก/คืนเงิน:\nคำสั่งซื้อจะถูกประมวลผลทันทีหลังจากส่งเพื่อการตอบสนองที่เร็วที่สุดเท่าที่จะเป็นไปได้ ดังนั้นจึงไม่มีเงินคืน/ยกเลิก';

  @override
  String get posterCustomizeTitle => 'ปรับแต่งโปสเตอร์';

  @override
  String get enterShippingAddress => 'ป้อนที่อยู่จัดส่ง';

  @override
  String get price => 'ราคา';

  @override
  String formattedPlusTax(Object formattedPrice) {
    return '$formattedPrice + ภาษี';
  }

  @override
  String get showSelections => 'แสดงการเลือก';

  @override
  String get posterNoRefunds => 'ไม่มีการคืนเงินหลังจากพิมพ์โปสเตอร์แล้ว';

  @override
  String get posterReviewOrder => 'ตรวจสอบคำสั่งซื้อของคุณ';

  @override
  String get email => 'อีเมล';

  @override
  String get emailEmptyError => 'กรุณากรอกอีเมลของคุณ';

  @override
  String get fullName => 'ชื่อเต็ม';

  @override
  String get fullNameEmptyError => 'กรุณาใส่ชื่อเต็มของคุณ';

  @override
  String get streetAddressEmptyError => 'โปรดป้อนที่อยู่ของคุณ';

  @override
  String get cityEmptyError => 'กรุณาเข้าสู่เมืองของคุณ';

  @override
  String fieldEmptyError(Object fieldName) {
    return 'โปรดป้อน $fieldName ของคุณ';
  }

  @override
  String get country => 'ประเทศ';

  @override
  String get countryEmptyError => 'กรุณาเข้าประเทศของคุณ';

  @override
  String get posterReviewOrderTitle => 'ตรวจสอบคำสั่งซื้อของคุณ';

  @override
  String get buyNow => 'ซื้อเลย';

  @override
  String get secureCheckoutDisclaimer => 'การชำระเงินที่ปลอดภัยโดย Stripe';

  @override
  String get total => 'ทั้งหมด';

  @override
  String get tax => 'ภาษี';

  @override
  String get subtotal => 'ผลรวมย่อย';

  @override
  String get posterProductName => 'โปสเตอร์แผนที่ที่กำหนดเอง';

  @override
  String get shipping => 'การส่งสินค้า';

  @override
  String get posterOrderReceivedTitle => 'ได้รับคำสั่งซื้อ';

  @override
  String get posterOrderReceivedSubtitle => 'เราได้รับคำสั่งซื้อของคุณ!';

  @override
  String get posterOrderReceivedInstructionsMarkdown =>
      'ตรวจสอบอีเมลของคุณสำหรับการอัปเดตเพิ่มเติมโปรดอนุญาตให้โปสเตอร์ของคุณถึง 4 สัปดาห์หากคุณมีคำถามใด ๆ โปรดส่งอีเมลถึงเราที่ [<EMAIL>] (<EMAIL>)';

  @override
  String get posterOrderReceivedEmailSubject =>
      'สถานะการสั่งซื้อโปสเตอร์ที่พิมพ์ออกมา';

  @override
  String get moreInfo => 'ข้อมูลมากกว่านี้';

  @override
  String get logoutConfirm => 'คุณต้องการออกจากระบบแอพหรือไม่?';

  @override
  String get emailNotAvailable => 'อีเมลนั้นได้รับการถ่าย';

  @override
  String get alphabetical => 'เกี่ยวกับตัวอักษร';

  @override
  String get firstTimeLiveTutorial =>
      'การจัดหาประเทศและเมืองของคุณจะปรับแต่งประสบการณ์แอปของคุณให้เป็นส่วนตัว';

  @override
  String get firstTimeBeenTutorial =>
      'การเลือกตำแหน่งที่คุณอนุญาตให้คุณดูแผนที่ของทุกประเทศที่คุณเคยไปและดูสถิติส่วนบุคคล';

  @override
  String get progressTooltipGoal =>
      'เป้าหมายการเดินทางของคุณขึ้นอยู่กับจำนวนประเทศที่คุณ \"ต้องการ\" ในการเดินทางเมื่อเทียบกับประเทศที่คุณเคยเป็น ';

  @override
  String get progressTooltipRank =>
      'ตัวเลขนี้แสดงให้เห็นว่าคุณจัดอันดับเมื่อเทียบกับนักเดินทางทั่วโลก คุณสามารถเพิ่มอันดับของคุณโดยการเดินทางไปยังประเทศอื่น ๆ';

  @override
  String get progressTooltipPercentageOfWorld =>
      'กราฟนี้ขึ้นอยู่กับจำนวนประเทศที่คุณเคยเทียบกับประเทศทั้งหมดของโลก';

  @override
  String get sortBy => 'เรียงตาม';

  @override
  String get updateWishlist => 'อัปเดตรายการปรารถนา';

  @override
  String get mapInfo =>
      'คลิกที่ประเทศเพื่อเลือกเคยต้องการหรือมีชีวิตอยู่ นอกจากนี้คุณยังสามารถคลิกที่ไอคอนที่พบในมุมซ้ายบนสำหรับมุมมองรายการ';

  @override
  String get oneTimePurchase => 'ทุกอย่างเป็นการซื้อครั้งเดียว!';

  @override
  String get contact => 'ติดต่อ';

  @override
  String get contactUs => 'ติดต่อเรา';

  @override
  String get noCitiesSelected => 'คุณยังไม่ได้เลือกเมืองใด ๆ เลย ...';

  @override
  String get updateTravelGoal => 'อัปเดตเป้าหมายการเดินทาง';

  @override
  String get travelGoalComplete =>
      'ขอแสดงความยินดี! \n\nyou บรรลุเป้าหมายการเดินทางของคุณแล้ว! \n\ntap ปุ่ม + เพื่อเพิ่มประเทศเพิ่มเติม';

  @override
  String loginEmailNotFoundError(String email) {
    return 'ไม่มีบัญชีที่เกี่ยวข้องกับอีเมล $email คุณต้องการสร้างตอนนี้หรือไม่?';
  }

  @override
  String get tryAgain => 'ลองอีกครั้ง';

  @override
  String get itineraries => 'แผนการท่องเที่ยว';

  @override
  String get itinerary => 'แผนการเดินทาง';

  @override
  String get place => 'สถานที่';

  @override
  String get itinerariesDescription =>
      'สถานที่เหล่านี้คือสถานที่ที่คุณแสดงความสนใจ\nใช้คำแนะนำนี้เพื่อช่วยวางแผนวันหยุดพักผ่อนครั้งต่อไปของคุณ';

  @override
  String get addMore => 'เพิ่มมากขึ้น';

  @override
  String get interests => 'ความสนใจ';

  @override
  String get selection => 'การเลือก';

  @override
  String get goal => 'เป้าหมาย';

  @override
  String get noItineraries => 'ไม่มีแผนทาง';

  @override
  String get noItinerariesExplanation =>
      'กรุณาเพิ่มสถานที่บางแห่ง แรงบันดาล์ หา';

  @override
  String get clusterPins => 'หมุดคลัสเตอร์แผนที่';

  @override
  String get toggleRegions => 'แสดงภูมิภาคขณะย่อ/ขยาย';

  @override
  String get mapProjection => 'ระบบแผนที่';

  @override
  String get mercator => 'เมอร์คาเตอร์';

  @override
  String get equirectangular => 'พื้นที่เท่ากัน';

  @override
  String get yourTravellerType => 'ประเภทนักเดินทางของคุณ:';

  @override
  String get yourHotelPreferences => 'ความชอบโรงแรมของคุณ:';

  @override
  String get budget => 'งบประมาณ';

  @override
  String get midScale => 'ระดับกลาง';

  @override
  String get luxury => 'หรูหรา';

  @override
  String get noTravellerType =>
      'เพิ่มรายการในรายการที่ต้องทำของคุณเพื่อค้นหาว่าคุณเป็นนักเดินทางประเภทใด';

  @override
  String get unlockLived => 'ปลดล็อคการใช้ชีวิต';

  @override
  String get unlockLivedDescription =>
      'เลือกสถานที่ที่คุณเคยอาศัยอยู่บนแผนที่!';

  @override
  String get futureFeaturesDescription => '...และคุณสมบัติทั้งหมดในอนาคต';

  @override
  String get yourMostFrequentlyVisitedCountry =>
      'ประเทศที่คุณเยี่ยมชมบ่อยที่สุด:';

  @override
  String get departureDate => 'วันที่ออกเดินทาง';

  @override
  String get returnDate => 'วันที่กลับ';

  @override
  String get hotels => 'โรงแรม';

  @override
  String get food => 'อาหาร';

  @override
  String get travelDates => 'วันที่เดินทาง';

  @override
  String get posterForMe => 'สำหรับฉัน';

  @override
  String get posterSendGift => 'ส่งของขวัญ';

  @override
  String get addSelections => 'เพิ่มการเลือก';

  @override
  String get posterType => 'ประเภทโปสเตอร์';

  @override
  String get help => 'ช่วย';

  @override
  String get tutorialMap =>
      'แตะที่ประเทศเพื่อเลือก: เคยไป ต้องการ และอาศัยอยู่';

  @override
  String get tutorialMapList =>
      'แตะที่ไอคอนรายการ (มุมซ้ายบน) เพื่อเลือกตามรายการ';

  @override
  String get tutorialCountryDetails =>
      'แตะที่ประเทศแล้วคลิก \"เพิ่มเติม\" เพื่อเลือกตามภูมิภาค';

  @override
  String get tutorialItems =>
      'เลื่อนตัวสลับเพื่อเลือกวิธีที่คุณต้องการเลือกรายการ';

  @override
  String get tutorialInspirations =>
      'ปัดไปทางขวาหรือซ้ายเพื่อเลื่อนไปยังการ์ดถัดไป';

  @override
  String get lifetime => 'ตลอดชีพ';

  @override
  String get chooseYourPlan => 'เลือกแผนของคุณ';

  @override
  String get requestARefund => 'ขอคืนเงิน';

  @override
  String get noPurchasesFound => 'ไม่พบการซื้อ';

  @override
  String get noProductsAvailable => 'ไม่มีสินค้าที่พร้อมใช้งาน';

  @override
  String get posterLandingAppBar => 'นำเรื่องราวของคุณกลับบ้าน';

  @override
  String get posterLandingSubHeading => 'การเดินทางของคุณ เรื่องราวของคุณ';

  @override
  String get posterLandingSubDescription =>
      'การเดินทางของคุณเป็นมากกว่าการเดินทาง แต่เป็นเรื่องราว ความทรงจำ และเหตุการณ์สำคัญ เปลี่ยนช่วงเวลาที่น่าจดจำเหล่านั้นให้กลายเป็นแผนที่โลกส่วนตัวที่ไม่เหมือนใครเช่นเดียวกับการผจญภัยของคุณ';

  @override
  String get posterLandingPromoBullet1 =>
      '• แผนที่แห่งความสำเร็จของคุณ: เน้นย้ำทุกจุดหมายปลายทาง ตั้งแต่การเดินทางครั้งใหญ่ครั้งแรกของคุณไปจนถึงการผจญภัยที่กล้าหาญที่สุดของคุณ';

  @override
  String get posterLandingPromoBullet2 =>
      '• เฉลิมฉลองทุกการเดินทาง: ย้อนรำลึกการเดินทางของคุณทุกวันด้วยโปสเตอร์ที่ประดิษฐ์อย่างสวยงามเพื่อสร้างแรงบันดาลใจ';

  @override
  String get posterLandingPromoBullet3 =>
      '• ของขวัญที่พวกเขาจะหวงแหน: สร้างความประหลาดใจให้กับนักเดินทางด้วยกันด้วยแผนที่ที่ปรับแต่งได้ซึ่งแสดงการเดินทางของพวกเขา เหมาะสำหรับวันเกิด เหตุการณ์สำคัญ หรือเพียงเพราะเหตุผลบางอย่าง';

  @override
  String get posterLandingHowItWorks => 'วิธีการทำงาน!';

  @override
  String get posterLandingHowItWorksStep1 =>
      '1. ปรับแต่งการออกแบบของคุณ: เลือกสี สไตล์ และทำเครื่องหมายการเดินทางของคุณ (หรือของพวกเขา!)';

  @override
  String get posterLandingHowItWorksStep2 =>
      '2. ดูตัวอย่างแผนที่ของคุณ: ดูแผนที่ที่กลายเป็นจริงก่อนที่คุณจะสั่งซื้อ';

  @override
  String get posterLandingHowItWorksStep3_iOS =>
      '3. การชำระเงินที่ปลอดภัย: รวดเร็วและปลอดภัยด้วย Apple Pay หรือ Stripe';

  @override
  String get posterLandingHowItWorksStep3_android =>
      '3. การชำระเงินที่ปลอดภัย: รวดเร็วและปลอดภัยด้วย Google Pay หรือ Stripe 4. พร้อมสำหรับการจัดแสดง: เราจะส่งตรงไปที่ประตูบ้านของคุณ (หรือของพวกเขา)';

  @override
  String get posterLandingHowItWorksStep4 =>
      '4. พร้อมสำหรับการจัดแสดง: เราจะส่งตรงไปที่ประตูบ้านของคุณ (หรือของพวกเขา)';

  @override
  String get posterLandingCustomerReviewsHeader =>
      'ประสบการณ์จากเพื่อนนักเดินทาง';

  @override
  String get posterLandingCustomerReview1 =>
      '“แผนที่นี้เป็นวิธีที่ดีเยี่ยมในการติดตามสถานที่ต่างๆ ที่ฉันเดินทางไปและวางแผนการเดินทางในอนาคต แผนที่นี้มีคุณภาพดีเยี่ยมและดูดีมากเมื่อแขวนไว้ในออฟฟิศของฉัน ฉันยังซื้อให้พี่ชายของฉันด้วยซ้ำ แต่เขาพูดไม่หยุดว่ามันเจ๋งแค่ไหน!” - จอห์น ซี.';

  @override
  String get posterLandingCustomerReview2 =>
      '“ฉันเดินทางไปท่าเรือมากกว่า 150 แห่งระหว่างทำงานบนเรือสำราญ แผนที่นี้เป็นส่วนเสริมที่ยอดเยี่ยมสำหรับห้องนั่งเล่นของฉันเพื่อเป็นความทรงจำตลอดหลายปีที่อยู่กลางทะเล” - เบ็ตตี้ เค.';

  @override
  String get posterLandingCustomerReview3 =>
      '“ของขวัญวันแม่ที่ยอดเยี่ยมมาก แม่ของฉันซาบซึ้งใจมาก!” ซาแมนธา ดับเบิลยู.';

  @override
  String get posterLandingCustomerReview4 =>
      '“พิมพ์แผนที่สถานที่ที่อยากไปกับแฟนสาว เป็นของขวัญคริสต์มาสที่ยอดเยี่ยม คุณภาพสูงด้วย” แบรด เจ.';

  @override
  String get posterLandingSpecifications => 'ข้อมูลจำเพาะ';

  @override
  String get posterLandingSpecification1 =>
      '• ขนาด: 16\" x 20\" (40.64 ซม. x 50.8 ซม.)';

  @override
  String get posterLandingSpecification2 => '• การวางแนว: แนวนอน';

  @override
  String get posterLandingSpecification3 =>
      '• คุณภาพการพิมพ์: ไมโครหมึก หยดหมึกเพื่อการพิมพ์ที่แม่นยำ สี 8 บิต คุณภาพเกือบจะเหมือนการพิมพ์ภาพถ่าย';

  @override
  String get posterLandingSpecification4 => '• กระดาษ: กระดาษซาตินหนา 0.22 มม.';

  @override
  String get posterLandingShippingHeader => 'รายละเอียดการจัดส่ง';

  @override
  String get posterLandingShipping1 =>
      '• จัดส่งจากโตรอนโต ประเทศแคนาดา ไปยังที่ใดก็ได้ในโลกโดยใช้ Canada Post';

  @override
  String get posterLandingShipping2 =>
      '• อนุญาตให้ใช้เวลา 2-4 สัปดาห์ในการจัดส่งไปยังจุดหมายปลายทางส่วนใหญ่';

  @override
  String get posterLandingShipping3 =>
      '• คำสั่งซื้อทั้งหมดจะถูกม้วนใส่ในกล่องกระดาษแข็งไปยังที่อยู่จัดส่งที่คุณส่งมา';

  @override
  String get posterLandingCancellationHeader => 'การยกเลิก/การคืนเงิน:';

  @override
  String get posterLandingCancellationBody =>
      'คุณสามารถขอคืนเงินได้ก่อนที่โปสเตอร์จะถูกส่งไปที่เครื่องพิมพ์ ซึ่งอาจใช้เวลาถึง 24 ชั่วโมง หลังจากดำเนินการสั่งซื้อแล้ว จะไม่สามารถขอคืนเงินหรือยกเลิกได้ คุณจะได้รับอีเมลเมื่อคำสั่งซื้อของคุณได้รับการพิมพ์';

  @override
  String get unsubscribe => 'ยกเลิกการสมัคร';

  @override
  String get unsubscribeConfirmMessage =>
      'คุณแน่ใจหรือไม่ว่าต้องการยกเลิกการสมัคร? คุณจะพลาดข้อเสนอพิเศษและการอัปเดต!';

  @override
  String get updateLive => 'อัปเดตที่อยู่อาศัย';

  @override
  String get updateLiveDescription =>
      'หากต้องการเปลี่ยนประเทศที่คุณอาศัยอยู่ คุณต้องเลือกประเทศใหม่เพื่อแทนที่ก่อน';

  @override
  String get underOneThousand => 'ต่ำกว่า 1,000';

  @override
  String get oneThousandToTenThousand => '1,000 – 10,000';

  @override
  String get overTenThousand => 'มากกว่า 10,000';

  @override
  String get becomeABrandAmbassador => 'เป็นแบรนด์แอมบาสเดอร์';

  @override
  String get instagram => 'Instagram';

  @override
  String get tiktok => 'TikTok';

  @override
  String get youtube => 'YouTube';

  @override
  String get website => 'เว็บไซต์';

  @override
  String get handle => 'ชื่อผู้ใช้';

  @override
  String get followers => 'ผู้ติดตาม';

  @override
  String get joinBrandAmbassadorProgram => 'เข้าร่วมโปรแกรมแบรนด์แอมบาสเดอร์';

  @override
  String get brandAmbassadorProgramDescription =>
      'รักแอป Visited ใช่ไหม? ในฐานะแบรนด์แอมบาสเดอร์ คุณจะเป็นตัวแทนของชุมชนนักเดินทางของเรา แสดงแผนที่และรายการท่องเที่ยวของคุณ และช่วยให้ผู้อื่นค้นพบจุดหมายและฟีเจอร์ใหม่ ๆ แลกกับรางวัล ส่วนลด ของที่ระลึก และอื่น ๆ อีกมากมาย!';

  @override
  String get fillOutTheFormToGetStarted => 'กรอกแบบฟอร์มด้านล่างเพื่อเริ่มต้น:';

  @override
  String get yourName => 'ชื่อของคุณ';

  @override
  String get yourNameEmptyError => 'กรุณากรอกชื่อของคุณ';

  @override
  String get fillInWhereApplicable => 'กรอกในส่วนที่เกี่ยวข้อง:';

  @override
  String get otherNetworks => 'เครือข่ายอื่นๆ';

  @override
  String get anythingElse => 'มีอะไรเพิ่มเติมที่คุณอยากบอกเราไหม?';

  @override
  String get yourTravelsByContinent => 'การเดินทางของคุณตามทวีป';

  @override
  String get territories => 'ดินแดน';

  @override
  String get couponCode => 'รหัสคูปอง';

  @override
  String get apply => 'ใช้';

  @override
  String get discount => 'ส่วนลด';

  @override
  String get noCouponCode => 'กรุณากรอกรหัสคูปอง';

  @override
  String get invalidCouponCode => 'รหัสคูปองไม่ถูกต้อง';

  @override
  String get couponApplied => 'ใช้คูปองแล้ว';

  @override
  String discountPercentage(double percentage) {
    return 'ลด $percentage%';
  }

  @override
  String discountAmount(double amount) {
    return 'ลดไป $amount!';
  }

  @override
  String get thankYou => 'ขอบคุณ!';

  @override
  String get formSubmitted =>
      'เราได้รับคำขอของคุณในการเป็นแบรนด์แอมบาสเดอร์แล้ว เราจะติดต่อกลับในเร็วๆ นี้!';
}
