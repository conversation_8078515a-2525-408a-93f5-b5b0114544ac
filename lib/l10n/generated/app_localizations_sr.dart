// ignore: unused_import
import 'package:intl/intl.dart' as intl;
import 'app_localizations.dart';

// ignore_for_file: type=lint

/// The translations for Serbian (`sr`).
class AppLocalizationsSr extends AppLocalizations {
  AppLocalizationsSr([String locale = 'sr']) : super(locale);

  @override
  String get appName => 'Visited';

  @override
  String get language => 'Језик';

  @override
  String get pickEmailApp => 'Изаберите своју апликацију за е-пошту';

  @override
  String numberOfCountriesShareMessage(int amount) {
    return 'Посетио/ла сам $amount земаља! Колико си ти посетио/ла? www.visitedapp.com';
  }

  @override
  String numberOfCitiesShareMessage(int amount) {
    return 'Посетио/ла сам $amount градова! Колико си ти посетио/ла? www.visitedapp.com';
  }

  @override
  String numberOfItemsInTodoListShareMessage(int amount, String listName) {
    return 'Посетио/ла сам $amount $listName! Колико си ти посетио/ла? www.visitedapp.com';
  }

  @override
  String get clear => 'Обриши';

  @override
  String get been => 'Био';

  @override
  String get want => 'Желим';

  @override
  String get live => 'Живим';

  @override
  String get lived => 'Живео';

  @override
  String get water => 'Вода';

  @override
  String get land => 'Земља';

  @override
  String get borders => 'Границе';

  @override
  String get labels => 'Ознаке';

  @override
  String get legend => 'Легенда';

  @override
  String get inspiration => 'Инспирација';

  @override
  String get inspirations => 'Инспирације';

  @override
  String get delete => 'Бриши';

  @override
  String get unlockVisitedUpsellTitle => 'Желите да видите још?';

  @override
  String get unlockVisitedUpsellSubtitle =>
      'Откључајте све функције и у потпуности уживајте у Visited';

  @override
  String get checkTheDetails => 'Погледајте детаље';

  @override
  String get moreInspirationsComingSoon =>
      'Радимо на убацивању више слика. Проверите поново ускоро!';

  @override
  String get unlockPremiumFeatures => 'Откључајте премијум функције';

  @override
  String get purchased => 'Купљено!';

  @override
  String get buy => 'Купи';

  @override
  String get restorePurchases => 'Врати куповину';

  @override
  String get ok => 'У реду';

  @override
  String get areYouSure => 'Да ли сте сигурни?';

  @override
  String get deleteInspirationConfirmMessage =>
      'Брисање ове картице је трајно. Не постоји начин да вратите ову слику.';

  @override
  String get cancel => 'Откажи';

  @override
  String get map => 'Мапа';

  @override
  String get progress => 'Напредак';

  @override
  String get myTravelGoal => 'Мој циљ путовања';

  @override
  String goalRemaining(int remaining) {
    return 'још $remaining!';
  }

  @override
  String get top => 'ВРХ';

  @override
  String get ofTheWorld => 'света';

  @override
  String get countries => 'државе';

  @override
  String topPlacesVisitedFromCountry(String country) {
    return 'Најпопуларније државе посећене из $country:';
  }

  @override
  String get login => 'Пријави се';

  @override
  String get logout => 'Одјави се';

  @override
  String get enterYourEmail => 'Унесите свој и-мејл';

  @override
  String get privacyPolicy => 'Политика приватности';

  @override
  String get privatePolicyUrl =>
      'https://www.arrivinginhighheels.com/privacy-policy-russian/';

  @override
  String get termsOfUse => 'Услови коришћења';

  @override
  String get termsOfUserUrl =>
      'https://www.arrivinginhighheels.com/terms-of-use-russian/';

  @override
  String get errorTitle => 'Упс!';

  @override
  String get enterValidEmail => 'Унесите важећи и-мејл';

  @override
  String get settings => 'Подешавања';

  @override
  String get whereDoYouLive => 'Где живите?';

  @override
  String get whereHaveYouBeen => 'Где сте били?';

  @override
  String get whereDoYouFlyFrom => 'Одакле летите?';

  @override
  String get next => 'Следеће';

  @override
  String get missingAirports =>
      'Не видите шта тражите? Пошаљите нам и-мејл на <EMAIL>';

  @override
  String get missingAirportsEmailTitle => 'Недостајући аеродроми!';

  @override
  String get supportEmailAddress => '<EMAIL>';

  @override
  String get welcomeTitle => 'Добродошли на Visited';

  @override
  String get welcomeSubtitle => 'Чека вас авантура живота';

  @override
  String get getStarted => 'Почните';

  @override
  String get privacyAgreement => 'Уговор о приватности';

  @override
  String get privacyAgreementSubtitle =>
      'Сложите се са следећим ставкама пре него што наставите да користите Visited.';

  @override
  String get privacyAgreementTermsMarkdown =>
      'Означавањем овог поља потврђујете да сте прочитали и прихватили услове [Политике приватности] (https://www.arrivinginhighheels.com/privacy-policy) и [Услове коришћења] (https://www.arrivinginhighheels.com/terms-of-use) \"Arriving in High Heels\" .';

  @override
  String get privacyAgreementOptIn =>
      'Сагласан сам да примам електронске поруке od Arriving in High Heels које садрже информације и понуде у вези са производима, апликацијама и услугама којe би ме моглe занимати, укључујући обавештења о распродајама, промоцијама, понудама и билтенима. Ову сагласност могу повући у сваком тренутку како је описано у Политици приватности или кликом на везу \"unsubscribe\" у електронским порукама.';

  @override
  String get submit => 'Пошаљи';

  @override
  String get companyName => 'Arriving In High Heels Corporation';

  @override
  String get companyAddress =>
      '31 Claudview Street\nKing City, Ontario\nCanada\nL7B 0C6';

  @override
  String get privacyAgreementRequired =>
      'Морате се сложити са оба наша услова и омогућити да бисте наставили да користите Visited.';

  @override
  String get deleteAccount => 'Обриши налог';

  @override
  String get removeAdsUpsell =>
      'Да ли желите да онемогућите огласе и откажете претплату на и-мејл маркетинг?';

  @override
  String get deleteAccountWarning =>
      'Брисањем вашег налога уклонићете све ваше податке са наших сервера. Овај поступак се не може поништити.';

  @override
  String get about => 'О';

  @override
  String get popularity => 'Популарност';

  @override
  String get regions => 'Региони';

  @override
  String get population => 'Популација';

  @override
  String get size => 'Величина';

  @override
  String get coverage => 'Покривеност';

  @override
  String get percentOfCountryVisited => '% посећених држава';

  @override
  String get visited => 'посећено';

  @override
  String get notes => 'Напомене';

  @override
  String get kmSquared => 'km²';

  @override
  String get customize => 'Прилагоди';

  @override
  String get onlyCountSovereign => 'Суверено према УН';

  @override
  String get countUkSeparately => 'Рачунај УК државе одвојено';

  @override
  String get showLegend => 'Прикажи легенду';

  @override
  String get showLivedPin => 'Прикажи означене државе у којима сам живео';

  @override
  String get useMyColours => 'Користи моје боје';

  @override
  String get mapColors => 'Боје мапе';

  @override
  String get traveller => 'Путник';

  @override
  String get nightTraveller => 'Ноћни путник';

  @override
  String get original => 'Оригинал';

  @override
  String get explorer => 'Истраживач';

  @override
  String get weekender => 'Викенд путник';

  @override
  String get naturalist => 'Натуралиста';

  @override
  String get historian => 'Историчар';

  @override
  String get thrillSeeker => 'Трагач за узбуђењем';

  @override
  String get culturalBuff => 'Љубитељ културе';

  @override
  String get myColors => 'Моје боје';

  @override
  String get experiences => 'Искуства';

  @override
  String get done => 'Заврши';

  @override
  String get experiencesInstructions => 'Додирните дугме + да бисте започели!';

  @override
  String get continueText => 'Настави';

  @override
  String get experiencesDescription => 'Шта волите да радите када путујете?';

  @override
  String get visitedWebsiteShortLink => 'https://www.visitedapp.com';

  @override
  String get sharingHashtag => '#Visited';

  @override
  String get myTravelMap => 'Мапа путовања';

  @override
  String percentOfWorldSeen(int percentage) {
    return 'Видео сам $percentage% света';
  }

  @override
  String get requiresOnline =>
      'Жао нам је, Visited захтева активну мрежну везу. Отворите подешавања апликације и уверите се да су омогућени Wi-Fi или мобилни подаци и онемогућен је режим летења';

  @override
  String get list => 'Листа';

  @override
  String get more => 'Још';

  @override
  String get myCountrySelections => 'Моји избори држава';

  @override
  String get cities => 'Градови';

  @override
  String get citiesInstructions =>
      'Додирните било коју државу да бисте започели одабир градова.';

  @override
  String get missingCitiesEmailTitle => 'Недостајући градови!';

  @override
  String get lists => 'Листе';

  @override
  String get disputedTerritories => 'Спорне територије';

  @override
  String get sponsored => 'Спонзорисано';

  @override
  String get places => 'Места';

  @override
  String get noListsError =>
      'Упс, тренутно није доступна листа, покушајте мало касније';

  @override
  String get noInspirationsError =>
      'Упс, тренутно нису доступне фотографије, покушајте мало касније';

  @override
  String get mostFrequentlyVisitedCountries => 'Ваше најчешће посећене земље:';

  @override
  String get update => 'ажурирање';

  @override
  String get signup => 'Пријави се';

  @override
  String get loginWallSubtitle =>
      'Креирање бесплатног налога за доживљај комплетне верзије Visited-а';

  @override
  String get loseAllSelectionsWarning =>
      'Изгубићете све изборе након затварања апликације.';

  @override
  String get createAccount => 'Креирај налог';

  @override
  String get continueWithoutAccount => 'Настави без налога';

  @override
  String get inspirationPromotion =>
      'Инспирисани прелепом фотографијом са путовања';

  @override
  String get saveStatsPromotion => 'Сачувајте статистику путовања!';

  @override
  String get selectRegionsPromotion => 'Избор држава и покрајина';

  @override
  String get experiencesPromotion => 'Праћење искустава широм света';

  @override
  String get missingListItem =>
      'Да ли смо пропустили нешто? Додирните овде да нам пошаљете е-пошту да бисте додали своје омиљено место.';

  @override
  String missingListItemEmailTitle(String list) {
    return 'Недостаје предмет из $list';
  }

  @override
  String listShareMessage(Object amount, Object listName) {
    return 'Посетио сам $amount $listName';
  }

  @override
  String get orderPoster => 'Плакат';

  @override
  String get shareMap => 'Мапа дељења';

  @override
  String get posterLandingPageTitle => 'Набавите свој постер';

  @override
  String get posterNotAvailableError =>
      'Куповина постера тренутно није доступна. Покушајте поново касније.';

  @override
  String posterPricePlusShipping(Object price, Object shipping) {
    return '$price + $shipping испорука';
  }

  @override
  String get posterDescriptionMarkdownApple =>
      '## О нашим прилагођеним штампама штампања\nИспишите своју персонализовану мапу света. Прилагодите га својим сопственим бојама и да ли је достављен директно у ваш дом.\n \n### Спецификације:\n- 16\" x 20\" (40.64cm x 50.8cm)\n- Оријентација пејзажа.\n- Микро мастило, капљице за прецизне отиске, 8 битне боје, скоро квалитет штампања,\n- 0,22 мм Дебели сатенски папир\n\n### Детаљи испоруке:\nДостава из Торонта, Канада у било где у свету која користи Цанада Пост. Молимо вас да оставите 2 до 4 недеље за испоруку у већину дестинација. Сва наређења се испоручују у кутији картонске цеви за поднесену адресу за доставу. Сва плата се рукује Аппле платама, Гоогле платама или пругом.\n\n\n### Отказивање / повраћај новца:\nНаруџбе се обрађују одмах након што су поднели најбржи преокрет. Стога не постоји поврат / отказ.';

  @override
  String get posterDescriptionMarkdown =>
      '## О нашим прилагођеним штампама штампања\nИспишите своју персонализовану мапу света. Прилагодите га својим сопственим бојама и да ли је достављен директно у ваш дом.\n \n### Спецификације:\n- 16\" x 20\" (40.64cm x 50.8cm)\n- Оријентација пејзажа.\n- Микро мастило, капљице за прецизне отиске, 8 битне боје, скоро квалитет штампања,\n- 0,22 мм Дебели сатенски папир\n\n### Детаљи испоруке:\nДостава из Торонта, Канада у било где у свету која користи Цанада Пост. Молимо вас да оставите 2 до 4 недеље за испоруку у већину дестинација. Сва наређења се испоручују у кутији картонске цеви за поднесену адресу за доставу. Сва плата се рукује Аппле платама, Гоогле платама или пругом.\n\n\n### Отказивање / повраћај новца:\nНаруџбе се обрађују одмах након што су поднели најбржи преокрет. Стога не постоји поврат / отказ.';

  @override
  String get posterCustomizeTitle => 'Прилагодите постер';

  @override
  String get enterShippingAddress => 'Унесите адресу за доставу';

  @override
  String get price => 'Цена';

  @override
  String formattedPlusTax(Object formattedPrice) {
    return '$formattedPrice + порез';
  }

  @override
  String get showSelections => 'Покажи избор';

  @override
  String get posterNoRefunds =>
      'Ниједан повраћај новца није на располагању након што је ваш постер одштампан.';

  @override
  String get posterReviewOrder => 'Прегледајте поруџбину';

  @override
  String get email => 'Е-маил';

  @override
  String get emailEmptyError => 'Унесите своју е-пошту';

  @override
  String get fullName => 'Пуно име';

  @override
  String get fullNameEmptyError => 'Молимо Вас да унесете име и презиме';

  @override
  String get streetAddressEmptyError => 'Унесите своју адресу улице';

  @override
  String get cityEmptyError => 'Унесите свој град';

  @override
  String fieldEmptyError(Object fieldName) {
    return 'Молимо унесите своје $fieldName';
  }

  @override
  String get country => 'Земља';

  @override
  String get countryEmptyError => 'Унесите своју земљу';

  @override
  String get posterReviewOrderTitle => 'Прегледајте поруџбину';

  @override
  String get buyNow => 'Купити сада';

  @override
  String get secureCheckoutDisclaimer => 'Сигурна одјава пружена пругом';

  @override
  String get total => 'Тотално';

  @override
  String get tax => 'Порез';

  @override
  String get subtotal => 'Субтотал';

  @override
  String get posterProductName => 'Посећени постер Цустом посетио мапу';

  @override
  String get shipping => 'поштарина';

  @override
  String get posterOrderReceivedTitle => 'Наређење примљеног';

  @override
  String get posterOrderReceivedSubtitle => 'Примили смо вашу поруџбину!';

  @override
  String get posterOrderReceivedInstructionsMarkdown =>
      'Проверите своју е-пошту за више ажурирања. \nДопустите до 4 недеље да бисте стигли до 4 недеље да бисте стигли да стигнете. \nАко имате било каквих питања, пошаљите нам е-пошту на [<EMAIL>] (<EMAIL>)';

  @override
  String get posterOrderReceivedEmailSubject =>
      'Статус наруџбе за штампање плаката';

  @override
  String get moreInfo => 'Више информација';

  @override
  String get logoutConfirm => 'Да ли желите да се одјавите из апликације?';

  @override
  String get emailNotAvailable => 'Тај е-пошта је узета.';

  @override
  String get alphabetical => 'Азбучни';

  @override
  String get firstTimeLiveTutorial =>
      'Омогућавање ваше матичне земље и града персонализираће ваше искуство апликација.';

  @override
  String get firstTimeBeenTutorial =>
      'Одабир где вам омогућава да прегледате своју мапу свих земаља које сте били и видели личну статистику.';

  @override
  String get progressTooltipGoal =>
      'Ваши циљеви путовања заснивају се на броју земаља које \"желите\" да путујете у поређењу са земљама у којима сте \"били\".';

  @override
  String get progressTooltipRank =>
      'Овај број показује како се рангирате у поређењу са путницима широм света. Можете повећати свој ранг путујући у више земаља.';

  @override
  String get progressTooltipPercentageOfWorld =>
      'Овај графикон се заснива на броју земаља које сте били у поређењу са укупним земљама света.';

  @override
  String get sortBy => 'Сортирај по';

  @override
  String get updateWishlist => 'Ажурирај листу жеља';

  @override
  String get mapInfo =>
      'Кликните на земљу да бисте изабрали, желите или уживо. Такође можете да кликнете на икону која је пронађена у горњем левом углу за приказ листе.';

  @override
  String get oneTimePurchase => 'Све је једнократна куповина!';

  @override
  String get contact => 'Контакт';

  @override
  String get contactUs => 'Контактирајте нас';

  @override
  String get noCitiesSelected => 'Још нисте одабрали ниједан градови ...';

  @override
  String get updateTravelGoal => 'Ажурирајте циљ путовања';

  @override
  String get travelGoalComplete =>
      'Честитамо! \n\nИоу сте завршили свој пут! \n\nТАП тастер + да бисте додали још земаља.';

  @override
  String loginEmailNotFoundError(String email) {
    return 'Не постоји рачун придружени е-маил $email. Да ли бисте то сада желели да креирате?';
  }

  @override
  String get tryAgain => 'Покушајте поново';

  @override
  String get itineraries => 'Планови за пут';

  @override
  String get itinerary => 'План путовања';

  @override
  String get place => 'Место';

  @override
  String get itinerariesDescription =>
      'Ово су места за која сте изразили интересовање.\nКористите овај водич да бисте лакше испланирали свој следећи одмор.';

  @override
  String get addMore => 'Додај још';

  @override
  String get interests => 'Интересовање';

  @override
  String get selection => 'Избор';

  @override
  String get goal => 'Голман';

  @override
  String get noItineraries => 'Нема Итинерара';

  @override
  String get noItinerariesExplanation =>
      'Молимо додате нека места, инспирације или искуства како бисте видели аутоматско генерисање вашег итинерара.';

  @override
  String get clusterPins => 'Групни маркери на мапи';

  @override
  String get toggleRegions => 'Прикажи регионе';

  @override
  String get mapProjection => 'Пројекција мапе';

  @override
  String get mercator => 'Меркатор';

  @override
  String get equirectangular => 'Еквиректангуларан';

  @override
  String get yourTravellerType => 'Vaš tip putnika:';

  @override
  String get yourHotelPreferences => 'Vaše hotelske preference:';

  @override
  String get budget => 'Budžet';

  @override
  String get midScale => 'Srednja klasa';

  @override
  String get luxury => 'Luksuz';

  @override
  String get noTravellerType =>
      'Dodajte stavke na svoju listu želja da biste otkrili kakav ste putnik.';

  @override
  String get unlockLived => 'Otključaj Život';

  @override
  String get unlockLivedDescription =>
      'Odaberite na karti gde ste ranije živeli!';

  @override
  String get futureFeaturesDescription => '...i sve buduće funkcije';

  @override
  String get yourMostFrequentlyVisitedCountry =>
      'Ваша најчешће посећена земља:';

  @override
  String get departureDate => 'Datum polaska';

  @override
  String get returnDate => 'Datum povratka';

  @override
  String get hotels => 'Hoteli';

  @override
  String get food => 'Hrana';

  @override
  String get travelDates => 'Datumi putovanja';

  @override
  String get posterForMe => 'За мене';

  @override
  String get posterSendGift => 'Пошаљи поклон';

  @override
  String get addSelections => 'Додај селекције';

  @override
  String get posterType => 'Тип постера';

  @override
  String get help => 'Помоћ';

  @override
  String get tutorialMap =>
      'Додирните земљу да бисте изабрали: био, желео и живео.';

  @override
  String get tutorialMapList =>
      'Додирните икону листе (горњи леви угао) да бисте изабрали по листи.';

  @override
  String get tutorialCountryDetails =>
      'Додирните земљу, а затим \"више\" да бисте изабрали по региону.';

  @override
  String get tutorialItems =>
      'Померите прекидач да бисте изабрали како желите да изаберете ставке.';

  @override
  String get tutorialInspirations =>
      'Превуците надесно или налево да бисте прешли на следећу картицу.';

  @override
  String get lifetime => 'Doživotno';

  @override
  String get chooseYourPlan => 'Izaberite svoj plan';

  @override
  String get requestARefund => 'Zatražite povrat novca';

  @override
  String get noPurchasesFound => 'Nema pronađenih kupovina.';

  @override
  String get noProductsAvailable => 'Nema dostupnih proizvoda';

  @override
  String get posterLandingAppBar => 'Донесите своје приче кући';

  @override
  String get posterLandingSubHeading => 'Ваше путовање, ваша прича';

  @override
  String get posterLandingSubDescription =>
      'Ваша путовања су више од путовања, она су приче, сећања и прекретнице. Претворите те незаборавне тренутке у персонализовану мапу света која је јединствена као и ваше авантуре.';

  @override
  String get posterLandingPromoBullet1 =>
      '• Мапа ваших достигнућа: Истакните сваку дестинацију, од вашег првог великог путовања до ваше најсмелије авантуре.';

  @override
  String get posterLandingPromoBullet2 =>
      '• Прославите свако путовање: Оживите свакодневна путовања са прелепо израђеним постером дизајнираним да инспирише.';

  @override
  String get posterLandingPromoBullet3 =>
      '• Поклон који ће ценити: Изненадите сапутника прилагођеном мапом која приказује њихово путовање, савршеном за рођендане, прекретнице или само зато.';

  @override
  String get posterLandingHowItWorks => 'Како то ради!';

  @override
  String get posterLandingHowItWorksStep1 =>
      '1. Прилагодите свој дизајн: Одаберите боје, стилове и означите своја путовања (или њихова!)';

  @override
  String get posterLandingHowItWorksStep2 =>
      '2. Прегледајте своју мапу: погледајте је како оживи пре ваше поруџбине.';

  @override
  String get posterLandingHowItWorksStep3_iOS =>
      '3. Сигурно плаћање: брзо и безбедно уз Аппле Паи или Стрипе.';

  @override
  String get posterLandingHowItWorksStep3_android =>
      '3. Безбедно плаћање: брзо и безбедно уз Гоогле Паи или Стрипе.';

  @override
  String get posterLandingHowItWorksStep4 =>
      '4. Спреман за приказ: Ми ћемо га послати право на ваша врата (или њихова).';

  @override
  String get posterLandingCustomerReviewsHeader => 'Искуства сапутника';

  @override
  String get posterLandingCustomerReview1 =>
      '„Ова мапа је одличан начин да пратим свуда где сам путовао и планирам наша будућа путовања. Квалитет је солидан и изгледа сјајно док виси у мојој канцеларији. Чак сам набавио једну за свог брата и није могао да престане да прича о томе како је супер!“ - Џон Ц.';

  @override
  String get posterLandingCustomerReview2 =>
      '\"Путовао сам у преко 150 лука док сам радио на крстарењу. Ова мапа је одличан додатак мојој дневној соби као успомена на све године на мору.\" - Бети К.';

  @override
  String get posterLandingCustomerReview3 =>
      '„Одличан поклон за мајчин дан. Моја мама је била супер дирнута!“ Саманта В.';

  @override
  String get posterLandingCustomerReview4 =>
      '„Одштампао сам мапу места која сам желео да посетим са својом девојком. Био је то сјајан божићни поклон. Такође висок квалитет.“ Бред Ј.';

  @override
  String get posterLandingSpecifications => 'Спецификације';

  @override
  String get posterLandingSpecification1 =>
      '• Димензије: 16\" к 20\" (40,64 цм к 50,8 цм)';

  @override
  String get posterLandingSpecification2 => '• Оријентација: пејзаж';

  @override
  String get posterLandingSpecification3 =>
      '- Квалита тлаче: Микротлач, квапочки пре пресне вытлачки.  8-битове фарби, такмер фотографицка квалита тлаче.';

  @override
  String get posterLandingSpecification4 =>
      '• Папир: сатен папир дебљине 0,22 мм';

  @override
  String get posterLandingShippingHeader => 'Детаљи испоруке';

  @override
  String get posterLandingShipping1 =>
      '• Достава из Торонта, Канада до било ког места у свету користећи Цанада Пост.';

  @override
  String get posterLandingShipping2 =>
      '• Дозволите 2-4 недеље за испоруку на већину дестинација.';

  @override
  String get posterLandingShipping3 =>
      '• Све поруџбине су умотане у картонску кутију на адресу за доставу коју доставите.';

  @override
  String get posterLandingCancellationHeader => 'Отказивање/повраћај новца:';

  @override
  String get posterLandingCancellationBody =>
      'Поврат новца је доступан пре него што ваш постер буде послат на штампач, што може да потраје и до 24 сата.  Након што је ваша поруџбина обрађена, поврат новца/отказивање није доступан.  Добићете е-поруку када ваша поруџбина буде одштампана.';

  @override
  String get unsubscribe => 'Одјави се';

  @override
  String get unsubscribeConfirmMessage =>
      'Да ли сте сигурни да се желите одјавити? Пропустићете ексклузивне понуде и ажурирања!';

  @override
  String get updateLive => 'Ажурирај Место Становања';

  @override
  String get updateLiveDescription =>
      'Да бисте променили земљу у којој живите, прво морате да изаберете нову земљу која ће је заменити.';

  @override
  String get underOneThousand => 'Испод 1.000';

  @override
  String get oneThousandToTenThousand => '1.000 – 10.000';

  @override
  String get overTenThousand => 'Преко 10.000';

  @override
  String get becomeABrandAmbassador => 'Постаните амбасадор бренда';

  @override
  String get instagram => 'Инстаграм';

  @override
  String get tiktok => 'ТикТок';

  @override
  String get youtube => 'Јутјуб';

  @override
  String get website => 'Веб сајт';

  @override
  String get handle => 'Надимак';

  @override
  String get followers => 'Пратиоци';

  @override
  String get joinBrandAmbassadorProgram =>
      'Придружите се програму амбасадора бренда';

  @override
  String get brandAmbassadorProgramDescription =>
      'Волите Visited? Као амбасадор бренда представљаћете нашу заједницу путника, показивати своју мапу и листе путовања и помагати другима да открију нове дестинације и функције апликације. Заузврат ћете добити награде, попусте, поклончиће и још много тога!';

  @override
  String get fillOutTheFormToGetStarted =>
      'Попуните формулар испод да бисте започели:';

  @override
  String get yourName => 'Ваше име';

  @override
  String get yourNameEmptyError => 'Унесите ваше име';

  @override
  String get fillInWhereApplicable => 'Попуните где је потребно:';

  @override
  String get otherNetworks => 'Остале мреже';

  @override
  String get anythingElse => 'Има ли још нешто што желите да нам кажете?';

  @override
  String get yourTravelsByContinent => 'Ваша путовања по континентима';

  @override
  String get territories => 'Територије';

  @override
  String get couponCode => 'Код купона';

  @override
  String get apply => 'Примени';

  @override
  String get discount => 'Попуст';

  @override
  String get noCouponCode => 'Унесите код купона';

  @override
  String get invalidCouponCode => 'Неважећи код купона';

  @override
  String get couponApplied => 'Купон примењен';

  @override
  String discountPercentage(double percentage) {
    return '$percentage% попуста!';
  }

  @override
  String discountAmount(double amount) {
    return 'Снижено за $amount!';
  }

  @override
  String get thankYou => 'Хвала!';

  @override
  String get formSubmitted =>
      'Примили смо ваш захтев да постанете амбасадор бренда. Ускоро ћемо вас контактирати!';
}
