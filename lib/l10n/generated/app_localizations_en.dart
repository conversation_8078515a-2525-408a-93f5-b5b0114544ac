// ignore: unused_import
import 'package:intl/intl.dart' as intl;
import 'app_localizations.dart';

// ignore_for_file: type=lint

/// The translations for English (`en`).
class AppLocalizationsEn extends AppLocalizations {
  AppLocalizationsEn([String locale = 'en']) : super(locale);

  @override
  String get appName => 'Visited';

  @override
  String get language => 'Language';

  @override
  String get pickEmailApp => 'Pick your email app';

  @override
  String numberOfCountriesShareMessage(int amount) {
    return 'I have visited $amount countries!  How many have you been to? www.visitedapp.com';
  }

  @override
  String numberOfCitiesShareMessage(int amount) {
    return 'I have visited $amount cities!  How many have you been to? www.visitedapp.com';
  }

  @override
  String numberOfItemsInTodoListShareMessage(int amount, String listName) {
    return 'I have visited $amount $listName!  How many have you been to? www.visitedapp.com';
  }

  @override
  String get clear => 'Clear';

  @override
  String get been => 'Been';

  @override
  String get want => 'Want';

  @override
  String get live => 'Live';

  @override
  String get lived => 'Lived';

  @override
  String get water => 'Water';

  @override
  String get land => 'Land';

  @override
  String get borders => 'Borders';

  @override
  String get labels => 'Labels';

  @override
  String get legend => 'Legend';

  @override
  String get inspiration => 'Inspiration';

  @override
  String get inspirations => 'Inspirations';

  @override
  String get delete => 'Delete';

  @override
  String get unlockVisitedUpsellTitle => 'Want to see more?';

  @override
  String get unlockVisitedUpsellSubtitle =>
      'Unlock all features and enjoy Visited in it\'s full strength';

  @override
  String get checkTheDetails => 'Check the Details';

  @override
  String get moreInspirationsComingSoon =>
      'We are working on getting more images.  Check back soon!';

  @override
  String get unlockPremiumFeatures => 'Unlock premium features';

  @override
  String get purchased => 'Purchased!';

  @override
  String get buy => 'Buy';

  @override
  String get restorePurchases => 'Restore Purchase';

  @override
  String get ok => 'Ok';

  @override
  String get areYouSure => 'Are you sure?';

  @override
  String get deleteInspirationConfirmMessage =>
      'Deleting this card is permanent.  There is no way to recover this image.';

  @override
  String get cancel => 'Cancel';

  @override
  String get map => 'Map';

  @override
  String get progress => 'Progress';

  @override
  String get myTravelGoal => 'My Travel Goal';

  @override
  String goalRemaining(int remaining) {
    return '$remaining more to go!';
  }

  @override
  String get top => 'TOP';

  @override
  String get ofTheWorld => 'of the world!';

  @override
  String get countries => 'countries';

  @override
  String topPlacesVisitedFromCountry(String country) {
    return 'Top Countries Visited from $country:';
  }

  @override
  String get login => 'Log In';

  @override
  String get logout => 'Log Out';

  @override
  String get enterYourEmail => 'Enter your email';

  @override
  String get privacyPolicy => 'Privacy Policy';

  @override
  String get privatePolicyUrl =>
      'https://www.arrivinginhighheels.com/privacy-policy';

  @override
  String get termsOfUse => 'Terms of Use';

  @override
  String get termsOfUserUrl =>
      'https://www.arrivinginhighheels.com/terms-of-use';

  @override
  String get errorTitle => 'Whoops!';

  @override
  String get enterValidEmail => 'Please enter a valid email';

  @override
  String get settings => 'Settings';

  @override
  String get whereDoYouLive => 'Where do you live?';

  @override
  String get whereHaveYouBeen => 'Where have you been?';

  @override
  String get whereDoYouFlyFrom => 'Where do you fly out of?';

  @override
  String get next => 'Next';

  @override
  String get missingAirports =>
      'Don\'t see what you are looking for? Send us an <NAME_EMAIL>';

  @override
  String get missingAirportsEmailTitle => 'Missing Airports!';

  @override
  String get supportEmailAddress => '<EMAIL>';

  @override
  String get welcomeTitle => 'Welcome to Visited';

  @override
  String get welcomeSubtitle => 'The adventure of a lifetime awaits';

  @override
  String get getStarted => 'Get Started';

  @override
  String get privacyAgreement => 'Privacy Agreement';

  @override
  String get privacyAgreementSubtitle =>
      'Please agree to the following items before continuing to use Visited.';

  @override
  String get privacyAgreementTermsMarkdown =>
      'By checking this box, you acknowledge that you have read and agree to be bound by Arriving in High Heels\'  [Privacy Policy](https://www.arrivinginhighheels.com/privacy-policy) and [Terms of Use](https://www.arrivinginhighheels.com/terms-of-use).';

  @override
  String get privacyAgreementOptIn =>
      'I agree to receive electronic messages from Arriving in High Heels containing information and offers with respect to products, applications and services that may be of interest to me, including notification of sales, promotions, offers and newsletters. I may withdraw this consent at any time as described in the Privacy Policy or by clicking on the \"unsubscribe\" link in the electronic messages.';

  @override
  String get submit => 'Submit';

  @override
  String get companyName => 'Arriving In High Heels Corporation';

  @override
  String get companyAddress =>
      '31 Claudview Street\nKing City, Ontario\nCanada\nL7B 0C6';

  @override
  String get privacyAgreementRequired =>
      'You must agree to both our terms and opt in order to continue using Visited.';

  @override
  String get deleteAccount => 'Delete Account';

  @override
  String get removeAdsUpsell =>
      'Do you wish to opt out of ads and unsubscribe from email marketing instead?';

  @override
  String get deleteAccountWarning =>
      'Deleting your account will remove all of your information from our servers.\nThis process cannot be undone.';

  @override
  String get about => 'About';

  @override
  String get popularity => 'Popularity';

  @override
  String get regions => 'Regions';

  @override
  String get population => 'Population';

  @override
  String get size => 'Size';

  @override
  String get coverage => 'Coverage';

  @override
  String get percentOfCountryVisited => '% of country visited';

  @override
  String get visited => 'visited';

  @override
  String get notes => 'Notes';

  @override
  String get kmSquared => 'km²';

  @override
  String get customize => 'Customize';

  @override
  String get onlyCountSovereign => 'U.N. Sovereign';

  @override
  String get countUkSeparately => 'Count U.K. Countries separately';

  @override
  String get showLegend => 'Show Legend';

  @override
  String get showLivedPin => 'Show Lived Pin';

  @override
  String get useMyColours => 'Use My Colors';

  @override
  String get mapColors => 'Map Colors';

  @override
  String get traveller => 'The Traveller';

  @override
  String get nightTraveller => 'The Night Traveller';

  @override
  String get original => 'The Original';

  @override
  String get explorer => 'The Explorer';

  @override
  String get weekender => 'The Weekender';

  @override
  String get naturalist => 'The Naturalist';

  @override
  String get historian => 'The Historian';

  @override
  String get thrillSeeker => 'The Thrill Seeker';

  @override
  String get culturalBuff => 'The Cultural Buff';

  @override
  String get myColors => 'My Colors';

  @override
  String get experiences => 'Experiences';

  @override
  String get done => 'Done';

  @override
  String get experiencesInstructions => 'Tap the + button to get started!';

  @override
  String get continueText => 'Continue';

  @override
  String get experiencesDescription =>
      'What do you like to do when you travel?';

  @override
  String get visitedWebsiteShortLink => 'https://www.visitedapp.com';

  @override
  String get sharingHashtag => '#Visited';

  @override
  String get myTravelMap => 'My Travel Map';

  @override
  String percentOfWorldSeen(int percentage) {
    return 'I\'ve seen $percentage% of the world';
  }

  @override
  String get requiresOnline =>
      'Sorry, Visited requires an active network connection.  Please open your settings app and ensure that either \\r Wi-Fi or Cellular data is enabled and Airplane Mode is disabled';

  @override
  String get list => 'List';

  @override
  String get more => 'More';

  @override
  String get myCountrySelections => 'My Country Selections';

  @override
  String get cities => 'Cities';

  @override
  String get citiesInstructions =>
      'Tap on any country to start selecting cities.';

  @override
  String get missingCitiesEmailTitle => 'Missing Cities!';

  @override
  String get lists => 'Lists';

  @override
  String get disputedTerritories => 'Disputed Territories';

  @override
  String get sponsored => 'Sponsored';

  @override
  String get places => 'Places';

  @override
  String get noListsError =>
      'Opps, no lists available at this time, please try a bit later';

  @override
  String get noInspirationsError =>
      'Opps, no photos are available right now, please try a bit later';

  @override
  String get mostFrequentlyVisitedCountries =>
      'Your most frequently visited countries:';

  @override
  String get update => 'update';

  @override
  String get signup => 'Sign Up';

  @override
  String get loginWallSubtitle =>
      'Create a free account to experience the full version of Visited';

  @override
  String get loseAllSelectionsWarning =>
      'You will lose all your selections after closing the app.';

  @override
  String get createAccount => 'Create Account';

  @override
  String get continueWithoutAccount => 'Continue without an Account';

  @override
  String get inspirationPromotion =>
      'Get inspired with beautiful travel photography';

  @override
  String get saveStatsPromotion => 'Save Your Travel Stats!';

  @override
  String get selectRegionsPromotion => 'Select States and Provinces';

  @override
  String get experiencesPromotion => 'Track Experiences all around the World';

  @override
  String get missingListItem =>
      'Did we miss something?  Tap here to send us an email to get your favorite place added.';

  @override
  String missingListItemEmailTitle(String list) {
    return 'Missing Item from $list';
  }

  @override
  String listShareMessage(Object amount, Object listName) {
    return 'I have visited $amount $listName';
  }

  @override
  String get orderPoster => 'Poster';

  @override
  String get shareMap => 'Share Map';

  @override
  String get posterLandingPageTitle => 'Get Your Poster';

  @override
  String get posterNotAvailableError =>
      'Poster purchasing is not available right now.  Please try again later.';

  @override
  String posterPricePlusShipping(Object price, Object shipping) {
    return '$price + $shipping shipping';
  }

  @override
  String get posterDescriptionMarkdownApple =>
      '## About Our Custom Print Maps\nPrint your personalized world map. Customize it with your very own colors and have it delivered straight to your home.\n \n### Specifications: \n- 16\" x 20\" (40.64cm x 50.8cm)\n- Landscape orientation.\n- Micro ink, droplets for precise prints, 8 bit color, almost photo print quality, \n- 0.22mm thick Satin Paper\n\n### Shipping Details:\n- Shipping from Toronto, Canada to anywhere in the world using Canada Post. \n- Please allow 2 to 4 weeks for delivery to most destinations. \n- All orders are shipped rolled up in a cardboard tube box to the shipping address submitted.\n\n### How Payment is Handled: \n All payment is handled by Apple Pay or Stripe.\n\n\n### Cancellation/Refund: \nOrders are processed immediately after being submitted for the fastest turnaround possible. Therefore, there is no refund/cancellation available.';

  @override
  String get posterDescriptionMarkdown =>
      '## About Our Custom Print Maps\nPrint your personalized world map. Customize it with your very own colors and have it delivered straight to your home.\n \n### Specifications: \n- 16\" x 20\" (40.64cm x 50.8cm)\n- Landscape orientation.\n- Micro ink, droplets for precise prints, 8 bit color, almost photo print quality, \n- 0.22mm thick Satin Paper\n\n### Shipping Details:\n- Shipping from Toronto, Canada to anywhere in the world using Canada Post. \n- Please allow 2 to 4 weeks for delivery to most destinations. \n- All orders are shipped rolled up in a cardboard tube box to the shipping address submitted.\n\n### How Payment is Handled: \n All payment is handled by Google Pay or Stripe.\n\n\n### Cancellation/Refund: \nOrders are processed immediately after being submitted for the fastest turnaround possible. Therefore, there is no refund/cancellation available.';

  @override
  String get posterCustomizeTitle => 'Customize Poster';

  @override
  String get enterShippingAddress => 'Enter Shipping Address';

  @override
  String get price => 'Price';

  @override
  String formattedPlusTax(Object formattedPrice) {
    return '$formattedPrice + tax';
  }

  @override
  String get showSelections => 'Show Selections';

  @override
  String get posterNoRefunds =>
      'No refunds are available after your poster has been printed.';

  @override
  String get posterReviewOrder => 'Review Your Order';

  @override
  String get email => 'Email';

  @override
  String get emailEmptyError => 'Please enter your email';

  @override
  String get fullName => 'Full Name';

  @override
  String get fullNameEmptyError => 'Please enter your full name';

  @override
  String get streetAddressEmptyError => 'Please enter your street address';

  @override
  String get cityEmptyError => 'Please enter your city';

  @override
  String fieldEmptyError(Object fieldName) {
    return 'Please enter your $fieldName';
  }

  @override
  String get country => 'Country';

  @override
  String get countryEmptyError => 'Please enter your country';

  @override
  String get posterReviewOrderTitle => 'Review Your Order';

  @override
  String get buyNow => 'Buy Now';

  @override
  String get secureCheckoutDisclaimer => 'Secure checkout provided by Stripe';

  @override
  String get total => 'Total';

  @override
  String get tax => 'Tax';

  @override
  String get subtotal => 'Subtotal';

  @override
  String get posterProductName => 'Custom Visited Map Poster';

  @override
  String get shipping => 'Shipping';

  @override
  String get posterOrderReceivedTitle => 'Order Received';

  @override
  String get posterOrderReceivedSubtitle => 'We received your order!';

  @override
  String get posterOrderReceivedInstructionsMarkdown =>
      'Check your email for more updates.\nPlease allow up to 4 week for your poster to arrive.\nIf you have any questions, please email us at [<EMAIL>](<EMAIL>)';

  @override
  String get posterOrderReceivedEmailSubject => 'Printed Poster Order Status';

  @override
  String get moreInfo => 'More Info';

  @override
  String get logoutConfirm => 'Would you like to log out of Visited?';

  @override
  String get emailNotAvailable => 'That email has been taken.';

  @override
  String get alphabetical => 'Alphabetical';

  @override
  String get firstTimeLiveTutorial =>
      'Providing your home country and city will personalize your app experience.';

  @override
  String get firstTimeBeenTutorial =>
      'Selecting where you have been allows you to view your map of all the countries you have been to and see personal stats.';

  @override
  String get progressTooltipGoal =>
      'Your travel goals are based on the number of countries you \"Want\" to travel compared to countries where you have \"Been\".';

  @override
  String get progressTooltipRank =>
      'This number shows how you rank compared to travellers around the world.  You can increase your rank by travelling to more countries.';

  @override
  String get progressTooltipPercentageOfWorld =>
      'This graph is based on number of countries you have been to compared to total countries of the world.';

  @override
  String get sortBy => 'Sort By';

  @override
  String get updateWishlist => 'Update Wish List';

  @override
  String get mapInfo =>
      'Click on the country to select been, want or live. You can also click on the icon found in the top left corner for the list view.';

  @override
  String get oneTimePurchase => 'Everything is a one time purchase!';

  @override
  String get contact => 'Contact';

  @override
  String get contactUs => 'Contact Us';

  @override
  String get noCitiesSelected => 'You have not selected any cities, yet...';

  @override
  String get updateTravelGoal => 'Update Travel Goal';

  @override
  String get travelGoalComplete =>
      'Congratulations!\n\nYou have completed your travel goal! \n\nTap the + button to add more countries.';

  @override
  String loginEmailNotFoundError(String email) {
    return 'There is no account associated the email $email.  Would you like to create it now?';
  }

  @override
  String get tryAgain => 'Try Again';

  @override
  String get itineraries => 'Itineraries';

  @override
  String get itinerary => 'Itinerary';

  @override
  String get place => 'Place';

  @override
  String get itinerariesDescription =>
      'These are places you\'ve expressed interest in.\nUse this guide to help plan your next vacation.';

  @override
  String get addMore => 'Add More';

  @override
  String get interests => 'Interests';

  @override
  String get selection => 'Selection';

  @override
  String get goal => 'Goal';

  @override
  String get noItineraries => 'No Itineraries';

  @override
  String get noItinerariesExplanation =>
      'Please add some places, inspirations or experiences to see your itineraries automatically generate.';

  @override
  String get clusterPins => 'Cluster Pins';

  @override
  String get toggleRegions => 'Show Regions';

  @override
  String get mapProjection => 'Map Projection';

  @override
  String get mercator => 'Mercator';

  @override
  String get equirectangular => 'Equirectangular';

  @override
  String get yourTravellerType => 'Your Traveller Type:';

  @override
  String get yourHotelPreferences => 'Your Hotel Preferences:';

  @override
  String get budget => 'Budget';

  @override
  String get midScale => 'Mid Scale';

  @override
  String get luxury => 'Luxury';

  @override
  String get noTravellerType =>
      'Add items to your bucket list to discover what type of traveller you are.';

  @override
  String get unlockLived => 'Unlock Lived';

  @override
  String get unlockLivedDescription =>
      'Select where you have previously lived on the map!';

  @override
  String get futureFeaturesDescription => '...and all future features';

  @override
  String get yourMostFrequentlyVisitedCountry =>
      'Your Most Frequently Visited Country:';

  @override
  String get departureDate => 'Departure Date';

  @override
  String get returnDate => 'Return Date';

  @override
  String get hotels => 'Hotels';

  @override
  String get food => 'Food';

  @override
  String get travelDates => 'Travel Dates';

  @override
  String get posterForMe => 'For Me';

  @override
  String get posterSendGift => 'Send a gift';

  @override
  String get addSelections => 'Add Selections';

  @override
  String get posterType => 'Poster Type';

  @override
  String get help => 'Help';

  @override
  String get tutorialMap => 'Tap on a country to select: been, want and lived.';

  @override
  String get tutorialMapList =>
      'Tap on list icon (top left corner) to select by list.';

  @override
  String get tutorialCountryDetails =>
      'Tap on the country and then \"more\" to select by region.';

  @override
  String get tutorialItems =>
      'Slide the toggle to choose how you want to select items.';

  @override
  String get tutorialInspirations =>
      'Swipe right or left to move to next card.';

  @override
  String get lifetime => 'Lifetime';

  @override
  String get chooseYourPlan => 'Choose Your Plan';

  @override
  String get requestARefund => 'Request a Refund';

  @override
  String get noPurchasesFound => 'No purchases found.';

  @override
  String get noProductsAvailable => 'No Products Available';

  @override
  String get posterLandingAppBar => 'Bring Your Stories Home';

  @override
  String get posterLandingSubHeading => 'Your Travel, Your Story';

  @override
  String get posterLandingSubDescription =>
      'Your travels are more than trips, they\'re a stories, memories, and milestones. Turn those unforgettable moments into a personalized world map that\'s as unique as your adventures.';

  @override
  String get posterLandingPromoBullet1 =>
      '• A Map of Your Achievements: Highlight every destination, from your first big trip to your most daring adventure.';

  @override
  String get posterLandingPromoBullet2 =>
      '• Celebrate Every Journey: Relive your travels daily with a beautifully crafted postere designed to inspire.';

  @override
  String get posterLandingPromoBullet3 =>
      '• A Gift They\'ll Treasure:  Surpise a fellow traveler with a custom map showcasing their journey, perfect for birthdays, milestones or just because.';

  @override
  String get posterLandingHowItWorks => 'How it Works!';

  @override
  String get posterLandingHowItWorksStep1 =>
      '1. Customize Your Design:  Choose colors, styles and mark your travels (or theirs!)';

  @override
  String get posterLandingHowItWorksStep2 =>
      '2. Preview Your Map:  See it come to life before your order.';

  @override
  String get posterLandingHowItWorksStep3_iOS =>
      '3. Safe Payment: Fast And secure with Apple Pay or Stripe.';

  @override
  String get posterLandingHowItWorksStep3_android =>
      '3. Safe Payment: Fast And secure with Google Pay or Stripe.';

  @override
  String get posterLandingHowItWorksStep4 =>
      '4. Ready for Display: We\'ll ship it straight to your door (or theirs).';

  @override
  String get posterLandingCustomerReviewsHeader =>
      'Experiences from Fellow Travelers';

  @override
  String get posterLandingCustomerReview1 =>
      '\"This map is a great way to keep track of everywhere I\'ve traveled and plan our future trips.  The quality is solid and it looks awesome hanging in my office.  I even got one for my brother and he couldn\'t, stop talking about how cool it is!\" - John C.';

  @override
  String get posterLandingCustomerReview2 =>
      '\"I have travelled to over 150 ports while working on cruise. This map is a great addition to my living room as a memory to all the years at sea.\" - Betty K. ';

  @override
  String get posterLandingCustomerReview3 =>
      '\"Great gift for mother\'s day. My mom was super touched!\" Samantha W.';

  @override
  String get posterLandingCustomerReview4 =>
      '\"Printed a map of places I wanted to visit with my gf. It was a great Christmas gift. High quality too.\" Brad J.';

  @override
  String get posterLandingSpecifications => 'Specifications';

  @override
  String get posterLandingSpecification1 =>
      '• Dimensions: 16\" x 20\" (40.64cm x 50.8cm)';

  @override
  String get posterLandingSpecification2 => '• Orientation: Landscape';

  @override
  String get posterLandingSpecification3 =>
      '• Print quality: Micro-ink, droplets for precise prints. 8-bit color, almost photographic print quality.';

  @override
  String get posterLandingSpecification4 => '• Paper: 0.22mm thick satin paper';

  @override
  String get posterLandingShippingHeader => 'Shipping Details';

  @override
  String get posterLandingShipping1 =>
      '• Shipping from Toronto, Canada to anywhere in the world using Canada Post.';

  @override
  String get posterLandingShipping2 =>
      '• Allow 2-4 weeks for delivery to most destinations.';

  @override
  String get posterLandingShipping3 =>
      '• All orders are rolled up in a cardboard tube box to the shipping address you submit.';

  @override
  String get posterLandingCancellationHeader => 'Cancellation/Refund:';

  @override
  String get posterLandingCancellationBody =>
      'Refunds are available before your poster has been sent to the printer, which can take up to 24 hours.  After your order has been processed, no refund/cancellation is available.  You will receive an email when your order has been printed.';

  @override
  String get unsubscribe => 'Unsubscribe';

  @override
  String get unsubscribeConfirmMessage =>
      'Are you sure you want to unsubscribe? You\'ll miss out on exclusive deals and updates!';

  @override
  String get updateLive => 'Update Live';

  @override
  String get updateLiveDescription =>
      'To change the country you live in, you must first select a new country to replace it.';

  @override
  String get underOneThousand => 'Under 1,000';

  @override
  String get oneThousandToTenThousand => '1,000 - 10,000';

  @override
  String get overTenThousand => '10,000+';

  @override
  String get becomeABrandAmbassador => 'Become a Brand Ambassador';

  @override
  String get instagram => 'Instagram';

  @override
  String get tiktok => 'TikTok';

  @override
  String get youtube => 'YouTube';

  @override
  String get website => 'Website';

  @override
  String get handle => 'Handle';

  @override
  String get followers => 'Followers';

  @override
  String get joinBrandAmbassadorProgram => 'Join the Brand Ambassador Program';

  @override
  String get brandAmbassadorProgramDescription =>
      'Love Visited? As a brand ambassador, you’ll represent our travel community, showcase your map and travel lists, and help others discover new destinations and app features. In return, you will get rewards, discounts, swag and more!';

  @override
  String get fillOutTheFormToGetStarted =>
      'Fill out the form below to get started:';

  @override
  String get yourName => 'Your Name';

  @override
  String get yourNameEmptyError => 'Please enter your name';

  @override
  String get fillInWhereApplicable => 'Fill in where applicable:';

  @override
  String get otherNetworks => 'Other Network(s)';

  @override
  String get anythingElse => 'Anything else you\'d like us to know?';

  @override
  String get yourTravelsByContinent => 'Your Travels by Continent';

  @override
  String get territories => 'Territories';

  @override
  String get couponCode => 'Coupon Code';

  @override
  String get apply => 'Apply';

  @override
  String get discount => 'Discount';

  @override
  String get noCouponCode => 'Please enter a coupon code';

  @override
  String get invalidCouponCode => 'Invalid coupon code';

  @override
  String get couponApplied => 'Coupon applied';

  @override
  String discountPercentage(double percentage) {
    return '$percentage% off!';
  }

  @override
  String discountAmount(double amount) {
    return '$amount discounted!';
  }

  @override
  String get thankYou => 'Thank You!';

  @override
  String get formSubmitted =>
      'We have received your request to become a brand ambassador.  We will be in touch soon!';
}

/// The translations for English, as used in Australia (`en_AU`).
class AppLocalizationsEnAu extends AppLocalizationsEn {
  AppLocalizationsEnAu() : super('en_AU');

  @override
  String get appName => 'Visited';

  @override
  String get language => 'Language';

  @override
  String get pickEmailApp => 'Pick your email app';

  @override
  String numberOfCountriesShareMessage(int amount) {
    return 'I have visited $amount countries!  How many have you been to? www.visitedapp.com';
  }

  @override
  String numberOfCitiesShareMessage(int amount) {
    return 'I have visited $amount cities!  How many have you been to? www.visitedapp.com';
  }

  @override
  String numberOfItemsInTodoListShareMessage(int amount, String listName) {
    return 'I have visited $amount $listName!  How many have you been to? www.visitedapp.com';
  }

  @override
  String get clear => 'Clear';

  @override
  String get been => 'Been';

  @override
  String get want => 'Want';

  @override
  String get live => 'Live';

  @override
  String get lived => 'Lived';

  @override
  String get water => 'Water';

  @override
  String get land => 'Land';

  @override
  String get borders => 'Borders';

  @override
  String get labels => 'Labels';

  @override
  String get legend => 'Legend';

  @override
  String get inspiration => 'Inspiration';

  @override
  String get inspirations => 'Inspirations';

  @override
  String get delete => 'Delete';

  @override
  String get unlockVisitedUpsellTitle => 'Want to see more?';

  @override
  String get unlockVisitedUpsellSubtitle =>
      'Unlock all features and enjoy Visited in its full strength';

  @override
  String get checkTheDetails => 'Check the Details';

  @override
  String get moreInspirationsComingSoon =>
      'We are working on getting more images.  Check back soon!';

  @override
  String get unlockPremiumFeatures => 'Unlock premium features';

  @override
  String get purchased => 'Purchased!';

  @override
  String get buy => 'Buy';

  @override
  String get restorePurchases => 'Restore Purchase';

  @override
  String get ok => 'Ok';

  @override
  String get areYouSure => 'Are you sure?';

  @override
  String get deleteInspirationConfirmMessage =>
      'Deleting this card is permanent.  There is no way to recover this image.';

  @override
  String get cancel => 'Cancel';

  @override
  String get map => 'Map';

  @override
  String get progress => 'Progress';

  @override
  String get myTravelGoal => 'My Travel Goal';

  @override
  String goalRemaining(int remaining) {
    return '$remaining more to go!';
  }

  @override
  String get top => 'TOP';

  @override
  String get ofTheWorld => 'of the world!';

  @override
  String get countries => 'countries';

  @override
  String topPlacesVisitedFromCountry(String country) {
    return 'Top Countries Visited from $country:';
  }

  @override
  String get login => 'Log In';

  @override
  String get logout => 'Log Out';

  @override
  String get enterYourEmail => 'Enter your email';

  @override
  String get privacyPolicy => 'Privacy Policy';

  @override
  String get privatePolicyUrl =>
      'https://www.arrivinginhighheels.com/privacy-policy';

  @override
  String get termsOfUse => 'Terms of Use';

  @override
  String get termsOfUserUrl =>
      'https://www.arrivinginhighheels.com/terms-of-use';

  @override
  String get errorTitle => 'Whoops!';

  @override
  String get enterValidEmail => 'Please enter a valid email';

  @override
  String get settings => 'Settings';

  @override
  String get whereDoYouLive => 'Where do you live?';

  @override
  String get whereHaveYouBeen => 'Where have you been?';

  @override
  String get whereDoYouFlyFrom => 'Where do you fly out of?';

  @override
  String get next => 'Next';

  @override
  String get missingAirports =>
      'Don\'t see what you are looking for? Send us an <NAME_EMAIL>';

  @override
  String get missingAirportsEmailTitle => 'Missing Airports!';

  @override
  String get supportEmailAddress => '<EMAIL>';

  @override
  String get welcomeTitle => 'Welcome to Visited';

  @override
  String get welcomeSubtitle => 'The adventure of a lifetime awaits';

  @override
  String get getStarted => 'Get Started';

  @override
  String get privacyAgreement => 'Privacy Agreement';

  @override
  String get privacyAgreementSubtitle =>
      'Please agree to the following items before continuing to use Visited.';

  @override
  String get privacyAgreementTermsMarkdown =>
      'By checking this box, you acknowledge that you have read and agree to be bound by Arriving in High Heels\'  [Privacy Policy](https://www.arrivinginhighheels.com/privacy-policy) and [Terms of Use](https://www.arrivinginhighheels.com/terms-of-use).';

  @override
  String get privacyAgreementOptIn =>
      'I agree to receive electronic messages from Arriving in High Heels containing information and offers with respect to products, applications and services that may be of interest to me, including notification of sales, promotions, offers and newsletters. I may withdraw this consent at any time as described in the Privacy Policy or by clicking on the \"unsubscribe\" link in the electronic messages.';

  @override
  String get submit => 'Submit';

  @override
  String get companyName => 'Arriving In High Heels Corporation';

  @override
  String get companyAddress =>
      '31 Claudview Street\nKing City, Ontario\nCanada\nL7B 0C6';

  @override
  String get privacyAgreementRequired =>
      'You must agree to both our terms and opt in order to continue using Visited.';

  @override
  String get deleteAccount => 'Delete Account';

  @override
  String get removeAdsUpsell =>
      'Do you wish to opt out of ads and unsubscribe from email marketing instead?';

  @override
  String get deleteAccountWarning =>
      'Deleting your account will remove all of your information from our servers.\nThis process cannot be undone.';

  @override
  String get about => 'About';

  @override
  String get popularity => 'Popularity';

  @override
  String get regions => 'Regions';

  @override
  String get population => 'Population';

  @override
  String get size => 'Size';

  @override
  String get coverage => 'Coverage';

  @override
  String get percentOfCountryVisited => '% of country visited';

  @override
  String get visited => 'visited';

  @override
  String get notes => 'Notes';

  @override
  String get kmSquared => 'km²';

  @override
  String get customize => 'Customize';

  @override
  String get onlyCountSovereign => 'U.N. Sovereign';

  @override
  String get countUkSeparately => 'Count U.K. Countries separately';

  @override
  String get showLegend => 'Show Legend';

  @override
  String get showLivedPin => 'Show Lived Pin';

  @override
  String get useMyColours => 'Use My Colors';

  @override
  String get mapColors => 'Map Colors';

  @override
  String get traveller => 'The Traveller';

  @override
  String get nightTraveller => 'The Night Traveller';

  @override
  String get original => 'The Original';

  @override
  String get explorer => 'The Explorer';

  @override
  String get weekender => 'The Weekender';

  @override
  String get naturalist => 'The Naturalist';

  @override
  String get historian => 'The Historian';

  @override
  String get thrillSeeker => 'The Thrill Seeker';

  @override
  String get culturalBuff => 'The Cultural Buff';

  @override
  String get myColors => 'My Colors';

  @override
  String get experiences => 'Experiences';

  @override
  String get done => 'Done';

  @override
  String get experiencesInstructions => 'Tap the + button to get started!';

  @override
  String get continueText => 'Continue';

  @override
  String get experiencesDescription =>
      'What do you like to do when you travel?';

  @override
  String get visitedWebsiteShortLink => 'https://www.visitedapp.com';

  @override
  String get sharingHashtag => '#Visited';

  @override
  String get myTravelMap => 'My Travel Map';

  @override
  String percentOfWorldSeen(int percentage) {
    return 'I\'ve seen $percentage% of the world';
  }

  @override
  String get requiresOnline =>
      'Sorry, Visited requires an active network connection.  Please open your settings app and ensure that either \\r Wi-Fi or Cellular data is enabled and Airplane Mode is disabled';

  @override
  String get list => 'List';

  @override
  String get more => 'More';

  @override
  String get myCountrySelections => 'My Country Selections';

  @override
  String get cities => 'Cities';

  @override
  String get citiesInstructions =>
      'Tap on any country to start selecting cities.';

  @override
  String get missingCitiesEmailTitle => 'Missing Cities!';

  @override
  String get lists => 'Lists';

  @override
  String get disputedTerritories => 'Disputed Territories';

  @override
  String get sponsored => 'Sponsored';

  @override
  String get places => 'Places';

  @override
  String get noListsError =>
      'Opps, no lists available at this time, please try a bit later';

  @override
  String get noInspirationsError =>
      'Opps, no photos are available right now, please try a bit later';

  @override
  String get mostFrequentlyVisitedCountries =>
      'Your most frequently visited countries:';

  @override
  String get update => 'Update';

  @override
  String get signup => 'Sign Up';

  @override
  String get loginWallSubtitle =>
      'Create a free account to experience the full version of Visited';

  @override
  String get loseAllSelectionsWarning =>
      'You will lose all your selections after closing the app.';

  @override
  String get createAccount => 'Create Account';

  @override
  String get continueWithoutAccount => 'Continue without an Account';

  @override
  String get inspirationPromotion =>
      'Get inspired with beautiful travel photography';

  @override
  String get saveStatsPromotion => 'Save Your Travel Stats!';

  @override
  String get selectRegionsPromotion => 'Select States and Provinces';

  @override
  String get experiencesPromotion => 'Track Experiences all around the World';

  @override
  String get missingListItem =>
      'Did we miss something?  Tap here to send us an email to get your favorite place added.';

  @override
  String missingListItemEmailTitle(String list) {
    return 'Missing Item from $list';
  }

  @override
  String listShareMessage(Object amount, Object listName) {
    return 'I have visited $amount $listName';
  }

  @override
  String get orderPoster => 'Poster';

  @override
  String get shareMap => 'Share Map';

  @override
  String get posterLandingPageTitle => 'Get Your Poster';

  @override
  String get posterNotAvailableError =>
      'Poster purchasing is not available right now.  Please try again later.';

  @override
  String posterPricePlusShipping(Object price, Object shipping) {
    return '$price + $shipping shipping';
  }

  @override
  String get posterDescriptionMarkdownApple =>
      '## About Our Custom Print Maps\nPrint your personalized world map. Customize it with your very own colors and have it delivered straight to your home.\n \n### Specifications: \n- 16\" x 20\" (40.64cm x 50.8cm)\n- Landscape orientation.\n- Micro ink, droplets for precise prints, 8 bit color, almost photo print quality, \n- 0.22mm thick Satin Paper\n\n### Shipping Details:\n- Shipping from Toronto, Canada to anywhere in the world using Canada Post. \n- Please allow 2 to 4 weeks for delivery to most destinations. \n- All orders are shipped rolled up in a cardboard tube box to the shipping address submitted.\n\n### How Payment is Handled: \n All payment is handled by Apple Pay or Stripe.\n\n\n### Cancellation/Refund: \nOrders are processed immediately after being submitted for the fastest turnaround possible. Therefore, there is no refund/cancellation available.';

  @override
  String get posterDescriptionMarkdown =>
      '## About Our Custom Print Maps\nPrint your personalized world map. Customize it with your very own colors and have it delivered straight to your home.\n \n### Specifications: \n- 16\" x 20\" (40.64cm x 50.8cm)\n- Landscape orientation.\n- Micro ink, droplets for precise prints, 8 bit color, almost photo print quality, \n- 0.22mm thick Satin Paper\n\n### Shipping Details:\n- Shipping from Toronto, Canada to anywhere in the world using Canada Post. \n- Please allow 2 to 4 weeks for delivery to most destinations. \n- All orders are shipped rolled up in a cardboard tube box to the shipping address submitted.\n\n### How Payment is Handled: \n All payment is handled by Google Pay or Stripe.\n\n\n### Cancellation/Refund: \nOrders are processed immediately after being submitted for the fastest turnaround possible. Therefore, there is no refund/cancellation available.';

  @override
  String get posterCustomizeTitle => 'Customize Poster';

  @override
  String get enterShippingAddress => 'Enter Shipping Address';

  @override
  String get price => 'Price';

  @override
  String formattedPlusTax(Object formattedPrice) {
    return '$formattedPrice + tax';
  }

  @override
  String get showSelections => 'Show Selection';

  @override
  String get posterNoRefunds =>
      'No refunds are available after your poster has been printed.';

  @override
  String get posterReviewOrder => 'Review Your Order';

  @override
  String get email => 'Email';

  @override
  String get emailEmptyError => 'Please enter your email';

  @override
  String get fullName => 'Full Name';

  @override
  String get fullNameEmptyError => 'Please enter your full name';

  @override
  String get streetAddressEmptyError => 'Please enter your street address';

  @override
  String get cityEmptyError => 'Please enter your city';

  @override
  String fieldEmptyError(Object fieldName) {
    return 'Please enter your $fieldName';
  }

  @override
  String get country => 'Country';

  @override
  String get countryEmptyError => 'Please enter your country';

  @override
  String get posterReviewOrderTitle => 'Review Your Order';

  @override
  String get buyNow => 'Buy Now';

  @override
  String get secureCheckoutDisclaimer => 'Secure checkout provided by Stripe';

  @override
  String get total => 'Total';

  @override
  String get tax => 'Tax';

  @override
  String get subtotal => 'Subtotal';

  @override
  String get posterProductName => 'Custom Visited Map Poster';

  @override
  String get shipping => 'Shipping';

  @override
  String get posterOrderReceivedTitle => 'Order Received';

  @override
  String get posterOrderReceivedSubtitle => 'We received your order!';

  @override
  String get posterOrderReceivedInstructionsMarkdown =>
      'Check your email for more updates.\nPlease allow up to 4 week for your poster to arrive.\nIf you have any questions, please email us at [<EMAIL>](<EMAIL>)';

  @override
  String get posterOrderReceivedEmailSubject => 'Printed Poster Order Status';

  @override
  String get moreInfo => 'More Info';

  @override
  String get logoutConfirm => 'Would you like to log out of Visited?';

  @override
  String get emailNotAvailable => 'That email has been taken.';

  @override
  String get alphabetical => 'Alphabetical';

  @override
  String get firstTimeLiveTutorial =>
      'Providing your home country and city will personalize your app experience.';

  @override
  String get firstTimeBeenTutorial =>
      'Selecting where you have been allows you to view your map of all the countries you have been to and see personal stats.';

  @override
  String get progressTooltipGoal =>
      'Your travel goals are based on the number of countries you \"Want\" to travel compared to countries where you have \"Been\".';

  @override
  String get progressTooltipRank =>
      'This number shows how you rank compared to travellers around the world.  You can increase your rank by travelling to more countries.';

  @override
  String get progressTooltipPercentageOfWorld =>
      'This graph is based on number of countries you have been to compared to total countries of the world.';

  @override
  String get sortBy => 'Sort By';

  @override
  String get updateWishlist => 'Update Wish List';

  @override
  String get mapInfo =>
      'Click on the country to select been, want or live. You can also click on the icon found in the top left corner for the list view.';

  @override
  String get oneTimePurchase => 'Everything is a one time purchase!';

  @override
  String get contact => 'Contact';

  @override
  String get contactUs => 'Contact Us';

  @override
  String get noCitiesSelected => 'You have not selected any cities, yet...';

  @override
  String get updateTravelGoal => 'Update Travel Goal';

  @override
  String get travelGoalComplete =>
      'Congratulations!\n\nYou have completed your travel goal! \n\nTap the + button to add more countries.';

  @override
  String loginEmailNotFoundError(String email) {
    return 'There is no account associated the email $email.  Would you like to create it now?';
  }

  @override
  String get tryAgain => 'Try Again';

  @override
  String get itineraries => 'Itineraries';

  @override
  String get itinerary => 'Itinerary';

  @override
  String get place => 'Place';

  @override
  String get itinerariesDescription =>
      'These are places you\'ve expressed interest in.\nUse this guide to help plan your next vacation.';

  @override
  String get addMore => 'Add More';

  @override
  String get interests => 'Interests';

  @override
  String get selection => 'Selection';

  @override
  String get goal => 'Goal';

  @override
  String get noItineraries => 'No Itineraries';

  @override
  String get noItinerariesExplanation =>
      'Please add some places, inspirations or experiences to see your itineraries automatically generate.';

  @override
  String get clusterPins => 'Cluster Pins';

  @override
  String get toggleRegions => 'Show Regions';

  @override
  String get mapProjection => 'Map Projection';

  @override
  String get mercator => 'Mercator';

  @override
  String get equirectangular => 'Equirectangular';

  @override
  String get yourTravellerType => 'Your Traveller Type:';

  @override
  String get yourHotelPreferences => 'Your Hotel Preferences:';

  @override
  String get budget => 'Budget';

  @override
  String get midScale => 'Mid Scale';

  @override
  String get luxury => 'Luxury';

  @override
  String get noTravellerType =>
      'Add items to your bucket list to discover what type of traveller you are.';

  @override
  String get unlockLived => 'Unlock Lived';

  @override
  String get unlockLivedDescription =>
      'Select where you have previously lived on the map!';

  @override
  String get futureFeaturesDescription => '...and all future features';

  @override
  String get yourMostFrequentlyVisitedCountry =>
      'Your Most Frequently Visited Country:';

  @override
  String get departureDate => 'Departure Date';

  @override
  String get returnDate => 'Return Date';

  @override
  String get hotels => 'Hotels';

  @override
  String get food => 'Food';

  @override
  String get travelDates => 'Travel Dates';

  @override
  String get posterForMe => 'For Me';

  @override
  String get posterSendGift => 'Send a Gift';

  @override
  String get addSelections => 'Add Selections';

  @override
  String get posterType => 'Poster Type';

  @override
  String get help => 'Help';

  @override
  String get tutorialMap => 'Tap on a country to select: been, want and lived.';

  @override
  String get tutorialMapList =>
      'Tap on list icon (top left corner) to select by list.';

  @override
  String get tutorialCountryDetails =>
      'Tap on the country and then \"more\" to select by region.';

  @override
  String get tutorialItems =>
      'Slide the toggle to choose how you want to select items.';

  @override
  String get tutorialInspirations =>
      'Swipe right or left to move to next card.';

  @override
  String get lifetime => 'Lifetime';

  @override
  String get chooseYourPlan => 'Choose Your Plan';

  @override
  String get requestARefund => 'Request a Refund';

  @override
  String get noPurchasesFound => 'No purchases found.';

  @override
  String get noProductsAvailable => 'No Products Available';

  @override
  String get posterLandingAppBar => 'Bring Your Stories Home';

  @override
  String get posterLandingSubHeading => 'Your Travel, Your Story';

  @override
  String get posterLandingSubDescription =>
      'Your travels are more than trips, they\'re a stories, memories, and milestones. Turn those unforgettable moments into a personalized world map that\'s as unique as your adventures.';

  @override
  String get posterLandingPromoBullet1 =>
      '• A Map of Your Achievements: Highlight every destination, from your first big trip to your most daring adventure.';

  @override
  String get posterLandingPromoBullet2 =>
      '• Celebrate Every Journey: Relive your travels daily with a beautifully crafted postere designed to inspire.';

  @override
  String get posterLandingPromoBullet3 =>
      '• A Gift They\'ll Treasure:  Surpise a fellow traveler with a custom map showcasing their journey, perfect for birthdays, milestones or just because.';

  @override
  String get posterLandingHowItWorks => 'How it Works!';

  @override
  String get posterLandingHowItWorksStep1 =>
      '1. Customize Your Design:  Choose colours, styles and mark your travels (or theirs!)';

  @override
  String get posterLandingHowItWorksStep2 =>
      '2. Preview Your Map:  See it come to life before your order.';

  @override
  String get posterLandingHowItWorksStep3_iOS =>
      '3. Safe Payment: Fast And secure with Apple Pay or Stripe.';

  @override
  String get posterLandingHowItWorksStep3_android =>
      '3. Safe Payment: Fast And secure with Google Pay or Stripe.';

  @override
  String get posterLandingHowItWorksStep4 =>
      '4. Ready for Display: We\'ll ship it straight to your door (or theirs).';

  @override
  String get posterLandingCustomerReviewsHeader =>
      'Experiences from Fellow Travelers';

  @override
  String get posterLandingCustomerReview1 =>
      '\"This map is a great way to keep track of everywhere I\'ve traveled and plan our future trips.  The quality is solid and it looks awesome hanging in my office.  I even got one for my brother and he couldn\'t, stop talking about how cool it is!\" - John C.';

  @override
  String get posterLandingCustomerReview2 =>
      '\"I have travelled to over 150 ports while working on cruise. This map is a great addition to my living room as a memory to all the years at sea.\" - Betty K. ';

  @override
  String get posterLandingCustomerReview3 =>
      '\"Great gift for mother\'s day. My mom was super touched!\" Samantha W.';

  @override
  String get posterLandingCustomerReview4 =>
      '\"Printed a map of places I wanted to visit with my gf. It was a great Christmas gift. High quality too.\" Brad J.';

  @override
  String get posterLandingSpecifications => 'Specifications';

  @override
  String get posterLandingSpecification1 =>
      '• Dimensions: 16\" x 20\" (40.64cm x 50.8cm)';

  @override
  String get posterLandingSpecification2 => '• Orientation: Landscape';

  @override
  String get posterLandingSpecification3 =>
      '• Print quality: Micro-ink, droplets for precise prints. 8-bit colour, almost photographic print quality.';

  @override
  String get posterLandingSpecification4 => '• Paper: 0.22mm thick satin paper';

  @override
  String get posterLandingShippingHeader => 'Shipping Details';

  @override
  String get posterLandingShipping1 =>
      '• Shipping from Toronto, Canada to anywhere in the world using Canada Post.';

  @override
  String get posterLandingShipping2 =>
      '• Allow 2-4 weeks for delivery to most destinations.';

  @override
  String get posterLandingShipping3 =>
      '• All orders are rolled up in a cardboard tube box to the shipping address you submit.';

  @override
  String get posterLandingCancellationHeader => 'Cancellation/Refund:';

  @override
  String get posterLandingCancellationBody =>
      'Refunds are available before your poster has been sent to the printer, which can take up to 24 hours.  After your order has been processed, no refund/cancellation is available.  You will receive an email when your order has been printed.';

  @override
  String get unsubscribe => 'Unsubscribe';

  @override
  String get unsubscribeConfirmMessage =>
      'Are you sure you want to unsubscribe? You\'ll miss out on exclusive deals and updates!';

  @override
  String get updateLive => 'Update Live';

  @override
  String get updateLiveDescription =>
      'To change the country you live in, you must first select a new country to replace it.';

  @override
  String get underOneThousand => 'Under 1,000';

  @override
  String get oneThousandToTenThousand => '1,000 - 10,000';

  @override
  String get overTenThousand => '10,000+';

  @override
  String get becomeABrandAmbassador => 'Become a Brand Ambassador';

  @override
  String get instagram => 'Instagram';

  @override
  String get tiktok => 'TikTok';

  @override
  String get youtube => 'YouTube';

  @override
  String get website => 'Website';

  @override
  String get handle => 'Handle';

  @override
  String get followers => 'Followers';

  @override
  String get joinBrandAmbassadorProgram => 'Join the Brand Ambassador Program';

  @override
  String get brandAmbassadorProgramDescription =>
      'Love Visited? As a brand ambassador, you’ll represent our travel community, showcase your map and travel lists, and help others discover new destinations and app features. In return, you will get rewards, discounts, swag and more!';

  @override
  String get fillOutTheFormToGetStarted =>
      'Fill out the form below to get started:';

  @override
  String get yourName => 'Your Name';

  @override
  String get yourNameEmptyError => 'Please enter your name';

  @override
  String get fillInWhereApplicable => 'Fill in where applicable:';

  @override
  String get otherNetworks => 'Other Network(s)';

  @override
  String get anythingElse => 'Anything else you\'d like us to know?';

  @override
  String get yourTravelsByContinent => 'Your Travels by Continent';

  @override
  String get territories => 'Territories';

  @override
  String get couponCode => 'Coupon Code';

  @override
  String get apply => 'Apply';

  @override
  String get discount => 'Discount';

  @override
  String get noCouponCode => 'Please enter a coupon code';

  @override
  String get invalidCouponCode => 'Invalid coupon code';

  @override
  String get couponApplied => 'Coupon applied';

  @override
  String discountPercentage(double percentage) {
    return '$percentage% off!';
  }

  @override
  String discountAmount(double amount) {
    return '$amount discounted!';
  }

  @override
  String get thankYou => 'Thank You!';

  @override
  String get formSubmitted =>
      'We have received your request to become a brand ambassador. We will be in touch soon!';
}

/// The translations for English, as used in Canada (`en_CA`).
class AppLocalizationsEnCa extends AppLocalizationsEn {
  AppLocalizationsEnCa() : super('en_CA');

  @override
  String get appName => 'Visited';

  @override
  String get language => 'Language';

  @override
  String get pickEmailApp => 'Pick your email app';

  @override
  String numberOfCountriesShareMessage(int amount) {
    return 'I have visited $amount countries!  How many have you been to? www.visitedapp.com';
  }

  @override
  String numberOfCitiesShareMessage(int amount) {
    return 'I have visited $amount cities!  How many have you been to? www.visitedapp.com';
  }

  @override
  String numberOfItemsInTodoListShareMessage(int amount, String listName) {
    return 'I have visited $amount $listName!  How many have you been to? www.visitedapp.com';
  }

  @override
  String get clear => 'Clear';

  @override
  String get been => 'Been';

  @override
  String get want => 'Want';

  @override
  String get live => 'Live';

  @override
  String get lived => 'Lived';

  @override
  String get water => 'Water';

  @override
  String get land => 'Land';

  @override
  String get borders => 'Borders';

  @override
  String get labels => 'Labels';

  @override
  String get legend => 'Legend';

  @override
  String get inspiration => 'Inspiration';

  @override
  String get inspirations => 'Inspirations';

  @override
  String get delete => 'Delete';

  @override
  String get unlockVisitedUpsellTitle => 'Want to see more?';

  @override
  String get unlockVisitedUpsellSubtitle =>
      'Unlock all features and enjoy Visited in it’s full strength';

  @override
  String get checkTheDetails => 'Check the Details';

  @override
  String get moreInspirationsComingSoon =>
      'We are working on getting more images.  Check back soon!';

  @override
  String get unlockPremiumFeatures => 'Unlock premium features';

  @override
  String get purchased => 'Purchased!';

  @override
  String get buy => 'Buy';

  @override
  String get restorePurchases => 'Restore Purchase';

  @override
  String get ok => 'Ok';

  @override
  String get areYouSure => 'Are you sure?';

  @override
  String get deleteInspirationConfirmMessage =>
      'Deleting this card is permanent.  There is no way to recover this image.';

  @override
  String get cancel => 'Cancel';

  @override
  String get map => 'Map';

  @override
  String get progress => 'Progress';

  @override
  String get myTravelGoal => 'My Travel Goal';

  @override
  String goalRemaining(int remaining) {
    return '$remaining more to go!';
  }

  @override
  String get top => 'TOP';

  @override
  String get ofTheWorld => 'of the world!';

  @override
  String get countries => 'countries';

  @override
  String topPlacesVisitedFromCountry(String country) {
    return 'Top Countries Visited from $country:';
  }

  @override
  String get login => 'Log In';

  @override
  String get logout => 'Log Out';

  @override
  String get enterYourEmail => 'Enter your email';

  @override
  String get privacyPolicy => 'Privacy Policy';

  @override
  String get privatePolicyUrl =>
      'https://www.arrivinginhighheels.com/privacy-policy';

  @override
  String get termsOfUse => 'Terms of Use';

  @override
  String get termsOfUserUrl =>
      'https://www.arrivinginhighheels.com/terms-of-use';

  @override
  String get errorTitle => 'Whoops!';

  @override
  String get enterValidEmail => 'Please enter a valid email';

  @override
  String get settings => 'Settings';

  @override
  String get whereDoYouLive => 'Where do you live?';

  @override
  String get whereHaveYouBeen => 'Where have you been?';

  @override
  String get whereDoYouFlyFrom => 'Where do you fly out of?';

  @override
  String get next => 'Next';

  @override
  String get missingAirports =>
      'Don\'t see what you are looking for? Send us an <NAME_EMAIL>';

  @override
  String get missingAirportsEmailTitle => 'Missing Airports!';

  @override
  String get supportEmailAddress => '<EMAIL>';

  @override
  String get welcomeTitle => 'Welcome to Visited';

  @override
  String get welcomeSubtitle => 'The adventure of a lifetime awaits';

  @override
  String get getStarted => 'Get Started';

  @override
  String get privacyAgreement => 'Privacy Agreement';

  @override
  String get privacyAgreementSubtitle =>
      'Please agree to the following items before continuing to use Visited.';

  @override
  String get privacyAgreementTermsMarkdown =>
      'By checking this box, you acknowledge that you have read and agree to be bound by Arriving in High Heels’  [Privacy Policy](https://www.arrivinginhighheels.com/privacy-policy) and [Terms of Use](https://www.arrivinginhighheels.com/terms-of-use).';

  @override
  String get privacyAgreementOptIn =>
      'I agree to receive electronic messages from Arriving in High Heels containing information and offers with respect to products, applications and services that may be of interest to me, including notification of sales, promotions, offers and newsletters. I may withdraw this consent at any time as described in the Privacy Policy or by clicking on the “unsubscribe” link in the electronic messages.';

  @override
  String get submit => 'Submit';

  @override
  String get companyName => 'Arriving In High Heels Corporation';

  @override
  String get companyAddress =>
      '31 Claudview Street\nKing City, Ontario\nCanada\nL7B 0C6';

  @override
  String get privacyAgreementRequired =>
      'You must agree to both our terms and opt in order to continue using Visited.';

  @override
  String get deleteAccount => 'Delete Account';

  @override
  String get removeAdsUpsell =>
      'Do you wish to opt out of ads and unsubscribe from email marketing instead?';

  @override
  String get deleteAccountWarning =>
      'Deleting your account will remove all of your information from our servers.\nThis process cannot be undone.';

  @override
  String get about => 'About';

  @override
  String get popularity => 'Popularity';

  @override
  String get regions => 'Regions';

  @override
  String get population => 'Population';

  @override
  String get size => 'Size';

  @override
  String get coverage => 'Coverage';

  @override
  String get percentOfCountryVisited => '% of country visited';

  @override
  String get visited => 'visited';

  @override
  String get notes => 'Notes';

  @override
  String get kmSquared => 'km²';

  @override
  String get customize => 'Customize';

  @override
  String get onlyCountSovereign => 'U.N. Sovereign';

  @override
  String get countUkSeparately => 'Count U.K. Countries separately';

  @override
  String get showLegend => 'Show Legend';

  @override
  String get showLivedPin => 'Show Lived Pin';

  @override
  String get useMyColours => 'Use My Colors';

  @override
  String get mapColors => 'Map Colors';

  @override
  String get traveller => 'The Traveller';

  @override
  String get nightTraveller => 'The Night Traveller';

  @override
  String get original => 'The Original';

  @override
  String get explorer => 'The Explorer';

  @override
  String get weekender => 'The Weekender';

  @override
  String get naturalist => 'The Naturalist';

  @override
  String get historian => 'The Historian';

  @override
  String get thrillSeeker => 'The Thrill Seeker';

  @override
  String get culturalBuff => 'The Cultural Buff';

  @override
  String get myColors => 'My Colors';

  @override
  String get experiences => 'Experiences';

  @override
  String get done => 'Done';

  @override
  String get experiencesInstructions => 'Tap the + button to get started!';

  @override
  String get continueText => 'Continue';

  @override
  String get experiencesDescription =>
      'What do you like to do when you travel?';

  @override
  String get visitedWebsiteShortLink => 'https://www.visitedapp.com';

  @override
  String get sharingHashtag => '#Visited';

  @override
  String get myTravelMap => 'My Travel Map';

  @override
  String percentOfWorldSeen(int percentage) {
    return 'I\'ve seen $percentage% of the world';
  }

  @override
  String get requiresOnline =>
      'Sorry, Visited requires an active network connection.  Please open your settings app and ensure that either \\r Wi-Fi or Cellular data is enabled and Airplane Mode is disabled';

  @override
  String get list => 'List';

  @override
  String get more => 'More';

  @override
  String get myCountrySelections => 'My Country Selections';

  @override
  String get cities => 'Cities';

  @override
  String get citiesInstructions =>
      'Tap on any country to start selecting cities.';

  @override
  String get missingCitiesEmailTitle => 'Missing Cities!';

  @override
  String get lists => 'Lists';

  @override
  String get disputedTerritories => 'Disputed Territories';

  @override
  String get sponsored => 'Sponsored';

  @override
  String get places => 'Places';

  @override
  String get noListsError =>
      'Opps, no lists available at this time, please try a bit later';

  @override
  String get noInspirationsError =>
      'Opps, no photos are available right now, please try a bit later';

  @override
  String get mostFrequentlyVisitedCountries =>
      'Your most frequently visited countries:';

  @override
  String get update => 'Update';

  @override
  String get signup => 'Sign Up';

  @override
  String get loginWallSubtitle =>
      'Create a free account to experience the full version of Visited';

  @override
  String get loseAllSelectionsWarning =>
      'You will lose all your selections after closing the app.';

  @override
  String get createAccount => 'Create Account';

  @override
  String get continueWithoutAccount => 'Continue without an Account';

  @override
  String get inspirationPromotion =>
      'Get inspired with beautiful travel photography';

  @override
  String get saveStatsPromotion => 'Save Your Travel Stats!';

  @override
  String get selectRegionsPromotion => 'Select States and Provinces';

  @override
  String get experiencesPromotion => 'Track Experiences all around the World';

  @override
  String get missingListItem =>
      'Did we miss something?  Tap here to send us an email to get your favorite place added.';

  @override
  String missingListItemEmailTitle(String list) {
    return 'Missing Item from $list';
  }

  @override
  String listShareMessage(Object amount, Object listName) {
    return 'I have visited $amount $listName';
  }

  @override
  String get orderPoster => 'Poster';

  @override
  String get shareMap => 'Share Map';

  @override
  String get posterLandingPageTitle => 'Get Your Poster';

  @override
  String get posterNotAvailableError =>
      'Poster purchasing is not available right now.  Please try again later.';

  @override
  String posterPricePlusShipping(Object price, Object shipping) {
    return '$price + $shipping shipping';
  }

  @override
  String get posterDescriptionMarkdownApple =>
      '## About Our Custom Print Maps\nPrint your personalized world map. Customize it with your very own colors and have it delivered straight to your home.\n \n### Specifications: \n- 16\" x 20\" (40.64cm x 50.8cm)\n- Landscape orientation.\n- Micro ink, droplets for precise prints, 8 bit color, almost photo print quality, \n- 0.22mm thick Satin Paper\n\n### Shipping Details:\n- Shipping from Toronto, Canada to anywhere in the world using Canada Post. \n- Please allow 2 to 4 weeks for delivery to most destinations. \n- All orders are shipped rolled up in a cardboard tube box to the shipping address submitted.\n\n### How Payment is Handled: \n All payment is handled by Apple Pay or Stripe.\n\n\n### Cancellation/Refund: \nOrders are processed immediately after being submitted for the fastest turnaround possible. Therefore, there is no refund/cancellation available.';

  @override
  String get posterDescriptionMarkdown =>
      '## About Our Custom Print Maps\nPrint your personalized world map. Customize it with your very own colors and have it delivered straight to your home.\n \n### Specifications: \n- 16\" x 20\" (40.64cm x 50.8cm)\n- Landscape orientation.\n- Micro ink, droplets for precise prints, 8 bit color, almost photo print quality, \n- 0.22mm thick Satin Paper\n\n### Shipping Details:\n- Shipping from Toronto, Canada to anywhere in the world using Canada Post. \n- Please allow 2 to 4 weeks for delivery to most destinations. \n- All orders are shipped rolled up in a cardboard tube box to the shipping address submitted.\n\n### How Payment is Handled: \n All payment is handled by Google Pay or Stripe.\n\n\n### Cancellation/Refund: \nOrders are processed immediately after being submitted for the fastest turnaround possible. Therefore, there is no refund/cancellation available.';

  @override
  String get posterCustomizeTitle => 'Customize Poster';

  @override
  String get enterShippingAddress => 'Enter Shipping Address';

  @override
  String get price => 'Price';

  @override
  String formattedPlusTax(Object formattedPrice) {
    return '$formattedPrice + tax';
  }

  @override
  String get showSelections => 'Show Selection';

  @override
  String get posterNoRefunds =>
      'No refunds are available after your poster has been printed.';

  @override
  String get posterReviewOrder => 'Review Your Order';

  @override
  String get email => 'Email';

  @override
  String get emailEmptyError => 'Please enter your email';

  @override
  String get fullName => 'Full Name';

  @override
  String get fullNameEmptyError => 'Please enter your full name';

  @override
  String get streetAddressEmptyError => 'Please enter your street address';

  @override
  String get cityEmptyError => 'Please enter your city';

  @override
  String fieldEmptyError(Object fieldName) {
    return 'Please enter your $fieldName';
  }

  @override
  String get country => 'Country';

  @override
  String get countryEmptyError => 'Please enter your country';

  @override
  String get posterReviewOrderTitle => 'Review Your Order';

  @override
  String get buyNow => 'Buy Now';

  @override
  String get secureCheckoutDisclaimer => 'Secure checkout provided by Stripe';

  @override
  String get total => 'Total';

  @override
  String get tax => 'Tax';

  @override
  String get subtotal => 'Subtotal';

  @override
  String get posterProductName => 'Custom Visited Map Poster';

  @override
  String get shipping => 'Shipping';

  @override
  String get posterOrderReceivedTitle => 'Order Received';

  @override
  String get posterOrderReceivedSubtitle => 'We received your order!';

  @override
  String get posterOrderReceivedInstructionsMarkdown =>
      'Check your email for more updates.\nPlease allow up to 4 week for your poster to arrive.\nIf you have any questions, please email us at [<EMAIL>](<EMAIL>)';

  @override
  String get posterOrderReceivedEmailSubject => 'Printed Poster Order Status';

  @override
  String get moreInfo => 'More Info';

  @override
  String get logoutConfirm => 'Would you like to log out of Visited?';

  @override
  String get emailNotAvailable => 'That email has been taken.';

  @override
  String get alphabetical => 'Alphabetical';

  @override
  String get firstTimeLiveTutorial =>
      'Providing your home country and city will personalize your app experience.';

  @override
  String get firstTimeBeenTutorial =>
      'Selecting where you have been allows you to view your map of all the countries you have been to and see personal stats.';

  @override
  String get progressTooltipGoal =>
      'Your travel goals are based on the number of countries you \"Want\" to travel compared to countries where you have \"Been\".';

  @override
  String get progressTooltipRank =>
      'This number shows how you rank compared to travellers around the world.  You can increase your rank by travelling to more countries.';

  @override
  String get progressTooltipPercentageOfWorld =>
      'This graph is based on number of countries you have been to compared to total countries of the world.';

  @override
  String get sortBy => 'Sort By';

  @override
  String get updateWishlist => 'Update Wish List';

  @override
  String get mapInfo =>
      'Click on the country to select been, want or live. You can also click on the icon found in the top left corner for the list view.';

  @override
  String get oneTimePurchase => 'Everything is a one time purchase!';

  @override
  String get contact => 'Contact';

  @override
  String get contactUs => 'Contact Us';

  @override
  String get noCitiesSelected => 'You have not selected any cities, yet...';

  @override
  String get updateTravelGoal => 'Update Travel Goal';

  @override
  String get travelGoalComplete =>
      'Congratulations!\n\nYou have completed your travel goal! \n\nTap the + button to add more countries.';

  @override
  String loginEmailNotFoundError(String email) {
    return 'There is no account associated the email $email.  Would you like to create it now?';
  }

  @override
  String get tryAgain => 'Try Again';

  @override
  String get itineraries => 'Itineraries';

  @override
  String get itinerary => 'Itinerary';

  @override
  String get place => 'Place';

  @override
  String get itinerariesDescription =>
      'These are places you\'ve expressed interest in.\nUse this guide to help plan your next vacation.';

  @override
  String get addMore => 'Add More';

  @override
  String get interests => 'Interests';

  @override
  String get selection => 'Selection';

  @override
  String get goal => 'Goal';

  @override
  String get noItineraries => 'No Itineraries';

  @override
  String get noItinerariesExplanation =>
      'Please add some places, inspirations or experiences to see your itineraries automatically generate.';

  @override
  String get clusterPins => 'Cluster Pins';

  @override
  String get toggleRegions => 'Show Regions';

  @override
  String get mapProjection => 'Map Projection';

  @override
  String get mercator => 'Mercator';

  @override
  String get equirectangular => 'Equirectangular';

  @override
  String get yourTravellerType => 'Your Traveller Type:';

  @override
  String get yourHotelPreferences => 'Your Hotel Preferences:';

  @override
  String get budget => 'Budget';

  @override
  String get midScale => 'Mid Scale';

  @override
  String get luxury => 'Luxury';

  @override
  String get noTravellerType =>
      'Add items to your bucket list to discover what type of traveller you are.';

  @override
  String get unlockLived => 'Unlock Lived';

  @override
  String get unlockLivedDescription =>
      'Select where you have previously lived on the map!';

  @override
  String get futureFeaturesDescription => '...and all future features';

  @override
  String get yourMostFrequentlyVisitedCountry =>
      'Your Most Frequently Visited Country:';

  @override
  String get departureDate => 'Departure Date';

  @override
  String get returnDate => 'Return Date';

  @override
  String get hotels => 'Hotels';

  @override
  String get food => 'Food';

  @override
  String get travelDates => 'Travel Dates';

  @override
  String get posterForMe => 'For Me';

  @override
  String get posterSendGift => 'Send a Gift';

  @override
  String get addSelections => 'Add Selections';

  @override
  String get posterType => 'Poster Type';

  @override
  String get help => 'Help';

  @override
  String get tutorialMap => 'Tap on a country to select: been, want and lived.';

  @override
  String get tutorialMapList =>
      'Tap on list icon (top left corner) to select by list.';

  @override
  String get tutorialCountryDetails =>
      'Tap on the country and then “more” to select by region.';

  @override
  String get tutorialItems =>
      'Slide the toggle to choose how you want to select items.';

  @override
  String get tutorialInspirations =>
      'Swipe right or left to move to next card.';

  @override
  String get lifetime => 'Lifetime';

  @override
  String get chooseYourPlan => 'Choose Your Plan';

  @override
  String get requestARefund => 'Request a Refund';

  @override
  String get noPurchasesFound => 'No purchases found.';

  @override
  String get noProductsAvailable => 'No Products Available';

  @override
  String get posterLandingAppBar => 'Bring Your Stories Home';

  @override
  String get posterLandingSubHeading => 'Your Travel, Your Story';

  @override
  String get posterLandingSubDescription =>
      'Your travels are more than trips, they\'re a stories, memories, and milestones. Turn those unforgettable moments into a personalized world map that\'s as unique as your adventures.';

  @override
  String get posterLandingPromoBullet1 =>
      '• A Map of Your Achievements: Highlight every destination, from your first big trip to your most daring adventure.';

  @override
  String get posterLandingPromoBullet2 =>
      '• Celebrate Every Journey: Relive your travels daily with a beautifully crafted postere designed to inspire.';

  @override
  String get posterLandingPromoBullet3 =>
      '• A Gift They\'ll Treasure:  Surpise a fellow traveler with a custom map showcasing their journey, perfect for birthdays, milestones or just because.';

  @override
  String get posterLandingHowItWorks => 'How it Works!';

  @override
  String get posterLandingHowItWorksStep1 =>
      '1. Customize Your Design:  Choose colours, styles and mark your travels (or theirs!)';

  @override
  String get posterLandingHowItWorksStep2 =>
      '2. Preview Your Map:  See it come to life before your order.';

  @override
  String get posterLandingHowItWorksStep3_iOS =>
      '3. Safe Payment: Fast And secure with Apple Pay or Stripe.';

  @override
  String get posterLandingHowItWorksStep3_android =>
      '3. Safe Payment: Fast And secure with Google Pay or Stripe.';

  @override
  String get posterLandingHowItWorksStep4 =>
      '4. Ready for Display: We\'ll ship it straight to your door (or theirs).';

  @override
  String get posterLandingCustomerReviewsHeader =>
      'Experiences from Fellow Travelers';

  @override
  String get posterLandingCustomerReview1 =>
      '\"This map is a great way to keep track of everywhere I\'ve traveled and plan our future trips.  The quality is solid and it looks awesome hanging in my office.  I even got one for my brother and he couldn\'t, stop talking about how cool it is!\" - John C.';

  @override
  String get posterLandingCustomerReview2 =>
      '\"I have travelled to over 150 ports while working on cruise. This map is a great addition to my living room as a memory to all the years at sea.\" - Betty K. ';

  @override
  String get posterLandingCustomerReview3 =>
      '\"Great gift for mother\'s day. My mom was super touched!\" Samantha W.';

  @override
  String get posterLandingCustomerReview4 =>
      '\"Printed a map of places I wanted to visit with my gf. It was a great Christmas gift. High quality too.\" Brad J.';

  @override
  String get posterLandingSpecifications => 'Specifications';

  @override
  String get posterLandingSpecification1 =>
      '• Dimensions: 16\" x 20\" (40.64cm x 50.8cm)';

  @override
  String get posterLandingSpecification2 => '• Orientation: Landscape';

  @override
  String get posterLandingSpecification3 =>
      '• Print quality: Micro-ink, droplets for precise prints. 8-bit colour, almost photographic print quality.';

  @override
  String get posterLandingSpecification4 => '• Paper: 0.22mm thick satin paper';

  @override
  String get posterLandingShippingHeader => 'Shipping Details';

  @override
  String get posterLandingShipping1 =>
      '• Shipping from Toronto, Canada to anywhere in the world using Canada Post.';

  @override
  String get posterLandingShipping2 =>
      '• Allow 2-4 weeks for delivery to most destinations.';

  @override
  String get posterLandingShipping3 =>
      '• All orders are rolled up in a cardboard tube box to the shipping address you submit.';

  @override
  String get posterLandingCancellationHeader => 'Cancellation/Refund:';

  @override
  String get posterLandingCancellationBody =>
      'Refunds are available before your poster has been sent to the printer, which can take up to 24 hours.  After your order has been processed, no refund/cancellation is available.  You will receive an email when your order has been printed.';

  @override
  String get unsubscribe => 'Unsubscribe';

  @override
  String get unsubscribeConfirmMessage =>
      'Are you sure you want to unsubscribe? You\'ll miss out on exclusive deals and updates!';

  @override
  String get updateLive => 'Update Live';

  @override
  String get updateLiveDescription =>
      'To change the country you live in, you must first select a new country to replace it.';

  @override
  String get underOneThousand => 'Under 1,000';

  @override
  String get oneThousandToTenThousand => '1,000 - 10,000';

  @override
  String get overTenThousand => '10,000+';

  @override
  String get becomeABrandAmbassador => 'Become a Brand Ambassador';

  @override
  String get instagram => 'Instagram';

  @override
  String get tiktok => 'TikTok';

  @override
  String get youtube => 'YouTube';

  @override
  String get website => 'Website';

  @override
  String get handle => 'Handle';

  @override
  String get followers => 'Followers';

  @override
  String get joinBrandAmbassadorProgram => 'Join the Brand Ambassador Program';

  @override
  String get brandAmbassadorProgramDescription =>
      'Love Visited? As a brand ambassador, you’ll represent our travel community, showcase your map and travel lists, and help others discover new destinations and app features. In return, you will get rewards, discounts, swag and more!';

  @override
  String get fillOutTheFormToGetStarted =>
      'Fill out the form below to get started:';

  @override
  String get yourName => 'Your Name';

  @override
  String get yourNameEmptyError => 'Please enter your name';

  @override
  String get fillInWhereApplicable => 'Fill in where applicable:';

  @override
  String get otherNetworks => 'Other Network(s)';

  @override
  String get anythingElse => 'Anything else you\'d like us to know?';

  @override
  String get yourTravelsByContinent => 'Your Travels by Continent';

  @override
  String get territories => 'Territories';

  @override
  String get couponCode => 'Coupon Code';

  @override
  String get apply => 'Apply';

  @override
  String get discount => 'Discount';

  @override
  String get noCouponCode => 'Please enter a coupon code';

  @override
  String get invalidCouponCode => 'Invalid coupon code';

  @override
  String get couponApplied => 'Coupon applied';

  @override
  String discountPercentage(double percentage) {
    return '$percentage% off!';
  }

  @override
  String discountAmount(double amount) {
    return '$amount discounted!';
  }

  @override
  String get thankYou => 'Thank You!';

  @override
  String get formSubmitted =>
      'We have received your request to become a brand ambassador. We will be in touch soon!';
}

/// The translations for English, as used in the United Kingdom (`en_GB`).
class AppLocalizationsEnGb extends AppLocalizationsEn {
  AppLocalizationsEnGb() : super('en_GB');

  @override
  String get appName => 'Visited';

  @override
  String get language => 'Language';

  @override
  String get pickEmailApp => 'Pick your email app';

  @override
  String numberOfCountriesShareMessage(int amount) {
    return 'I have visited $amount countries!  How many have you been to? www.visitedapp.com';
  }

  @override
  String numberOfCitiesShareMessage(int amount) {
    return 'I have visited $amount cities!  How many have you been to? www.visitedapp.com';
  }

  @override
  String numberOfItemsInTodoListShareMessage(int amount, String listName) {
    return 'I have visited $amount $listName!  How many have you been to? www.visitedapp.com';
  }

  @override
  String get clear => 'Clear';

  @override
  String get been => 'Been';

  @override
  String get want => 'Want';

  @override
  String get live => 'Live';

  @override
  String get lived => 'Lived';

  @override
  String get water => 'Water';

  @override
  String get land => 'Land';

  @override
  String get borders => 'Borders';

  @override
  String get labels => 'Labels';

  @override
  String get legend => 'Legend';

  @override
  String get inspiration => 'Inspiration';

  @override
  String get inspirations => 'Inspirations';

  @override
  String get delete => 'Delete';

  @override
  String get unlockVisitedUpsellTitle => 'Want to see more?';

  @override
  String get unlockVisitedUpsellSubtitle =>
      'Unlock all features and enjoy Visited in its full strength';

  @override
  String get checkTheDetails => 'Check the Details';

  @override
  String get moreInspirationsComingSoon =>
      'We are working on getting more images.  Check back soon!';

  @override
  String get unlockPremiumFeatures => 'Unlock premium features';

  @override
  String get purchased => 'Purchased!';

  @override
  String get buy => 'Buy';

  @override
  String get restorePurchases => 'Restore Purchase';

  @override
  String get ok => 'Ok';

  @override
  String get areYouSure => 'Are you sure?';

  @override
  String get deleteInspirationConfirmMessage =>
      'Deleting this card is permanent.  There is no way to recover this image.';

  @override
  String get cancel => 'Cancel';

  @override
  String get map => 'Map';

  @override
  String get progress => 'Progress';

  @override
  String get myTravelGoal => 'My Travel Goal';

  @override
  String goalRemaining(int remaining) {
    return '$remaining more to go!';
  }

  @override
  String get top => 'TOP';

  @override
  String get ofTheWorld => 'of the world!';

  @override
  String get countries => 'countries';

  @override
  String topPlacesVisitedFromCountry(String country) {
    return 'Top Countries Visited from $country:';
  }

  @override
  String get login => 'Log In';

  @override
  String get logout => 'Log Out';

  @override
  String get enterYourEmail => 'Enter your email';

  @override
  String get privacyPolicy => 'Privacy Policy';

  @override
  String get privatePolicyUrl =>
      'https://www.arrivinginhighheels.com/privacy-policy';

  @override
  String get termsOfUse => 'Terms of Use';

  @override
  String get termsOfUserUrl =>
      'https://www.arrivinginhighheels.com/terms-of-use';

  @override
  String get errorTitle => 'Whoops!';

  @override
  String get enterValidEmail => 'Please enter a valid email';

  @override
  String get settings => 'Settings';

  @override
  String get whereDoYouLive => 'Where do you live?';

  @override
  String get whereHaveYouBeen => 'Where have you been?';

  @override
  String get whereDoYouFlyFrom => 'Where do you fly out of?';

  @override
  String get next => 'Next';

  @override
  String get missingAirports =>
      'Don\'t see what you are looking for? Send us an <NAME_EMAIL>';

  @override
  String get missingAirportsEmailTitle => 'Missing Airports!';

  @override
  String get supportEmailAddress => '<EMAIL>';

  @override
  String get welcomeTitle => 'Welcome to Visited';

  @override
  String get welcomeSubtitle => 'The adventure of a lifetime awaits';

  @override
  String get getStarted => 'Get Started';

  @override
  String get privacyAgreement => 'Privacy Agreement';

  @override
  String get privacyAgreementSubtitle =>
      'Please agree to the following items before continuing to use Visited.';

  @override
  String get privacyAgreementTermsMarkdown =>
      'By checking this box, you acknowledge that you have read and agree to be bound by Arriving in High Heels\'  [Privacy Policy](https://www.arrivinginhighheels.com/privacy-policy) and [Terms of Use](https://www.arrivinginhighheels.com/terms-of-use).';

  @override
  String get privacyAgreementOptIn =>
      'I agree to receive electronic messages from Arriving in High Heels containing information and offers with respect to products, applications and services that may be of interest to me, including notification of sales, promotions, offers and newsletters. I may withdraw this consent at any time as described in the Privacy Policy or by clicking on the \"unsubscribe\" link in the electronic messages.';

  @override
  String get submit => 'Submit';

  @override
  String get companyName => 'Arriving In High Heels Corporation';

  @override
  String get companyAddress =>
      '31 Claudview Street\nKing City, Ontario\nCanada\nL7B 0C6';

  @override
  String get privacyAgreementRequired =>
      'You must agree to both our terms and opt in order to continue using Visited.';

  @override
  String get deleteAccount => 'Delete Account';

  @override
  String get removeAdsUpsell =>
      'Do you wish to opt out of ads and unsubscribe from email marketing instead?';

  @override
  String get deleteAccountWarning =>
      'Deleting your account will remove all of your information from our servers.\nThis process cannot be undone.';

  @override
  String get about => 'About';

  @override
  String get popularity => 'Popularity';

  @override
  String get regions => 'Regions';

  @override
  String get population => 'Population';

  @override
  String get size => 'Size';

  @override
  String get coverage => 'Coverage';

  @override
  String get percentOfCountryVisited => '% of country visited';

  @override
  String get visited => 'visited';

  @override
  String get notes => 'Notes';

  @override
  String get kmSquared => 'km²';

  @override
  String get customize => 'Customize';

  @override
  String get onlyCountSovereign => 'U.N. Sovereign';

  @override
  String get countUkSeparately => 'Count U.K. Countries separately';

  @override
  String get showLegend => 'Show Legend';

  @override
  String get showLivedPin => 'Show Lived Pin';

  @override
  String get useMyColours => 'Use My Colors';

  @override
  String get mapColors => 'Map Colors';

  @override
  String get traveller => 'The Traveller';

  @override
  String get nightTraveller => 'The Night Traveller';

  @override
  String get original => 'The Original';

  @override
  String get explorer => 'The Explorer';

  @override
  String get weekender => 'The Weekender';

  @override
  String get naturalist => 'The Naturalist';

  @override
  String get historian => 'The Historian';

  @override
  String get thrillSeeker => 'The Thrill Seeker';

  @override
  String get culturalBuff => 'The Cultural Buff';

  @override
  String get myColors => 'My Colors';

  @override
  String get experiences => 'Experiences';

  @override
  String get done => 'Done';

  @override
  String get experiencesInstructions => 'Tap the + button to get started!';

  @override
  String get continueText => 'Continue';

  @override
  String get experiencesDescription =>
      'What do you like to do when you travel?';

  @override
  String get visitedWebsiteShortLink => 'https://www.visitedapp.com';

  @override
  String get sharingHashtag => '#Visited';

  @override
  String get myTravelMap => 'My Travel Map';

  @override
  String percentOfWorldSeen(int percentage) {
    return 'I\'ve seen $percentage% of the world';
  }

  @override
  String get requiresOnline =>
      'Sorry, Visited requires an active network connection.  Please open your settings app and ensure that either \\r Wi-Fi or Cellular data is enabled and Airplane Mode is disabled';

  @override
  String get list => 'List';

  @override
  String get more => 'More';

  @override
  String get myCountrySelections => 'My Country Selections';

  @override
  String get cities => 'Cities';

  @override
  String get citiesInstructions =>
      'Tap on any country to start selecting cities.';

  @override
  String get missingCitiesEmailTitle => 'Missing Cities!';

  @override
  String get lists => 'Lists';

  @override
  String get disputedTerritories => 'Disputed Territories';

  @override
  String get sponsored => 'Sponsored';

  @override
  String get places => 'Places';

  @override
  String get noListsError =>
      'Opps, no lists available at this time, please try a bit later';

  @override
  String get noInspirationsError =>
      'Opps, no photos are available right now, please try a bit later';

  @override
  String get mostFrequentlyVisitedCountries =>
      'Your most frequently visited countries:';

  @override
  String get update => 'Update';

  @override
  String get signup => 'Sign Up';

  @override
  String get loginWallSubtitle =>
      'Create a free account to experience the full version of Visited';

  @override
  String get loseAllSelectionsWarning =>
      'You will lose all your selections after closing the app.';

  @override
  String get createAccount => 'Create Account';

  @override
  String get continueWithoutAccount => 'Continue without an Account';

  @override
  String get inspirationPromotion =>
      'Get inspired with beautiful travel photography';

  @override
  String get saveStatsPromotion => 'Save Your Travel Stats!';

  @override
  String get selectRegionsPromotion => 'Select States and Provinces';

  @override
  String get experiencesPromotion => 'Track Experiences all around the World';

  @override
  String get missingListItem =>
      'Did we miss something?  Tap here to send us an email to get your favorite place added.';

  @override
  String missingListItemEmailTitle(String list) {
    return 'Missing Item from $list';
  }

  @override
  String listShareMessage(Object amount, Object listName) {
    return 'I have visited $amount $listName';
  }

  @override
  String get orderPoster => 'Poster';

  @override
  String get shareMap => 'Share Map';

  @override
  String get posterLandingPageTitle => 'Get Your Poster';

  @override
  String get posterNotAvailableError =>
      'Poster purchasing is not available right now.  Please try again later.';

  @override
  String posterPricePlusShipping(Object price, Object shipping) {
    return '$price + $shipping shipping';
  }

  @override
  String get posterDescriptionMarkdownApple =>
      '## About Our Custom Print Maps\nPrint your personalized world map. Customize it with your very own colors and have it delivered straight to your home.\n \n### Specifications: \n- 16\" x 20\" (40.64cm x 50.8cm)\n- Landscape orientation.\n- Micro ink, droplets for precise prints, 8 bit color, almost photo print quality, \n- 0.22mm thick Satin Paper\n\n### Shipping Details:\n- Shipping from Toronto, Canada to anywhere in the world using Canada Post. \n- Please allow 2 to 4 weeks for delivery to most destinations. \n- All orders are shipped rolled up in a cardboard tube box to the shipping address submitted.\n\n### How Payment is Handled: \n All payment is handled by Apple Pay or Stripe.\n\n\n### Cancellation/Refund: \nOrders are processed immediately after being submitted for the fastest turnaround possible. Therefore, there is no refund/cancellation available.';

  @override
  String get posterDescriptionMarkdown =>
      '## About Our Custom Print Maps\nPrint your personalized world map. Customize it with your very own colors and have it delivered straight to your home.\n \n### Specifications: \n- 16\" x 20\" (40.64cm x 50.8cm)\n- Landscape orientation.\n- Micro ink, droplets for precise prints, 8 bit color, almost photo print quality, \n- 0.22mm thick Satin Paper\n\n### Shipping Details:\n- Shipping from Toronto, Canada to anywhere in the world using Canada Post. \n- Please allow 2 to 4 weeks for delivery to most destinations. \n- All orders are shipped rolled up in a cardboard tube box to the shipping address submitted.\n\n### How Payment is Handled: \n All payment is handled by Google Pay or Stripe.\n\n\n### Cancellation/Refund: \nOrders are processed immediately after being submitted for the fastest turnaround possible. Therefore, there is no refund/cancellation available.';

  @override
  String get posterCustomizeTitle => 'Customize Poster';

  @override
  String get enterShippingAddress => 'Enter Shipping Address';

  @override
  String get price => 'Price';

  @override
  String formattedPlusTax(Object formattedPrice) {
    return '$formattedPrice + tax';
  }

  @override
  String get showSelections => 'Show Selection';

  @override
  String get posterNoRefunds =>
      'No refunds are available after your poster has been printed.';

  @override
  String get posterReviewOrder => 'Review Your Order';

  @override
  String get email => 'Email';

  @override
  String get emailEmptyError => 'Please enter your email';

  @override
  String get fullName => 'Full Name';

  @override
  String get fullNameEmptyError => 'Please enter your full name';

  @override
  String get streetAddressEmptyError => 'Please enter your street address';

  @override
  String get cityEmptyError => 'Please enter your city';

  @override
  String fieldEmptyError(Object fieldName) {
    return 'Please enter your $fieldName';
  }

  @override
  String get country => 'Country';

  @override
  String get countryEmptyError => 'Please enter your country';

  @override
  String get posterReviewOrderTitle => 'Review Your Order';

  @override
  String get buyNow => 'Buy Now';

  @override
  String get secureCheckoutDisclaimer => 'Secure checkout provided by Stripe';

  @override
  String get total => 'Total';

  @override
  String get tax => 'Tax';

  @override
  String get subtotal => 'Subtotal';

  @override
  String get posterProductName => 'Custom Visited Map Poster';

  @override
  String get shipping => 'Shipping';

  @override
  String get posterOrderReceivedTitle => 'Order Received';

  @override
  String get posterOrderReceivedSubtitle => 'We received your order!';

  @override
  String get posterOrderReceivedInstructionsMarkdown =>
      'Check your email for more updates.\nPlease allow up to 4 week for your poster to arrive.\nIf you have any questions, please email us at [<EMAIL>](<EMAIL>)';

  @override
  String get posterOrderReceivedEmailSubject => 'Printed Poster Order Status';

  @override
  String get moreInfo => 'More Info';

  @override
  String get logoutConfirm => 'Would you like to log out of Visited?';

  @override
  String get emailNotAvailable => 'That email has been taken.';

  @override
  String get alphabetical => 'Alphabetical';

  @override
  String get firstTimeLiveTutorial =>
      'Providing your home country and city will personalize your app experience.';

  @override
  String get firstTimeBeenTutorial =>
      'Selecting where you have been allows you to view your map of all the countries you have been to and see personal stats.';

  @override
  String get progressTooltipGoal =>
      'Your travel goals are based on the number of countries you \"Want\" to travel compared to countries where you have \"Been\".';

  @override
  String get progressTooltipRank =>
      'This number shows how you rank compared to travellers around the world.  You can increase your rank by travelling to more countries.';

  @override
  String get progressTooltipPercentageOfWorld =>
      'This graph is based on number of countries you have been to compared to total countries of the world.';

  @override
  String get sortBy => 'Sort By';

  @override
  String get updateWishlist => 'Update Wish List';

  @override
  String get mapInfo =>
      'Click on the country to select been, want or live. You can also click on the icon found in the top left corner for the list view.';

  @override
  String get oneTimePurchase => 'Everything is a one time purchase!';

  @override
  String get contact => 'Contact';

  @override
  String get contactUs => 'Contact Us';

  @override
  String get noCitiesSelected => 'You have not selected any cities, yet...';

  @override
  String get updateTravelGoal => 'Update Travel Goal';

  @override
  String get travelGoalComplete =>
      'Congratulations!\n\nYou have completed your travel goal! \n\nTap the + button to add more countries.';

  @override
  String loginEmailNotFoundError(String email) {
    return 'There is no account associated the email $email.  Would you like to create it now?';
  }

  @override
  String get tryAgain => 'Try Again';

  @override
  String get itineraries => 'Itineraries';

  @override
  String get itinerary => 'Itinerary';

  @override
  String get place => 'Place';

  @override
  String get itinerariesDescription =>
      'These are places you\'ve expressed interest in.\nUse this guide to help plan your next vacation.';

  @override
  String get addMore => 'Add More';

  @override
  String get interests => 'Interests';

  @override
  String get selection => 'Selection';

  @override
  String get goal => 'Goal';

  @override
  String get noItineraries => 'No Itineraries';

  @override
  String get noItinerariesExplanation =>
      'Please add some places, inspirations or experiences to see your itineraries automatically generate.';

  @override
  String get clusterPins => 'Cluster Pins';

  @override
  String get toggleRegions => 'Show Regions';

  @override
  String get mapProjection => 'Map Projection';

  @override
  String get mercator => 'Mercator';

  @override
  String get equirectangular => 'Equirectangular';

  @override
  String get yourTravellerType => 'Your Traveller Type:';

  @override
  String get yourHotelPreferences => 'Your Hotel Preferences:';

  @override
  String get budget => 'Budget';

  @override
  String get midScale => 'Mid Scale';

  @override
  String get luxury => 'Luxury';

  @override
  String get noTravellerType =>
      'Add items to your bucket list to discover what type of traveller you are.';

  @override
  String get unlockLived => 'Unlock Lived';

  @override
  String get unlockLivedDescription =>
      'Select where you have previously lived on the map!';

  @override
  String get futureFeaturesDescription => '...and all future features';

  @override
  String get yourMostFrequentlyVisitedCountry =>
      'Your Most Frequently Visited Country:';

  @override
  String get departureDate => 'Departure Date';

  @override
  String get returnDate => 'Return Date';

  @override
  String get hotels => 'Hotels';

  @override
  String get food => 'Food';

  @override
  String get travelDates => 'Travel Dates';

  @override
  String get posterForMe => 'For Me';

  @override
  String get posterSendGift => 'Send a Gift';

  @override
  String get addSelections => 'Add Selections';

  @override
  String get posterType => 'Poster Type';

  @override
  String get help => 'Help';

  @override
  String get tutorialMap => 'Tap on a country to select: been, want and lived.';

  @override
  String get tutorialMapList =>
      'Tap on list icon (top left corner) to select by list.';

  @override
  String get tutorialCountryDetails =>
      'Tap on the country and then \"more\" to select by region.';

  @override
  String get tutorialItems =>
      'Slide the toggle to choose how you want to select items.';

  @override
  String get tutorialInspirations =>
      'Swipe right or left to move to next card.';

  @override
  String get lifetime => 'Lifetime';

  @override
  String get chooseYourPlan => 'Choose Your Plan';

  @override
  String get requestARefund => 'Request a Refund';

  @override
  String get noPurchasesFound => 'No purchases found.';

  @override
  String get noProductsAvailable => 'No Products Available';

  @override
  String get posterLandingAppBar => 'Bring Your Stories Home';

  @override
  String get posterLandingSubHeading => 'Your Travel, Your Story';

  @override
  String get posterLandingSubDescription =>
      'Your travels are more than trips, they\'re a stories, memories, and milestones. Turn those unforgettable moments into a personalized world map that\'s as unique as your adventures.';

  @override
  String get posterLandingPromoBullet1 =>
      '• A Map of Your Achievements: Highlight every destination, from your first big trip to your most daring adventure.';

  @override
  String get posterLandingPromoBullet2 =>
      '• Celebrate Every Journey: Relive your travels daily with a beautifully crafted postere designed to inspire.';

  @override
  String get posterLandingPromoBullet3 =>
      '• A Gift They\'ll Treasure:  Surpise a fellow traveler with a custom map showcasing their journey, perfect for birthdays, milestones or just because.';

  @override
  String get posterLandingHowItWorks => 'How it Works!';

  @override
  String get posterLandingHowItWorksStep1 =>
      '1. Customize Your Design:  Choose colours, styles and mark your travels (or theirs!)';

  @override
  String get posterLandingHowItWorksStep2 =>
      '2. Preview Your Map:  See it come to life before your order.';

  @override
  String get posterLandingHowItWorksStep3_iOS =>
      '3. Safe Payment: Fast And secure with Apple Pay or Stripe.';

  @override
  String get posterLandingHowItWorksStep3_android =>
      '3. Safe Payment: Fast And secure with Google Pay or Stripe.';

  @override
  String get posterLandingHowItWorksStep4 =>
      '4. Ready for Display: We\'ll ship it straight to your door (or theirs).';

  @override
  String get posterLandingCustomerReviewsHeader =>
      'Experiences from Fellow Travelers';

  @override
  String get posterLandingCustomerReview1 =>
      '\"This map is a great way to keep track of everywhere I\'ve traveled and plan our future trips.  The quality is solid and it looks awesome hanging in my office.  I even got one for my brother and he couldn\'t, stop talking about how cool it is!\" - John C.';

  @override
  String get posterLandingCustomerReview2 =>
      '\"I have travelled to over 150 ports while working on cruise. This map is a great addition to my living room as a memory to all the years at sea.\" - Betty K. ';

  @override
  String get posterLandingCustomerReview3 =>
      '\"Great gift for mother\'s day. My mom was super touched!\" Samantha W.';

  @override
  String get posterLandingCustomerReview4 =>
      '\"Printed a map of places I wanted to visit with my gf. It was a great Christmas gift. High quality too.\" Brad J.';

  @override
  String get posterLandingSpecifications => 'Specifications';

  @override
  String get posterLandingSpecification1 =>
      '• Dimensions: 16\" x 20\" (40.64cm x 50.8cm)';

  @override
  String get posterLandingSpecification2 => '• Orientation: Landscape';

  @override
  String get posterLandingSpecification3 =>
      '• Print quality: Micro-ink, droplets for precise prints. 8-bit colour, almost photographic print quality.';

  @override
  String get posterLandingSpecification4 => '• Paper: 0.22mm thick satin paper';

  @override
  String get posterLandingShippingHeader => 'Shipping Details';

  @override
  String get posterLandingShipping1 =>
      '• Shipping from Toronto, Canada to anywhere in the world using Canada Post.';

  @override
  String get posterLandingShipping2 =>
      '• Allow 2-4 weeks for delivery to most destinations.';

  @override
  String get posterLandingShipping3 =>
      '• All orders are rolled up in a cardboard tube box to the shipping address you submit.';

  @override
  String get posterLandingCancellationHeader => 'Cancellation/Refund:';

  @override
  String get posterLandingCancellationBody =>
      'Refunds are available before your poster has been sent to the printer, which can take up to 24 hours.  After your order has been processed, no refund/cancellation is available.  You will receive an email when your order has been printed.';

  @override
  String get unsubscribe => 'Unsubscribe';

  @override
  String get unsubscribeConfirmMessage =>
      'Are you sure you want to unsubscribe? You\'ll miss out on exclusive deals and updates!';

  @override
  String get updateLive => 'Update Live';

  @override
  String get updateLiveDescription =>
      'To change the country you live in, you must first select a new country to replace it.';

  @override
  String get underOneThousand => 'Under 1,000';

  @override
  String get oneThousandToTenThousand => '1,000 - 10,000';

  @override
  String get overTenThousand => '10,000+';

  @override
  String get becomeABrandAmbassador => 'Become a Brand Ambassador';

  @override
  String get instagram => 'Instagram';

  @override
  String get tiktok => 'TikTok';

  @override
  String get youtube => 'YouTube';

  @override
  String get website => 'Website';

  @override
  String get handle => 'Handle';

  @override
  String get followers => 'Followers';

  @override
  String get joinBrandAmbassadorProgram => 'Join the Brand Ambassador Program';

  @override
  String get brandAmbassadorProgramDescription =>
      'Love Visited? As a brand ambassador, you’ll represent our travel community, showcase your map and travel lists, and help others discover new destinations and app features. In return, you will get rewards, discounts, swag and more!';

  @override
  String get fillOutTheFormToGetStarted =>
      'Fill out the form below to get started:';

  @override
  String get yourName => 'Your Name';

  @override
  String get yourNameEmptyError => 'Please enter your name';

  @override
  String get fillInWhereApplicable => 'Fill in where applicable:';

  @override
  String get otherNetworks => 'Other Network(s)';

  @override
  String get anythingElse => 'Anything else you\'d like us to know?';

  @override
  String get yourTravelsByContinent => 'Your Travels by Continent';

  @override
  String get territories => 'Territories';

  @override
  String get couponCode => 'Coupon Code';

  @override
  String get apply => 'Apply';

  @override
  String get discount => 'Discount';

  @override
  String get noCouponCode => 'Please enter a coupon code';

  @override
  String get invalidCouponCode => 'Invalid coupon code';

  @override
  String get couponApplied => 'Coupon applied';

  @override
  String discountPercentage(double percentage) {
    return '$percentage% off!';
  }

  @override
  String discountAmount(double amount) {
    return '$amount discounted!';
  }

  @override
  String get thankYou => 'Thank You!';

  @override
  String get formSubmitted =>
      'We have received your request to become a brand ambassador. We will be in touch soon!';
}
