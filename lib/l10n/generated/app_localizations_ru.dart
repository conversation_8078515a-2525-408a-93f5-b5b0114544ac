// ignore: unused_import
import 'package:intl/intl.dart' as intl;
import 'app_localizations.dart';

// ignore_for_file: type=lint

/// The translations for Russian (`ru`).
class AppLocalizationsRu extends AppLocalizations {
  AppLocalizationsRu([String locale = 'ru']) : super(locale);

  @override
  String get appName => 'Visited';

  @override
  String get language => 'Язык';

  @override
  String get pickEmailApp => 'Выберите ваше почтовое приложение';

  @override
  String numberOfCountriesShareMessage(int amount) {
    return 'Я посетил(а) $amount стран! Сколько посетили вы? www.visitedapp.com';
  }

  @override
  String numberOfCitiesShareMessage(int amount) {
    return 'Я посетил(а) $amount городов! Сколько посетили вы? www.visitedapp.com';
  }

  @override
  String numberOfItemsInTodoListShareMessage(int amount, String listName) {
    return 'Я посетил(а) $amount $listName! Сколько посетили вы? www.visitedapp.com';
  }

  @override
  String get clear => 'Убрать';

  @override
  String get been => 'Был';

  @override
  String get want => 'Хочу';

  @override
  String get live => 'Живу';

  @override
  String get lived => 'Жил';

  @override
  String get water => 'Вода';

  @override
  String get land => 'Земля';

  @override
  String get borders => 'Границы';

  @override
  String get labels => 'Метки';

  @override
  String get legend => 'Легенда';

  @override
  String get inspiration => 'Вдохновение';

  @override
  String get inspirations => 'Вдохновения';

  @override
  String get delete => 'Удалить';

  @override
  String get unlockVisitedUpsellTitle => 'Хотите увидеть больше?';

  @override
  String get unlockVisitedUpsellSubtitle =>
      'Разблокируйте все функции и наслаждайтесь Visited в полной мере';

  @override
  String get checkTheDetails => 'Проверьте детали';

  @override
  String get moreInspirationsComingSoon =>
      'Мы работаем над тем, чтобы получить больше изображений. Зайдите в ближайшее время!';

  @override
  String get unlockPremiumFeatures => 'Разблокируйте премиум-функции';

  @override
  String get purchased => 'Куплено!';

  @override
  String get buy => 'Купить';

  @override
  String get restorePurchases => 'Восстановить покупку';

  @override
  String get ok => 'ОК';

  @override
  String get areYouSure => 'Вы уверены?';

  @override
  String get deleteInspirationConfirmMessage =>
      'Удаление этой карты является необратимым. Восстановить этот образ невозможно.';

  @override
  String get cancel => 'Отменить';

  @override
  String get map => 'Карта';

  @override
  String get progress => 'Прогресс';

  @override
  String get myTravelGoal => 'Цель моего путешествия';

  @override
  String goalRemaining(int remaining) {
    return 'Осталось еще $remaining!';
  }

  @override
  String get top => 'Топ';

  @override
  String get ofTheWorld => 'мира';

  @override
  String get countries => 'страны';

  @override
  String topPlacesVisitedFromCountry(String country) {
    return 'Самые популярные страны, посещенные из $country:';
  }

  @override
  String get login => 'Авторизуйтесь';

  @override
  String get logout => 'Выйти';

  @override
  String get enterYourEmail => 'Введите адрес электронной почты';

  @override
  String get privacyPolicy => 'Политика конфиденциальности';

  @override
  String get privatePolicyUrl =>
      'https://www.arrivinginhighheels.com/privacy-policy-russian/';

  @override
  String get termsOfUse => 'Условия Пользования';

  @override
  String get termsOfUserUrl =>
      'https://www.arrivinginhighheels.com/terms-of-use-russian/';

  @override
  String get errorTitle => 'Упс';

  @override
  String get enterValidEmail =>
      'Пожалуйста, введите действительный адрес электронной почты';

  @override
  String get settings => 'Настройки';

  @override
  String get whereDoYouLive => 'Где вы живете?';

  @override
  String get whereHaveYouBeen => 'Где вы побывали?';

  @override
  String get whereDoYouFlyFrom => 'Откуда вы вылетаете?';

  @override
  String get next => 'Следующий';

  @override
  String get missingAirports =>
      'Не видите то, что ищете? Отправьте нам письмо по адресу <EMAIL>.';

  @override
  String get missingAirportsEmailTitle => 'Отсутствующие аэропорты!';

  @override
  String get supportEmailAddress => '';

  @override
  String get welcomeTitle => 'Добро пожаловать в Visited';

  @override
  String get welcomeSubtitle => 'Приключение всей жизни ждет';

  @override
  String get getStarted => 'Начать';

  @override
  String get privacyAgreement => 'Соглашение о конфиденциальности';

  @override
  String get privacyAgreementSubtitle =>
      'Пожалуйста, согласитесь со следующими пунктами, прежде чем продолжить использование Visited.';

  @override
  String get privacyAgreementTermsMarkdown =>
      'Отмечая это поле, вы подтверждаете, что прочитали и согласны с [Политикой конфиденциальности] (https://www.arrivinginhighheels.com/privacy-policy) и [Условиями использования] (https://www.arrivinginhighheels.com/terms-of-use) сайта Arriving in High Heels.';

  @override
  String get privacyAgreementOptIn =>
      'Я согласен получать электронные сообщения от Arriving in High Heels, содержащие информацию и предложения в отношении продуктов, приложений и услуг, которые могут представлять для меня интерес, включая уведомления о продажах, акциях, предложениях и информационных бюллетенях. Я могу отозвать данное согласие в любое время, как описано в Политике конфиденциальности или нажав на ссылку \"отписаться\" в электронных сообщениях.';

  @override
  String get submit => 'Отправить';

  @override
  String get companyName => 'Arriving In High Heels Corporation';

  @override
  String get companyAddress =>
      '31 Claudview Street\nKing City, Ontario\nCanada\nL7B 0C6';

  @override
  String get privacyAgreementRequired =>
      'Вы должны согласиться с нашими условиями и условиями opt, чтобы продолжать пользоваться Visited.';

  @override
  String get deleteAccount => 'Удалить аккаунт';

  @override
  String get removeAdsUpsell =>
      'Хотите ли вы отказаться от рекламы и отписаться от маркетинга по электронной почте?';

  @override
  String get deleteAccountWarning =>
      'Удаление вашего аккаунта приведет к удалению всей вашей информации с наших серверов. Этот процесс нельзя отменить.';

  @override
  String get about => 'О';

  @override
  String get popularity => 'Популярность';

  @override
  String get regions => 'Регионы';

  @override
  String get population => 'Население';

  @override
  String get size => 'Размер';

  @override
  String get coverage => 'Покрытие';

  @override
  String get percentOfCountryVisited => '% посещенных стран';

  @override
  String get visited => 'посетил';

  @override
  String get notes => 'Заметки';

  @override
  String get kmSquared => 'км²';

  @override
  String get customize => 'Персонализировать';

  @override
  String get onlyCountSovereign => 'Признано ООН';

  @override
  String get countUkSeparately => 'Считайте страны Великобритании отдельно';

  @override
  String get showLegend => 'Показать легенду';

  @override
  String get showLivedPin => 'Показать активный пин';

  @override
  String get useMyColours => 'Использовать мои цвета';

  @override
  String get mapColors => 'Цвета карты';

  @override
  String get traveller => 'Путешественник';

  @override
  String get nightTraveller => 'Ночной путешественник';

  @override
  String get original => 'Оригинал';

  @override
  String get explorer => 'Исследователь';

  @override
  String get weekender => 'Уикендер';

  @override
  String get naturalist => 'Натуралист';

  @override
  String get historian => 'Историк';

  @override
  String get thrillSeeker => 'Искатель острых ощущений';

  @override
  String get culturalBuff => 'Культурный буфф';

  @override
  String get myColors => 'Мои цвета';

  @override
  String get experiences => 'Опыт';

  @override
  String get done => 'Сделано';

  @override
  String get experiencesInstructions => 'Нажмите кнопку +, чтобы начать!';

  @override
  String get continueText => 'Продолжать';

  @override
  String get experiencesDescription =>
      'Чем вы любите заниматься во время путешествий?';

  @override
  String get visitedWebsiteShortLink =>
      'https://visitedapp.com/visited-%d0%bf%d1%80%d0%b8%d0%bb%d0%be%d0%b6%d0%b5%d0%bd%d0%b8%d0%b5-%d0%b4%d0%bb%d1%8f-%d0%bf%d1%83%d1%82%d0%b5%d1%88%d0%b5%d1%81%d1%82%d0%b2%d0%b8%d0%b9/';

  @override
  String get sharingHashtag => '#Visited';

  @override
  String get myTravelMap => 'Моя карта путешествий';

  @override
  String percentOfWorldSeen(int percentage) {
    return 'Я видел $percentage% мира';
  }

  @override
  String get requiresOnline =>
      'К сожалению, для посещения требуется активное подключение к сети. Пожалуйста, откройте приложение настроек и убедитесь, что либо \\r Wi-Fi, либо сотовые данные включены, а режим самолета отключен.';

  @override
  String get list => 'Список';

  @override
  String get more => 'Более';

  @override
  String get myCountrySelections => 'Мой выбор стран';

  @override
  String get cities => 'Города';

  @override
  String get citiesInstructions =>
      'Нажмите на любую страну, чтобы начать выбирать города.';

  @override
  String get missingCitiesEmailTitle => 'Отсутствующие города!';

  @override
  String get lists => 'Списки';

  @override
  String get disputedTerritories => 'Спорные территории';

  @override
  String get sponsored => 'Спонсируемый';

  @override
  String get places => 'Места';

  @override
  String get noListsError =>
      'Опс, в данный момент списки недоступны, пожалуйста, попробуйте немного позже';

  @override
  String get noInspirationsError =>
      'Оппс, сейчас нет фотографий, пожалуйста, попробуйте немного позже';

  @override
  String get mostFrequentlyVisitedCountries =>
      'Ваши наиболее часто посещаемые страны:';

  @override
  String get update => 'Обновлять';

  @override
  String get signup => 'Зарегистрироваться';

  @override
  String get loginWallSubtitle =>
      'Создайте бесплатную учетную запись, чтобы испытать полную версию Visited';

  @override
  String get loseAllSelectionsWarning =>
      'Вы потеряете все свои выборы после закрытия приложения.';

  @override
  String get createAccount => 'Создать аккаунт';

  @override
  String get continueWithoutAccount => 'Продолжить без учетной записи';

  @override
  String get inspirationPromotion =>
      'Вдохновитесь красивой фотографией путешествий';

  @override
  String get saveStatsPromotion => 'Сохраните статистику путешествий!';

  @override
  String get selectRegionsPromotion => 'Выберите штаты и провинции';

  @override
  String get experiencesPromotion => 'Отслеживайте впечатления по всему миру';

  @override
  String get missingListItem =>
      'Мы что -то пропустили? Нажмите здесь, чтобы прислать нам электронное письмо, чтобы добавить ваше любимое место.';

  @override
  String missingListItemEmailTitle(String list) {
    return 'Отсутствующий элемент из $list';
  }

  @override
  String listShareMessage(Object amount, Object listName) {
    return 'Я посетил $amount $listName';
  }

  @override
  String get orderPoster => 'Плакат';

  @override
  String get shareMap => 'Поделиться картой';

  @override
  String get posterLandingPageTitle => 'Получите свой постер';

  @override
  String get posterNotAvailableError =>
      'Закупка плаката сейчас недоступна. Пожалуйста, попробуйте позже.';

  @override
  String posterPricePlusShipping(Object price, Object shipping) {
    return '$price + $shipping Доставка';
  }

  @override
  String get posterDescriptionMarkdownApple =>
      '## О наших пользовательских картах печати\nРаспечатайте свою персонализированную карту мира. Настройте его с помощью собственных цветов и доставят его прямо к вашему дому.\n \n### Характеристики:\n- 16\" x 20\" (40.64cm x 50.8cm)\n- Альбомная ориентация.\n- Micro Ink, капли для точных отпечаток, 8 -битный цвет, почти качество фото печати,\n- Атласная бумага толщиной 0,22 мм\n\n### Отгрузочные реквизиты:\nДоставка из Торонто, Канада, в любой точке мира, используя Канаду Пост. Пожалуйста, позвольте от 2 до 4 недель для доставки в большинство мест назначения. Все заказы отправляются в картонную трубную коробку на отправленный адрес доставки. Весь платеж обрабатывается Apple Pay, или Stripe.\n\n\n### Отмена/возврат:\nЗаказы обрабатываются сразу же после того, как они представлены для самого быстрого поворота. Следовательно, нет возврата/отмены.';

  @override
  String get posterDescriptionMarkdown =>
      '## О наших пользовательских картах печати\nРаспечатайте свою персонализированную карту мира. Настройте его с помощью собственных цветов и доставят его прямо к вашему дому.\n \n### Характеристики:\n- 16\" x 20\" (40.64cm x 50.8cm)\n- Альбомная ориентация.\n- Micro Ink, капли для точных отпечаток, 8 -битный цвет, почти качество фото печати,\n- Атласная бумага толщиной 0,22 мм\n\n### Отгрузочные реквизиты:\nДоставка из Торонто, Канада, в любой точке мира, используя Канаду Пост. Пожалуйста, позвольте от 2 до 4 недель для доставки в большинство мест назначения. Все заказы отправляются в картонную трубную коробку на отправленный адрес доставки. Весь платеж обрабатывается Google Play или Stripe.\n\n\n### Отмена/возврат:\nЗаказы обрабатываются сразу же после того, как они представлены для самого быстрого поворота. Следовательно, нет возврата/отмены.';

  @override
  String get posterCustomizeTitle => 'Настроить плакат';

  @override
  String get enterShippingAddress => 'Введите адрес доставки';

  @override
  String get price => 'Цена';

  @override
  String formattedPlusTax(Object formattedPrice) {
    return '$formattedPrice + налог';
  }

  @override
  String get showSelections => 'Показать выбор';

  @override
  String get posterNoRefunds =>
      'Возврат средств не доступен после того, как ваш плакат был напечатан.';

  @override
  String get posterReviewOrder => 'Просмотрите заказ';

  @override
  String get email => 'Эл. адрес';

  @override
  String get emailEmptyError => 'Пожалуйста, введите свою электронную почту';

  @override
  String get fullName => 'ФИО';

  @override
  String get fullNameEmptyError => 'пожалуйста введите свое полное имя';

  @override
  String get streetAddressEmptyError =>
      'Пожалуйста, введите свой уличный адрес';

  @override
  String get cityEmptyError => 'Пожалуйста, введите свой город';

  @override
  String fieldEmptyError(Object fieldName) {
    return 'Пожалуйста, введите $fieldName';
  }

  @override
  String get country => 'Страна';

  @override
  String get countryEmptyError => 'Пожалуйста, введите свою страну';

  @override
  String get posterReviewOrderTitle => 'Просмотрите заказ';

  @override
  String get buyNow => 'купить сейчас';

  @override
  String get secureCheckoutDisclaimer =>
      'Безопасная проверка, предоставленная Stripe';

  @override
  String get total => 'Общий';

  @override
  String get tax => 'Налога';

  @override
  String get subtotal => 'Промежуточный итог';

  @override
  String get posterProductName => 'Посеченный плакат на заказ';

  @override
  String get shipping => 'Перевозки';

  @override
  String get posterOrderReceivedTitle => 'Заявка принята';

  @override
  String get posterOrderReceivedSubtitle => 'Мы получили ваш заказ!';

  @override
  String get posterOrderReceivedInstructionsMarkdown =>
      'Проверьте свое электронное письмо для получения дополнительных обновлений. \nПожалуйста, позвольте добраться до 4 недели для вашего плаката. \nЕсли у вас есть какие -либо вопросы, пожалуйста, напишите нам по адресу [<EMAIL>] (<EMAIL>)';

  @override
  String get posterOrderReceivedEmailSubject =>
      'Статус заказа на печатный плакат';

  @override
  String get moreInfo => 'Дополнительная информация';

  @override
  String get logoutConfirm => 'Хотели бы вы выйти из приложения?';

  @override
  String get emailNotAvailable => 'Это электронное письмо было взято.';

  @override
  String get alphabetical => 'По алфавиту';

  @override
  String get firstTimeLiveTutorial =>
      'Указание страны и города, где вы живете, сделает ваше приложение более персонализированным.';

  @override
  String get firstTimeBeenTutorial =>
      'Выбрав пункт \"Где вы были\", вы сможете просмотреть карту всех стран, в которых вы побывали, и увидеть личную статистику.';

  @override
  String get progressTooltipGoal =>
      'Ваши цели путешествия основаны на количестве стран, которые вы \"хотите\" посетить, по сравнению со странами, где вы \"Были\".';

  @override
  String get progressTooltipRank =>
      'Это число показывает, какое место вы занимаете по сравнению с путешественниками по всему миру. Вы можете повысить свой рейтинг, посетив больше стран.';

  @override
  String get progressTooltipPercentageOfWorld =>
      'Этот график основан на количестве стран, в которых вы побывали, по сравнению с общим количеством стран мира.';

  @override
  String get sortBy => 'Сортировать по';

  @override
  String get updateWishlist => 'Обновить список желаний';

  @override
  String get mapInfo =>
      'Нажмите на страну, чтобы выбрать страну, в которой вы были, хотите побывать или живете. Вы также можете нажать на значок в левом верхнем углу для просмотра списка.';

  @override
  String get oneTimePurchase => 'Все - одноразовая покупка!';

  @override
  String get contact => 'Контакт';

  @override
  String get contactUs => 'Свяжитесь с нами';

  @override
  String get noCitiesSelected => 'Вы еще не выбрали ни одного города...';

  @override
  String get updateTravelGoal => 'Обновите цель путешествия';

  @override
  String get travelGoalComplete =>
      'Поздравляю! \n\nyou \n\ntap кнопка +, чтобы добавить больше стран.';

  @override
  String loginEmailNotFoundError(String email) {
    return 'Нет учетной записи, связанной с электронной почтой $email. Хотели бы вы создать это сейчас?';
  }

  @override
  String get tryAgain => 'Попробуйте еще раз';

  @override
  String get itineraries => 'Планы путешествий';

  @override
  String get itinerary => 'План путешествия';

  @override
  String get place => 'Место';

  @override
  String get itinerariesDescription =>
      'Это места, к которым вы проявили интерес.\nИспользуйте этот путеводитель, чтобы помочь спланировать свой следующий отпуск.';

  @override
  String get addMore => 'Добавить еще';

  @override
  String get interests => 'Интересы';

  @override
  String get selection => 'Выбор';

  @override
  String get goal => 'Цель';

  @override
  String get noItineraries => 'Нет Маршрутов';

  @override
  String get noItinerariesExplanation =>
      'Пожалуйста, добавьте некоторые места, вдохновения или впечатления, чтобы увидеть, как автоматически генерируются ваши маршруты.';

  @override
  String get clusterPins => 'Сгруппировать метки на карте';

  @override
  String get toggleRegions => 'Показывать регионы при увеличении';

  @override
  String get mapProjection => 'Проекция карты';

  @override
  String get mercator => 'Меркатор';

  @override
  String get equirectangular => 'Равновеликая';

  @override
  String get yourTravellerType => 'Ваш тип путешественника:';

  @override
  String get yourHotelPreferences => 'Ваши предпочтения по отелям:';

  @override
  String get budget => 'Бюджетный';

  @override
  String get midScale => 'Средний класс';

  @override
  String get luxury => 'Люкс';

  @override
  String get noTravellerType =>
      'Добавьте элементы в свой список желаний, чтобы узнать, какой вы путешественник.';

  @override
  String get unlockLived => 'Разблокировать Прожитое';

  @override
  String get unlockLivedDescription => 'Выберите на карте, где вы раньше жили!';

  @override
  String get futureFeaturesDescription => '...и все будущие функции';

  @override
  String get yourMostFrequentlyVisitedCountry =>
      'Ваша наиболее часто посещаемая страна:';

  @override
  String get departureDate => 'Дата вылета';

  @override
  String get returnDate => 'Дата возвращения';

  @override
  String get hotels => 'Отели';

  @override
  String get food => 'Еда';

  @override
  String get travelDates => 'Даты поездки';

  @override
  String get posterForMe => 'Для меня';

  @override
  String get posterSendGift => 'Отправить подарок';

  @override
  String get addSelections => 'Добавить выбор';

  @override
  String get posterType => 'Тип постера';

  @override
  String get help => 'Помощь';

  @override
  String get tutorialMap =>
      'Нажмите на страну, чтобы выбрать: был, хотел и жил.';

  @override
  String get tutorialMapList =>
      'Нажмите на значок списка (в верхнем левом углу), чтобы выбрать список.';

  @override
  String get tutorialCountryDetails =>
      'Нажмите на страну, а затем «еще», чтобы выбрать регион.';

  @override
  String get tutorialItems =>
      'Сдвиньте переключатель, чтобы выбрать способ выбора элементов.';

  @override
  String get tutorialInspirations =>
      'Проведите пальцем вправо или влево, чтобы перейти к следующей карточке.';

  @override
  String get lifetime => 'Пожизненно';

  @override
  String get chooseYourPlan => 'Выберите свой план';

  @override
  String get requestARefund => 'Запросить возврат';

  @override
  String get noPurchasesFound => 'Покупки не найдены.';

  @override
  String get noProductsAvailable => 'Нет доступных продуктов';

  @override
  String get posterLandingAppBar => 'Перенесите свои истории домой';

  @override
  String get posterLandingSubHeading => 'Ваши путешествия, ваша история';

  @override
  String get posterLandingSubDescription =>
      'Ваши путешествия — это больше, чем просто поездки, это истории, воспоминания и вехи. Превратите эти незабываемые моменты в персонализированную карту мира, которая так же уникальна, как и ваши приключения.';

  @override
  String get posterLandingPromoBullet1 =>
      '• Карта ваших достижений: выделите каждое место назначения, от вашего первого большого путешествия до вашего самого смелого приключения.';

  @override
  String get posterLandingPromoBullet2 =>
      '• Празднуйте каждое путешествие: ежедневно переживайте свои путешествия с помощью прекрасно созданного постера, призванного вдохновлять.';

  @override
  String get posterLandingPromoBullet3 =>
      '• Подарок, который они будут ценить: удивите попутчика индивидуальной картой, демонстрирующей его путешествие, идеально подходящей для дней рождения, важных событий или просто так.';

  @override
  String get posterLandingHowItWorks => 'Как это работает!';

  @override
  String get posterLandingHowItWorksStep1 =>
      '1. Настройте свой дизайн: выберите цвета, стили и отметьте свои путешествия (или их!)';

  @override
  String get posterLandingHowItWorksStep2 =>
      '2. Предварительный просмотр вашей карты: посмотрите, как она оживает, перед тем как сделать заказ.';

  @override
  String get posterLandingHowItWorksStep3_iOS =>
      '3. Безопасная оплата: быстро и безопасно с Apple Pay или Stripe.';

  @override
  String get posterLandingHowItWorksStep3_android =>
      '3. Безопасная оплата: быстро и безопасно с Google Pay или Stripe.';

  @override
  String get posterLandingHowItWorksStep4 =>
      '4. Готово к показу: мы доставим его прямо к вашей двери (или к ним).';

  @override
  String get posterLandingCustomerReviewsHeader => 'Опыт путешественников';

  @override
  String get posterLandingCustomerReview1 =>
      '«Эта карта - отличный способ отслеживать все, где я побывал, и планировать наши будущие поездки.  Качество очень хорошее, и она отлично смотрится в моем офисе.  Я даже подарил ее своему брату, и он не мог перестать говорить о том, какая она классная!» - Джон С.';

  @override
  String get posterLandingCustomerReview2 =>
      '«За время работы в круизе я побывал более чем в 150 портах. Эта карта - прекрасное дополнение к моей гостиной как память обо всех годах, проведенных в море». - Бетти К. ';

  @override
  String get posterLandingCustomerReview3 =>
      '«Отличный подарок на День матери. Моя мама была очень тронута!» Саманта В.';

  @override
  String get posterLandingCustomerReview4 =>
      '«Распечатал карту мест, которые я хотел посетить со своей девушкой. Это был отличный рождественский подарок. Высокое качество». Брэд Дж.';

  @override
  String get posterLandingSpecifications => 'Технические характеристики';

  @override
  String get posterLandingSpecification1 =>
      '- Размеры: 16« x 20» (40,64 см x 50,8 см)';

  @override
  String get posterLandingSpecification2 => '- Ориентация: Пейзаж';

  @override
  String get posterLandingSpecification3 =>
      '- Качество печати: Микрочернила, капельки для точных отпечатков.  8-битный цвет, качество печати почти как у фотографии.';

  @override
  String get posterLandingSpecification4 =>
      '- Бумага: Атласная бумага толщиной 0,22 мм';

  @override
  String get posterLandingShippingHeader => 'Подробности доставки';

  @override
  String get posterLandingShipping1 =>
      '- Доставка из Торонто, Канада, в любую точку мира с помощью Canada Post.';

  @override
  String get posterLandingShipping2 =>
      '- Доставка в большинство пунктов назначения занимает 2-4 недели.';

  @override
  String get posterLandingShipping3 =>
      '- Все заказы доставляются по указанному вами адресу в картонной коробке.';

  @override
  String get posterLandingCancellationHeader => 'Отмена/возврат:';

  @override
  String get posterLandingCancellationBody =>
      'Возврат средств возможен до того, как ваш плакат будет отправлен в типографию, что может занять до 24 часов.  После обработки заказа возврат/отмена заказа невозможны.  Вы получите электронное письмо, когда ваш заказ будет напечатан.';

  @override
  String get unsubscribe => 'Отписаться';

  @override
  String get unsubscribeConfirmMessage =>
      'Вы уверены, что хотите отписаться? Вы упустите эксклюзивные предложения и обновления!';

  @override
  String get updateLive => 'Обновить Место Жительства';

  @override
  String get updateLiveDescription =>
      'Чтобы изменить страну, в которой вы живете, сначала необходимо выбрать новую страну для замены.';

  @override
  String get underOneThousand => 'Менее 1 000';

  @override
  String get oneThousandToTenThousand => '1 000 – 10 000';

  @override
  String get overTenThousand => 'Более 10 000';

  @override
  String get becomeABrandAmbassador => 'Стать послом бренда';

  @override
  String get instagram => 'Instagram';

  @override
  String get tiktok => 'TikTok';

  @override
  String get youtube => 'YouTube';

  @override
  String get website => 'Веб-сайт';

  @override
  String get handle => 'Имя пользователя';

  @override
  String get followers => 'Подписчики';

  @override
  String get joinBrandAmbassadorProgram =>
      'Присоединиться к программе послов бренда';

  @override
  String get brandAmbassadorProgramDescription =>
      'Любите Visited? В качестве посла бренда вы будете представлять наше туристическое сообщество, делиться своей картой и списками поездок, а также помогать другим открывать новые направления и функции приложения. Взамен вы получите награды, скидки, мерч и многое другое!';

  @override
  String get fillOutTheFormToGetStarted =>
      'Заполните форму ниже, чтобы начать:';

  @override
  String get yourName => 'Ваше имя';

  @override
  String get yourNameEmptyError => 'Пожалуйста, введите ваше имя';

  @override
  String get fillInWhereApplicable => 'Заполните, где применимо:';

  @override
  String get otherNetworks => 'Другие сети';

  @override
  String get anythingElse => 'Что-нибудь ещё, что вы хотели бы сообщить?';

  @override
  String get yourTravelsByContinent => 'Ваши путешествия по континентам';

  @override
  String get territories => 'Территории';

  @override
  String get couponCode => 'Промокод';

  @override
  String get apply => 'Применить';

  @override
  String get discount => 'Скидка';

  @override
  String get noCouponCode => 'Пожалуйста, введите промокод';

  @override
  String get invalidCouponCode => 'Недействительный промокод';

  @override
  String get couponApplied => 'Промокод применён';

  @override
  String discountPercentage(double percentage) {
    return 'Скидка $percentage%';
  }

  @override
  String discountAmount(double amount) {
    return 'Скидка $amount!';
  }

  @override
  String get thankYou => 'Спасибо!';

  @override
  String get formSubmitted =>
      'Мы получили вашу заявку на участие в программе амбассадоров. Скоро свяжемся с вами!';
}
