// ignore: unused_import
import 'package:intl/intl.dart' as intl;
import 'app_localizations.dart';

// ignore_for_file: type=lint

/// The translations for Modern Greek (`el`).
class AppLocalizationsEl extends AppLocalizations {
  AppLocalizationsEl([String locale = 'el']) : super(locale);

  @override
  String get appName => 'Visited';

  @override
  String get language => 'Γλώσσα';

  @override
  String get pickEmailApp =>
      'Επιλέξτε την εφαρμογή ηλεκτρονικού ταχυδρομείου σας';

  @override
  String numberOfCountriesShareMessage(int amount) {
    return 'Έχω επισκεφτεί $amount χώρες! Πόσες έχετε επισκεφτεί εσείς; www.visitedapp.com';
  }

  @override
  String numberOfCitiesShareMessage(int amount) {
    return 'Έχω επισκεφτεί $amount πόλεις! Πόσες έχετε επισκεφτεί εσείς; www.visitedapp.com';
  }

  @override
  String numberOfItemsInTodoListShareMessage(int amount, String listName) {
    return 'Έχω επισκεφτεί $amount $listName! Πόσα έχετε επισκεφτεί εσείς; www.visitedapp.com';
  }

  @override
  String get clear => 'Σαφές, Καθαρό';

  @override
  String get been => 'ήταν';

  @override
  String get want => 'Θέλω';

  @override
  String get live => 'Ζωντανά';

  @override
  String get lived => 'Έζησε';

  @override
  String get water => 'Νερό';

  @override
  String get land => 'Γη';

  @override
  String get borders => 'Σύνορα';

  @override
  String get labels => 'Ετικέτες';

  @override
  String get legend => 'Θρύλος';

  @override
  String get inspiration => 'Έμπνευση';

  @override
  String get inspirations => 'Εμπνεύσεις';

  @override
  String get delete => 'Διαγραφή';

  @override
  String get unlockVisitedUpsellTitle => 'Θέλετε να δείτε περισσότερα;';

  @override
  String get unlockVisitedUpsellSubtitle =>
      'Ξεκλειδώστε όλα τα χαρακτηριστικά και απολαύστε το Visited σε όλη του τη δύναμη';

  @override
  String get checkTheDetails => 'Ελέγξτε τις λεπτομέρειες';

  @override
  String get moreInspirationsComingSoon =>
      'Προσπαθούμε να συγκεντρώσουμε περισσότερες εικόνες. Επιστρέψτε σύντομα!';

  @override
  String get unlockPremiumFeatures => 'Ξεκλειδώστε premium χαρακτηριστικά';

  @override
  String get purchased => 'Αγοράστηκε!';

  @override
  String get buy => 'Αγοράστε';

  @override
  String get restorePurchases => 'Επαναφορά αγοράς';

  @override
  String get ok => 'Οκ';

  @override
  String get areYouSure => 'Είστε σίγουροι;';

  @override
  String get deleteInspirationConfirmMessage =>
      'Η διαγραφή αυτής της κάρτας είναι μόνιμη. Δεν υπάρχει τρόπος ανάκτησης αυτής της εικόνας.';

  @override
  String get cancel => 'Ακύρωση';

  @override
  String get map => 'Χάρτης';

  @override
  String get progress => 'Πρόοδος';

  @override
  String get myTravelGoal => 'Ο ταξιδιωτικός μου στόχος';

  @override
  String goalRemaining(int remaining) {
    return '$remaining περισσότερα για να πάμε!';
  }

  @override
  String get top => 'ΤΟΠ';

  @override
  String get ofTheWorld => 'του κόσμου!';

  @override
  String get countries => 'Χώρες';

  @override
  String topPlacesVisitedFromCountry(String country) {
    return 'Κορυφαίες χώρες σε επισκεψιμότητα από $country:';
  }

  @override
  String get login => 'Συνδεθείτε';

  @override
  String get logout => 'Αποσυνδεθείτε';

  @override
  String get enterYourEmail => 'Εισάγετε το email σας';

  @override
  String get privacyPolicy => 'Πολιτική απορρήτου';

  @override
  String get privatePolicyUrl =>
      'https://www.arrivinginhighheels.com/privacy-policy-greek/';

  @override
  String get termsOfUse => 'Όροι χρήσης';

  @override
  String get termsOfUserUrl =>
      'https://www.arrivinginhighheels.com/terms-of-use-greek/';

  @override
  String get errorTitle => 'Ουυπς!';

  @override
  String get enterValidEmail => 'Παρακαλώ εισάγετε ένα έγκυρο email';

  @override
  String get settings => 'Ρυθμίσεις';

  @override
  String get whereDoYouLive => 'Πού ζείτε;';

  @override
  String get whereHaveYouBeen => 'Πού έχετε πάει;';

  @override
  String get whereDoYouFlyFrom => 'Από πού πετάτε;';

  @override
  String get next => 'Επόμενο';

  @override
  String get missingAirports =>
      'Δεν βλέπετε αυτό που ψάχνετε; Στείλτε μας ένα email στο <EMAIL>';

  @override
  String get missingAirportsEmailTitle => 'Αεροδρόμια που λείπουν!';

  @override
  String get supportEmailAddress => '<EMAIL>';

  @override
  String get welcomeTitle => 'Καλώς ήρθατε στο Visited';

  @override
  String get welcomeSubtitle => 'Η περιπέτεια μιας ζωής σας περιμένει';

  @override
  String get getStarted => 'Ξεκινήστε';

  @override
  String get privacyAgreement => 'Συμφωνία προστασίας απορρήτου';

  @override
  String get privacyAgreementSubtitle =>
      'Παρακαλούμε συμφωνήστε με τα ακόλουθα στοιχεία πριν συνεχίσετε να χρησιμοποιείτε το Visited.';

  @override
  String get privacyAgreementTermsMarkdown =>
      'Τσεκάροντας αυτό το πλαίσιο, αναγνωρίζετε ότι έχετε διαβάσει και συμφωνείτε με την [Πολιτική απορρήτου](https://www.arrivinginhighheels.com/privacy-policy) και τους [Όρους χρήσης](https://www.arrivinginhighheels.com/terms-of-use) του Arriving in High Heels.';

  @override
  String get privacyAgreementOptIn =>
      'Συμφωνώ να λαμβάνω ηλεκτρονικά μηνύματα από την Arriving in High Heels που περιέχουν πληροφορίες και προσφορές σχετικά με προϊόντα, εφαρμογές και υπηρεσίες που μπορεί να με ενδιαφέρουν, συμπεριλαμβανομένων ειδοποιήσεων για πωλήσεις, προωθητικές ενέργειες, προσφορές και ενημερωτικά δελτία. Μπορώ να ανακαλέσω αυτή τη συγκατάθεση ανά πάσα στιγμή, όπως περιγράφεται στην Πολιτική Απορρήτου ή κάνοντας κλικ στον σύνδεσμο \"διαγραφή\" στα ηλεκτρονικά μηνύματα.';

  @override
  String get submit => 'Υποβολή';

  @override
  String get companyName => 'Arriving In High Heels Corporation';

  @override
  String get companyAddress =>
      '31 Claudview Street\nKing City, Ontario\nCanada\nL7B 0C6';

  @override
  String get privacyAgreementRequired =>
      'Για να συνεχίσετε να χρησιμοποιείτε το Visited, πρέπει να συμφωνήσετε και με τους δύο όρους μας και να τους αποδεχτείτε.';

  @override
  String get deleteAccount => 'Διαγραφή λογαριασμού';

  @override
  String get removeAdsUpsell =>
      'Θέλετε να εξαιρεθείτε από τις διαφημίσεις και να διαγραφείτε από το μάρκετινγκ ηλεκτρονικού ταχυδρομείου;';

  @override
  String get deleteAccountWarning =>
      'Η διαγραφή του λογαριασμού σας θα αφαιρέσει όλες τις πληροφορίες σας από τους διακομιστές μας.\n \n Αυτή η διαδικασία δεν μπορεί να αναιρεθεί.';

  @override
  String get about => 'Σχετικά με';

  @override
  String get popularity => 'Δημοτικότητα';

  @override
  String get regions => 'Περιοχές';

  @override
  String get population => 'Πληθυσμός';

  @override
  String get size => 'Μέγεθος';

  @override
  String get coverage => 'Κάλυψη';

  @override
  String get percentOfCountryVisited => '% της χώρας επισκεφθήκατε';

  @override
  String get visited => 'επισκεφθήκατε';

  @override
  String get notes => 'Σημειώσεις';

  @override
  String get kmSquared => 'km²';

  @override
  String get customize => 'Προσαρμογή';

  @override
  String get onlyCountSovereign => 'Κυρίαρχο αναγνωρισμένο από τον ΟΗΕ';

  @override
  String get countUkSeparately =>
      'Μετρά τις χώρες του Ηνωμένου Βασιλείου ξεχωριστά';

  @override
  String get showLegend => 'Εμφάνιση Υπόμνημα';

  @override
  String get showLivedPin => 'Εμφάνιση καρφίτσας Lived Pin';

  @override
  String get useMyColours => 'Χρησιμοποιήστε τα χρώματά μου';

  @override
  String get mapColors => 'Χρώματα χάρτη';

  @override
  String get traveller => 'Ο ταξιδιώτης';

  @override
  String get nightTraveller => 'Ο ταξιδιώτης της νύχτας';

  @override
  String get original => 'Το πρωτότυπο';

  @override
  String get explorer => 'Ο εξερευνητής';

  @override
  String get weekender => 'Ο εβδομαδιαίος ταξιδιώτης';

  @override
  String get naturalist => 'Ο φυσιοδίφης';

  @override
  String get historian => 'Ο ιστορικός';

  @override
  String get thrillSeeker => 'Ο αναζητητής συγκινήσεων';

  @override
  String get culturalBuff => 'Ο Πολιτιστικός Μπουφές';

  @override
  String get myColors => 'Τα χρώματά μου';

  @override
  String get experiences => 'Εμπειρίες';

  @override
  String get done => 'Έγινε';

  @override
  String get experiencesInstructions =>
      'Πατήστε το κουμπί + για να ξεκινήσετε!';

  @override
  String get continueText => 'Συνεχίστε';

  @override
  String get experiencesDescription =>
      'Τι σας αρέσει να κάνετε όταν ταξιδεύετε;';

  @override
  String get visitedWebsiteShortLink => 'https://www.visitedapp.com';

  @override
  String get sharingHashtag => '#Visited';

  @override
  String get myTravelMap => 'Ο ταξιδιωτικός μου χάρτης';

  @override
  String percentOfWorldSeen(int percentage) {
    return 'Έχω δει το $percentage% του κόσμου';
  }

  @override
  String get requiresOnline =>
      'Συγγνώμη, το Visited απαιτεί ενεργή σύνδεση δικτύου. Ανοίξτε την εφαρμογή ρυθμίσεων και βεβαιωθείτε ότι είναι ενεργοποιημένη η επιλογή \\r Wi-Fi ή δεδομένα κινητής τηλεφωνίας και ότι η λειτουργία αεροπλάνου είναι απενεργοποιημένη.';

  @override
  String get list => 'Λίστα';

  @override
  String get more => 'Περισσότερα';

  @override
  String get myCountrySelections => 'Οι επιλογές μου για τη χώρα';

  @override
  String get cities => 'Πόλεις';

  @override
  String get citiesInstructions =>
      'Πατήστε σε οποιαδήποτε χώρα για να αρχίσετε να επιλέγετε πόλεις.';

  @override
  String get missingCitiesEmailTitle => 'Αγνοούμενες πόλεις!';

  @override
  String get lists => 'Τόπος αγώνων';

  @override
  String get disputedTerritories => 'Αμφισβητούμενες περιοχές';

  @override
  String get sponsored => 'Χορηγία';

  @override
  String get places => 'Μέρη';

  @override
  String get noListsError =>
      'Oυυπς, δεν υπάρχουν διαθέσιμες λίστες αυτή τη στιγμή, παρακαλώ δοκιμάστε λίγο αργότερα';

  @override
  String get noInspirationsError =>
      'Oυυπς, δεν υπάρχουν διαθέσιμες φωτογραφίες αυτή τη στιγμή, παρακαλώ δοκιμάστε λίγο αργότερα';

  @override
  String get mostFrequentlyVisitedCountries =>
      'Οι χώρες που επισκέπτεστε συχνότερα:';

  @override
  String get update => 'Εκσυγχρονίζω';

  @override
  String get signup => 'Εγγραφείτε';

  @override
  String get loginWallSubtitle =>
      'Δημιουργήστε έναν δωρεάν λογαριασμό για να ζήσετε την πλήρη έκδοση του Visited';

  @override
  String get loseAllSelectionsWarning =>
      'Θα χάσετε όλες τις επιλογές σας μετά το κλείσιμο της εφαρμογής.';

  @override
  String get createAccount => 'Δημιουργία λογαριασμού';

  @override
  String get continueWithoutAccount => 'Συνεχίστε χωρίς λογαριασμό';

  @override
  String get inspirationPromotion =>
      'Εμπνευστείτε με όμορφη ταξιδιωτική φωτογραφία';

  @override
  String get saveStatsPromotion => 'Αποθηκεύστε τα ταξιδιωτικά σας στατιστικά!';

  @override
  String get selectRegionsPromotion => 'Επιλογή πολιτειών και επαρχιών';

  @override
  String get experiencesPromotion =>
      'Παρακολουθήστε εμπειρίες σε όλο τον κόσμο';

  @override
  String get missingListItem =>
      'Χάσαμε κάτι; Πατήστε εδώ για να μας στείλετε ένα email για να προστεθεί το αγαπημένο σας μέρος.';

  @override
  String missingListItemEmailTitle(String list) {
    return 'Λείπει το στοιχείο από $list';
  }

  @override
  String listShareMessage(Object amount, Object listName) {
    return 'Έχω επισκεφθεί $amount $listName';
  }

  @override
  String get orderPoster => 'Αφίσα';

  @override
  String get shareMap => 'Μοιράς';

  @override
  String get posterLandingPageTitle => 'Πάρε την αφίσα σου';

  @override
  String get posterNotAvailableError =>
      'Η αγορά αφίσας δεν είναι διαθέσιμη αυτή τη στιγμή. Παρακαλώ προσπαθήστε ξανά αργότερα.';

  @override
  String posterPricePlusShipping(Object price, Object shipping) {
    return '$price + $shipping μεταφορικα';
  }

  @override
  String get posterDescriptionMarkdownApple =>
      '## Σχετικά με τους προσαρμοσμένους χάρτες εκτύπωσης\nΕκτυπώστε τον εξατομικευμένο παγκόσμιο χάρτη σας. Προσαρμόστε το με τα δικά σας χρώματα και το παραδώσετε κατευθείαν στο σπίτι σας.\n \n### Προδιαγραφές:\n- 16\" x 20\" (40.64cm x 50.8cm)\n- Προσανατολισμός τοπίου.\n- Μικρό μελάνι, σταγονίδια για ακριβείς εκτυπώσεις, χρώμα 8 bit, σχεδόν ποιότητα εκτύπωσης φωτογραφιών,\n- Χαρτί σατέν πάχους 0,22 mm\n\n### Λεπτομέρειες αποστολής:\nΑποστολή από το Τορόντο του Καναδά σε οπουδήποτε στον κόσμο χρησιμοποιώντας το Canada Post. Επιτρέψτε 2 έως 4 εβδομάδες για παράδοση στους περισσότερους προορισμούς. Όλες οι παραγγελίες αποστέλλονται σε ένα κουτί σωλήνα από χαρτόνι στη διεύθυνση αποστολής που υποβλήθηκε. Όλη η πληρωμή χειρίζεται η Apple Pay, η ή η Stripe.\n\n\n### Ακύρωση/επιστροφή χρημάτων:\nΟι παραγγελίες υποβάλλονται σε επεξεργασία αμέσως μετά την υποβολή για την ταχύτερη δυνατή ανάκαμψη. Επομένως, δεν υπάρχει διαθέσιμη επιστροφή/ακύρωση.';

  @override
  String get posterDescriptionMarkdown =>
      '## Σχετικά με τους προσαρμοσμένους χάρτες εκτύπωσης\nΕκτυπώστε τον εξατομικευμένο παγκόσμιο χάρτη σας. Προσαρμόστε το με τα δικά σας χρώματα και το παραδώσετε κατευθείαν στο σπίτι σας.\n \n### Προδιαγραφές:\n- 16\" x 20\" (40.64cm x 50.8cm)\n- Προσανατολισμός τοπίου.\n- Μικρό μελάνι, σταγονίδια για ακριβείς εκτυπώσεις, χρώμα 8 bit, σχεδόν ποιότητα εκτύπωσης φωτογραφιών,\n- Χαρτί σατέν πάχους 0,22 mm\n\n### Λεπτομέρειες αποστολής:\nΑποστολή από το Τορόντο του Καναδά σε οπουδήποτε στον κόσμο χρησιμοποιώντας το Canada Post. Επιτρέψτε 2 έως 4 εβδομάδες για παράδοση στους περισσότερους προορισμούς. Όλες οι παραγγελίες αποστέλλονται σε ένα κουτί σωλήνα από χαρτόνι στη διεύθυνση αποστολής που υποβλήθηκε. Όλη η πληρωμή χειρίζεται η η Google Pay ή η Stripe.\n\n\n### Ακύρωση/επιστροφή χρημάτων:\nΟι παραγγελίες υποβάλλονται σε επεξεργασία αμέσως μετά την υποβολή για την ταχύτερη δυνατή ανάκαμψη. Επομένως, δεν υπάρχει διαθέσιμη επιστροφή/ακύρωση.';

  @override
  String get posterCustomizeTitle => 'Προσαρμόστε την αφίσα';

  @override
  String get enterShippingAddress => 'Εισαγάγετε τη διεύθυνση αποστολής';

  @override
  String get price => 'Τιμή';

  @override
  String formattedPlusTax(Object formattedPrice) {
    return '$formattedPrice + Φόρος';
  }

  @override
  String get showSelections => 'Εμφάνιση επιλογής';

  @override
  String get posterNoRefunds =>
      'Δεν υπάρχουν διαθέσιμες επιστροφές μετά την εκτύπωση της αφίσας σας.';

  @override
  String get posterReviewOrder => 'Ελέγξτε την παραγγελία σας';

  @override
  String get email => 'ΗΛΕΚΤΡΟΝΙΚΗ ΔΙΕΥΘΥΝΣΗ';

  @override
  String get emailEmptyError => 'Εισαγάγετε το email σας';

  @override
  String get fullName => 'Πλήρες όνομα';

  @override
  String get fullNameEmptyError => 'Παρακαλώ εισάγετε το πλήρες όνομά σας';

  @override
  String get streetAddressEmptyError =>
      'Εισαγάγετε τη διεύθυνση του δρόμου σας';

  @override
  String get cityEmptyError => 'Εισαγάγετε την πόλη σας';

  @override
  String fieldEmptyError(Object fieldName) {
    return 'Εισαγάγετε το $fieldName';
  }

  @override
  String get country => 'Χώρα';

  @override
  String get countryEmptyError => 'Εισαγάγετε τη χώρα σας';

  @override
  String get posterReviewOrderTitle => 'Ελέγξτε την παραγγελία σας';

  @override
  String get buyNow => 'Αγορασε τωρα';

  @override
  String get secureCheckoutDisclaimer =>
      'Ασφαλές checkout που παρέχεται από το Stripe';

  @override
  String get total => 'Σύνολο';

  @override
  String get tax => 'Φόρος';

  @override
  String get subtotal => 'ΜΕΡΙΚΟ ΣΥΝΟΛΟ';

  @override
  String get posterProductName => 'Προσαρμοσμένη αφίσα χάρτη επισκέψεων';

  @override
  String get shipping => 'Αποστολή';

  @override
  String get posterOrderReceivedTitle => 'Εντολή Ελήφθη';

  @override
  String get posterOrderReceivedSubtitle => 'Λάβαμε την παραγγελία σας!';

  @override
  String get posterOrderReceivedInstructionsMarkdown =>
      'Ελέγξτε το email σας για περισσότερες ενημερώσεις. \nΑφήστε έως και 4 εβδομάδες για να φτάσει η αφίσα σας. \nΕάν έχετε οποιεσδήποτε ερωτήσεις, στείλτε μας email στο [<EMAIL>](<EMAIL>)';

  @override
  String get posterOrderReceivedEmailSubject =>
      'Τυπωμένη κατάσταση παραγγελίας αφίσας';

  @override
  String get moreInfo => 'Περισσότερες πληροφορίες';

  @override
  String get logoutConfirm => 'Θέλετε να αποσυνδεθείτε από την εφαρμογή;';

  @override
  String get emailNotAvailable => 'Αυτό το email έχει ληφθεί.';

  @override
  String get alphabetical => 'Αλφαβητικός';

  @override
  String get firstTimeLiveTutorial =>
      'Η παροχή της χώρας και της πόλης καταγωγής σας θα εξατομικεύσει την εμπειρία σας στην εφαρμογή.';

  @override
  String get firstTimeBeenTutorial =>
      'Επιλέγοντας πού έχετε πάει, μπορείτε να δείτε το χάρτη με όλες τις χώρες στις οποίες έχετε πάει και να δείτε τα προσωπικά σας στατιστικά στοιχεία.';

  @override
  String get progressTooltipGoal =>
      'Οι ταξιδιωτικοί σας στόχοι βασίζονται στον αριθμό των χωρών που \"Θέλετε\" να ταξιδέψετε σε σύγκριση με τις χώρες στις οποίες \"Έχετε πάει\".';

  @override
  String get progressTooltipRank =>
      'Αυτός ο αριθμός δείχνει πώς κατατάσσεστε σε σύγκριση με τους ταξιδιώτες σε όλο τον κόσμο.  Μπορείτε να αυξήσετε την κατάταξή σας ταξιδεύοντας σε περισσότερες χώρες.';

  @override
  String get progressTooltipPercentageOfWorld =>
      'Αυτό το γράφημα βασίζεται στον αριθμό των χωρών στις οποίες έχετε πάει σε σύγκριση με το σύνολο των χωρών του κόσμου.';

  @override
  String get sortBy => 'Ταξινόμηση κατά';

  @override
  String get updateWishlist => 'Ενημέρωση λίστας επιθυμιών';

  @override
  String get mapInfo =>
      'Κάντε κλικ στη χώρα για να επιλέξετε been, want ή live. Μπορείτε επίσης να κάνετε κλικ στο εικονίδιο που βρίσκεται στην επάνω αριστερή γωνία για την προβολή της λίστας.';

  @override
  String get oneTimePurchase => 'Τα πάντα είναι μια εφάπαξ αγορά!';

  @override
  String get contact => 'Επικοινωνία';

  @override
  String get contactUs => 'Επικοινωνήστε μαζί μας';

  @override
  String get noCitiesSelected => 'Δεν έχετε επιλέξει καμία πόλη, ακόμα...';

  @override
  String get updateTravelGoal => 'Ενημέρωση του ταξιδιωτικού στόχου';

  @override
  String get travelGoalComplete =>
      'Συγχαρητήρια! \n\ntap Το κουμπί + για να προσθέσετε περισσότερες χώρες.';

  @override
  String loginEmailNotFoundError(String email) {
    return 'Δεν υπάρχει κανένας λογαριασμός που να συνδέεται με το email $email. Θέλετε να το δημιουργήσετε τώρα;';
  }

  @override
  String get tryAgain => 'Δοκιμάστε ξανά';

  @override
  String get itineraries => 'Ταξιδιωτικά σχέδια';

  @override
  String get itinerary => 'Ταξιδιωτικό σχέδιο';

  @override
  String get place => 'Τόπος';

  @override
  String get itinerariesDescription =>
      'Αυτά είναι μέρη για τα οποία έχετε εκφράσει ενδιαφέρον.\nΧρησιμοποιήστε αυτόν τον οδηγό για να σας βοηθήσει να σχεδιάσετε τις επόμενες διακοπές σας.';

  @override
  String get addMore => 'Προσθέστε περισσότερα';

  @override
  String get interests => 'Τα ενδιαφέροντα';

  @override
  String get selection => 'Επιλογή';

  @override
  String get goal => 'Στόχος';

  @override
  String get noItineraries => 'Χωρίς Δρομολόγια';

  @override
  String get noItinerariesExplanation =>
      'Προσθέστε κάποια μέρη, έμπνευση ή εμπειρίες για να δείτε τα δρομολόγιά σας να δημιουργούνται αυτόματα.';

  @override
  String get clusterPins => 'Ομαδοποιήστε δείκτες στο χάρτη';

  @override
  String get toggleRegions => 'Εμφάνιση περιοχών κατά το ζουμ';

  @override
  String get mapProjection => 'Προβολή χάρτη';

  @override
  String get mercator => 'Mercator';

  @override
  String get equirectangular => 'Ισόρροπη';

  @override
  String get yourTravellerType => 'Ο τύπος ταξιδιώτη σας:';

  @override
  String get yourHotelPreferences => 'Οι προτιμήσεις σας για ξενοδοχείο:';

  @override
  String get budget => 'Οικονομικό';

  @override
  String get midScale => 'Μεσαία Κατηγορία';

  @override
  String get luxury => 'Πολυτελές';

  @override
  String get noTravellerType =>
      'Προσθέστε στοιχεία στη λίστα επιθυμιών σας για να ανακαλύψετε τι είδους ταξιδιώτης είστε.';

  @override
  String get unlockLived => 'Ξεκλειδώστε Ζωή';

  @override
  String get unlockLivedDescription =>
      'Επιλέξτε πού έχετε ζήσει στο παρελθόν στον χάρτη!';

  @override
  String get futureFeaturesDescription =>
      '...και όλες τις μελλοντικές δυνατότητες';

  @override
  String get yourMostFrequentlyVisitedCountry =>
      'Η χώρα που επισκέπτεστε πιο συχνά:';

  @override
  String get departureDate => 'Ημερομηνία αναχώρησης';

  @override
  String get returnDate => 'Ημερομηνία επιστροφής';

  @override
  String get hotels => 'Ξενοδοχεία';

  @override
  String get food => 'Φαγητό';

  @override
  String get travelDates => 'Ημερομηνίες ταξιδιού';

  @override
  String get posterForMe => 'Für mich';

  @override
  String get posterSendGift => 'Geschenk senden';

  @override
  String get addSelections => 'Auswahlen hinzufügen';

  @override
  String get posterType => 'Postertyp';

  @override
  String get help => 'Βοήθεια';

  @override
  String get tutorialMap =>
      'Πατήστε σε μια χώρα για να επιλέξετε: ήταν, επιθυμούσα και έζησες.';

  @override
  String get tutorialMapList =>
      'Πατήστε στο εικονίδιο λίστας (πάνω αριστερή γωνία) για να επιλέξετε ανά λίστα.';

  @override
  String get tutorialCountryDetails =>
      'Πατήστε στη χώρα και μετά «περισσότερα» για να επιλέξετε ανά περιοχή.';

  @override
  String get tutorialItems =>
      'Σύρετε το διακόπτη για να επιλέξετε πώς θέλετε να επιλέξετε στοιχεία.';

  @override
  String get tutorialInspirations =>
      'Σύρετε δεξιά ή αριστερά για να μεταβείτε στην επόμενη κάρτα.';

  @override
  String get lifetime => 'Διάρκεια ζωής';

  @override
  String get chooseYourPlan => 'Επιλέξτε το πρόγραμμά σας';

  @override
  String get requestARefund => 'Αίτημα επιστροφής χρημάτων';

  @override
  String get noPurchasesFound => 'Δεν βρέθηκαν αγορές.';

  @override
  String get noProductsAvailable => 'Δεν υπάρχουν διαθέσιμα προϊόντα';

  @override
  String get posterLandingAppBar => 'Φέρτε τις ιστορίες σας στο σπίτι';

  @override
  String get posterLandingSubHeading => 'Το ταξίδι σας, η ιστορία σας';

  @override
  String get posterLandingSubDescription =>
      'Τα ταξίδια σας είναι κάτι περισσότερο από ταξίδια, είναι ιστορίες, αναμνήσεις και ορόσημα. Μετατρέψτε αυτές τις αξέχαστες στιγμές σε έναν εξατομικευμένο παγκόσμιο χάρτη που είναι τόσο μοναδικός όσο και οι περιπέτειές σας.';

  @override
  String get posterLandingPromoBullet1 =>
      '• Ένας Χάρτης των Επιτευγμάτων σου: Επισήμανε κάθε προορισμό, από το πρώτο σου μεγάλο ταξίδι έως την πιο τολμηρή περιπέτειά σου.';

  @override
  String get posterLandingPromoBullet2 =>
      '• Γιορτάστε Κάθε Ταξίδι: Ανάγνωστε τα ταξίδια σας καθημερινά με μια όμορφα σχεδιασμένη αφίσα που έχει σκοπό να εμπνεύσει.';

  @override
  String get posterLandingPromoBullet3 =>
      '• Ένα Δώρο που θα το Εκτιμήσουν: Ξαφνιάστε έναν fellow ταξιδιώτη με έναν προσαρμοσμένο χάρτη που δείχνει το ταξίδι τους, ιδανικό για γενέθλια, σημαντικές στιγμές ή απλώς γιατί.';

  @override
  String get posterLandingHowItWorks => 'Πώς λειτουργεί!';

  @override
  String get posterLandingHowItWorksStep1 =>
      '1. Προσαρμόστε το σχέδιό σας:  Επιλέξτε χρώματα, στυλ και σημειώστε τα ταξίδια σας (ή τα δικά τους!)';

  @override
  String get posterLandingHowItWorksStep2 =>
      '2. Προεπισκόπηση του χάρτη σας:  Δείτε τον να ζωντανεύει πριν από την παραγγελία σας.';

  @override
  String get posterLandingHowItWorksStep3_iOS =>
      '3. Ασφαλής πληρωμή: Apple Pay ή Stripe.';

  @override
  String get posterLandingHowItWorksStep3_android =>
      '3. Ασφαλής πληρωμή: Google Pay ή Stripe.';

  @override
  String get posterLandingHowItWorksStep4 =>
      '4. Έτοιμο για προβολή: Θα το στείλουμε κατευθείαν στην πόρτα σας (ή στη δική τους).';

  @override
  String get posterLandingCustomerReviewsHeader =>
      'Εμπειρίες από άλλους ταξιδιώτες';

  @override
  String get posterLandingCustomerReview1 =>
      '«Αυτός ο χάρτης είναι ένας πολύ καλός τρόπος για να παρακολουθώ τα μέρη που έχω ταξιδέψει και να σχεδιάζω τα μελλοντικά μας ταξίδια.  Η ποιότητα είναι σταθερή και φαίνεται φοβερός κρεμασμένος στο γραφείο μου.  Πήρα ακόμη και έναν για τον αδελφό μου και δεν μπορούσε, να σταματήσει να μιλάει για το πόσο ωραίος είναι!» - John C.';

  @override
  String get posterLandingCustomerReview2 =>
      '«Έχω ταξιδέψει σε πάνω από 150 λιμάνια ενώ δούλευα σε κρουαζιέρα. Αυτός ο χάρτης είναι μια εξαιρετική προσθήκη στο σαλόνι μου ως ανάμνηση όλων των ετών στη θάλασσα». - Betty K.';

  @override
  String get posterLandingCustomerReview3 =>
      '«Υπέροχο δώρο για την ημέρα της μητέρας. Η μαμά μου ήταν πολύ συγκινημένη!» Samantha W.';

  @override
  String get posterLandingCustomerReview4 =>
      '«Εκτύπωσα έναν χάρτη με τα μέρη που ήθελα να επισκεφτώ με το gf μου. Ήταν ένα υπέροχο χριστουγεννιάτικο δώρο. Υψηλής ποιότητας επίσης.» Brad J.';

  @override
  String get posterLandingSpecifications => 'Προδιαγραφές';

  @override
  String get posterLandingSpecification1 => '- Διαστάσεις: (40.64cm x 50.8cm)';

  @override
  String get posterLandingSpecification2 => '- Προσανατολισμός: .';

  @override
  String get posterLandingSpecification3 =>
      '- Ποιότητα εκτύπωσης: για ακριβείς εκτυπώσεις.  8 bit χρώμα, ποιότητα εκτύπωσης σχεδόν φωτογραφίας.';

  @override
  String get posterLandingSpecification4 =>
      '- Χαρτί: 0,22 χιλιοστά πάχους σατέν χαρτί';

  @override
  String get posterLandingShippingHeader => 'Λεπτομέρειες αποστολής';

  @override
  String get posterLandingShipping1 =>
      '- Αποστολή από το Τορόντο του Καναδά σε οποιοδήποτε μέρος του κόσμου με την Canada Post.';

  @override
  String get posterLandingShipping2 =>
      '- Περιμένετε 2-4 εβδομάδες για την παράδοση στους περισσότερους προορισμούς.';

  @override
  String get posterLandingShipping3 =>
      '- Όλες οι παραγγελίες μεταφέρονται τυλιγμένες σε κουτί από χαρτόνι με σωλήνα στη διεύθυνση αποστολής που υποβάλετε.';

  @override
  String get posterLandingCancellationHeader => 'Ακύρωση/επιστροφή χρημάτων:';

  @override
  String get posterLandingCancellationBody =>
      'Οι επιστροφές χρημάτων είναι διαθέσιμες πριν από την αποστολή της αφίσας σας στον εκτυπωτή, η οποία μπορεί να διαρκέσει έως και 24 ώρες.  Μετά την επεξεργασία της παραγγελίας σας, δεν υπάρχει δυνατότητα επιστροφής χρημάτων/ακύρωσης.  Θα λάβετε ένα μήνυμα ηλεκτρονικού ταχυδρομείου όταν η παραγγελία σας εκτυπωθεί.';

  @override
  String get unsubscribe => 'Κατάργηση εγγραφής';

  @override
  String get unsubscribeConfirmMessage =>
      'Είστε βέβαιοι ότι θέλετε να καταργήσετε την εγγραφή σας; Θα χάσετε αποκλειστικές προσφορές και ενημερώσεις!';

  @override
  String get updateLive => 'Ενημέρωση Κατοικίας';

  @override
  String get updateLiveDescription =>
      'Για να αλλάξετε τη χώρα στην οποία ζείτε, πρέπει πρώτα να επιλέξετε μια νέα χώρα για να την αντικαταστήσετε.';

  @override
  String get underOneThousand => 'Κάτω από 1.000';

  @override
  String get oneThousandToTenThousand => '1.000 – 10.000';

  @override
  String get overTenThousand => 'Πάνω από 10.000';

  @override
  String get becomeABrandAmbassador => 'Γίνε Πρεσβευτής Μάρκας';

  @override
  String get instagram => 'Instagram';

  @override
  String get tiktok => 'TikTok';

  @override
  String get youtube => 'YouTube';

  @override
  String get website => 'Ιστότοπος';

  @override
  String get handle => 'Χειριστήριο';

  @override
  String get followers => 'Ακόλουθοι';

  @override
  String get joinBrandAmbassadorProgram =>
      'Συμμετοχή στο Πρόγραμμα Πρεσβευτών Μάρκας';

  @override
  String get brandAmbassadorProgramDescription =>
      'Αγαπάς το Visited; Ως πρεσβευτής μάρκας θα εκπροσωπείς την ταξιδιωτική μας κοινότητα, θα παρουσιάζεις τον χάρτη και τις λίστες ταξιδιών σου και θα βοηθάς άλλους να ανακαλύπτουν νέους προορισμούς και δυνατότητες. Σε αντάλλαγμα θα λάβεις ανταμοιβές, εκπτώσεις, δώρα και άλλα!';

  @override
  String get fillOutTheFormToGetStarted =>
      'Συμπλήρωσε την παρακάτω φόρμα για να ξεκινήσεις:';

  @override
  String get yourName => 'Το όνομά σου';

  @override
  String get yourNameEmptyError => 'Παρακαλώ εισάγετε το όνομά σας';

  @override
  String get fillInWhereApplicable => 'Συμπλήρωσε όπου ισχύει:';

  @override
  String get otherNetworks => 'Άλλα δίκτυα';

  @override
  String get anythingElse => 'Υπάρχει κάτι άλλο που θέλεις να μας πεις;';

  @override
  String get yourTravelsByContinent => 'Τα ταξίδια σου ανά ήπειρο';

  @override
  String get territories => 'Περιοχές';

  @override
  String get couponCode => 'Κωδικός κουπονιού';

  @override
  String get apply => 'Εφαρμογή';

  @override
  String get discount => 'Έκπτωση';

  @override
  String get noCouponCode => 'Εισάγετε έναν κωδικό κουπονιού';

  @override
  String get invalidCouponCode => 'Μη έγκυρος κωδικός κουπονιού';

  @override
  String get couponApplied => 'Ο κωδικός εφαρμόστηκε';

  @override
  String discountPercentage(double percentage) {
    return 'Έκπτωση $percentage%';
  }

  @override
  String discountAmount(double amount) {
    return 'Έκπτωση $amount!';
  }

  @override
  String get thankYou => 'Ευχαριστούμε!';

  @override
  String get formSubmitted =>
      'Λάβαμε το αίτημά σας να γίνετε πρεσβευτής της μάρκας. Θα επικοινωνήσουμε σύντομα!';
}
