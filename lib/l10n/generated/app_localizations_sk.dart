// ignore: unused_import
import 'package:intl/intl.dart' as intl;
import 'app_localizations.dart';

// ignore_for_file: type=lint

/// The translations for Slovak (`sk`).
class AppLocalizationsSk extends AppLocalizations {
  AppLocalizationsSk([String locale = 'sk']) : super(locale);

  @override
  String get appName => 'Visited';

  @override
  String get language => 'Jazyk';

  @override
  String get pickEmailApp => 'Vyberte svoju e-mailovú aplikáciu';

  @override
  String numberOfCountriesShareMessage(int amount) {
    return 'Navštívil/a som $amount krajín! Koľko si navštívil/a ty? www.visitedapp.com';
  }

  @override
  String numberOfCitiesShareMessage(int amount) {
    return 'Navštívil/a som $amount miest! Koľko si navštívil/a ty? www.visitedapp.com';
  }

  @override
  String numberOfItemsInTodoListShareMessage(int amount, String listName) {
    return 'Navštívil/a som $amount $listName! Koľko si navštívil/a ty? www.visitedapp.com';
  }

  @override
  String get clear => 'Vymazať';

  @override
  String get been => 'Bol';

  @override
  String get want => 'Chcieť';

  @override
  String get live => 'Žiť';

  @override
  String get lived => 'Žil';

  @override
  String get water => 'Voda';

  @override
  String get land => 'Pôda';

  @override
  String get borders => 'Hranice';

  @override
  String get labels => 'Štítky';

  @override
  String get legend => 'Legenda';

  @override
  String get inspiration => 'Inšpirácia';

  @override
  String get inspirations => 'Inšpirácie';

  @override
  String get delete => 'Odstrániť';

  @override
  String get unlockVisitedUpsellTitle => 'Chcete vidieť viac?';

  @override
  String get unlockVisitedUpsellSubtitle =>
      'Odomknite všetky funkcie a užívajte si Visited v plnej sile';

  @override
  String get checkTheDetails => 'Skontrolujte podrobnosti';

  @override
  String get moreInspirationsComingSoon =>
      'Pracujeme na získavaní ďalších obrázkov. Vráťte sa čoskoro späť!';

  @override
  String get unlockPremiumFeatures => 'Odomknite prémiové funkcie';

  @override
  String get purchased => 'Zakúpené!';

  @override
  String get buy => 'Kúpiť';

  @override
  String get restorePurchases => 'Obnoviť nákup';

  @override
  String get ok => 'Ok';

  @override
  String get areYouSure => 'Ste si istý?';

  @override
  String get deleteInspirationConfirmMessage =>
      'Odstránenie tejto karty je trvalé. Tento obrázok nie je možné obnoviť.';

  @override
  String get cancel => 'Zrušiť';

  @override
  String get map => 'Mapa';

  @override
  String get progress => 'Pokrok';

  @override
  String get myTravelGoal => 'Môj cestovný cieľ';

  @override
  String goalRemaining(int remaining) {
    return 'Zostáva $remaining!';
  }

  @override
  String get top => 'NAJ';

  @override
  String get ofTheWorld => 'zo sveta!';

  @override
  String get countries => 'krajiny';

  @override
  String topPlacesVisitedFromCountry(String country) {
    return 'Najlepšie navštívené krajiny z $country:';
  }

  @override
  String get login => 'Prihlásiť sa';

  @override
  String get logout => 'Odhlásiť sa';

  @override
  String get enterYourEmail => 'Zadajte svoj e-mail';

  @override
  String get privacyPolicy => 'Zásady ochrany osobných údajov';

  @override
  String get privatePolicyUrl =>
      'https://www.arrivinginhighheels.com/privacy-policy-slovak/';

  @override
  String get termsOfUse => 'Podmienky používania';

  @override
  String get termsOfUserUrl =>
      'https://www.arrivinginhighheels.com/terms-of-use-slovak/';

  @override
  String get errorTitle => 'Ojoj!';

  @override
  String get enterValidEmail => 'Prosím zadajte platný e-mail';

  @override
  String get settings => 'Nastavenia';

  @override
  String get whereDoYouLive => 'Kde bývate?';

  @override
  String get whereHaveYouBeen => 'Kde ste boli?';

  @override
  String get whereDoYouFlyFrom => 'Odkiaľ lietate?';

  @override
  String get next => 'Ďalšie';

  @override
  String get missingAirports =>
      'Nevidíte, čo hľadáte? Zašlite nám e-mail <NAME_EMAIL>';

  @override
  String get missingAirportsEmailTitle => 'Chýbajúce letiská!';

  @override
  String get supportEmailAddress => '<EMAIL>';

  @override
  String get welcomeTitle => 'Vitajte vo Visited';

  @override
  String get welcomeSubtitle => 'Čaká vás dobrodružstvo na celý život';

  @override
  String get getStarted => 'Začať';

  @override
  String get privacyAgreement => 'Dohoda o ochrane osobných údajov';

  @override
  String get privacyAgreementSubtitle =>
      'Pred ďalším používaním služby Visited odsúhlaste nasledujúce položky.';

  @override
  String get privacyAgreementTermsMarkdown =>
      'Zaškrtnutím tohto políčka potvrdzujete, že ste si prečítali a súhlasíte s tým, že budete viazaní dokumentmi [Zásady ochrany osobných údajov] spoločnosti Arriving in High Heels (https://www.arrivinginhighheels.com/privacy-policy) a [Podmienky používania] (http: //www.arrivinginhighheels.com/terms-of-use).';

  @override
  String get privacyAgreementOptIn =>
      'Súhlasím so zasielaním elektronických správ od Arriving in High Heels obsahujúcich informácie a ponuky týkajúce sa produktov, aplikácií a služieb, ktoré by ma mohli zaujímať, vrátane upozornení na výpredaje, propagačné akcie, ponuky a informačné vestníky. Tento súhlas môžem kedykoľvek zrušiť, a to podľa Zásad ochrany osobných údajov, alebo kliknutím na odkaz „zrušiť odber“ v elektronických správach.';

  @override
  String get submit => 'Odoslať';

  @override
  String get companyName => 'Arriving In High Heels Corporation';

  @override
  String get companyAddress =>
      '31 Claudview Street\nKing City, Ontario\nCanada\nL7B 0C6';

  @override
  String get privacyAgreementRequired =>
      'Pre pokračovanie v používaní Visite musíte súhlasiť s našimi Podmienkami i Odberom.';

  @override
  String get deleteAccount => 'Vymazať účet';

  @override
  String get removeAdsUpsell =>
      'Prajete si namiesto toho zrušiť zobrazovanie reklám a odhlásiť sa z e-mailového marketingu?';

  @override
  String get deleteAccountWarning =>
      'Odstránením svojho účtu odstránite z našich serverov všetky vaše informácie.\n Tento proces nie je možné vrátiť.';

  @override
  String get about => 'O nás';

  @override
  String get popularity => 'Popularita';

  @override
  String get regions => 'Regióny';

  @override
  String get population => 'Populácia';

  @override
  String get size => 'Veľkosť';

  @override
  String get coverage => 'Pokrytie';

  @override
  String get percentOfCountryVisited => '% navštívenej krajiny';

  @override
  String get visited => 'navštívené';

  @override
  String get notes => 'Poznámky';

  @override
  String get kmSquared => 'km²';

  @override
  String get customize => 'Prispôsobiť';

  @override
  String get onlyCountSovereign => 'Štát uznaný OSN';

  @override
  String get countUkSeparately => 'Počítať krajiny UK samostatne';

  @override
  String get showLegend => 'Zobraziť legendu';

  @override
  String get showLivedPin => 'Zobraziť živý bod';

  @override
  String get useMyColours => 'Použiť moje farby';

  @override
  String get mapColors => 'Farby mapy';

  @override
  String get traveller => 'Cestovateľ';

  @override
  String get nightTraveller => 'Nočný cestovateľ';

  @override
  String get original => 'Originál';

  @override
  String get explorer => 'Prieskumník';

  @override
  String get weekender => 'Víkendár';

  @override
  String get naturalist => 'Prírodovedec';

  @override
  String get historian => 'Historik';

  @override
  String get thrillSeeker => 'Hľadač vzrušenia';

  @override
  String get culturalBuff => 'Milovník kultúry';

  @override
  String get myColors => 'Moje farby';

  @override
  String get experiences => 'Zážirky';

  @override
  String get done => 'Hotovo';

  @override
  String get experiencesInstructions => 'Začnite klepnutím na tlačidlo +!';

  @override
  String get continueText => 'Pokračovať';

  @override
  String get experiencesDescription => 'Čo radi pri cestovaní robíte?';

  @override
  String get visitedWebsiteShortLink => 'https://www.visitedapp.com';

  @override
  String get sharingHashtag => 'E';

  @override
  String get myTravelMap => 'Moja cestovná mapa';

  @override
  String percentOfWorldSeen(int percentage) {
    return 'Videl som $percentage% sveta';
  }

  @override
  String get requiresOnline =>
      'Ospravedlňujeme sa, ale Visited si vyžaduje aktívne pripojenie na internet. Otvorte nastavenia a uistite sa, že sú Wi-Fi alebo mobilné dáta povolené a režim v lietadle je deaktivovaný.';

  @override
  String get list => 'Zoznam';

  @override
  String get more => 'Viac';

  @override
  String get myCountrySelections => 'Výber mojich krajín';

  @override
  String get cities => 'Mestá';

  @override
  String get citiesInstructions =>
      'Klepnutím na ľubovoľnú krajinu začnete vyberať mestá.';

  @override
  String get missingCitiesEmailTitle => 'Chýbajúce mestá!';

  @override
  String get lists => 'Zoznamy';

  @override
  String get disputedTerritories => 'Sporné územia';

  @override
  String get sponsored => 'Sponzorované';

  @override
  String get places => 'Miesta';

  @override
  String get noListsError =>
      'Hups, momentálne nie sú k dispozícii žiadne zoznamy, skúste to neskôr';

  @override
  String get noInspirationsError =>
      'Hups, momentálne nie sú k dispozícii žiadne fotografie, skúste to neskôr';

  @override
  String get mostFrequentlyVisitedCountries =>
      'Vaše najnavštevovanejšie krajiny:';

  @override
  String get update => 'Aktualizácia';

  @override
  String get signup => 'Prihlásiť Se';

  @override
  String get loginWallSubtitle =>
      'Vytvorte si bezplatný účet, aby ste si prezlali plnú verziu Visited';

  @override
  String get loseAllSelectionsWarning =>
      'Po zatvorení aplikácie stratíte všetky svoje výbery.';

  @override
  String get createAccount => 'Vytvorenie účtu';

  @override
  String get continueWithoutAccount => 'Pokračovať bez účtu';

  @override
  String get inspirationPromotion =>
      'Nechajte sa inšpirovať krásnou cestovateľskú fotografiu';

  @override
  String get saveStatsPromotion => 'Zachráňte svoje cestovné štatistiky!';

  @override
  String get selectRegionsPromotion => 'Vyberte štáty a provincie';

  @override
  String get experiencesPromotion => 'Sledujte zážitky po celom svete';

  @override
  String get missingListItem =>
      'Chýbalo nám niečo? Klepnutím sem pošlite e -mail, aby ste si pridali svoje obľúbené miesto.';

  @override
  String missingListItemEmailTitle(String list) {
    return 'Chýbajúca položka z $list';
  }

  @override
  String listShareMessage(Object amount, Object listName) {
    return 'Navštívil som $amount $listName';
  }

  @override
  String get orderPoster => 'Plagát';

  @override
  String get shareMap => 'Zdieľať mapu';

  @override
  String get posterLandingPageTitle => 'Získajte svoj plagát';

  @override
  String get posterNotAvailableError =>
      'Nákup plagátov momentálne nie je k dispozícii. Skúste neskôr prosím.';

  @override
  String posterPricePlusShipping(Object price, Object shipping) {
    return '$price + $shipping Doprava';
  }

  @override
  String get posterDescriptionMarkdownApple =>
      '## O našich vlastných tlačových mapách\nVytlačte si svoju personalizovanú mapu sveta. Prispôsobte ho pomocou vlastných farieb a nechajte ich dodať priamo do vášho domu.\n \n### Technické údaje:\n- 16\" x 20\" (40.64cm x 50.8cm)\n- Orientácia na krajinu.\n- Micro atrament, kvapky pre presné výtlačky, 8 -bitová farba, takmer kvalita tlače fotografií,\n- 0,22 mm s hrúbkou saténového papiera\n\n### Dodávateľské údaje:\nDoprava z Toronta v Kanade na kdekoľvek na svete pomocou Kanady Post. Doručenie do väčšiny destinácií nechajte 2 až 4 týždne. Všetky objednávky sa dodávajú zvinuté v kartónovej rúrkovej skrinke na odoslanú prepravnú adresu. Všetky platby sa zaoberajú spoločnosťou Apple Pay, alebo Stripe.\n\n\n### Zrušenie/vrátenie peňazí:\nObjednávky sa spracúvajú okamžite po odoslaní na najrýchlejší možný obrat. Preto nie je k dispozícii žiadne vrátenie/zrušenie.';

  @override
  String get posterDescriptionMarkdown =>
      '## O našich vlastných tlačových mapách\nVytlačte si svoju personalizovanú mapu sveta. Prispôsobte ho pomocou vlastných farieb a nechajte ich dodať priamo do vášho domu.\n \n### Technické údaje:\n- 16\" x 20\" (40.64cm x 50.8cm)\n- Orientácia na krajinu.\n- Micro atrament, kvapky pre presné výtlačky, 8 -bitová farba, takmer kvalita tlače fotografií,\n- 0,22 mm s hrúbkou saténového papiera\n\n### Dodávateľské údaje:\nDoprava z Toronta v Kanade na kdekoľvek na svete pomocou Kanady Post. Doručenie do väčšiny destinácií nechajte 2 až 4 týždne. Všetky objednávky sa dodávajú zvinuté v kartónovej rúrkovej skrinke na odoslanú prepravnú adresu. Všetky platby sa zaoberajú spoločnosťou Google Pay alebo Stripe.\n\n\n### Zrušenie/vrátenie peňazí:\nObjednávky sa spracúvajú okamžite po odoslaní na najrýchlejší možný obrat. Preto nie je k dispozícii žiadne vrátenie/zrušenie.';

  @override
  String get posterCustomizeTitle => 'Prispôsobiť plagát';

  @override
  String get enterShippingAddress => 'Zadajte prepravnú adresu';

  @override
  String get price => 'cena';

  @override
  String formattedPlusTax(Object formattedPrice) {
    return '$formattedPrice + daň';
  }

  @override
  String get showSelections => 'Výber';

  @override
  String get posterNoRefunds =>
      'Po vytlačení plagátu nie sú k dispozícii žiadne náhrady.';

  @override
  String get posterReviewOrder => 'Ohodnoť svoju objednávku';

  @override
  String get email => 'E -mail';

  @override
  String get emailEmptyError => 'Zadajte svoj e -mail';

  @override
  String get fullName => 'Celé meno';

  @override
  String get fullNameEmptyError => 'Prosím zadajte vaše celé meno';

  @override
  String get streetAddressEmptyError => 'Zadajte svoju ulicu';

  @override
  String get cityEmptyError => 'Zadajte prosím do svojho mesta';

  @override
  String fieldEmptyError(Object fieldName) {
    return 'Zadajte svoj $fieldName';
  }

  @override
  String get country => 'Krajina';

  @override
  String get countryEmptyError => 'Zadajte do svojej krajiny';

  @override
  String get posterReviewOrderTitle => 'Ohodnoť svoju objednávku';

  @override
  String get buyNow => 'Kúpte teraz';

  @override
  String get secureCheckoutDisclaimer =>
      'Zabezpečená pokladňa poskytnutá spoločnosťou Stripe';

  @override
  String get total => 'Celkom';

  @override
  String get tax => 'Zdatnosť';

  @override
  String get subtotal => 'Medzisúčet';

  @override
  String get posterProductName => 'Plagát na mieru navštívený map';

  @override
  String get shipping => 'Doprava';

  @override
  String get posterOrderReceivedTitle => 'objednavka prijata';

  @override
  String get posterOrderReceivedSubtitle => 'Dostali sme vašu objednávku!';

  @override
  String get posterOrderReceivedInstructionsMarkdown =>
      'Ďalšie aktualizácie nájdete vo svojom e-maile. \nDoručenie plagátu môže trvať až 4 týždne. \nAk máte nejaké otázky, pošlite nám e-mail na adresu [<EMAIL>] (<EMAIL>)';

  @override
  String get posterOrderReceivedEmailSubject =>
      'Stav objednávky tlačeného plagátu';

  @override
  String get moreInfo => 'Viac informácií';

  @override
  String get logoutConfirm => 'Chceli by ste sa odhlásiť z aplikácie?';

  @override
  String get emailNotAvailable => 'Tento e -mail bol prijatý.';

  @override
  String get alphabetical => 'Abecedný';

  @override
  String get firstTimeLiveTutorial =>
      'Ak uvediete svoju domovskú krajinu a mesto, prispôsobíte si svoje skúsenosti s aplikáciou.';

  @override
  String get firstTimeBeenTutorial =>
      'Výber miesta, kde ste boli, vám umožní zobraziť mapu všetkých krajín, v ktorých ste boli, a zobraziť osobné štatistiky.';

  @override
  String get progressTooltipGoal =>
      'Vaše cestovné ciele sú založené na počte krajín, ktoré \"Chcete\" precestovať, v porovnaní s krajinami, v ktorých ste \"Boli\".';

  @override
  String get progressTooltipRank =>
      'Toto číslo ukazuje, ako sa umiestňujete v porovnaní s cestovateľmi na celom svete.  Svoju pozíciu môžete zvýšiť tým, že precestujete viac krajín.';

  @override
  String get progressTooltipPercentageOfWorld =>
      'Tento graf je založený na počte krajín, v ktorých ste boli, v porovnaní s celkovým počtom krajín sveta.';

  @override
  String get sortBy => 'Zoradiť podľa';

  @override
  String get updateWishlist => 'Aktualizovať zoznam želaní';

  @override
  String get mapInfo =>
      'Kliknutím na krajinu vyberte, či ste boli, chcete alebo žijete. Môžete tiež kliknúť na ikonu, ktorú nájdete v ľavom hornom rohu pre zobrazenie zoznamu.';

  @override
  String get oneTimePurchase => 'Všetko je možné zakúpiť jednorazovo!';

  @override
  String get contact => 'Kontakt';

  @override
  String get contactUs => 'Kontaktujte nás';

  @override
  String get noCitiesSelected => 'Zatiaľ ste nevybrali žiadne mesto...';

  @override
  String get updateTravelGoal => 'Aktualizujte cestovný cieľ';

  @override
  String get travelGoalComplete =>
      'Gratulujeme! \n\ntap tlačidlo + pridať ďalšie krajiny.';

  @override
  String loginEmailNotFoundError(String email) {
    return 'E -mail nie je spojený s účtom $email. Chceli by ste to teraz vytvoriť?';
  }

  @override
  String get tryAgain => 'Skúste to znova';

  @override
  String get itineraries => 'Cestovné plány';

  @override
  String get itinerary => 'Cestovný plán';

  @override
  String get place => 'Miesto';

  @override
  String get itinerariesDescription =>
      'Toto sú miesta, o ktoré ste prejavili záujem.\nPomocou tohto sprievodcu si môžete naplánovať svoju ďalšiu dovolenku.';

  @override
  String get addMore => 'Pridať viac';

  @override
  String get interests => 'Záujmy';

  @override
  String get selection => 'Výber';

  @override
  String get goal => 'Cieľ';

  @override
  String get noItineraries => 'Žiadne Itineráre';

  @override
  String get noItinerariesExplanation =>
      'Prosím, pridajte niektoré miesta, inšpirácie alebo zážitky, aby ste videli, ako sa automaticky generujú vaše itineráre.';

  @override
  String get clusterPins => 'Zoskupiť Značky Máp';

  @override
  String get toggleRegions => 'Zobraziť regióny pri približovaní';

  @override
  String get mapProjection => 'Mapová projekcia';

  @override
  String get mercator => 'Mercator';

  @override
  String get equirectangular => 'Ekvirektangulárna';

  @override
  String get yourTravellerType => 'Váš typ cestovateľa:';

  @override
  String get yourHotelPreferences => 'Vaše preferencie hotela:';

  @override
  String get budget => 'Rozpočet';

  @override
  String get midScale => 'Stredná trieda';

  @override
  String get luxury => 'Luxusný';

  @override
  String get noTravellerType =>
      'Pridajte položky do svojho zoznamu želaní, aby ste zistili, aký typ cestovateľa ste.';

  @override
  String get unlockLived => 'Odomknúť Život';

  @override
  String get unlockLivedDescription => 'Vyberte na mape, kde ste predtým žili!';

  @override
  String get futureFeaturesDescription => '...a všetky budúce funkcie';

  @override
  String get yourMostFrequentlyVisitedCountry =>
      'Vaša najčastejšie navštevovaná krajina:';

  @override
  String get departureDate => 'Dátum odchodu';

  @override
  String get returnDate => 'Dátum návratu';

  @override
  String get hotels => 'Hotely';

  @override
  String get food => 'Jedlo';

  @override
  String get travelDates => 'Dátumy cestovania';

  @override
  String get posterForMe => 'Pre mňa';

  @override
  String get posterSendGift => 'Poslať darček';

  @override
  String get addSelections => 'Pridať výbery';

  @override
  String get posterType => 'Typ plagátu';

  @override
  String get help => 'Pomoc';

  @override
  String get tutorialMap =>
      'Klepnutím na krajinu vyberte: boli, chceli a žili.';

  @override
  String get tutorialMapList =>
      'Klepnutím na ikonu zoznamu (ľavý horný roh) vyberiete podľa zoznamu.';

  @override
  String get tutorialCountryDetails =>
      'Klepnite na krajinu a potom na „viac“ pre výber podľa regiónu.';

  @override
  String get tutorialItems =>
      'Posunutím prepínača vyberte, ako chcete vybrať položky.';

  @override
  String get tutorialInspirations =>
      'Potiahnutím prstom doprava alebo doľava sa presuniete na ďalšiu kartu.';

  @override
  String get lifetime => 'Doživotné';

  @override
  String get chooseYourPlan => 'Vyberte si svoj plán';

  @override
  String get requestARefund => 'Požiadať o vrátenie peňazí';

  @override
  String get noPurchasesFound => 'Neboli nájdené žiadne nákupy.';

  @override
  String get noProductsAvailable => 'Žiadne dostupné produkty';

  @override
  String get posterLandingAppBar => 'Prineste si svoje príbehy domov';

  @override
  String get posterLandingSubHeading => 'Vaša cesta, váš príbeh';

  @override
  String get posterLandingSubDescription =>
      'Vaše cesty sú viac ako výlety, sú to príbehy, spomienky a míľniky. Premeňte tieto nezabudnuteľné chvíle na personalizovanú mapu sveta, ktorá je jedinečná ako vaše dobrodružstvá.';

  @override
  String get posterLandingPromoBullet1 =>
      '• Mapa vašich úspechov: Zvýraznite každú destináciu, od vášho prvého veľkého výletu až po vaše najodvážnejšie dobrodružstvo.';

  @override
  String get posterLandingPromoBullet2 =>
      '• Oslávte každú cestu: Oživte svoje cesty každý deň s krásne vytvoreným plagátom, ktorý má inšpirovať.';

  @override
  String get posterLandingPromoBullet3 =>
      '• Darček, ktorý ocenia: Prekvapte spolucestujúcich vlastnou mapou zobrazujúcou ich cestu, ktorá je ideálna na narodeniny, míľniky alebo len tak.';

  @override
  String get posterLandingHowItWorks => 'Ako to funguje!';

  @override
  String get posterLandingHowItWorksStep1 =>
      '1. Prispôsobte si svoj dizajn: Vyberte si farby, štýly a označte svoje cesty (alebo ich!)';

  @override
  String get posterLandingHowItWorksStep2 =>
      '2. Ukážka mapy: Pozrite sa, ako ožíva ešte pred vašou objednávkou.';

  @override
  String get posterLandingHowItWorksStep3_iOS =>
      '3. Bezpečná platba: Rýchla a bezpečná s Apple Pay alebo Stripe.';

  @override
  String get posterLandingHowItWorksStep3_android =>
      '3. Bezpečná platba: Rýchla a bezpečná so službou Google Pay alebo Stripe.';

  @override
  String get posterLandingHowItWorksStep4 =>
      '4. Pripravené na vystavenie: Doručíme vám ho priamo k vašim dverám (alebo k nim).';

  @override
  String get posterLandingCustomerReviewsHeader =>
      'Skúsenosti od spolucestujúcich';

  @override
  String get posterLandingCustomerReview1 =>
      '„Táto mapa je skvelý spôsob, ako sledovať, kam všade som cestoval, a plánovať naše budúce výlety.  Kvalita je solídna a vyzerá úžasne zavesená v mojej kancelárii.  Dokonca som ju kúpil aj pre brata a on nemohol, prestať hovoriť o tom, aká je super!“ - John C.';

  @override
  String get posterLandingCustomerReview2 =>
      '„Počas práce na výletnej lodi som precestoval viac ako 150 prístavov. Táto mapa je skvelým doplnkom do mojej obývačky ako spomienka na všetky roky strávené na mori.“ - Betty K. ';

  @override
  String get posterLandingCustomerReview3 =>
      '„Skvelý darček ku dňu matiek. Moja mama bola veľmi dojatá!“ Samantha W.';

  @override
  String get posterLandingCustomerReview4 =>
      '„Vytlačil som si mapu miest, ktoré som chcel navštíviť so svojou priateľkou. Bol to skvelý vianočný darček. Aj vysoká kvalita.“ Brad J.';

  @override
  String get posterLandingSpecifications => 'Špecifikácie';

  @override
  String get posterLandingSpecification1 =>
      '- Rozmery: 16„ x 20“ (40,64 cm x 50,8 cm)';

  @override
  String get posterLandingSpecification2 => '- Orientácia: Krajina';

  @override
  String get posterLandingSpecification3 =>
      '- Kvalita tlače: Mikrotlač, kvapôčky pre presné výtlačky.  8-bitové farby, takmer fotografická kvalita tlače.';

  @override
  String get posterLandingSpecification4 =>
      '- Papier: 0,22 mm hrubý saténový papier';

  @override
  String get posterLandingShippingHeader => 'Podrobnosti o preprave';

  @override
  String get posterLandingShipping1 =>
      '- Preprava z Toronta, Kanada kamkoľvek na svete pomocou kanadskej pošty.';

  @override
  String get posterLandingShipping2 =>
      '- Na doručenie do väčšiny destinácií si vyhraďte 2-4 týždne.';

  @override
  String get posterLandingShipping3 =>
      '- Všetky objednávky sú zabalené v kartónovej škatuli na odosielaciu adresu, ktorú ste zadali.';

  @override
  String get posterLandingCancellationHeader => 'Zrušenie/vrátenie peňazí:';

  @override
  String get posterLandingCancellationBody =>
      'Vrátenie peňazí je možné pred odoslaním plagátu do tlačiarne, čo môže trvať až 24 hodín.  Po spracovaní vašej objednávky nie je možné vrátiť peniaze/zrušiť objednávku.  Po vytlačení vašej objednávky dostanete e-mail.';

  @override
  String get unsubscribe => 'Odhlásiť sa';

  @override
  String get unsubscribeConfirmMessage =>
      'Ste si istí, že sa chcete odhlásiť? Prídete o exkluzívne ponuky a aktualizácie!';

  @override
  String get updateLive => 'Aktualizovať Bydlisko';

  @override
  String get updateLiveDescription =>
      'Ak chcete zmeniť krajinu, v ktorej žijete, musíte najprv vybrať novú krajinu, ktorá ju nahradí.';

  @override
  String get underOneThousand => 'Pod 1 000';

  @override
  String get oneThousandToTenThousand => '1 000 – 10 000';

  @override
  String get overTenThousand => 'Nad 10 000';

  @override
  String get becomeABrandAmbassador => 'Staň sa ambasádorom značky';

  @override
  String get instagram => 'Instagram';

  @override
  String get tiktok => 'TikTok';

  @override
  String get youtube => 'YouTube';

  @override
  String get website => 'Webstránka';

  @override
  String get handle => 'Používateľské meno';

  @override
  String get followers => 'Sledujúci';

  @override
  String get joinBrandAmbassadorProgram => 'Pripoj sa k programu ambasádorov';

  @override
  String get brandAmbassadorProgramDescription =>
      'Miluješ Visited? Ako ambasádor značky budeš reprezentovať našu cestovateľskú komunitu, ukazovať svoju mapu a cestovné zoznamy a pomáhať ostatným objavovať nové miesta a funkcie aplikácie. Na oplátku získaš odmeny, zľavy, darčeky a ešte viac!';

  @override
  String get fillOutTheFormToGetStarted => 'Vyplň formulár nižšie a začni:';

  @override
  String get yourName => 'Tvoje meno';

  @override
  String get yourNameEmptyError => 'Zadaj svoje meno';

  @override
  String get fillInWhereApplicable => 'Vyplň podľa potreby:';

  @override
  String get otherNetworks => 'Iné siete';

  @override
  String get anythingElse => 'Niečo ďalšie, čo by si chcel(a) zdieľať?';

  @override
  String get yourTravelsByContinent => 'Tvoje cesty podľa kontinentov';

  @override
  String get territories => 'Územia';

  @override
  String get couponCode => 'Zľavový kód';

  @override
  String get apply => 'Použiť';

  @override
  String get discount => 'Zľava';

  @override
  String get noCouponCode => 'Zadaj zľavový kód';

  @override
  String get invalidCouponCode => 'Neplatný zľavový kód';

  @override
  String get couponApplied => 'Zľavový kód použitý';

  @override
  String discountPercentage(double percentage) {
    return '$percentage% zľava!';
  }

  @override
  String discountAmount(double amount) {
    return 'Zľava $amount!';
  }

  @override
  String get thankYou => 'Ďakujeme!';

  @override
  String get formSubmitted =>
      'Vaša žiadosť o zapojenie do programu ambasádorov značky bola prijatá. Čoskoro sa vám ozveme!';
}
