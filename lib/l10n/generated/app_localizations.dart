import 'dart:async';

import 'package:flutter/foundation.dart';
import 'package:flutter/widgets.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:intl/intl.dart' as intl;

import 'app_localizations_ca.dart';
import 'app_localizations_cs.dart';
import 'app_localizations_da.dart';
import 'app_localizations_de.dart';
import 'app_localizations_el.dart';
import 'app_localizations_en.dart';
import 'app_localizations_es.dart';
import 'app_localizations_fi.dart';
import 'app_localizations_fr.dart';
import 'app_localizations_hr.dart';
import 'app_localizations_hu.dart';
import 'app_localizations_id.dart';
import 'app_localizations_it.dart';
import 'app_localizations_ja.dart';
import 'app_localizations_ko.dart';
import 'app_localizations_ms.dart';
import 'app_localizations_nb.dart';
import 'app_localizations_nl.dart';
import 'app_localizations_pl.dart';
import 'app_localizations_pt.dart';
import 'app_localizations_ro.dart';
import 'app_localizations_ru.dart';
import 'app_localizations_sk.dart';
import 'app_localizations_sr.dart';
import 'app_localizations_sv.dart';
import 'app_localizations_th.dart';
import 'app_localizations_tr.dart';
import 'app_localizations_uk.dart';
import 'app_localizations_vi.dart';
import 'app_localizations_zh.dart';

// ignore_for_file: type=lint

/// Callers can lookup localized strings with an instance of AppLocalizations
/// returned by `AppLocalizations.of(context)`.
///
/// Applications need to include `AppLocalizations.delegate()` in their app's
/// `localizationDelegates` list, and the locales they support in the app's
/// `supportedLocales` list. For example:
///
/// ```dart
/// import 'generated/app_localizations.dart';
///
/// return MaterialApp(
///   localizationsDelegates: AppLocalizations.localizationsDelegates,
///   supportedLocales: AppLocalizations.supportedLocales,
///   home: MyApplicationHome(),
/// );
/// ```
///
/// ## Update pubspec.yaml
///
/// Please make sure to update your pubspec.yaml to include the following
/// packages:
///
/// ```yaml
/// dependencies:
///   # Internationalization support.
///   flutter_localizations:
///     sdk: flutter
///   intl: any # Use the pinned version from flutter_localizations
///
///   # Rest of dependencies
/// ```
///
/// ## iOS Applications
///
/// iOS applications define key application metadata, including supported
/// locales, in an Info.plist file that is built into the application bundle.
/// To configure the locales supported by your app, you’ll need to edit this
/// file.
///
/// First, open your project’s ios/Runner.xcworkspace Xcode workspace file.
/// Then, in the Project Navigator, open the Info.plist file under the Runner
/// project’s Runner folder.
///
/// Next, select the Information Property List item, select Add Item from the
/// Editor menu, then select Localizations from the pop-up menu.
///
/// Select and expand the newly-created Localizations item then, for each
/// locale your application supports, add a new item and select the locale
/// you wish to add from the pop-up menu in the Value field. This list should
/// be consistent with the languages listed in the AppLocalizations.supportedLocales
/// property.
abstract class AppLocalizations {
  AppLocalizations(String locale)
    : localeName = intl.Intl.canonicalizedLocale(locale.toString());

  final String localeName;

  static AppLocalizations? of(BuildContext context) {
    return Localizations.of<AppLocalizations>(context, AppLocalizations);
  }

  static const LocalizationsDelegate<AppLocalizations> delegate =
      _AppLocalizationsDelegate();

  /// A list of this localizations delegate along with the default localizations
  /// delegates.
  ///
  /// Returns a list of localizations delegates containing this delegate along with
  /// GlobalMaterialLocalizations.delegate, GlobalCupertinoLocalizations.delegate,
  /// and GlobalWidgetsLocalizations.delegate.
  ///
  /// Additional delegates can be added by appending to this list in
  /// MaterialApp. This list does not have to be used at all if a custom list
  /// of delegates is preferred or required.
  static const List<LocalizationsDelegate<dynamic>> localizationsDelegates =
      <LocalizationsDelegate<dynamic>>[
        delegate,
        GlobalMaterialLocalizations.delegate,
        GlobalCupertinoLocalizations.delegate,
        GlobalWidgetsLocalizations.delegate,
      ];

  /// A list of this localizations delegate's supported locales.
  static const List<Locale> supportedLocales = <Locale>[
    Locale('ca'),
    Locale('cs'),
    Locale('da'),
    Locale('de'),
    Locale('el'),
    Locale('en', 'AU'),
    Locale('en', 'CA'),
    Locale('en', 'GB'),
    Locale('en'),
    Locale('es'),
    Locale('es', 'MX'),
    Locale('fi'),
    Locale('fr', 'CA'),
    Locale('fr'),
    Locale('hr'),
    Locale('hu'),
    Locale('id'),
    Locale('it'),
    Locale('ja'),
    Locale('ko'),
    Locale('ms'),
    Locale('nb'),
    Locale('nl'),
    Locale('pl'),
    Locale('pt', 'BR'),
    Locale('pt'),
    Locale('ro'),
    Locale('ru'),
    Locale('sk'),
    Locale('sr'),
    Locale('sv'),
    Locale('th'),
    Locale('tr'),
    Locale('uk'),
    Locale('vi'),
    Locale('zh'),
    Locale('zh', 'TW'),
  ];

  /// No description provided for @appName.
  ///
  /// In en, this message translates to:
  /// **'Visited'**
  String get appName;

  /// No description provided for @language.
  ///
  /// In en, this message translates to:
  /// **'Language'**
  String get language;

  /// No description provided for @pickEmailApp.
  ///
  /// In en, this message translates to:
  /// **'Pick your email app'**
  String get pickEmailApp;

  /// Message added when sharing your map
  ///
  /// In en, this message translates to:
  /// **'I have visited {amount} countries!  How many have you been to? www.visitedapp.com'**
  String numberOfCountriesShareMessage(int amount);

  /// Message added when sharing your city map
  ///
  /// In en, this message translates to:
  /// **'I have visited {amount} cities!  How many have you been to? www.visitedapp.com'**
  String numberOfCitiesShareMessage(int amount);

  /// Message added when sharing your city map
  ///
  /// In en, this message translates to:
  /// **'I have visited {amount} {listName}!  How many have you been to? www.visitedapp.com'**
  String numberOfItemsInTodoListShareMessage(int amount, String listName);

  /// No description provided for @clear.
  ///
  /// In en, this message translates to:
  /// **'Clear'**
  String get clear;

  /// No description provided for @been.
  ///
  /// In en, this message translates to:
  /// **'Been'**
  String get been;

  /// No description provided for @want.
  ///
  /// In en, this message translates to:
  /// **'Want'**
  String get want;

  /// No description provided for @live.
  ///
  /// In en, this message translates to:
  /// **'Live'**
  String get live;

  /// No description provided for @lived.
  ///
  /// In en, this message translates to:
  /// **'Lived'**
  String get lived;

  /// No description provided for @water.
  ///
  /// In en, this message translates to:
  /// **'Water'**
  String get water;

  /// No description provided for @land.
  ///
  /// In en, this message translates to:
  /// **'Land'**
  String get land;

  /// No description provided for @borders.
  ///
  /// In en, this message translates to:
  /// **'Borders'**
  String get borders;

  /// No description provided for @labels.
  ///
  /// In en, this message translates to:
  /// **'Labels'**
  String get labels;

  /// No description provided for @legend.
  ///
  /// In en, this message translates to:
  /// **'Legend'**
  String get legend;

  /// No description provided for @inspiration.
  ///
  /// In en, this message translates to:
  /// **'Inspiration'**
  String get inspiration;

  /// No description provided for @inspirations.
  ///
  /// In en, this message translates to:
  /// **'Inspirations'**
  String get inspirations;

  /// No description provided for @delete.
  ///
  /// In en, this message translates to:
  /// **'Delete'**
  String get delete;

  /// No description provided for @unlockVisitedUpsellTitle.
  ///
  /// In en, this message translates to:
  /// **'Want to see more?'**
  String get unlockVisitedUpsellTitle;

  /// No description provided for @unlockVisitedUpsellSubtitle.
  ///
  /// In en, this message translates to:
  /// **'Unlock all features and enjoy Visited in it\'s full strength'**
  String get unlockVisitedUpsellSubtitle;

  /// No description provided for @checkTheDetails.
  ///
  /// In en, this message translates to:
  /// **'Check the Details'**
  String get checkTheDetails;

  /// No description provided for @moreInspirationsComingSoon.
  ///
  /// In en, this message translates to:
  /// **'We are working on getting more images.  Check back soon!'**
  String get moreInspirationsComingSoon;

  /// No description provided for @unlockPremiumFeatures.
  ///
  /// In en, this message translates to:
  /// **'Unlock premium features'**
  String get unlockPremiumFeatures;

  /// No description provided for @purchased.
  ///
  /// In en, this message translates to:
  /// **'Purchased!'**
  String get purchased;

  /// No description provided for @buy.
  ///
  /// In en, this message translates to:
  /// **'Buy'**
  String get buy;

  /// No description provided for @restorePurchases.
  ///
  /// In en, this message translates to:
  /// **'Restore Purchase'**
  String get restorePurchases;

  /// No description provided for @ok.
  ///
  /// In en, this message translates to:
  /// **'Ok'**
  String get ok;

  /// No description provided for @areYouSure.
  ///
  /// In en, this message translates to:
  /// **'Are you sure?'**
  String get areYouSure;

  /// No description provided for @deleteInspirationConfirmMessage.
  ///
  /// In en, this message translates to:
  /// **'Deleting this card is permanent.  There is no way to recover this image.'**
  String get deleteInspirationConfirmMessage;

  /// No description provided for @cancel.
  ///
  /// In en, this message translates to:
  /// **'Cancel'**
  String get cancel;

  /// No description provided for @map.
  ///
  /// In en, this message translates to:
  /// **'Map'**
  String get map;

  /// No description provided for @progress.
  ///
  /// In en, this message translates to:
  /// **'Progress'**
  String get progress;

  /// No description provided for @myTravelGoal.
  ///
  /// In en, this message translates to:
  /// **'My Travel Goal'**
  String get myTravelGoal;

  /// Shows on the dashboard how many countries remaining to visit.
  ///
  /// In en, this message translates to:
  /// **'{remaining} more to go!'**
  String goalRemaining(int remaining);

  /// No description provided for @top.
  ///
  /// In en, this message translates to:
  /// **'TOP'**
  String get top;

  /// No description provided for @ofTheWorld.
  ///
  /// In en, this message translates to:
  /// **'of the world!'**
  String get ofTheWorld;

  /// No description provided for @countries.
  ///
  /// In en, this message translates to:
  /// **'countries'**
  String get countries;

  /// No description provided for @topPlacesVisitedFromCountry.
  ///
  /// In en, this message translates to:
  /// **'Top Countries Visited from {country}:'**
  String topPlacesVisitedFromCountry(String country);

  /// No description provided for @login.
  ///
  /// In en, this message translates to:
  /// **'Log In'**
  String get login;

  /// No description provided for @logout.
  ///
  /// In en, this message translates to:
  /// **'Log Out'**
  String get logout;

  /// No description provided for @enterYourEmail.
  ///
  /// In en, this message translates to:
  /// **'Enter your email'**
  String get enterYourEmail;

  /// No description provided for @privacyPolicy.
  ///
  /// In en, this message translates to:
  /// **'Privacy Policy'**
  String get privacyPolicy;

  /// No description provided for @privatePolicyUrl.
  ///
  /// In en, this message translates to:
  /// **'https://www.arrivinginhighheels.com/privacy-policy'**
  String get privatePolicyUrl;

  /// No description provided for @termsOfUse.
  ///
  /// In en, this message translates to:
  /// **'Terms of Use'**
  String get termsOfUse;

  /// No description provided for @termsOfUserUrl.
  ///
  /// In en, this message translates to:
  /// **'https://www.arrivinginhighheels.com/terms-of-use'**
  String get termsOfUserUrl;

  /// No description provided for @errorTitle.
  ///
  /// In en, this message translates to:
  /// **'Whoops!'**
  String get errorTitle;

  /// No description provided for @enterValidEmail.
  ///
  /// In en, this message translates to:
  /// **'Please enter a valid email'**
  String get enterValidEmail;

  /// No description provided for @settings.
  ///
  /// In en, this message translates to:
  /// **'Settings'**
  String get settings;

  /// No description provided for @whereDoYouLive.
  ///
  /// In en, this message translates to:
  /// **'Where do you live?'**
  String get whereDoYouLive;

  /// No description provided for @whereHaveYouBeen.
  ///
  /// In en, this message translates to:
  /// **'Where have you been?'**
  String get whereHaveYouBeen;

  /// No description provided for @whereDoYouFlyFrom.
  ///
  /// In en, this message translates to:
  /// **'Where do you fly out of?'**
  String get whereDoYouFlyFrom;

  /// No description provided for @next.
  ///
  /// In en, this message translates to:
  /// **'Next'**
  String get next;

  /// No description provided for @missingAirports.
  ///
  /// In en, this message translates to:
  /// **'Don\'t see what you are looking for? Send us an <NAME_EMAIL>'**
  String get missingAirports;

  /// No description provided for @missingAirportsEmailTitle.
  ///
  /// In en, this message translates to:
  /// **'Missing Airports!'**
  String get missingAirportsEmailTitle;

  /// No description provided for @supportEmailAddress.
  ///
  /// In en, this message translates to:
  /// **'<EMAIL>'**
  String get supportEmailAddress;

  /// No description provided for @welcomeTitle.
  ///
  /// In en, this message translates to:
  /// **'Welcome to Visited'**
  String get welcomeTitle;

  /// No description provided for @welcomeSubtitle.
  ///
  /// In en, this message translates to:
  /// **'The adventure of a lifetime awaits'**
  String get welcomeSubtitle;

  /// No description provided for @getStarted.
  ///
  /// In en, this message translates to:
  /// **'Get Started'**
  String get getStarted;

  /// No description provided for @privacyAgreement.
  ///
  /// In en, this message translates to:
  /// **'Privacy Agreement'**
  String get privacyAgreement;

  /// No description provided for @privacyAgreementSubtitle.
  ///
  /// In en, this message translates to:
  /// **'Please agree to the following items before continuing to use Visited.'**
  String get privacyAgreementSubtitle;

  /// No description provided for @privacyAgreementTermsMarkdown.
  ///
  /// In en, this message translates to:
  /// **'By checking this box, you acknowledge that you have read and agree to be bound by Arriving in High Heels\'  [Privacy Policy](https://www.arrivinginhighheels.com/privacy-policy) and [Terms of Use](https://www.arrivinginhighheels.com/terms-of-use).'**
  String get privacyAgreementTermsMarkdown;

  /// No description provided for @privacyAgreementOptIn.
  ///
  /// In en, this message translates to:
  /// **'I agree to receive electronic messages from Arriving in High Heels containing information and offers with respect to products, applications and services that may be of interest to me, including notification of sales, promotions, offers and newsletters. I may withdraw this consent at any time as described in the Privacy Policy or by clicking on the \"unsubscribe\" link in the electronic messages.'**
  String get privacyAgreementOptIn;

  /// No description provided for @submit.
  ///
  /// In en, this message translates to:
  /// **'Submit'**
  String get submit;

  /// No description provided for @companyName.
  ///
  /// In en, this message translates to:
  /// **'Arriving In High Heels Corporation'**
  String get companyName;

  /// No description provided for @companyAddress.
  ///
  /// In en, this message translates to:
  /// **'31 Claudview Street\nKing City, Ontario\nCanada\nL7B 0C6'**
  String get companyAddress;

  /// No description provided for @privacyAgreementRequired.
  ///
  /// In en, this message translates to:
  /// **'You must agree to both our terms and opt in order to continue using Visited.'**
  String get privacyAgreementRequired;

  /// No description provided for @deleteAccount.
  ///
  /// In en, this message translates to:
  /// **'Delete Account'**
  String get deleteAccount;

  /// No description provided for @removeAdsUpsell.
  ///
  /// In en, this message translates to:
  /// **'Do you wish to opt out of ads and unsubscribe from email marketing instead?'**
  String get removeAdsUpsell;

  /// No description provided for @deleteAccountWarning.
  ///
  /// In en, this message translates to:
  /// **'Deleting your account will remove all of your information from our servers.\nThis process cannot be undone.'**
  String get deleteAccountWarning;

  /// No description provided for @about.
  ///
  /// In en, this message translates to:
  /// **'About'**
  String get about;

  /// No description provided for @popularity.
  ///
  /// In en, this message translates to:
  /// **'Popularity'**
  String get popularity;

  /// No description provided for @regions.
  ///
  /// In en, this message translates to:
  /// **'Regions'**
  String get regions;

  /// No description provided for @population.
  ///
  /// In en, this message translates to:
  /// **'Population'**
  String get population;

  /// No description provided for @size.
  ///
  /// In en, this message translates to:
  /// **'Size'**
  String get size;

  /// No description provided for @coverage.
  ///
  /// In en, this message translates to:
  /// **'Coverage'**
  String get coverage;

  /// No description provided for @percentOfCountryVisited.
  ///
  /// In en, this message translates to:
  /// **'% of country visited'**
  String get percentOfCountryVisited;

  /// No description provided for @visited.
  ///
  /// In en, this message translates to:
  /// **'visited'**
  String get visited;

  /// No description provided for @notes.
  ///
  /// In en, this message translates to:
  /// **'Notes'**
  String get notes;

  /// No description provided for @kmSquared.
  ///
  /// In en, this message translates to:
  /// **'km²'**
  String get kmSquared;

  /// No description provided for @customize.
  ///
  /// In en, this message translates to:
  /// **'Customize'**
  String get customize;

  /// No description provided for @onlyCountSovereign.
  ///
  /// In en, this message translates to:
  /// **'U.N. Sovereign'**
  String get onlyCountSovereign;

  /// No description provided for @countUkSeparately.
  ///
  /// In en, this message translates to:
  /// **'Count U.K. Countries separately'**
  String get countUkSeparately;

  /// No description provided for @showLegend.
  ///
  /// In en, this message translates to:
  /// **'Show Legend'**
  String get showLegend;

  /// No description provided for @showLivedPin.
  ///
  /// In en, this message translates to:
  /// **'Show Lived Pin'**
  String get showLivedPin;

  /// No description provided for @useMyColours.
  ///
  /// In en, this message translates to:
  /// **'Use My Colors'**
  String get useMyColours;

  /// No description provided for @mapColors.
  ///
  /// In en, this message translates to:
  /// **'Map Colors'**
  String get mapColors;

  /// No description provided for @traveller.
  ///
  /// In en, this message translates to:
  /// **'The Traveller'**
  String get traveller;

  /// No description provided for @nightTraveller.
  ///
  /// In en, this message translates to:
  /// **'The Night Traveller'**
  String get nightTraveller;

  /// No description provided for @original.
  ///
  /// In en, this message translates to:
  /// **'The Original'**
  String get original;

  /// No description provided for @explorer.
  ///
  /// In en, this message translates to:
  /// **'The Explorer'**
  String get explorer;

  /// No description provided for @weekender.
  ///
  /// In en, this message translates to:
  /// **'The Weekender'**
  String get weekender;

  /// No description provided for @naturalist.
  ///
  /// In en, this message translates to:
  /// **'The Naturalist'**
  String get naturalist;

  /// No description provided for @historian.
  ///
  /// In en, this message translates to:
  /// **'The Historian'**
  String get historian;

  /// No description provided for @thrillSeeker.
  ///
  /// In en, this message translates to:
  /// **'The Thrill Seeker'**
  String get thrillSeeker;

  /// No description provided for @culturalBuff.
  ///
  /// In en, this message translates to:
  /// **'The Cultural Buff'**
  String get culturalBuff;

  /// No description provided for @myColors.
  ///
  /// In en, this message translates to:
  /// **'My Colors'**
  String get myColors;

  /// No description provided for @experiences.
  ///
  /// In en, this message translates to:
  /// **'Experiences'**
  String get experiences;

  /// No description provided for @done.
  ///
  /// In en, this message translates to:
  /// **'Done'**
  String get done;

  /// No description provided for @experiencesInstructions.
  ///
  /// In en, this message translates to:
  /// **'Tap the + button to get started!'**
  String get experiencesInstructions;

  /// No description provided for @continueText.
  ///
  /// In en, this message translates to:
  /// **'Continue'**
  String get continueText;

  /// No description provided for @experiencesDescription.
  ///
  /// In en, this message translates to:
  /// **'What do you like to do when you travel?'**
  String get experiencesDescription;

  /// No description provided for @visitedWebsiteShortLink.
  ///
  /// In en, this message translates to:
  /// **'https://www.visitedapp.com'**
  String get visitedWebsiteShortLink;

  /// No description provided for @sharingHashtag.
  ///
  /// In en, this message translates to:
  /// **'#Visited'**
  String get sharingHashtag;

  /// No description provided for @myTravelMap.
  ///
  /// In en, this message translates to:
  /// **'My Travel Map'**
  String get myTravelMap;

  /// Message sent when sharing your map
  ///
  /// In en, this message translates to:
  /// **'I\'ve seen {percentage}% of the world'**
  String percentOfWorldSeen(int percentage);

  /// No description provided for @requiresOnline.
  ///
  /// In en, this message translates to:
  /// **'Sorry, Visited requires an active network connection.  Please open your settings app and ensure that either \\r Wi-Fi or Cellular data is enabled and Airplane Mode is disabled'**
  String get requiresOnline;

  /// No description provided for @list.
  ///
  /// In en, this message translates to:
  /// **'List'**
  String get list;

  /// No description provided for @more.
  ///
  /// In en, this message translates to:
  /// **'More'**
  String get more;

  /// No description provided for @myCountrySelections.
  ///
  /// In en, this message translates to:
  /// **'My Country Selections'**
  String get myCountrySelections;

  /// No description provided for @cities.
  ///
  /// In en, this message translates to:
  /// **'Cities'**
  String get cities;

  /// No description provided for @citiesInstructions.
  ///
  /// In en, this message translates to:
  /// **'Tap on any country to start selecting cities.'**
  String get citiesInstructions;

  /// No description provided for @missingCitiesEmailTitle.
  ///
  /// In en, this message translates to:
  /// **'Missing Cities!'**
  String get missingCitiesEmailTitle;

  /// No description provided for @lists.
  ///
  /// In en, this message translates to:
  /// **'Lists'**
  String get lists;

  /// No description provided for @disputedTerritories.
  ///
  /// In en, this message translates to:
  /// **'Disputed Territories'**
  String get disputedTerritories;

  /// No description provided for @sponsored.
  ///
  /// In en, this message translates to:
  /// **'Sponsored'**
  String get sponsored;

  /// No description provided for @places.
  ///
  /// In en, this message translates to:
  /// **'Places'**
  String get places;

  /// No description provided for @noListsError.
  ///
  /// In en, this message translates to:
  /// **'Opps, no lists available at this time, please try a bit later'**
  String get noListsError;

  /// No description provided for @noInspirationsError.
  ///
  /// In en, this message translates to:
  /// **'Opps, no photos are available right now, please try a bit later'**
  String get noInspirationsError;

  /// No description provided for @mostFrequentlyVisitedCountries.
  ///
  /// In en, this message translates to:
  /// **'Your most frequently visited countries:'**
  String get mostFrequentlyVisitedCountries;

  /// No description provided for @update.
  ///
  /// In en, this message translates to:
  /// **'update'**
  String get update;

  /// No description provided for @signup.
  ///
  /// In en, this message translates to:
  /// **'Sign Up'**
  String get signup;

  /// No description provided for @loginWallSubtitle.
  ///
  /// In en, this message translates to:
  /// **'Create a free account to experience the full version of Visited'**
  String get loginWallSubtitle;

  /// No description provided for @loseAllSelectionsWarning.
  ///
  /// In en, this message translates to:
  /// **'You will lose all your selections after closing the app.'**
  String get loseAllSelectionsWarning;

  /// No description provided for @createAccount.
  ///
  /// In en, this message translates to:
  /// **'Create Account'**
  String get createAccount;

  /// No description provided for @continueWithoutAccount.
  ///
  /// In en, this message translates to:
  /// **'Continue without an Account'**
  String get continueWithoutAccount;

  /// No description provided for @inspirationPromotion.
  ///
  /// In en, this message translates to:
  /// **'Get inspired with beautiful travel photography'**
  String get inspirationPromotion;

  /// No description provided for @saveStatsPromotion.
  ///
  /// In en, this message translates to:
  /// **'Save Your Travel Stats!'**
  String get saveStatsPromotion;

  /// No description provided for @selectRegionsPromotion.
  ///
  /// In en, this message translates to:
  /// **'Select States and Provinces'**
  String get selectRegionsPromotion;

  /// No description provided for @experiencesPromotion.
  ///
  /// In en, this message translates to:
  /// **'Track Experiences all around the World'**
  String get experiencesPromotion;

  /// No description provided for @missingListItem.
  ///
  /// In en, this message translates to:
  /// **'Did we miss something?  Tap here to send us an email to get your favorite place added.'**
  String get missingListItem;

  /// Subject of email when requesting a missing item
  ///
  /// In en, this message translates to:
  /// **'Missing Item from {list}'**
  String missingListItemEmailTitle(String list);

  /// No description provided for @listShareMessage.
  ///
  /// In en, this message translates to:
  /// **'I have visited {amount} {listName}'**
  String listShareMessage(Object amount, Object listName);

  /// No description provided for @orderPoster.
  ///
  /// In en, this message translates to:
  /// **'Poster'**
  String get orderPoster;

  /// No description provided for @shareMap.
  ///
  /// In en, this message translates to:
  /// **'Share Map'**
  String get shareMap;

  /// No description provided for @posterLandingPageTitle.
  ///
  /// In en, this message translates to:
  /// **'Get Your Poster'**
  String get posterLandingPageTitle;

  /// No description provided for @posterNotAvailableError.
  ///
  /// In en, this message translates to:
  /// **'Poster purchasing is not available right now.  Please try again later.'**
  String get posterNotAvailableError;

  /// No description provided for @posterPricePlusShipping.
  ///
  /// In en, this message translates to:
  /// **'{price} + {shipping} shipping'**
  String posterPricePlusShipping(Object price, Object shipping);

  /// No description provided for @posterDescriptionMarkdownApple.
  ///
  /// In en, this message translates to:
  /// **'## About Our Custom Print Maps\nPrint your personalized world map. Customize it with your very own colors and have it delivered straight to your home.\n \n### Specifications: \n- 16\" x 20\" (40.64cm x 50.8cm)\n- Landscape orientation.\n- Micro ink, droplets for precise prints, 8 bit color, almost photo print quality, \n- 0.22mm thick Satin Paper\n\n### Shipping Details:\n- Shipping from Toronto, Canada to anywhere in the world using Canada Post. \n- Please allow 2 to 4 weeks for delivery to most destinations. \n- All orders are shipped rolled up in a cardboard tube box to the shipping address submitted.\n\n### How Payment is Handled: \n All payment is handled by Apple Pay or Stripe.\n\n\n### Cancellation/Refund: \nOrders are processed immediately after being submitted for the fastest turnaround possible. Therefore, there is no refund/cancellation available.'**
  String get posterDescriptionMarkdownApple;

  /// No description provided for @posterDescriptionMarkdown.
  ///
  /// In en, this message translates to:
  /// **'## About Our Custom Print Maps\nPrint your personalized world map. Customize it with your very own colors and have it delivered straight to your home.\n \n### Specifications: \n- 16\" x 20\" (40.64cm x 50.8cm)\n- Landscape orientation.\n- Micro ink, droplets for precise prints, 8 bit color, almost photo print quality, \n- 0.22mm thick Satin Paper\n\n### Shipping Details:\n- Shipping from Toronto, Canada to anywhere in the world using Canada Post. \n- Please allow 2 to 4 weeks for delivery to most destinations. \n- All orders are shipped rolled up in a cardboard tube box to the shipping address submitted.\n\n### How Payment is Handled: \n All payment is handled by Google Pay or Stripe.\n\n\n### Cancellation/Refund: \nOrders are processed immediately after being submitted for the fastest turnaround possible. Therefore, there is no refund/cancellation available.'**
  String get posterDescriptionMarkdown;

  /// No description provided for @posterCustomizeTitle.
  ///
  /// In en, this message translates to:
  /// **'Customize Poster'**
  String get posterCustomizeTitle;

  /// No description provided for @enterShippingAddress.
  ///
  /// In en, this message translates to:
  /// **'Enter Shipping Address'**
  String get enterShippingAddress;

  /// No description provided for @price.
  ///
  /// In en, this message translates to:
  /// **'Price'**
  String get price;

  /// No description provided for @formattedPlusTax.
  ///
  /// In en, this message translates to:
  /// **'{formattedPrice} + tax'**
  String formattedPlusTax(Object formattedPrice);

  /// No description provided for @showSelections.
  ///
  /// In en, this message translates to:
  /// **'Show Selections'**
  String get showSelections;

  /// No description provided for @posterNoRefunds.
  ///
  /// In en, this message translates to:
  /// **'No refunds are available after your poster has been printed.'**
  String get posterNoRefunds;

  /// No description provided for @posterReviewOrder.
  ///
  /// In en, this message translates to:
  /// **'Review Your Order'**
  String get posterReviewOrder;

  /// No description provided for @email.
  ///
  /// In en, this message translates to:
  /// **'Email'**
  String get email;

  /// No description provided for @emailEmptyError.
  ///
  /// In en, this message translates to:
  /// **'Please enter your email'**
  String get emailEmptyError;

  /// No description provided for @fullName.
  ///
  /// In en, this message translates to:
  /// **'Full Name'**
  String get fullName;

  /// No description provided for @fullNameEmptyError.
  ///
  /// In en, this message translates to:
  /// **'Please enter your full name'**
  String get fullNameEmptyError;

  /// No description provided for @streetAddressEmptyError.
  ///
  /// In en, this message translates to:
  /// **'Please enter your street address'**
  String get streetAddressEmptyError;

  /// No description provided for @cityEmptyError.
  ///
  /// In en, this message translates to:
  /// **'Please enter your city'**
  String get cityEmptyError;

  /// No description provided for @fieldEmptyError.
  ///
  /// In en, this message translates to:
  /// **'Please enter your {fieldName}'**
  String fieldEmptyError(Object fieldName);

  /// No description provided for @country.
  ///
  /// In en, this message translates to:
  /// **'Country'**
  String get country;

  /// No description provided for @countryEmptyError.
  ///
  /// In en, this message translates to:
  /// **'Please enter your country'**
  String get countryEmptyError;

  /// No description provided for @posterReviewOrderTitle.
  ///
  /// In en, this message translates to:
  /// **'Review Your Order'**
  String get posterReviewOrderTitle;

  /// No description provided for @buyNow.
  ///
  /// In en, this message translates to:
  /// **'Buy Now'**
  String get buyNow;

  /// No description provided for @secureCheckoutDisclaimer.
  ///
  /// In en, this message translates to:
  /// **'Secure checkout provided by Stripe'**
  String get secureCheckoutDisclaimer;

  /// No description provided for @total.
  ///
  /// In en, this message translates to:
  /// **'Total'**
  String get total;

  /// No description provided for @tax.
  ///
  /// In en, this message translates to:
  /// **'Tax'**
  String get tax;

  /// No description provided for @subtotal.
  ///
  /// In en, this message translates to:
  /// **'Subtotal'**
  String get subtotal;

  /// No description provided for @posterProductName.
  ///
  /// In en, this message translates to:
  /// **'Custom Visited Map Poster'**
  String get posterProductName;

  /// No description provided for @shipping.
  ///
  /// In en, this message translates to:
  /// **'Shipping'**
  String get shipping;

  /// No description provided for @posterOrderReceivedTitle.
  ///
  /// In en, this message translates to:
  /// **'Order Received'**
  String get posterOrderReceivedTitle;

  /// No description provided for @posterOrderReceivedSubtitle.
  ///
  /// In en, this message translates to:
  /// **'We received your order!'**
  String get posterOrderReceivedSubtitle;

  /// No description provided for @posterOrderReceivedInstructionsMarkdown.
  ///
  /// In en, this message translates to:
  /// **'Check your email for more updates.\nPlease allow up to 4 week for your poster to arrive.\nIf you have any questions, please email us at [<EMAIL>](<EMAIL>)'**
  String get posterOrderReceivedInstructionsMarkdown;

  /// No description provided for @posterOrderReceivedEmailSubject.
  ///
  /// In en, this message translates to:
  /// **'Printed Poster Order Status'**
  String get posterOrderReceivedEmailSubject;

  /// No description provided for @moreInfo.
  ///
  /// In en, this message translates to:
  /// **'More Info'**
  String get moreInfo;

  /// No description provided for @logoutConfirm.
  ///
  /// In en, this message translates to:
  /// **'Would you like to log out of Visited?'**
  String get logoutConfirm;

  /// No description provided for @emailNotAvailable.
  ///
  /// In en, this message translates to:
  /// **'That email has been taken.'**
  String get emailNotAvailable;

  /// No description provided for @alphabetical.
  ///
  /// In en, this message translates to:
  /// **'Alphabetical'**
  String get alphabetical;

  /// No description provided for @firstTimeLiveTutorial.
  ///
  /// In en, this message translates to:
  /// **'Providing your home country and city will personalize your app experience.'**
  String get firstTimeLiveTutorial;

  /// No description provided for @firstTimeBeenTutorial.
  ///
  /// In en, this message translates to:
  /// **'Selecting where you have been allows you to view your map of all the countries you have been to and see personal stats.'**
  String get firstTimeBeenTutorial;

  /// No description provided for @progressTooltipGoal.
  ///
  /// In en, this message translates to:
  /// **'Your travel goals are based on the number of countries you \"Want\" to travel compared to countries where you have \"Been\".'**
  String get progressTooltipGoal;

  /// No description provided for @progressTooltipRank.
  ///
  /// In en, this message translates to:
  /// **'This number shows how you rank compared to travellers around the world.  You can increase your rank by travelling to more countries.'**
  String get progressTooltipRank;

  /// No description provided for @progressTooltipPercentageOfWorld.
  ///
  /// In en, this message translates to:
  /// **'This graph is based on number of countries you have been to compared to total countries of the world.'**
  String get progressTooltipPercentageOfWorld;

  /// No description provided for @sortBy.
  ///
  /// In en, this message translates to:
  /// **'Sort By'**
  String get sortBy;

  /// No description provided for @updateWishlist.
  ///
  /// In en, this message translates to:
  /// **'Update Wish List'**
  String get updateWishlist;

  /// No description provided for @mapInfo.
  ///
  /// In en, this message translates to:
  /// **'Click on the country to select been, want or live. You can also click on the icon found in the top left corner for the list view.'**
  String get mapInfo;

  /// No description provided for @oneTimePurchase.
  ///
  /// In en, this message translates to:
  /// **'Everything is a one time purchase!'**
  String get oneTimePurchase;

  /// No description provided for @contact.
  ///
  /// In en, this message translates to:
  /// **'Contact'**
  String get contact;

  /// No description provided for @contactUs.
  ///
  /// In en, this message translates to:
  /// **'Contact Us'**
  String get contactUs;

  /// No description provided for @noCitiesSelected.
  ///
  /// In en, this message translates to:
  /// **'You have not selected any cities, yet...'**
  String get noCitiesSelected;

  /// No description provided for @updateTravelGoal.
  ///
  /// In en, this message translates to:
  /// **'Update Travel Goal'**
  String get updateTravelGoal;

  /// No description provided for @travelGoalComplete.
  ///
  /// In en, this message translates to:
  /// **'Congratulations!\n\nYou have completed your travel goal! \n\nTap the + button to add more countries.'**
  String get travelGoalComplete;

  /// Presents an error if the user tries to log in with an email that does not exist
  ///
  /// In en, this message translates to:
  /// **'There is no account associated the email {email}.  Would you like to create it now?'**
  String loginEmailNotFoundError(String email);

  /// No description provided for @tryAgain.
  ///
  /// In en, this message translates to:
  /// **'Try Again'**
  String get tryAgain;

  /// No description provided for @itineraries.
  ///
  /// In en, this message translates to:
  /// **'Itineraries'**
  String get itineraries;

  /// No description provided for @itinerary.
  ///
  /// In en, this message translates to:
  /// **'Itinerary'**
  String get itinerary;

  /// No description provided for @place.
  ///
  /// In en, this message translates to:
  /// **'Place'**
  String get place;

  /// No description provided for @itinerariesDescription.
  ///
  /// In en, this message translates to:
  /// **'These are places you\'ve expressed interest in.\nUse this guide to help plan your next vacation.'**
  String get itinerariesDescription;

  /// No description provided for @addMore.
  ///
  /// In en, this message translates to:
  /// **'Add More'**
  String get addMore;

  /// No description provided for @interests.
  ///
  /// In en, this message translates to:
  /// **'Interests'**
  String get interests;

  /// No description provided for @selection.
  ///
  /// In en, this message translates to:
  /// **'Selection'**
  String get selection;

  /// No description provided for @goal.
  ///
  /// In en, this message translates to:
  /// **'Goal'**
  String get goal;

  /// No description provided for @noItineraries.
  ///
  /// In en, this message translates to:
  /// **'No Itineraries'**
  String get noItineraries;

  /// No description provided for @noItinerariesExplanation.
  ///
  /// In en, this message translates to:
  /// **'Please add some places, inspirations or experiences to see your itineraries automatically generate.'**
  String get noItinerariesExplanation;

  /// No description provided for @clusterPins.
  ///
  /// In en, this message translates to:
  /// **'Cluster Pins'**
  String get clusterPins;

  /// No description provided for @toggleRegions.
  ///
  /// In en, this message translates to:
  /// **'Show Regions'**
  String get toggleRegions;

  /// No description provided for @mapProjection.
  ///
  /// In en, this message translates to:
  /// **'Map Projection'**
  String get mapProjection;

  /// No description provided for @mercator.
  ///
  /// In en, this message translates to:
  /// **'Mercator'**
  String get mercator;

  /// No description provided for @equirectangular.
  ///
  /// In en, this message translates to:
  /// **'Equirectangular'**
  String get equirectangular;

  /// No description provided for @yourTravellerType.
  ///
  /// In en, this message translates to:
  /// **'Your Traveller Type:'**
  String get yourTravellerType;

  /// No description provided for @yourHotelPreferences.
  ///
  /// In en, this message translates to:
  /// **'Your Hotel Preferences:'**
  String get yourHotelPreferences;

  /// No description provided for @budget.
  ///
  /// In en, this message translates to:
  /// **'Budget'**
  String get budget;

  /// No description provided for @midScale.
  ///
  /// In en, this message translates to:
  /// **'Mid Scale'**
  String get midScale;

  /// No description provided for @luxury.
  ///
  /// In en, this message translates to:
  /// **'Luxury'**
  String get luxury;

  /// No description provided for @noTravellerType.
  ///
  /// In en, this message translates to:
  /// **'Add items to your bucket list to discover what type of traveller you are.'**
  String get noTravellerType;

  /// No description provided for @unlockLived.
  ///
  /// In en, this message translates to:
  /// **'Unlock Lived'**
  String get unlockLived;

  /// No description provided for @unlockLivedDescription.
  ///
  /// In en, this message translates to:
  /// **'Select where you have previously lived on the map!'**
  String get unlockLivedDescription;

  /// No description provided for @futureFeaturesDescription.
  ///
  /// In en, this message translates to:
  /// **'...and all future features'**
  String get futureFeaturesDescription;

  /// No description provided for @yourMostFrequentlyVisitedCountry.
  ///
  /// In en, this message translates to:
  /// **'Your Most Frequently Visited Country:'**
  String get yourMostFrequentlyVisitedCountry;

  /// No description provided for @departureDate.
  ///
  /// In en, this message translates to:
  /// **'Departure Date'**
  String get departureDate;

  /// No description provided for @returnDate.
  ///
  /// In en, this message translates to:
  /// **'Return Date'**
  String get returnDate;

  /// No description provided for @hotels.
  ///
  /// In en, this message translates to:
  /// **'Hotels'**
  String get hotels;

  /// No description provided for @food.
  ///
  /// In en, this message translates to:
  /// **'Food'**
  String get food;

  /// No description provided for @travelDates.
  ///
  /// In en, this message translates to:
  /// **'Travel Dates'**
  String get travelDates;

  /// No description provided for @posterForMe.
  ///
  /// In en, this message translates to:
  /// **'For Me'**
  String get posterForMe;

  /// No description provided for @posterSendGift.
  ///
  /// In en, this message translates to:
  /// **'Send a gift'**
  String get posterSendGift;

  /// No description provided for @addSelections.
  ///
  /// In en, this message translates to:
  /// **'Add Selections'**
  String get addSelections;

  /// No description provided for @posterType.
  ///
  /// In en, this message translates to:
  /// **'Poster Type'**
  String get posterType;

  /// No description provided for @help.
  ///
  /// In en, this message translates to:
  /// **'Help'**
  String get help;

  /// No description provided for @tutorialMap.
  ///
  /// In en, this message translates to:
  /// **'Tap on a country to select: been, want and lived.'**
  String get tutorialMap;

  /// No description provided for @tutorialMapList.
  ///
  /// In en, this message translates to:
  /// **'Tap on list icon (top left corner) to select by list.'**
  String get tutorialMapList;

  /// No description provided for @tutorialCountryDetails.
  ///
  /// In en, this message translates to:
  /// **'Tap on the country and then \"more\" to select by region.'**
  String get tutorialCountryDetails;

  /// No description provided for @tutorialItems.
  ///
  /// In en, this message translates to:
  /// **'Slide the toggle to choose how you want to select items.'**
  String get tutorialItems;

  /// No description provided for @tutorialInspirations.
  ///
  /// In en, this message translates to:
  /// **'Swipe right or left to move to next card.'**
  String get tutorialInspirations;

  /// No description provided for @lifetime.
  ///
  /// In en, this message translates to:
  /// **'Lifetime'**
  String get lifetime;

  /// No description provided for @chooseYourPlan.
  ///
  /// In en, this message translates to:
  /// **'Choose Your Plan'**
  String get chooseYourPlan;

  /// No description provided for @requestARefund.
  ///
  /// In en, this message translates to:
  /// **'Request a Refund'**
  String get requestARefund;

  /// No description provided for @noPurchasesFound.
  ///
  /// In en, this message translates to:
  /// **'No purchases found.'**
  String get noPurchasesFound;

  /// No description provided for @noProductsAvailable.
  ///
  /// In en, this message translates to:
  /// **'No Products Available'**
  String get noProductsAvailable;

  /// No description provided for @posterLandingAppBar.
  ///
  /// In en, this message translates to:
  /// **'Bring Your Stories Home'**
  String get posterLandingAppBar;

  /// No description provided for @posterLandingSubHeading.
  ///
  /// In en, this message translates to:
  /// **'Your Travel, Your Story'**
  String get posterLandingSubHeading;

  /// No description provided for @posterLandingSubDescription.
  ///
  /// In en, this message translates to:
  /// **'Your travels are more than trips, they\'re a stories, memories, and milestones. Turn those unforgettable moments into a personalized world map that\'s as unique as your adventures.'**
  String get posterLandingSubDescription;

  /// No description provided for @posterLandingPromoBullet1.
  ///
  /// In en, this message translates to:
  /// **'• A Map of Your Achievements: Highlight every destination, from your first big trip to your most daring adventure.'**
  String get posterLandingPromoBullet1;

  /// No description provided for @posterLandingPromoBullet2.
  ///
  /// In en, this message translates to:
  /// **'• Celebrate Every Journey: Relive your travels daily with a beautifully crafted postere designed to inspire.'**
  String get posterLandingPromoBullet2;

  /// No description provided for @posterLandingPromoBullet3.
  ///
  /// In en, this message translates to:
  /// **'• A Gift They\'ll Treasure:  Surpise a fellow traveler with a custom map showcasing their journey, perfect for birthdays, milestones or just because.'**
  String get posterLandingPromoBullet3;

  /// No description provided for @posterLandingHowItWorks.
  ///
  /// In en, this message translates to:
  /// **'How it Works!'**
  String get posterLandingHowItWorks;

  /// No description provided for @posterLandingHowItWorksStep1.
  ///
  /// In en, this message translates to:
  /// **'1. Customize Your Design:  Choose colors, styles and mark your travels (or theirs!)'**
  String get posterLandingHowItWorksStep1;

  /// No description provided for @posterLandingHowItWorksStep2.
  ///
  /// In en, this message translates to:
  /// **'2. Preview Your Map:  See it come to life before your order.'**
  String get posterLandingHowItWorksStep2;

  /// No description provided for @posterLandingHowItWorksStep3_iOS.
  ///
  /// In en, this message translates to:
  /// **'3. Safe Payment: Fast And secure with Apple Pay or Stripe.'**
  String get posterLandingHowItWorksStep3_iOS;

  /// No description provided for @posterLandingHowItWorksStep3_android.
  ///
  /// In en, this message translates to:
  /// **'3. Safe Payment: Fast And secure with Google Pay or Stripe.'**
  String get posterLandingHowItWorksStep3_android;

  /// No description provided for @posterLandingHowItWorksStep4.
  ///
  /// In en, this message translates to:
  /// **'4. Ready for Display: We\'ll ship it straight to your door (or theirs).'**
  String get posterLandingHowItWorksStep4;

  /// No description provided for @posterLandingCustomerReviewsHeader.
  ///
  /// In en, this message translates to:
  /// **'Experiences from Fellow Travelers'**
  String get posterLandingCustomerReviewsHeader;

  /// No description provided for @posterLandingCustomerReview1.
  ///
  /// In en, this message translates to:
  /// **'\"This map is a great way to keep track of everywhere I\'ve traveled and plan our future trips.  The quality is solid and it looks awesome hanging in my office.  I even got one for my brother and he couldn\'t, stop talking about how cool it is!\" - John C.'**
  String get posterLandingCustomerReview1;

  /// No description provided for @posterLandingCustomerReview2.
  ///
  /// In en, this message translates to:
  /// **'\"I have travelled to over 150 ports while working on cruise. This map is a great addition to my living room as a memory to all the years at sea.\" - Betty K. '**
  String get posterLandingCustomerReview2;

  /// No description provided for @posterLandingCustomerReview3.
  ///
  /// In en, this message translates to:
  /// **'\"Great gift for mother\'s day. My mom was super touched!\" Samantha W.'**
  String get posterLandingCustomerReview3;

  /// No description provided for @posterLandingCustomerReview4.
  ///
  /// In en, this message translates to:
  /// **'\"Printed a map of places I wanted to visit with my gf. It was a great Christmas gift. High quality too.\" Brad J.'**
  String get posterLandingCustomerReview4;

  /// No description provided for @posterLandingSpecifications.
  ///
  /// In en, this message translates to:
  /// **'Specifications'**
  String get posterLandingSpecifications;

  /// No description provided for @posterLandingSpecification1.
  ///
  /// In en, this message translates to:
  /// **'• Dimensions: 16\" x 20\" (40.64cm x 50.8cm)'**
  String get posterLandingSpecification1;

  /// No description provided for @posterLandingSpecification2.
  ///
  /// In en, this message translates to:
  /// **'• Orientation: Landscape'**
  String get posterLandingSpecification2;

  /// No description provided for @posterLandingSpecification3.
  ///
  /// In en, this message translates to:
  /// **'• Print quality: Micro-ink, droplets for precise prints. 8-bit color, almost photographic print quality.'**
  String get posterLandingSpecification3;

  /// No description provided for @posterLandingSpecification4.
  ///
  /// In en, this message translates to:
  /// **'• Paper: 0.22mm thick satin paper'**
  String get posterLandingSpecification4;

  /// No description provided for @posterLandingShippingHeader.
  ///
  /// In en, this message translates to:
  /// **'Shipping Details'**
  String get posterLandingShippingHeader;

  /// No description provided for @posterLandingShipping1.
  ///
  /// In en, this message translates to:
  /// **'• Shipping from Toronto, Canada to anywhere in the world using Canada Post.'**
  String get posterLandingShipping1;

  /// No description provided for @posterLandingShipping2.
  ///
  /// In en, this message translates to:
  /// **'• Allow 2-4 weeks for delivery to most destinations.'**
  String get posterLandingShipping2;

  /// No description provided for @posterLandingShipping3.
  ///
  /// In en, this message translates to:
  /// **'• All orders are rolled up in a cardboard tube box to the shipping address you submit.'**
  String get posterLandingShipping3;

  /// No description provided for @posterLandingCancellationHeader.
  ///
  /// In en, this message translates to:
  /// **'Cancellation/Refund:'**
  String get posterLandingCancellationHeader;

  /// No description provided for @posterLandingCancellationBody.
  ///
  /// In en, this message translates to:
  /// **'Refunds are available before your poster has been sent to the printer, which can take up to 24 hours.  After your order has been processed, no refund/cancellation is available.  You will receive an email when your order has been printed.'**
  String get posterLandingCancellationBody;

  /// No description provided for @unsubscribe.
  ///
  /// In en, this message translates to:
  /// **'Unsubscribe'**
  String get unsubscribe;

  /// No description provided for @unsubscribeConfirmMessage.
  ///
  /// In en, this message translates to:
  /// **'Are you sure you want to unsubscribe? You\'ll miss out on exclusive deals and updates!'**
  String get unsubscribeConfirmMessage;

  /// No description provided for @updateLive.
  ///
  /// In en, this message translates to:
  /// **'Update Live'**
  String get updateLive;

  /// No description provided for @updateLiveDescription.
  ///
  /// In en, this message translates to:
  /// **'To change the country you live in, you must first select a new country to replace it.'**
  String get updateLiveDescription;

  /// No description provided for @underOneThousand.
  ///
  /// In en, this message translates to:
  /// **'Under 1,000'**
  String get underOneThousand;

  /// No description provided for @oneThousandToTenThousand.
  ///
  /// In en, this message translates to:
  /// **'1,000 - 10,000'**
  String get oneThousandToTenThousand;

  /// No description provided for @overTenThousand.
  ///
  /// In en, this message translates to:
  /// **'10,000+'**
  String get overTenThousand;

  /// No description provided for @becomeABrandAmbassador.
  ///
  /// In en, this message translates to:
  /// **'Become a Brand Ambassador'**
  String get becomeABrandAmbassador;

  /// No description provided for @instagram.
  ///
  /// In en, this message translates to:
  /// **'Instagram'**
  String get instagram;

  /// No description provided for @tiktok.
  ///
  /// In en, this message translates to:
  /// **'TikTok'**
  String get tiktok;

  /// No description provided for @youtube.
  ///
  /// In en, this message translates to:
  /// **'YouTube'**
  String get youtube;

  /// No description provided for @website.
  ///
  /// In en, this message translates to:
  /// **'Website'**
  String get website;

  /// No description provided for @handle.
  ///
  /// In en, this message translates to:
  /// **'Handle'**
  String get handle;

  /// No description provided for @followers.
  ///
  /// In en, this message translates to:
  /// **'Followers'**
  String get followers;

  /// No description provided for @joinBrandAmbassadorProgram.
  ///
  /// In en, this message translates to:
  /// **'Join the Brand Ambassador Program'**
  String get joinBrandAmbassadorProgram;

  /// No description provided for @brandAmbassadorProgramDescription.
  ///
  /// In en, this message translates to:
  /// **'Love Visited? As a brand ambassador, you’ll represent our travel community, showcase your map and travel lists, and help others discover new destinations and app features. In return, you will get rewards, discounts, swag and more!'**
  String get brandAmbassadorProgramDescription;

  /// No description provided for @fillOutTheFormToGetStarted.
  ///
  /// In en, this message translates to:
  /// **'Fill out the form below to get started:'**
  String get fillOutTheFormToGetStarted;

  /// No description provided for @yourName.
  ///
  /// In en, this message translates to:
  /// **'Your Name'**
  String get yourName;

  /// No description provided for @yourNameEmptyError.
  ///
  /// In en, this message translates to:
  /// **'Please enter your name'**
  String get yourNameEmptyError;

  /// No description provided for @fillInWhereApplicable.
  ///
  /// In en, this message translates to:
  /// **'Fill in where applicable:'**
  String get fillInWhereApplicable;

  /// No description provided for @otherNetworks.
  ///
  /// In en, this message translates to:
  /// **'Other Network(s)'**
  String get otherNetworks;

  /// No description provided for @anythingElse.
  ///
  /// In en, this message translates to:
  /// **'Anything else you\'d like us to know?'**
  String get anythingElse;

  /// No description provided for @yourTravelsByContinent.
  ///
  /// In en, this message translates to:
  /// **'Your Travels by Continent'**
  String get yourTravelsByContinent;

  /// No description provided for @territories.
  ///
  /// In en, this message translates to:
  /// **'Territories'**
  String get territories;

  /// No description provided for @couponCode.
  ///
  /// In en, this message translates to:
  /// **'Coupon Code'**
  String get couponCode;

  /// No description provided for @apply.
  ///
  /// In en, this message translates to:
  /// **'Apply'**
  String get apply;

  /// No description provided for @discount.
  ///
  /// In en, this message translates to:
  /// **'Discount'**
  String get discount;

  /// No description provided for @noCouponCode.
  ///
  /// In en, this message translates to:
  /// **'Please enter a coupon code'**
  String get noCouponCode;

  /// No description provided for @invalidCouponCode.
  ///
  /// In en, this message translates to:
  /// **'Invalid coupon code'**
  String get invalidCouponCode;

  /// No description provided for @couponApplied.
  ///
  /// In en, this message translates to:
  /// **'Coupon applied'**
  String get couponApplied;

  /// Presents the percent discounted
  ///
  /// In en, this message translates to:
  /// **'{percentage}% off!'**
  String discountPercentage(double percentage);

  /// Presents the amount discounted
  ///
  /// In en, this message translates to:
  /// **'{amount} discounted!'**
  String discountAmount(double amount);

  /// No description provided for @thankYou.
  ///
  /// In en, this message translates to:
  /// **'Thank You!'**
  String get thankYou;

  /// No description provided for @formSubmitted.
  ///
  /// In en, this message translates to:
  /// **'We have received your request to become a brand ambassador.  We will be in touch soon!'**
  String get formSubmitted;
}

class _AppLocalizationsDelegate
    extends LocalizationsDelegate<AppLocalizations> {
  const _AppLocalizationsDelegate();

  @override
  Future<AppLocalizations> load(Locale locale) {
    return SynchronousFuture<AppLocalizations>(lookupAppLocalizations(locale));
  }

  @override
  bool isSupported(Locale locale) => <String>[
    'ca',
    'cs',
    'da',
    'de',
    'el',
    'en',
    'es',
    'fi',
    'fr',
    'hr',
    'hu',
    'id',
    'it',
    'ja',
    'ko',
    'ms',
    'nb',
    'nl',
    'pl',
    'pt',
    'ro',
    'ru',
    'sk',
    'sr',
    'sv',
    'th',
    'tr',
    'uk',
    'vi',
    'zh',
  ].contains(locale.languageCode);

  @override
  bool shouldReload(_AppLocalizationsDelegate old) => false;
}

AppLocalizations lookupAppLocalizations(Locale locale) {
  // Lookup logic when language+country codes are specified.
  switch (locale.languageCode) {
    case 'en':
      {
        switch (locale.countryCode) {
          case 'AU':
            return AppLocalizationsEnAu();
          case 'CA':
            return AppLocalizationsEnCa();
          case 'GB':
            return AppLocalizationsEnGb();
        }
        break;
      }
    case 'es':
      {
        switch (locale.countryCode) {
          case 'MX':
            return AppLocalizationsEsMx();
        }
        break;
      }
    case 'fr':
      {
        switch (locale.countryCode) {
          case 'CA':
            return AppLocalizationsFrCa();
        }
        break;
      }
    case 'pt':
      {
        switch (locale.countryCode) {
          case 'BR':
            return AppLocalizationsPtBr();
        }
        break;
      }
    case 'zh':
      {
        switch (locale.countryCode) {
          case 'TW':
            return AppLocalizationsZhTw();
        }
        break;
      }
  }

  // Lookup logic when only language code is specified.
  switch (locale.languageCode) {
    case 'ca':
      return AppLocalizationsCa();
    case 'cs':
      return AppLocalizationsCs();
    case 'da':
      return AppLocalizationsDa();
    case 'de':
      return AppLocalizationsDe();
    case 'el':
      return AppLocalizationsEl();
    case 'en':
      return AppLocalizationsEn();
    case 'es':
      return AppLocalizationsEs();
    case 'fi':
      return AppLocalizationsFi();
    case 'fr':
      return AppLocalizationsFr();
    case 'hr':
      return AppLocalizationsHr();
    case 'hu':
      return AppLocalizationsHu();
    case 'id':
      return AppLocalizationsId();
    case 'it':
      return AppLocalizationsIt();
    case 'ja':
      return AppLocalizationsJa();
    case 'ko':
      return AppLocalizationsKo();
    case 'ms':
      return AppLocalizationsMs();
    case 'nb':
      return AppLocalizationsNb();
    case 'nl':
      return AppLocalizationsNl();
    case 'pl':
      return AppLocalizationsPl();
    case 'pt':
      return AppLocalizationsPt();
    case 'ro':
      return AppLocalizationsRo();
    case 'ru':
      return AppLocalizationsRu();
    case 'sk':
      return AppLocalizationsSk();
    case 'sr':
      return AppLocalizationsSr();
    case 'sv':
      return AppLocalizationsSv();
    case 'th':
      return AppLocalizationsTh();
    case 'tr':
      return AppLocalizationsTr();
    case 'uk':
      return AppLocalizationsUk();
    case 'vi':
      return AppLocalizationsVi();
    case 'zh':
      return AppLocalizationsZh();
  }

  throw FlutterError(
    'AppLocalizations.delegate failed to load unsupported locale "$locale". This is likely '
    'an issue with the localizations generation tool. Please file an issue '
    'on GitHub with a reproducible sample app and the gen-l10n configuration '
    'that was used.',
  );
}
