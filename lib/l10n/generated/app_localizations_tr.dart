// ignore: unused_import
import 'package:intl/intl.dart' as intl;
import 'app_localizations.dart';

// ignore_for_file: type=lint

/// The translations for Turkish (`tr`).
class AppLocalizationsTr extends AppLocalizations {
  AppLocalizationsTr([String locale = 'tr']) : super(locale);

  @override
  String get appName => 'Visited';

  @override
  String get language => 'Dil';

  @override
  String get pickEmailApp => 'E-posta uygulamanızı seçin';

  @override
  String numberOfCountriesShareMessage(int amount) {
    return '$amount ülke ziyaret ettim! Sen kaç ülke ziyaret ettin? www.visitedapp.com';
  }

  @override
  String numberOfCitiesShareMessage(int amount) {
    return '$amount şehir ziyaret ettim! Sen kaç şehir ziyaret ettin? www.visitedapp.com';
  }

  @override
  String numberOfItemsInTodoListShareMessage(int amount, String listName) {
    return '$amount $listName ziyaret ettim! Sen kaç tane ziyaret ettin? www.visitedapp.com';
  }

  @override
  String get clear => 'Temizle';

  @override
  String get been => 'Gidildi';

  @override
  String get want => 'İstek';

  @override
  String get live => 'Canlı';

  @override
  String get lived => 'Yaşadım';

  @override
  String get water => 'Su';

  @override
  String get land => 'Toprak';

  @override
  String get borders => 'Sınırlar';

  @override
  String get labels => 'Etiketler';

  @override
  String get legend => 'Gösterge';

  @override
  String get inspiration => 'İlham';

  @override
  String get inspirations => 'İlhamlar';

  @override
  String get delete => 'Sil';

  @override
  String get unlockVisitedUpsellTitle => 'Daha fazlasını görmek ister misiniz?';

  @override
  String get unlockVisitedUpsellSubtitle =>
      'Tüm özelliklerin kilidini açın ve Visited\'in tüm gücüyle keyfini çıkarın';

  @override
  String get checkTheDetails => 'Ayrıntıları Kontrol Edin';

  @override
  String get moreInspirationsComingSoon =>
      'Daha fazla görüntü elde etmek için çalışıyoruz. Daha sonra tekrar kontrol edin!';

  @override
  String get unlockPremiumFeatures => 'Premium özelliklerin kilidini açın';

  @override
  String get purchased => 'Satın alındı!';

  @override
  String get buy => 'Satın al';

  @override
  String get restorePurchases => 'Satın Alımı Geri Yükle';

  @override
  String get ok => 'Tamam';

  @override
  String get areYouSure => 'Emin misiniz?';

  @override
  String get deleteInspirationConfirmMessage =>
      'Bu kartın silinmesi kalıcıdır. Bu görüntüyü kurtarmanın bir yolu yoktur.';

  @override
  String get cancel => 'İptal et';

  @override
  String get map => 'Harita';

  @override
  String get progress => 'İlerleme';

  @override
  String get myTravelGoal => 'Seyahat Hedefim';

  @override
  String goalRemaining(int remaining) {
    return '$remaining tane daha var!';
  }

  @override
  String get top => 'EN İYİ';

  @override
  String get ofTheWorld => 'dünyanın!';

  @override
  String get countries => 'ülkeler';

  @override
  String topPlacesVisitedFromCountry(String country) {
    return '$country\'den En Çok Ziyaret Edilen Ülkeler:';
  }

  @override
  String get login => 'Oturum Aç';

  @override
  String get logout => 'Çıkış Yap';

  @override
  String get enterYourEmail => 'E-postanızı giriniz';

  @override
  String get privacyPolicy => 'Gizlilik Politikası';

  @override
  String get privatePolicyUrl =>
      'https://www.arrivinginhighheels.com/privacy-policy-turkish/';

  @override
  String get termsOfUse => 'Kullanım Şartları';

  @override
  String get termsOfUserUrl =>
      'https://www.arrivinginhighheels.com/terms-of-use-turkish/';

  @override
  String get errorTitle => 'Opps!';

  @override
  String get enterValidEmail => 'Lütfen geçerli bir eposta adresi girin';

  @override
  String get settings => 'Ayarlar';

  @override
  String get whereDoYouLive => 'Nerede yaşıyorsunuz?';

  @override
  String get whereHaveYouBeen => 'Nerelerde bulundunuz?';

  @override
  String get whereDoYouFlyFrom => 'Nereden uçuyorsunuz?';

  @override
  String get next => 'Sonraki';

  @override
  String get missingAirports =>
      'Aradığınızı görmüyor musunuz? <EMAIL> adresinden bize bir e-posta gönderin';

  @override
  String get missingAirportsEmailTitle => 'Eksik Havaalanları!';

  @override
  String get supportEmailAddress => '<EMAIL>';

  @override
  String get welcomeTitle => 'Visited\'e Hoş Geldiniz';

  @override
  String get welcomeSubtitle => 'Bir ömür boyu macera bekliyor';

  @override
  String get getStarted => 'Başlayın';

  @override
  String get privacyAgreement => 'Gizlilik Sözleşmesi';

  @override
  String get privacyAgreementSubtitle =>
      'Visited\'i kullanmaya devam etmeden önce lütfen aşağıdaki maddeleri kabul edin.';

  @override
  String get privacyAgreementTermsMarkdown =>
      'Bu kutuyu işaretleyerek, Arriving in High Heels\'in [Gizlilik Politikasını](https://www.arrivinginhighheels.com/privacy-policy) ve [Kullanım Koşullarını] (https://www.arrivinginhighheels.com/terms-of-use) Okuduğunuzu ve Bağlanmayı kabul ettiğinizi onaylamış olursunuz.';

  @override
  String get privacyAgreementOptIn =>
      'Arriving in High Heels\'den satış, promosyon, teklif ve haber bültenleri bildirimleri de dahil olmak üzere ilgimi çekebilecek ürünler, uygulamalar ve hizmetlerle ilgili bilgi ve teklifler içeren elektronik mesajlar almayı kabul ediyorum. Bu onayı, Gizlilik Politikasında açıklandığı şekilde veya elektronik iletilerdeki \"abonelikten çık\" bağlantısına tıklayarak herhangi bir zamanda geri çekebilirim.';

  @override
  String get submit => 'Onayla';

  @override
  String get companyName => 'Arriving In High Heels Corporation';

  @override
  String get companyAddress =>
      '31 Claudview Street\nKing City, Ontario\nCanada\nL7B 0C6';

  @override
  String get privacyAgreementRequired =>
      'Visited\'i kullanmaya devam etmek için hem şartlarımızı hem de kaydolmayı kabul etmelisiniz.';

  @override
  String get deleteAccount => 'Hesabı Sil';

  @override
  String get removeAdsUpsell =>
      'Bunun yerine reklamları devre dışı bırakmak ve e-posta pazarlama aboneliğinden çıkmak ister musunuz?';

  @override
  String get deleteAccountWarning =>
      'Hesabınızı silmek, tüm bilgilerinizi sunucularımızdan kaldıracaktır.\n \n Bu süreç geri alınamaz.';

  @override
  String get about => 'Hakkında';

  @override
  String get popularity => 'Popülerlik';

  @override
  String get regions => 'Bölgeler';

  @override
  String get population => 'Nüfus';

  @override
  String get size => 'Boyut';

  @override
  String get coverage => 'Kapsam';

  @override
  String get percentOfCountryVisited => 'ülkenin % ziyaret edildi';

  @override
  String get visited => 'ziyaret edildi';

  @override
  String get notes => 'Notlar';

  @override
  String get kmSquared => 'km²';

  @override
  String get customize => 'Özelleştir';

  @override
  String get onlyCountSovereign => 'BM tarafından tanınan egemen';

  @override
  String get countUkSeparately => 'Birleşik Krallık Ülkelerini ayrı ayrı say';

  @override
  String get showLegend => 'Göstergeyi Göster';

  @override
  String get showLivedPin => 'Canlı Pini Göster';

  @override
  String get useMyColours => 'Renklerimi Kullan';

  @override
  String get mapColors => 'Harita Renkleri';

  @override
  String get traveller => 'Gezgin';

  @override
  String get nightTraveller => 'Gece Gezgini';

  @override
  String get original => 'Orijinal';

  @override
  String get explorer => 'Kaşif';

  @override
  String get weekender => 'Hafta soncu';

  @override
  String get naturalist => 'Doğa bilimci';

  @override
  String get historian => 'Tarihçi';

  @override
  String get thrillSeeker => 'Heyecan Arayıcı';

  @override
  String get culturalBuff => 'Kültür Meraklısı';

  @override
  String get myColors => 'Renklerim';

  @override
  String get experiences => 'Deneyimler';

  @override
  String get done => 'Bitti';

  @override
  String get experiencesInstructions => 'Başlamak için + düğmesine dokunun!';

  @override
  String get continueText => 'Devam et';

  @override
  String get experiencesDescription =>
      'Seyahat ederken ne yapmaktan hoşlanırsınız?';

  @override
  String get visitedWebsiteShortLink => 'https://www.visitedapp.com';

  @override
  String get sharingHashtag => '#Visited';

  @override
  String get myTravelMap => 'Seyahat Haritam';

  @override
  String percentOfWorldSeen(int percentage) {
    return 'Dünyanın %$percentage kadarını gördüm';
  }

  @override
  String get requiresOnline =>
      'Üzgünüz, Visited etkin bir ağ bağlantısına ihtiyaç duyar. Lütfen ayarlar uygulamanızı açın ve \\r Wi-Fi veya Hücresel verilerin etkin ve Uçak Modunun devre dışı olduğundan emin olun';

  @override
  String get list => 'Liste';

  @override
  String get more => 'Daha fazla';

  @override
  String get myCountrySelections => 'Ülke Seçimlerim';

  @override
  String get cities => 'Şehirler';

  @override
  String get citiesInstructions =>
      'Şehirleri seçmeye başlamak için herhangi bir ülkeye dokunun.';

  @override
  String get missingCitiesEmailTitle => 'Eksik Şehirler!';

  @override
  String get lists => 'Listeler';

  @override
  String get disputedTerritories => 'Tartışmalı Bölgeler';

  @override
  String get sponsored => 'Sponsorlu';

  @override
  String get places => 'Yerler';

  @override
  String get noListsError =>
      'Opps, şu anda mevcut bir liste yok, lütfen biraz sonra deneyin';

  @override
  String get noInspirationsError =>
      'Opps, şu anda mevcut bir fotoğraf yok, lütfen biraz sonra deneyin';

  @override
  String get mostFrequentlyVisitedCountries =>
      'En sık ziyaret ettiğiniz ülkeler:';

  @override
  String get update => 'Güncelleme';

  @override
  String get signup => 'Üye olmak';

  @override
  String get loginWallSubtitle =>
      'Visited\'nin tam sürümünü deneyimlemek için ücretsiz bir hesap oluşturun';

  @override
  String get loseAllSelectionsWarning =>
      'Uygulamayı kapattıktan sonra tüm seçimlerinizi kaybedersiniz.';

  @override
  String get createAccount => 'Hesap Oluştur';

  @override
  String get continueWithoutAccount => 'Hesap olmadan devam et';

  @override
  String get inspirationPromotion =>
      'Güzel seyahat fotoğrafçılığı ile ilham alın';

  @override
  String get saveStatsPromotion => 'Seyahat İstatistiklerinizi Kaydedin!';

  @override
  String get selectRegionsPromotion => 'Eyaletleri ve İlleri Seç';

  @override
  String get experiencesPromotion => 'Tüm Dünyadaki Deneyimleri İzleyin';

  @override
  String get missingListItem =>
      'Bir şeyi özledik mi? En sevdiğiniz yer eklemek için bize bir e -posta göndermek için buraya dokunun.';

  @override
  String missingListItemEmailTitle(String list) {
    return '$list \'den eksik öğe';
  }

  @override
  String listShareMessage(Object amount, Object listName) {
    return '$amount $listName \'i ziyaret ettim';
  }

  @override
  String get orderPoster => 'Afiş';

  @override
  String get shareMap => 'Paylaşım Haritası';

  @override
  String get posterLandingPageTitle => 'Posterinizi alın';

  @override
  String get posterNotAvailableError =>
      'Poster satın alma şu anda mevcut değil. Lütfen daha sonra tekrar deneyiniz.';

  @override
  String posterPricePlusShipping(Object price, Object shipping) {
    return '$price + $shipping Nakliye';
  }

  @override
  String get posterDescriptionMarkdownApple =>
      '## Özel baskı haritalarımız hakkında\nKişiselleştirilmiş dünya haritanızı yazdırın. Kendi renklerinizle özelleştirin ve doğrudan evinize teslim etmesini sağlayın.\n \n### Özellikler:\n- 16\" x 20\" (40.64cm x 50.8cm)\n- Peyzaj yönelimi.\n- Mikro mürekkep, hassas baskılar için damlacıklar, 8 bit renk, neredeyse fotoğraf baskı kalitesi,\n- 0.22mm kalınlığında saten kağıt\n\n### Nakliye ayrıntıları:\nKanada Post kullanarak Toronto, Kanada\'dan dünyanın herhangi bir yerine nakliye. Çoğu hedefe teslimat için lütfen 2 ila 4 hafta bekleyin. Tüm siparişler, gönderilen gönderim adresine bir karton tüp kutusunda toparlanır. Tüm ödemeler Apple Pay, veya Stripe tarafından ele alınır.\n\n\n### İptal/Geri Ödeme:\nSiparişler, mümkün olan en hızlı geri dönüş için gönderildikten hemen sonra işlenir. Bu nedenle, geri ödeme/iptal yoktur.';

  @override
  String get posterDescriptionMarkdown =>
      '## Özel baskı haritalarımız hakkında\nKişiselleştirilmiş dünya haritanızı yazdırın. Kendi renklerinizle özelleştirin ve doğrudan evinize teslim etmesini sağlayın.\n \n### Özellikler:\n- 16\" x 20\" (40.64cm x 50.8cm)\n- Peyzaj yönelimi.\n- Mikro mürekkep, hassas baskılar için damlacıklar, 8 bit renk, neredeyse fotoğraf baskı kalitesi,\n- 0.22mm kalınlığında saten kağıt\n\n### Nakliye ayrıntıları:\nKanada Post kullanarak Toronto, Kanada\'dan dünyanın herhangi bir yerine nakliye. Çoğu hedefe teslimat için lütfen 2 ila 4 hafta bekleyin. Tüm siparişler, gönderilen gönderim adresine bir karton tüp kutusunda toparlanır. Tüm ödemeler Google Pay veya Stripe tarafından ele alınır.\n\n\n### İptal/Geri Ödeme:\nSiparişler, mümkün olan en hızlı geri dönüş için gönderildikten hemen sonra işlenir. Bu nedenle, geri ödeme/iptal yoktur.';

  @override
  String get posterCustomizeTitle => 'Poster\'i özelleştirin';

  @override
  String get enterShippingAddress => 'Nakliye adresini girin';

  @override
  String get price => 'Fiyat';

  @override
  String formattedPlusTax(Object formattedPrice) {
    return '$formattedPrice + Vergi';
  }

  @override
  String get showSelections => 'Seçimi Göster';

  @override
  String get posterNoRefunds =>
      'Posteriniz yazdırıldıktan sonra geri ödeme mevcut değildir.';

  @override
  String get posterReviewOrder => 'Siparişini gözden geçir';

  @override
  String get email => 'E -posta';

  @override
  String get emailEmptyError => 'Lütfen E-postanızı girin';

  @override
  String get fullName => 'Ad Soyad';

  @override
  String get fullNameEmptyError => 'lütfen tam adınızı giriniz';

  @override
  String get streetAddressEmptyError => 'Lütfen sokak adresinizi girin';

  @override
  String get cityEmptyError => 'Lütfen şehrinize girin';

  @override
  String fieldEmptyError(Object fieldName) {
    return 'Lütfen $fieldName \'ınızı girin';
  }

  @override
  String get country => 'Ülke';

  @override
  String get countryEmptyError => 'Lütfen ülkene girin';

  @override
  String get posterReviewOrderTitle => 'Siparişini gözden geçir';

  @override
  String get buyNow => 'Şimdi al';

  @override
  String get secureCheckoutDisclaimer =>
      'Stripe tarafından sağlanan güvenli ödeme';

  @override
  String get total => 'Toplam';

  @override
  String get tax => 'Vergi';

  @override
  String get subtotal => 'ara toplam';

  @override
  String get posterProductName => 'Özel ziyaret edilen harita posteri';

  @override
  String get shipping => 'Nakliye';

  @override
  String get posterOrderReceivedTitle => 'Sipariş Alındı';

  @override
  String get posterOrderReceivedSubtitle => 'Siparişinizi aldık!';

  @override
  String get posterOrderReceivedInstructionsMarkdown =>
      'Daha fazla güncelleme için e -postanızı kontrol edin. \nPosterinizin gelmesi için 4 haftaya kadar izin verin. \nHerhangi bir sorunuz varsa, lütfen bize [<EMAIL>] (<EMAIL>) adresinden e -posta gönderin.';

  @override
  String get posterOrderReceivedEmailSubject => 'Basılı Poster Sipariş Durumu';

  @override
  String get moreInfo => 'Daha fazla bilgi';

  @override
  String get logoutConfirm => 'Uygulamadan çıkış yapmak ister misiniz?';

  @override
  String get emailNotAvailable => 'Bu e -posta alındı.';

  @override
  String get alphabetical => 'Alfabetik';

  @override
  String get firstTimeLiveTutorial =>
      'Kendi ülkenizi ve şehrinizi belirtmeniz uygulama deneyiminizi kişiselleştirecektir.';

  @override
  String get firstTimeBeenTutorial =>
      'Nerede bulunduğunuzu seçmek, bulunduğunuz tüm ülkelerin haritasını görüntülemenizi ve kişisel istatistikleri görmenizi sağlar.';

  @override
  String get progressTooltipGoal =>
      'Seyahat hedefleriniz, \"Gittiğiniz\" ülkelere kıyasla \"Gitmek İstediğiniz\" ülke sayısına dayanmaktadır.';

  @override
  String get progressTooltipRank =>
      'Bu sayı, dünya genelindeki gezginlere kıyasla kaçıncı sırada olduğunuzu gösterir.  Daha fazla ülkeye seyahat ederek sıralamanızı yükseltebilirsiniz.';

  @override
  String get progressTooltipPercentageOfWorld =>
      'Bu grafik, dünyanın toplam ülkelerine kıyasla gittiğiniz ülke sayısına dayanmaktadır.';

  @override
  String get sortBy => 'Sıralama Ölçütü';

  @override
  String get updateWishlist => 'Dilek Listesini Güncelle';

  @override
  String get mapInfo =>
      'Bulunduğunuz, istediğiniz veya yaşadığınız ülkeyi seçmek için ülkeye tıklayın. Liste görünümü için sol üst köşede bulunan simgeye de tıklayabilirsiniz.';

  @override
  String get oneTimePurchase => 'Her şey tek seferlik bir satın alımdır!';

  @override
  String get contact => 'İletişim';

  @override
  String get contactUs => 'Bize Ulaşın';

  @override
  String get noCitiesSelected => 'Henüz herhangi bir şehir seçmediniz...';

  @override
  String get updateTravelGoal => 'Seyahat Hedefini Güncelle';

  @override
  String get travelGoalComplete =>
      'Tebrikler! \n\nyo seyahat hedefinizi tamamladı! Daha fazla ülke eklemek için \n\ntap + düğmesini.';

  @override
  String loginEmailNotFoundError(String email) {
    return 'E -posta $email ile ilgili bir hesap yoktur. Şimdi oluşturmak ister misiniz?';
  }

  @override
  String get tryAgain => 'Tekrar Deneyin';

  @override
  String get itineraries => 'Seyahat Planları';

  @override
  String get itinerary => 'Seyahat Planı';

  @override
  String get place => 'Yer';

  @override
  String get itinerariesDescription =>
      'Bunlar ilgilendiğinizi belirttiğiniz yerler.\nBir sonraki tatilinizi planlamak için bu rehberi kullanın.';

  @override
  String get addMore => 'Daha Fazla Ekle';

  @override
  String get interests => 'İlgi alanları';

  @override
  String get selection => 'Seçme';

  @override
  String get goal => 'Amaç';

  @override
  String get noItineraries => 'Rotalar Yok';

  @override
  String get noItinerariesExplanation =>
      'Lütfen otomatik olarak oluşturulan rota görmek için bazı yerler, ilham kaynakları veya deneyimler ekleyin.';

  @override
  String get clusterPins => 'Küme Harita İşaretçileri';

  @override
  String get toggleRegions => 'Yakınlaştırıldığında Bölgeleri Göster';

  @override
  String get mapProjection => 'Harita Projeksiyonu';

  @override
  String get mercator => 'Merkatör';

  @override
  String get equirectangular => 'Eşdikdörtgensel';

  @override
  String get yourTravellerType => 'Seyahat türünüz:';

  @override
  String get yourHotelPreferences => 'Otel Tercihleriniz:';

  @override
  String get budget => 'Bütçe';

  @override
  String get midScale => 'Orta Ölçek';

  @override
  String get luxury => 'Lüks';

  @override
  String get noTravellerType =>
      'Hangi tür gezgin olduğunuzu keşfetmek için yapılacaklar listenize öğeler ekleyin.';

  @override
  String get unlockLived => 'Yaşanmışlığı Aç';

  @override
  String get unlockLivedDescription =>
      'Haritada daha önce yaşadığınız yerleri seçin!';

  @override
  String get futureFeaturesDescription => '...ve tüm gelecekteki özellikler';

  @override
  String get yourMostFrequentlyVisitedCountry =>
      'En Sık Ziyaret Ettiğiniz Ülke:';

  @override
  String get departureDate => 'Gidiş Tarihi';

  @override
  String get returnDate => 'Dönüş Tarihi';

  @override
  String get hotels => 'Oteller';

  @override
  String get food => 'Yiyecek';

  @override
  String get travelDates => 'Seyahat Tarihleri';

  @override
  String get posterForMe => 'Benim için';

  @override
  String get posterSendGift => 'Hediye gönder';

  @override
  String get addSelections => 'Seçimleri ekle';

  @override
  String get posterType => 'Poster türü';

  @override
  String get help => 'Yardım';

  @override
  String get tutorialMap =>
      'Seçmek için bir ülkeye dokunun: bulundum, istedim ve yaşadım.';

  @override
  String get tutorialMapList =>
      'Listeye göre seçmek için liste simgesine (sol üst köşe) dokunun.';

  @override
  String get tutorialCountryDetails =>
      'Bölgeye göre seçim yapmak için ülkeye ve ardından “daha ​​fazla”ya dokunun.';

  @override
  String get tutorialItems =>
      'Öğeleri nasıl seçmek istediğinizi seçmek için açma/kapatma düğmesini kaydırın.';

  @override
  String get tutorialInspirations =>
      'Sonraki karta geçmek için sağa veya sola kaydırın.';

  @override
  String get lifetime => 'Ömür boyu';

  @override
  String get chooseYourPlan => 'Planınızı seçin';

  @override
  String get requestARefund => 'Para iadesi talep et';

  @override
  String get noPurchasesFound => 'Satın alma bulunamadı.';

  @override
  String get noProductsAvailable => 'Mevcut ürün yok';

  @override
  String get posterLandingAppBar => 'Hikayelerinizi Eve Getirin';

  @override
  String get posterLandingSubHeading => 'Seyahatiniz, Hikayeniz';

  @override
  String get posterLandingSubDescription =>
      'Seyahatleriniz sadece seyahatlerden ibaret değildir, onlar birer hikaye, anı ve dönüm noktasıdır. Bu unutulmaz anları, maceralarınız kadar benzersiz, kişiselleştirilmiş bir dünya haritasına dönüştürün.';

  @override
  String get posterLandingPromoBullet1 =>
      '• Başarılarınızın Haritası: İlk büyük seyahatinizden en cesur maceranıza kadar her varış noktasını vurgulayın.';

  @override
  String get posterLandingPromoBullet2 =>
      '• Her Yolculuğu Kutlayın: İlham vermek için tasarlanmış, güzelce hazırlanmış bir posterle seyahatlerinizi her gün yeniden yaşayın.';

  @override
  String get posterLandingPromoBullet3 =>
      '• Değerli Bir Hediye: Doğum günleri, dönüm noktaları veya sadece öyle olsun diye mükemmel olan, seyahatlerini sergileyen özel bir haritayla bir yol arkadaşınızı şaşırtın.';

  @override
  String get posterLandingHowItWorks => 'Nasıl Çalışır!';

  @override
  String get posterLandingHowItWorksStep1 =>
      '1. Tasarımınızı Özelleştirin: Renkleri, stilleri seçin ve seyahatlerinizi (veya onların seyahatlerini!) işaretleyin';

  @override
  String get posterLandingHowItWorksStep2 =>
      '2. Haritanızı Önizleyin: Siparişinizden önce canlandığını görün.';

  @override
  String get posterLandingHowItWorksStep3_iOS =>
      '3. Güvenli Ödeme: Apple Pay veya Stripe ile Hızlı ve güvenli.';

  @override
  String get posterLandingHowItWorksStep3_android =>
      '3. Güvenli Ödeme: Google Pay veya Stripe ile Hızlı ve güvenli. 4. Sergilenmeye Hazır: Doğrudan kapınıza (veya onların evine) göndereceğiz.';

  @override
  String get posterLandingHowItWorksStep4 =>
      '4. Sergilenmeye Hazır: Doğrudan kapınıza (veya onlarınkine) göndereceğiz.';

  @override
  String get posterLandingCustomerReviewsHeader =>
      'Diğer Gezginlerden Deneyimler';

  @override
  String get posterLandingCustomerReview1 =>
      '“Bu harita, seyahat ettiğim her yeri takip etmek ve gelecekteki gezilerimizi planlamak için harika bir yol.  Kalitesi sağlam ve ofisimde asılı harika görünüyor.  Kardeşime bile bir tane aldım ve ne kadar havalı olduğunu anlata anlata bitiremedi!” - John C.';

  @override
  String get posterLandingCustomerReview2 =>
      '“Kruvaziyerde çalışırken 150\'den fazla limana seyahat ettim. Bu harita, denizde geçirdiğim onca yılın anısı olarak oturma odama harika bir eklenti oldu.” - Betty K. ';

  @override
  String get posterLandingCustomerReview3 =>
      '“Anneler günü için harika bir hediye. Annem çok duygulandı!” Samantha W.';

  @override
  String get posterLandingCustomerReview4 =>
      '“Kız arkadaşımla ziyaret etmek istediğim yerlerin bir haritasını bastım. Harika bir Noel hediyesi oldu. Yüksek kaliteli de.” Brad J.';

  @override
  String get posterLandingSpecifications => 'Teknik Özellikler';

  @override
  String get posterLandingSpecification1 =>
      '- Boyutlar: 16“ x 20” (40,64cm x 50,8cm)';

  @override
  String get posterLandingSpecification2 => '- Oryantasyon: Manzara';

  @override
  String get posterLandingSpecification3 =>
      '- Baskı kalitesi: Hassas baskılar için mikro mürekkep, damlacıklar. 8 bit renk, neredeyse fotoğrafik baskı kalitesi.';

  @override
  String get posterLandingSpecification4 =>
      '- Kağıt: 0,22 mm kalınlığında saten kağıt';

  @override
  String get posterLandingShippingHeader => 'Nakliye Detayları';

  @override
  String get posterLandingShipping1 =>
      '- Toronto, Kanada\'dan Canada Post ile dünyanın her yerine gönderim.';

  @override
  String get posterLandingShipping2 =>
      '- Çoğu varış noktasına teslimat için 2-4 hafta bekleyin.';

  @override
  String get posterLandingShipping3 =>
      '- Tüm siparişler karton bir tüp kutuya sarılarak gönderdiğiniz teslimat adresine gönderilir.';

  @override
  String get posterLandingCancellationHeader => 'İptal/İade:';

  @override
  String get posterLandingCancellationBody =>
      'Para iadeleri, posteriniz 24 saate kadar sürebilen yazıcıya gönderilmeden önce yapılabilir.  Siparişiniz işleme alındıktan sonra iade/iptal yapılamaz.  Siparişiniz basıldığında bir e-posta alacaksınız.';

  @override
  String get unsubscribe => 'Abonelikten çık';

  @override
  String get unsubscribeConfirmMessage =>
      'Abonelikten çıkmak istediğinizden emin misiniz? Özel teklifleri ve güncellemeleri kaçıracaksınız!';

  @override
  String get updateLive => 'İkamet Güncelle';

  @override
  String get updateLiveDescription =>
      'Yaşadığınız ülkeyi değiştirmek için önce onu değiştirecek yeni bir ülke seçmelisiniz.';

  @override
  String get underOneThousand => '1.000\'in altında';

  @override
  String get oneThousandToTenThousand => '1.000 – 10.000';

  @override
  String get overTenThousand => '10.000\'in üzerinde';

  @override
  String get becomeABrandAmbassador => 'Marka Elçisi Ol';

  @override
  String get instagram => 'Instagram';

  @override
  String get tiktok => 'TikTok';

  @override
  String get youtube => 'YouTube';

  @override
  String get website => 'Web sitesi';

  @override
  String get handle => 'Kullanıcı adı';

  @override
  String get followers => 'Takipçiler';

  @override
  String get joinBrandAmbassadorProgram => 'Marka Elçisi Programına Katıl';

  @override
  String get brandAmbassadorProgramDescription =>
      'Visited\'ı seviyor musun? Marka elçisi olarak seyahat topluluğumuzu temsil edecek, haritanı ve seyahat listelerini paylaşacak ve başkalarının yeni yerleri ve uygulama özelliklerini keşfetmesine yardımcı olacaksın. Karşılığında ödüller, indirimler, hediyelikler ve daha fazlasını kazanacaksın!';

  @override
  String get fillOutTheFormToGetStarted =>
      'Başlamak için aşağıdaki formu doldurun:';

  @override
  String get yourName => 'Adınız';

  @override
  String get yourNameEmptyError => 'Lütfen adınızı girin';

  @override
  String get fillInWhereApplicable => 'Uygun olan yerleri doldurun:';

  @override
  String get otherNetworks => 'Diğer Ağlar';

  @override
  String get anythingElse => 'Başka bilmemizi istediğiniz bir şey var mı?';

  @override
  String get yourTravelsByContinent => 'Kıtaya Göre Seyahatleriniz';

  @override
  String get territories => 'Bölgeler';

  @override
  String get couponCode => 'Kupon Kodu';

  @override
  String get apply => 'Uygula';

  @override
  String get discount => 'İndirim';

  @override
  String get noCouponCode => 'Lütfen bir kupon kodu girin';

  @override
  String get invalidCouponCode => 'Geçersiz kupon kodu';

  @override
  String get couponApplied => 'Kupon uygulandı';

  @override
  String discountPercentage(double percentage) {
    return '$percentage% indirim!';
  }

  @override
  String discountAmount(double amount) {
    return '$amount indirim uygulandı!';
  }

  @override
  String get thankYou => 'Teşekkürler!';

  @override
  String get formSubmitted =>
      'Marka elçisi olmak için başvurunuzu aldık. En kısa sürede sizinle iletişime geçeceğiz!';
}
