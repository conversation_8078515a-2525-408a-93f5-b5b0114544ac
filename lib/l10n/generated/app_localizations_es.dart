// ignore: unused_import
import 'package:intl/intl.dart' as intl;
import 'app_localizations.dart';

// ignore_for_file: type=lint

/// The translations for Spanish Castilian (`es`).
class AppLocalizationsEs extends AppLocalizations {
  AppLocalizationsEs([String locale = 'es']) : super(locale);

  @override
  String get appName => 'Visited';

  @override
  String get language => 'Idioma';

  @override
  String get pickEmailApp => 'Elige tu aplicación de correo electrónico';

  @override
  String numberOfCountriesShareMessage(int amount) {
    return '¡He visitado $amount países! ¿<PERSON><PERSON><PERSON><PERSON> has visitado tú? www.visitedapp.com';
  }

  @override
  String numberOfCitiesShareMessage(int amount) {
    return '¡He visitado $amount ciudades! ¿Cuántas has visitado tú? www.visitedapp.com';
  }

  @override
  String numberOfItemsInTodoListShareMessage(int amount, String listName) {
    return '¡He visitado $amount $listName! ¿<PERSON><PERSON><PERSON><PERSON> has visitado tú? www.visitedapp.com';
  }

  @override
  String get clear => 'Despejar';

  @override
  String get been => 'Has ido';

  @override
  String get want => 'Quiero';

  @override
  String get live => 'Vivo';

  @override
  String get lived => 'Vivida';

  @override
  String get water => 'Agua';

  @override
  String get land => 'Tierra';

  @override
  String get borders => 'Fronteras';

  @override
  String get labels => 'Etiquetas';

  @override
  String get legend => 'Leyenda';

  @override
  String get inspiration => 'Inspiración';

  @override
  String get inspirations => 'Inspiraciones';

  @override
  String get delete => 'Borrar';

  @override
  String get unlockVisitedUpsellTitle => '¿Quieres ver más?';

  @override
  String get unlockVisitedUpsellSubtitle =>
      'Desbloquea todas las funciones y disfruta de Visited en toda su extensión';

  @override
  String get checkTheDetails => 'Comprueba los detalles';

  @override
  String get moreInspirationsComingSoon =>
      'Estamos trabajando para conseguir más imágenes. ¡Vuelve a comprobarlo pronto!';

  @override
  String get unlockPremiumFeatures => 'Desbloquea las funciones premium';

  @override
  String get purchased => '¡Comprado!';

  @override
  String get buy => 'Comprar';

  @override
  String get restorePurchases => 'Restaurar compra';

  @override
  String get ok => 'Ok';

  @override
  String get areYouSure => '¿Está seguro?';

  @override
  String get deleteInspirationConfirmMessage =>
      'La eliminación de esta tarjeta es permanente. No hay forma de recuperar esta imagen.';

  @override
  String get cancel => 'Cancelar';

  @override
  String get map => 'Mapa';

  @override
  String get progress => 'Progreso';

  @override
  String get myTravelGoal => 'Mi objetivo de viaje';

  @override
  String goalRemaining(int remaining) {
    return '¡$remaining más por recorrer!';
  }

  @override
  String get top => 'SUPERIOR';

  @override
  String get ofTheWorld => '¡del mundo!';

  @override
  String get countries => 'países';

  @override
  String topPlacesVisitedFromCountry(String country) {
    return 'Principales países visitados de $country:';
  }

  @override
  String get login => 'Iniciar sesión';

  @override
  String get logout => 'Cerrar sesión';

  @override
  String get enterYourEmail => 'Introduzca su correo electrónico';

  @override
  String get privacyPolicy => 'Política de privacidad';

  @override
  String get privatePolicyUrl =>
      'https://www.arrivinginhighheels.com/privacy-policy-spanish/';

  @override
  String get termsOfUse => 'Condiciones de uso';

  @override
  String get termsOfUserUrl =>
      'https://www.arrivinginhighheels.com/terms-of-use-spanish/';

  @override
  String get errorTitle => '¡Ups!';

  @override
  String get enterValidEmail =>
      'Por favor, introduzca un correo electrónico válido';

  @override
  String get settings => 'Configuración';

  @override
  String get whereDoYouLive => '¿Dónde vives?';

  @override
  String get whereHaveYouBeen => '¿Dónde has estado?';

  @override
  String get whereDoYouFlyFrom => '¿Desde dónde vuelas?';

  @override
  String get next => 'Siguiente';

  @override
  String get missingAirports =>
      '¿No ves lo que buscas? Envíenos un correo electró<NAME_EMAIL>';

  @override
  String get missingAirportsEmailTitle => 'Aeropuertos que faltan';

  @override
  String get supportEmailAddress => '<EMAIL>';

  @override
  String get welcomeTitle => 'Bienvenido a Visited';

  @override
  String get welcomeSubtitle => 'La aventura de tu vida te espera';

  @override
  String get getStarted => 'Comenzar';

  @override
  String get privacyAgreement => 'Acuerdo de privacidad';

  @override
  String get privacyAgreementSubtitle =>
      'Por favor, acepte los siguientes puntos antes de continuar utilizando Visited.';

  @override
  String get privacyAgreementTermsMarkdown =>
      'Al marcar esta casilla, reconoce haber leído y estar de acuerdo con la [Política de privacidad](https://www.arrivinginhighheels.com/privacy-policy) y las [Condiciones de uso](https://www.arrivinginhighheels.com/terms-of-use) de Arriving in High Heels.';

  @override
  String get privacyAgreementOptIn =>
      'Acepto recibir mensajes electrónicos de Arriving in High Heels que contengan información y ofertas con respecto a productos, aplicaciones y servicios que puedan ser de mi interés, incluyendo la notificación de ventas, promociones, ofertas y boletines de noticias. Puedo retirar este consentimiento en cualquier momento tal y como se describe en la Política de Privacidad o haciendo clic en el enlace \"cancelar la suscripción\" en los mensajes electrónicos.';

  @override
  String get submit => 'Enviar';

  @override
  String get companyName => 'Arriving In High Heels Corporation';

  @override
  String get companyAddress =>
      '31 Claudview Street\nKing City, Ontario\nCanada\nL7B 0C6';

  @override
  String get privacyAgreementRequired =>
      'Debe aceptar nuestros términos y optar por seguir utilizando Visited.';

  @override
  String get deleteAccount => 'Eliminar la cuenta';

  @override
  String get removeAdsUpsell =>
      '¿Desea optar por no recibir anuncios y abandonar el marketing por correo electrónico?';

  @override
  String get deleteAccountWarning =>
      '\"Al borrar tu cuenta se eliminará toda tu información de nuestros servidores. Este proceso no se puede deshacer\"';

  @override
  String get about => 'Acerca de';

  @override
  String get popularity => 'Popularidad';

  @override
  String get regions => 'Regiones';

  @override
  String get population => 'Población';

  @override
  String get size => 'Área';

  @override
  String get coverage => 'Cobertura';

  @override
  String get percentOfCountryVisited => '% del país visitado';

  @override
  String get visited => 'visitado';

  @override
  String get notes => 'Notas';

  @override
  String get kmSquared => 'km²';

  @override
  String get customize => 'Personalizar';

  @override
  String get onlyCountSovereign => 'Soberano de la ONU';

  @override
  String get countUkSeparately =>
      'Contar los países del Reino Unido por separado';

  @override
  String get showLegend => 'Mostrar leyenda';

  @override
  String get showLivedPin => 'Mostrar el pin de habtiación';

  @override
  String get useMyColours => 'Usar mis colores';

  @override
  String get mapColors => 'Colores del mapa';

  @override
  String get traveller => 'El viajero';

  @override
  String get nightTraveller => 'El viajero nocturno';

  @override
  String get original => 'El original';

  @override
  String get explorer => 'El explorador';

  @override
  String get weekender => 'El fin de semana';

  @override
  String get naturalist => 'El naturalista';

  @override
  String get historian => 'El historiador';

  @override
  String get thrillSeeker => 'El buscador de emociones';

  @override
  String get culturalBuff => 'El aficionado a la cultura';

  @override
  String get myColors => 'Mis colores';

  @override
  String get experiences => 'Experiencias';

  @override
  String get done => 'Hecho';

  @override
  String get experiencesInstructions => 'Toque el botón + para empezar.';

  @override
  String get continueText => 'Continuar';

  @override
  String get experiencesDescription => '¿Qué le gusta hacer cuando viaja?';

  @override
  String get visitedWebsiteShortLink =>
      'https://visitedapp.com/visited-aplicacion-de-viajes/';

  @override
  String get sharingHashtag => '#Visited';

  @override
  String get myTravelMap => 'Mi mapa de viajes';

  @override
  String percentOfWorldSeen(int percentage) {
    return 'He visto $percentage% del mundo';
  }

  @override
  String get requiresOnline =>
      'Lo sentimos, Visited necesita una conexión de red activa. Abre tu aplicación de ajustes y asegúrate de que la conexión Wi-Fi o uno de los símbolos de red están activados y el modo avión inhabilitado.';

  @override
  String get list => 'Lista';

  @override
  String get more => 'Más';

  @override
  String get myCountrySelections => 'Mis selecciones de países';

  @override
  String get cities => 'Ciudades';

  @override
  String get citiesInstructions =>
      'Pulse sobre cualquier país para empezar a seleccionar ciudades.';

  @override
  String get missingCitiesEmailTitle => 'Ciudades que faltan';

  @override
  String get lists => 'Liza';

  @override
  String get disputedTerritories => 'Territorios en disputa';

  @override
  String get sponsored => 'Patrocinado';

  @override
  String get places => 'Lugares';

  @override
  String get noListsError =>
      'Lo sentimos, no hay listados disponibles en este momento, por favor inténtelo más tarde.';

  @override
  String get noInspirationsError =>
      'Lo sentimos, no hay imágenes disponibles en este momento, por favor, inténtelo más tarde.';

  @override
  String get mostFrequentlyVisitedCountries =>
      'Sus países visitados con más frecuencia:';

  @override
  String get update => 'Actualizar';

  @override
  String get signup => 'Inscribirse';

  @override
  String get loginWallSubtitle =>
      'Crea una cuenta gratuita para experimentar la versión completa de Visited';

  @override
  String get loseAllSelectionsWarning =>
      'Perderá todas sus selecciones después de cerrar la aplicación.';

  @override
  String get createAccount => 'Crear cuenta';

  @override
  String get continueWithoutAccount => 'Continuar sin una cuenta';

  @override
  String get inspirationPromotion =>
      'Inspírate con una hermosa fotografía de viajes';

  @override
  String get saveStatsPromotion => '¡Guarda tus estadísticas de viaje!';

  @override
  String get selectRegionsPromotion => 'Seleccionar estados y provincias';

  @override
  String get experiencesPromotion =>
      'Seguimiento de experiencias en todo el mundo';

  @override
  String get missingListItem =>
      '¿Nos perdimos algo? Toque aquí para enviarnos un correo electrónico para agregar su lugar favorito.';

  @override
  String missingListItemEmailTitle(String list) {
    return 'El elemento faltante de $list';
  }

  @override
  String listShareMessage(Object amount, Object listName) {
    return 'He visitado $amount $listName';
  }

  @override
  String get orderPoster => 'Póster';

  @override
  String get shareMap => 'Compartir mapa';

  @override
  String get posterLandingPageTitle => 'Consigue tu póster';

  @override
  String get posterNotAvailableError =>
      'La compra de póster no está disponible en este momento. Por favor, inténtelo de nuevo más tarde.';

  @override
  String posterPricePlusShipping(Object price, Object shipping) {
    return '$price + $shipping Envío';
  }

  @override
  String get posterDescriptionMarkdownApple =>
      '## Acerca de nuestros mapas de impresión personalizados\nImprima su mapa mundial personalizado. Personaliza con tus propios colores y hazlo entregado directamente a tu hogar.\n \n### Especificaciones:\n- 16\" x 20\" (40.64cm x 50.8cm)\n- Orientación horizontal.\n- Micro tinta, gotas para impresiones precisas, color de 8 bits, casi calidad de impresión, calidad de impresión,\n- Papel de satén de 0.22 mm de espesor\n\n### Detalles de envío:\nEnvío desde Toronto, Canadá a cualquier parte del mundo usando Canada Post. Espere de 2 a 4 semanas para la entrega a la mayoría de los destinos. Todos los pedidos se envían enrollados en una caja de tubo de cartón a la dirección de envío enviada. Todo el pago se maneja por Apple Pay, o Stripe.\n\n\n### Cancelación/reembolso:\nLos pedidos se procesan inmediatamente después de ser enviados para el cambio más rápido posible. Por lo tanto, no hay reembolso/cancelación disponible.';

  @override
  String get posterDescriptionMarkdown =>
      '## Acerca de nuestros mapas de impresión personalizados\nImprima su mapa mundial personalizado. Personaliza con tus propios colores y hazlo entregado directamente a tu hogar.\n \n### Especificaciones:\n- 16\" x 20\" (40.64cm x 50.8cm)\n- Orientación horizontal.\n- Micro tinta, gotas para impresiones precisas, color de 8 bits, casi calidad de impresión, calidad de impresión,\n- Papel de satén de 0.22 mm de espesor\n\n### Detalles de envío:\nEnvío desde Toronto, Canadá a cualquier parte del mundo usando Canada Post. Espere de 2 a 4 semanas para la entrega a la mayoría de los destinos. Todos los pedidos se envían enrollados en una caja de tubo de cartón a la dirección de envío enviada. Todo el pago se maneja por Google Pay o Stripe.\n\n\n### Cancelación/reembolso:\nLos pedidos se procesan inmediatamente después de ser enviados para el cambio más rápido posible. Por lo tanto, no hay reembolso/cancelación disponible.';

  @override
  String get posterCustomizeTitle => 'Personalizar póster';

  @override
  String get enterShippingAddress => 'Ingrese la dirección de envío';

  @override
  String get price => 'Precio';

  @override
  String formattedPlusTax(Object formattedPrice) {
    return '$formattedPrice + impuesto';
  }

  @override
  String get showSelections => 'Selección de espectáculos';

  @override
  String get posterNoRefunds =>
      'No hay reembolsos disponibles después de que se haya impreso su póster.';

  @override
  String get posterReviewOrder => 'Revise su orden';

  @override
  String get email => 'Correo electrónico';

  @override
  String get emailEmptyError => 'Por favor introduzca su correo electrónico';

  @override
  String get fullName => 'Nombre completo';

  @override
  String get fullNameEmptyError => 'Por favor ingresa tu nombre completo';

  @override
  String get streetAddressEmptyError => 'Ingrese su dirección de calle';

  @override
  String get cityEmptyError => 'Por favor ingrese a su ciudad';

  @override
  String fieldEmptyError(Object fieldName) {
    return 'Por favor ingrese su $fieldName';
  }

  @override
  String get country => 'País';

  @override
  String get countryEmptyError => 'Por favor ingrese a su país';

  @override
  String get posterReviewOrderTitle => 'Revise su orden';

  @override
  String get buyNow => 'Comprar ahora';

  @override
  String get secureCheckoutDisclaimer =>
      'Deciente seguro proporcionado por Stripe';

  @override
  String get total => 'Total';

  @override
  String get tax => 'Impuesto';

  @override
  String get subtotal => 'Total parcial';

  @override
  String get posterProductName => 'Póster de mapa visitado personalizado';

  @override
  String get shipping => 'Transporte';

  @override
  String get posterOrderReceivedTitle => 'orden recibida';

  @override
  String get posterOrderReceivedSubtitle => '¡Recibimos su pedido!';

  @override
  String get posterOrderReceivedInstructionsMarkdown =>
      'Revisa tu correo electrónico para más actualizaciones. \nEspere hasta 4 semanas para que llegue su póster. \nSi tiene alguna pregunta, envíenos un correo electrónico a [<EMAIL>](<EMAIL>)';

  @override
  String get posterOrderReceivedEmailSubject =>
      'Estado de orden de póster impreso';

  @override
  String get moreInfo => 'Más información';

  @override
  String get logoutConfirm => '¿Le gustaría cerrar la sesión de la aplicación?';

  @override
  String get emailNotAvailable => 'Ese correo electrónico ha sido tomado.';

  @override
  String get alphabetical => 'Alfabético';

  @override
  String get firstTimeLiveTutorial =>
      'Proporcionar su país de origen y su ciudad personalizará la experiencia de su aplicación.';

  @override
  String get firstTimeBeenTutorial =>
      'Seleccionar dónde ha estado le permite ver su mapa de todos los países en los que ha estado y ver estadísticas personales.';

  @override
  String get progressTooltipGoal =>
      'Sus objetivos de viaje se basan en la cantidad de países que \"desea\" viajar en comparación con los países donde ha \"estado\".';

  @override
  String get progressTooltipRank =>
      'Este número muestra cómo se clasifica en comparación con los viajeros de todo el mundo. Puede aumentar su rango viajando a más países.';

  @override
  String get progressTooltipPercentageOfWorld =>
      'Este gráfico se basa en el número de países en los que ha estado en comparación con el total de países del mundo.';

  @override
  String get sortBy => 'Ordenar por';

  @override
  String get updateWishlist => 'Actualizar la lista de deseos';

  @override
  String get mapInfo =>
      'Haga clic en el país para seleccionar Been, Werry o Live. También puede hacer clic en el icono que se encuentra en la esquina superior izquierda para la vista de lista.';

  @override
  String get oneTimePurchase => '¡Todo es una compra única!';

  @override
  String get contact => 'Contacto';

  @override
  String get contactUs => 'Contáctenos';

  @override
  String get noCitiesSelected =>
      'No ha seleccionado ninguna ciudad, no aún ...';

  @override
  String get updateTravelGoal => 'Actualizar el objetivo de viaje';

  @override
  String get travelGoalComplete =>
      '¡Felicitaciones! \n\ntap el botón + para agregar más países.';

  @override
  String loginEmailNotFoundError(String email) {
    return 'No hay una cuenta asociada al correo electrónico $email. ¿Te gustaría crearlo ahora?';
  }

  @override
  String get tryAgain => 'Volver a intentarlo';

  @override
  String get itineraries => 'Planes de viaje';

  @override
  String get itinerary => 'Plan de viaje';

  @override
  String get place => 'Lugar';

  @override
  String get itinerariesDescription =>
      'Estos son los lugares por los que has expresado interés.\nUtilice esta guía para planificar sus próximas vacaciones.';

  @override
  String get addMore => 'Añadir más';

  @override
  String get interests => 'Intereses';

  @override
  String get selection => 'Selección';

  @override
  String get goal => 'Meta';

  @override
  String get noItineraries => 'Sin Itinerarios';

  @override
  String get noItinerariesExplanation =>
      'Por favor, añade algunos lugares, inspiraciones o experiencias para que tus itinerarios se generen automáticamente.';

  @override
  String get clusterPins => 'Agrupar Marcadores de Mapa';

  @override
  String get toggleRegions => 'Mostrar regiones al ampliar';

  @override
  String get mapProjection => 'Proyección del mapa';

  @override
  String get mercator => 'Mercator';

  @override
  String get equirectangular => 'Equirectangular';

  @override
  String get yourTravellerType => 'Tu tipo de viajero:';

  @override
  String get yourHotelPreferences => 'Tus preferencias de hotel:';

  @override
  String get budget => 'Económico';

  @override
  String get midScale => 'Gama media';

  @override
  String get luxury => 'Lujo';

  @override
  String get noTravellerType =>
      'Añade elementos a tu lista de deseos para descubrir qué tipo de viajero eres.';

  @override
  String get unlockLived => 'Desbloquear Vivido';

  @override
  String get unlockLivedDescription =>
      '¡Selecciona en el mapa dónde has vivido anteriormente!';

  @override
  String get futureFeaturesDescription => '...y todas las funciones futuras';

  @override
  String get yourMostFrequentlyVisitedCountry =>
      'Su país visitado con más frecuencia:';

  @override
  String get departureDate => 'Fecha de salida';

  @override
  String get returnDate => 'Fecha de regreso';

  @override
  String get hotels => 'Hoteles';

  @override
  String get food => 'Comida';

  @override
  String get travelDates => 'Fechas de viaje';

  @override
  String get posterForMe => 'Para mí';

  @override
  String get posterSendGift => 'Enviar un regalo';

  @override
  String get addSelections => 'Agregar selecciones';

  @override
  String get posterType => 'Tipo de póster';

  @override
  String get help => 'Ayuda';

  @override
  String get tutorialMap =>
      'Toque un país para seleccionar: estado, deseo y vivido.';

  @override
  String get tutorialMapList =>
      'Toque el ícono de lista (esquina superior izquierda) para seleccionar por lista.';

  @override
  String get tutorialCountryDetails =>
      'Toque el país y luego \"más\" para seleccionar por región.';

  @override
  String get tutorialItems =>
      'Deslice el interruptor para elegir cómo desea seleccionar los elementos.';

  @override
  String get tutorialInspirations =>
      'Desliza el dedo hacia la derecha o hacia la izquierda para pasar a la siguiente tarjeta.';

  @override
  String get lifetime => 'De por vida';

  @override
  String get chooseYourPlan => 'Elige tu plan';

  @override
  String get requestARefund => 'Solicitar un reembolso';

  @override
  String get noPurchasesFound => 'No se encontraron compras.';

  @override
  String get noProductsAvailable => 'No hay productos disponibles';

  @override
  String get posterLandingAppBar => 'Lleva tus historias a casa';

  @override
  String get posterLandingSubHeading => 'Tus viajes, tu historia';

  @override
  String get posterLandingSubDescription =>
      'Tus viajes son más que viajes; son historias, recuerdos y logros. Convierte esos momentos inolvidables en un mapa del mundo personalizado, tan único como tus aventuras.';

  @override
  String get posterLandingPromoBullet1 =>
      '• Un mapa de tus logros: Destaca cada destino, desde tu primer gran viaje hasta tu aventura más audaz.';

  @override
  String get posterLandingPromoBullet2 =>
      '• Celebra cada viaje: Revive tus viajes a diario con un póster bellamente diseñado para inspirar.';

  @override
  String get posterLandingPromoBullet3 =>
      '• Un regalo que atesorarán: Sorprende a un compañero de viaje con un mapa personalizado que muestra su viaje, perfecto para cumpleaños, hitos o simplemente porque sí.';

  @override
  String get posterLandingHowItWorks => '¡Cómo funciona!';

  @override
  String get posterLandingHowItWorksStep1 =>
      '1. Personaliza tu diseño: Elige colores, estilos y marca tus viajes (¡o los suyos!).';

  @override
  String get posterLandingHowItWorksStep2 =>
      '2. Previsualiza tu mapa: Ve cómo cobra vida antes de realizar tu pedido.';

  @override
  String get posterLandingHowItWorksStep3_iOS =>
      '3. Pago seguro: Rápido y seguro con Apple Pay o Stripe.';

  @override
  String get posterLandingHowItWorksStep3_android =>
      '3. Pago seguro: Rápido y seguro con Google Pay o Stripe. 4. Listo para exhibir: lo enviaremos directamente a su puerta (o a la de ellos).';

  @override
  String get posterLandingHowItWorksStep4 =>
      '4. Listo para exponer: Se lo enviamos directamente a su puerta (o a la de ellos).';

  @override
  String get posterLandingCustomerReviewsHeader =>
      'Experiencias de otros viajeros';

  @override
  String get posterLandingCustomerReview1 =>
      'Este mapa es una forma fantástica de llevar un registro de todos los lugares a los que he viajado y planificar nuestros futuros viajes. La calidad es excelente y queda genial colgado en mi oficina. ¡Incluso le compré uno a mi hermano y no paraba de hablar de lo genial que es! - John C.';

  @override
  String get posterLandingCustomerReview2 =>
      'He viajado a más de 150 puertos mientras trabajaba en un crucero. Este mapa es una gran adición a mi sala de estar como recuerdo de todos los años en el mar. - Betty K.';

  @override
  String get posterLandingCustomerReview3 =>
      'Un regalo genial para el Día de la Madre. ¡Mi madre se conmovió muchísimo! Samantha W.';

  @override
  String get posterLandingCustomerReview4 =>
      'Imprimí un mapa de los lugares que quería visitar con mi novia. Fue un regalo de Navidad genial. Además, de alta calidad. Brad J.';

  @override
  String get posterLandingSpecifications => 'Especificaciones';

  @override
  String get posterLandingSpecification1 =>
      '• Dimensiones: 40,64 cm x 50,8 cm (16\" x 20\")';

  @override
  String get posterLandingSpecification2 => '• Orientación: Horizontal';

  @override
  String get posterLandingSpecification3 =>
      '• Calidad de impresión: Microtinta, gotas para impresiones precisas. Color de 8 bits, calidad de impresión casi fotográfica. • Papel: Papel satinado de 0,22 mm de grosor';

  @override
  String get posterLandingSpecification4 => 'Detalles del envío';

  @override
  String get posterLandingShippingHeader =>
      '• Envío desde Toronto, Canadá, a cualquier parte del mundo mediante Canada Post.';

  @override
  String get posterLandingShipping1 =>
      '• El plazo de entrega en la mayoría de los destinos es de 2 a 4 semanas.';

  @override
  String get posterLandingShipping2 =>
      '• Todos los pedidos se envían enrollados en una caja de cartón a la dirección de envío que indique.';

  @override
  String get posterLandingShipping3 => 'Cancelación/Reembolso:';

  @override
  String get posterLandingCancellationHeader =>
      'Los reembolsos están disponibles antes de que su póster se envíe a la imprenta, lo que puede tardar hasta 24 horas. Una vez procesado su pedido, no se realizarán reembolsos ni cancelaciones. Recibirá un correo electrónico cuando su pedido se haya impreso.';

  @override
  String get posterLandingCancellationBody =>
      'Las devoluciones se realizan antes de que el póster se envíe a la imprenta, lo que puede tardar hasta 24 horas.  Una vez procesado el pedido, no se admiten devoluciones ni cancelaciones.  Recibirá un correo electrónico cuando su pedido se haya impreso.';

  @override
  String get unsubscribe => 'Darse de baja';

  @override
  String get unsubscribeConfirmMessage =>
      '¿Estás seguro de que quieres darte de baja? ¡Te perderás ofertas exclusivas y actualizaciones!';

  @override
  String get updateLive => 'Actualizar Residencia';

  @override
  String get updateLiveDescription =>
      'Para cambiar el país en el que vives, primero debes seleccionar un nuevo país para reemplazarlo.';

  @override
  String get underOneThousand => 'Menos de 1 000';

  @override
  String get oneThousandToTenThousand => '1 000 – 10 000';

  @override
  String get overTenThousand => 'Más de 10 000';

  @override
  String get becomeABrandAmbassador => 'Conviértete en Embajador de Marca';

  @override
  String get instagram => 'Instagram';

  @override
  String get tiktok => 'TikTok';

  @override
  String get youtube => 'YouTube';

  @override
  String get website => 'Sitio web';

  @override
  String get handle => 'Usuario';

  @override
  String get followers => 'Seguidores';

  @override
  String get joinBrandAmbassadorProgram =>
      'Únete al Programa de Embajadores de Marca';

  @override
  String get brandAmbassadorProgramDescription =>
      '¿Amas Visited? Como embajador de marca, representarás nuestra comunidad viajera, mostrarás tu mapa y tus listas de viajes, y ayudarás a otros a descubrir nuevos destinos y funciones de la app. A cambio, recibirás recompensas, descuentos, swag ¡y más!';

  @override
  String get fillOutTheFormToGetStarted =>
      'Rellena el formulario a continuación para comenzar:';

  @override
  String get yourName => 'Tu nombre';

  @override
  String get yourNameEmptyError => 'Por favor ingresa tu nombre';

  @override
  String get fillInWhereApplicable => 'Completa donde corresponda:';

  @override
  String get otherNetworks => 'Otras redes';

  @override
  String get anythingElse => '¿Algo más que quieras contarnos?';

  @override
  String get yourTravelsByContinent => 'Tus viajes por continente';

  @override
  String get territories => 'Territorios';

  @override
  String get couponCode => 'Código de cupón';

  @override
  String get apply => 'Aplicar';

  @override
  String get discount => 'Descuento';

  @override
  String get noCouponCode => 'Por favor ingresa un código de cupón';

  @override
  String get invalidCouponCode => 'Código de cupón inválido';

  @override
  String get couponApplied => 'Cupón aplicado';

  @override
  String discountPercentage(double percentage) {
    return '$percentage% de descuento!';
  }

  @override
  String discountAmount(double amount) {
    return '$amount descontado!';
  }

  @override
  String get thankYou => '¡Gracias!';

  @override
  String get formSubmitted =>
      'Hemos recibido tu solicitud para convertirte en embajador de la marca. ¡Nos pondremos en contacto contigo pronto!';
}

/// The translations for Spanish Castilian, as used in Mexico (`es_MX`).
class AppLocalizationsEsMx extends AppLocalizationsEs {
  AppLocalizationsEsMx() : super('es_MX');

  @override
  String get appName => 'Visited';

  @override
  String get language => 'Idioma';

  @override
  String get pickEmailApp => 'Elige tu aplicación de correo electrónico';

  @override
  String numberOfCountriesShareMessage(int amount) {
    return '¡He visitado $amount países! ¿Cuántos has visitado tú? www.visitedapp.com';
  }

  @override
  String numberOfCitiesShareMessage(int amount) {
    return '¡He visitado $amount ciudades! ¿Cuántas has visitado tú? www.visitedapp.com';
  }

  @override
  String numberOfItemsInTodoListShareMessage(int amount, String listName) {
    return '¡He visitado $amount $listName! ¿Cuántos has visitado tú? www.visitedapp.com';
  }

  @override
  String get clear => 'Despejar';

  @override
  String get been => 'Has ido';

  @override
  String get want => 'Quiero';

  @override
  String get live => 'Vivo';

  @override
  String get lived => 'Vivida';

  @override
  String get water => 'Agua';

  @override
  String get land => 'Tierra';

  @override
  String get borders => 'Fronteras';

  @override
  String get labels => 'Etiquetas';

  @override
  String get legend => 'Leyenda';

  @override
  String get inspiration => 'Inspiración';

  @override
  String get inspirations => 'Inspiraciones';

  @override
  String get delete => 'Borrar';

  @override
  String get unlockVisitedUpsellTitle => '¿Quieres ver más?';

  @override
  String get unlockVisitedUpsellSubtitle =>
      'Desbloquea todas las funciones y disfruta de Visited en toda su extensión';

  @override
  String get checkTheDetails => 'Comprueba los detalles';

  @override
  String get moreInspirationsComingSoon =>
      'Estamos trabajando para conseguir más imágenes. ¡Vuelve a comprobarlo pronto!';

  @override
  String get unlockPremiumFeatures => 'Desbloquea las funciones premium';

  @override
  String get purchased => '¡Comprado!';

  @override
  String get buy => 'Comprar';

  @override
  String get restorePurchases => 'Restaurar compra';

  @override
  String get ok => 'Ok';

  @override
  String get areYouSure => '¿Está seguro?';

  @override
  String get deleteInspirationConfirmMessage =>
      'La eliminación de esta tarjeta es permanente. No hay forma de recuperar esta imagen.';

  @override
  String get cancel => 'Cancelar';

  @override
  String get map => 'Mapa';

  @override
  String get progress => 'Progreso';

  @override
  String get myTravelGoal => 'Mi objetivo de viaje';

  @override
  String goalRemaining(int remaining) {
    return '¡$remaining más por recorrer!';
  }

  @override
  String get top => 'SUPERIOR';

  @override
  String get ofTheWorld => '¡del mundo!';

  @override
  String get countries => 'países';

  @override
  String topPlacesVisitedFromCountry(String country) {
    return 'Principales países visitados de $country:';
  }

  @override
  String get login => 'Iniciar sesión';

  @override
  String get logout => 'Cerrar sesión';

  @override
  String get enterYourEmail => 'Introduzca su correo electrónico';

  @override
  String get privacyPolicy => 'Política de privacidad';

  @override
  String get privatePolicyUrl =>
      'https://www.arrivinginhighheels.com/privacy-policy-spanish/';

  @override
  String get termsOfUse => 'Condiciones de uso';

  @override
  String get termsOfUserUrl =>
      'https://www.arrivinginhighheels.com/terms-of-use-spanish/';

  @override
  String get errorTitle => '¡Ups!';

  @override
  String get enterValidEmail =>
      'Por favor, introduzca un correo electrónico válido';

  @override
  String get settings => 'Configuración';

  @override
  String get whereDoYouLive => '¿Dónde vives?';

  @override
  String get whereHaveYouBeen => '¿Dónde has estado?';

  @override
  String get whereDoYouFlyFrom => '¿Desde dónde vuelas?';

  @override
  String get next => 'Siguiente';

  @override
  String get missingAirports =>
      '¿No ves lo que buscas? Envíenos un correo electró<NAME_EMAIL>';

  @override
  String get missingAirportsEmailTitle => 'Aeropuertos que faltan';

  @override
  String get supportEmailAddress => '<EMAIL>';

  @override
  String get welcomeTitle => 'Bienvenido a Visited';

  @override
  String get welcomeSubtitle => 'La aventura de tu vida te espera';

  @override
  String get getStarted => 'Comenzar';

  @override
  String get privacyAgreement => 'Acuerdo de privacidad';

  @override
  String get privacyAgreementSubtitle =>
      'Por favor, acepte los siguientes puntos antes de continuar utilizando Visited.';

  @override
  String get privacyAgreementTermsMarkdown =>
      'Al marcar esta casilla, reconoce haber leído y estar de acuerdo con la [Política de privacidad](https://www.arrivinginhighheels.com/privacy-policy) y las [Condiciones de uso](https://www.arrivinginhighheels.com/terms-of-use) de Arriving in High Heels.';

  @override
  String get privacyAgreementOptIn =>
      'Acepto recibir mensajes electrónicos de Arriving in High Heels que contengan información y ofertas con respecto a productos, aplicaciones y servicios que puedan ser de mi interés, incluyendo la notificación de ventas, promociones, ofertas y boletines de noticias. Puedo retirar este consentimiento en cualquier momento tal y como se describe en la Política de Privacidad o haciendo clic en el enlace \"cancelar la suscripción\" en los mensajes electrónicos.';

  @override
  String get submit => 'Enviar';

  @override
  String get companyName => 'Arriving In High Heels Corporation';

  @override
  String get companyAddress =>
      '31 Claudview Street\nKing City, Ontario\nCanada\nL7B 0C6';

  @override
  String get privacyAgreementRequired =>
      'Debe aceptar nuestros términos y optar por seguir utilizando Visited.';

  @override
  String get deleteAccount => 'Eliminar la cuenta';

  @override
  String get removeAdsUpsell =>
      '¿Desea optar por no recibir anuncios y abandonar el marketing por correo electrónico?';

  @override
  String get deleteAccountWarning =>
      '\"Al borrar tu cuenta se eliminará toda tu información de nuestros servidores. Este proceso no se puede deshacer\"';

  @override
  String get about => 'Acerca de';

  @override
  String get popularity => 'Popularidad';

  @override
  String get regions => 'Regiones';

  @override
  String get population => 'Población';

  @override
  String get size => 'Área';

  @override
  String get coverage => 'Cobertura';

  @override
  String get percentOfCountryVisited => '% del país visitado';

  @override
  String get visited => 'visitado';

  @override
  String get notes => 'Notas';

  @override
  String get kmSquared => 'km²';

  @override
  String get customize => 'Personalizar';

  @override
  String get onlyCountSovereign => 'Soberanía reconocida por la ONU';

  @override
  String get countUkSeparately =>
      'Contar los países del Reino Unido por separado';

  @override
  String get showLegend => 'Mostrar leyenda';

  @override
  String get showLivedPin => 'Mostrar el pin de habtiación';

  @override
  String get useMyColours => 'Usar mis colores';

  @override
  String get mapColors => 'Colores del mapa';

  @override
  String get traveller => 'El viajero';

  @override
  String get nightTraveller => 'El viajero nocturno';

  @override
  String get original => 'El original';

  @override
  String get explorer => 'El explorador';

  @override
  String get weekender => 'El fin de semana';

  @override
  String get naturalist => 'El naturalista';

  @override
  String get historian => 'El historiador';

  @override
  String get thrillSeeker => 'El buscador de emociones';

  @override
  String get culturalBuff => 'El aficionado a la cultura';

  @override
  String get myColors => 'Mis colores';

  @override
  String get experiences => 'Experiencias';

  @override
  String get done => 'Hecho';

  @override
  String get experiencesInstructions => 'Toque el botón + para empezar.';

  @override
  String get continueText => 'Continuar';

  @override
  String get experiencesDescription => '¿Qué le gusta hacer cuando viaja?';

  @override
  String get visitedWebsiteShortLink =>
      'https://visitedapp.com/visited-aplicacion-de-viajes/';

  @override
  String get sharingHashtag => '#Visited';

  @override
  String get myTravelMap => 'Mi mapa de viajes';

  @override
  String percentOfWorldSeen(int percentage) {
    return 'He visto $percentage% del mundo';
  }

  @override
  String get requiresOnline =>
      'Lo sentimos, Visited necesita una conexión de red activa. Abre tu aplicación de ajustes y asegúrate de que la conexión Wi-Fi o uno de los símbolos de red están activados y el modo avión inhabilitado.';

  @override
  String get list => 'Lista';

  @override
  String get more => 'Más';

  @override
  String get myCountrySelections => 'Mis selecciones de países';

  @override
  String get cities => 'Ciudades';

  @override
  String get citiesInstructions =>
      'Pulse sobre cualquier país para empezar a seleccionar ciudades.';

  @override
  String get missingCitiesEmailTitle => 'Ciudades que faltan';

  @override
  String get lists => 'Liza';

  @override
  String get disputedTerritories => 'Territorios en disputa';

  @override
  String get sponsored => 'Patrocinado';

  @override
  String get places => 'Lugares';

  @override
  String get noListsError =>
      'Lo sentimos, no hay listados disponibles en este momento, por favor inténtelo más tarde.';

  @override
  String get noInspirationsError =>
      'Lo sentimos, no hay imágenes disponibles en este momento, por favor, inténtelo más tarde.';

  @override
  String get mostFrequentlyVisitedCountries =>
      'Sus países visitados con más frecuencia:';

  @override
  String get update => 'Actualizar';

  @override
  String get signup => 'Inscribirse';

  @override
  String get loginWallSubtitle =>
      'Crea una cuenta gratuita para experimentar la versión completa de Visited';

  @override
  String get loseAllSelectionsWarning =>
      'Perderá todas sus selecciones después de cerrar la aplicación.';

  @override
  String get createAccount => 'Crear cuenta';

  @override
  String get continueWithoutAccount => 'Continuar sin una cuenta';

  @override
  String get inspirationPromotion =>
      'Inspírate con una hermosa fotografía de viajes';

  @override
  String get saveStatsPromotion => '¡Guarda tus estadísticas de viaje!';

  @override
  String get selectRegionsPromotion => 'Seleccionar estados y provincias';

  @override
  String get experiencesPromotion =>
      'Seguimiento de experiencias en todo el mundo';

  @override
  String get missingListItem =>
      '¿Nos perdimos algo? Toque aquí para enviarnos un correo electrónico para agregar su lugar favorito.';

  @override
  String missingListItemEmailTitle(String list) {
    return 'El elemento faltante de $list';
  }

  @override
  String listShareMessage(Object amount, Object listName) {
    return 'He visitado $amount $listName';
  }

  @override
  String get orderPoster => 'Póster';

  @override
  String get shareMap => 'Compartir mapa';

  @override
  String get posterLandingPageTitle => 'Obtén tu póster';

  @override
  String get posterNotAvailableError =>
      'La compra de póster no está disponible en este momento. Por favor, inténtelo de nuevo más tarde.';

  @override
  String posterPricePlusShipping(Object price, Object shipping) {
    return '$price + $shipping Envío';
  }

  @override
  String get posterDescriptionMarkdownApple =>
      '## Acerca de nuestros mapas de impresión personalizados\nImprima su mapa mundial personalizado. Personaliza con tus propios colores y hazlo entregado directamente a tu hogar.\n \n### Especificaciones:\n- 16\" x 20\" (40.64cm x 50.8cm)\n- Orientación horizontal.\n- Micro tinta, gotas para impresiones precisas, color de 8 bits, casi calidad de impresión, calidad de impresión,\n- Papel de satén de 0.22 mm de espesor\n\n### Detalles de envío:\nEnvío desde Toronto, Canadá a cualquier parte del mundo usando Canada Post. Espere de 2 a 4 semanas para la entrega a la mayoría de los destinos. Todos los pedidos se envían enrollados en una caja de tubo de cartón a la dirección de envío enviada. Todo el pago se maneja por Apple Pay, o Stripe.\n\n\n### Cancelación/reembolso:\nLos pedidos se procesan inmediatamente después de ser enviados para el cambio más rápido posible. Por lo tanto, no hay reembolso/cancelación disponible.';

  @override
  String get posterDescriptionMarkdown =>
      '## Acerca de nuestros mapas de impresión personalizados\nImprima su mapa mundial personalizado. Personaliza con tus propios colores y hazlo entregado directamente a tu hogar.\n \n### Especificaciones:\n- 16\" x 20\" (40.64cm x 50.8cm)\n- Orientación horizontal.\n- Micro tinta, gotas para impresiones precisas, color de 8 bits, casi calidad de impresión, calidad de impresión,\n- Papel de satén de 0.22 mm de espesor\n\n### Detalles de envío:\nEnvío desde Toronto, Canadá a cualquier parte del mundo usando Canada Post. Espere de 2 a 4 semanas para la entrega a la mayoría de los destinos. Todos los pedidos se envían enrollados en una caja de tubo de cartón a la dirección de envío enviada. Todo el pago se maneja por Google Pay o Stripe.\n\n\n### Cancelación/reembolso:\nLos pedidos se procesan inmediatamente después de ser enviados para el cambio más rápido posible. Por lo tanto, no hay reembolso/cancelación disponible.';

  @override
  String get posterCustomizeTitle => 'Personalizar póster';

  @override
  String get enterShippingAddress => 'Ingrese la dirección de envío';

  @override
  String get price => 'Precio';

  @override
  String formattedPlusTax(Object formattedPrice) {
    return '$formattedPrice + impuesto';
  }

  @override
  String get showSelections => 'Selección de espectáculos';

  @override
  String get posterNoRefunds =>
      'No hay reembolsos disponibles después de que se haya impreso su póster.';

  @override
  String get posterReviewOrder => 'Revise su orden';

  @override
  String get email => 'Correo electrónico';

  @override
  String get emailEmptyError => 'Por favor introduzca su correo electrónico';

  @override
  String get fullName => 'Nombre completo';

  @override
  String get fullNameEmptyError => 'Por favor ingresa tu nombre completo';

  @override
  String get streetAddressEmptyError => 'Ingrese su dirección de calle';

  @override
  String get cityEmptyError => 'Por favor ingrese a su ciudad';

  @override
  String fieldEmptyError(Object fieldName) {
    return 'Por favor ingrese su $fieldName';
  }

  @override
  String get country => 'País';

  @override
  String get countryEmptyError => 'Por favor ingrese a su país';

  @override
  String get posterReviewOrderTitle => 'Revise su orden';

  @override
  String get buyNow => 'Comprar ahora';

  @override
  String get secureCheckoutDisclaimer =>
      'Deciente seguro proporcionado por Stripe';

  @override
  String get total => 'Total';

  @override
  String get tax => 'Impuesto';

  @override
  String get subtotal => 'Total parcial';

  @override
  String get posterProductName => 'Póster de mapa visitado personalizado';

  @override
  String get shipping => 'Transporte';

  @override
  String get posterOrderReceivedTitle => 'orden recibida';

  @override
  String get posterOrderReceivedSubtitle => '¡Recibimos su pedido!';

  @override
  String get posterOrderReceivedInstructionsMarkdown =>
      'Revisa tu correo electrónico para más actualizaciones. \nEspere hasta 4 semanas para que llegue su póster. \nSi tiene alguna pregunta, envíenos un correo electrónico a [<EMAIL>](<EMAIL>)';

  @override
  String get posterOrderReceivedEmailSubject =>
      'Estado de orden de póster impreso';

  @override
  String get moreInfo => 'Más información';

  @override
  String get logoutConfirm => '¿Le gustaría cerrar la sesión de la aplicación?';

  @override
  String get emailNotAvailable => 'Ese correo electrónico ha sido tomado.';

  @override
  String get alphabetical => 'Alfabético';

  @override
  String get firstTimeLiveTutorial =>
      'Proporcionar su país de origen y su ciudad personalizará la experiencia de su aplicación.';

  @override
  String get firstTimeBeenTutorial =>
      'Seleccionar dónde ha estado le permite ver su mapa de todos los países en los que ha estado y ver estadísticas personales.';

  @override
  String get progressTooltipGoal =>
      'Sus objetivos de viaje se basan en la cantidad de países que \"desea\" viajar en comparación con los países donde ha \"estado\".';

  @override
  String get progressTooltipRank =>
      'Este número muestra cómo se clasifica en comparación con los viajeros de todo el mundo. Puede aumentar su rango viajando a más países.';

  @override
  String get progressTooltipPercentageOfWorld =>
      'Este gráfico se basa en el número de países en los que ha estado en comparación con el total de países del mundo.';

  @override
  String get sortBy => 'Ordenar por';

  @override
  String get updateWishlist => 'Actualizar la lista de deseos';

  @override
  String get mapInfo =>
      'Haga clic en el país para seleccionar Been, Werry o Live. También puede hacer clic en el icono que se encuentra en la esquina superior izquierda para la vista de lista.';

  @override
  String get oneTimePurchase => '¡Todo es una compra única!';

  @override
  String get contact => 'Contacto';

  @override
  String get contactUs => 'Contáctenos';

  @override
  String get noCitiesSelected =>
      'No ha seleccionado ninguna ciudad, no aún ...';

  @override
  String get updateTravelGoal => 'Actualizar el objetivo de viaje';

  @override
  String get travelGoalComplete =>
      '¡Felicitaciones! \n\ntap el botón + para agregar más países.';

  @override
  String loginEmailNotFoundError(String email) {
    return 'No hay una cuenta asociada al correo electrónico $email. ¿Te gustaría crearlo ahora?';
  }

  @override
  String get tryAgain => 'Volver a intentarlo';

  @override
  String get itineraries => 'Planes de viaje';

  @override
  String get itinerary => 'Plan de viaje';

  @override
  String get place => 'Lugar';

  @override
  String get itinerariesDescription =>
      'Estos son los lugares por los que has expresado interés.\nUtilice esta guía para planificar sus próximas vacaciones.';

  @override
  String get addMore => 'Añadir más';

  @override
  String get interests => 'Intereses';

  @override
  String get selection => 'Selección';

  @override
  String get goal => 'Meta';

  @override
  String get noItineraries => 'Sin Itinerarios';

  @override
  String get noItinerariesExplanation =>
      'Por favor, agrega algunos lugares, inspiraciones o experiencias para que tus itinerarios se generen automáticamente.';

  @override
  String get clusterPins => 'Agrupar Marcadores de Mapa';

  @override
  String get toggleRegions => 'Mostrar regiones al acercar';

  @override
  String get mapProjection => 'Proyección del mapa';

  @override
  String get mercator => 'Mercator';

  @override
  String get equirectangular => 'Equirectangular';

  @override
  String get yourTravellerType => 'Tu tipo de viajero:';

  @override
  String get yourHotelPreferences => 'Tus preferencias de hotel:';

  @override
  String get budget => 'Económico';

  @override
  String get midScale => 'Gama media';

  @override
  String get luxury => 'Lujo';

  @override
  String get noTravellerType =>
      'Añade elementos a tu lista de deseos para descubrir qué tipo de viajero eres.';

  @override
  String get unlockLived => 'Desbloquear Vivido';

  @override
  String get unlockLivedDescription =>
      '¡Selecciona en el mapa dónde has vivido anteriormente!';

  @override
  String get futureFeaturesDescription => '...y todas las funciones futuras';

  @override
  String get yourMostFrequentlyVisitedCountry =>
      'Su país visitado con más frecuencia:';

  @override
  String get departureDate => 'Fecha de salida';

  @override
  String get returnDate => 'Fecha de regreso';

  @override
  String get hotels => 'Hoteles';

  @override
  String get food => 'Comida';

  @override
  String get travelDates => 'Fechas de viaje';

  @override
  String get posterForMe => 'Para mí';

  @override
  String get posterSendGift => 'Enviar un regalo';

  @override
  String get addSelections => 'Agregar selecciones';

  @override
  String get posterType => 'Tipo de póster';

  @override
  String get help => 'Ayuda';

  @override
  String get tutorialMap =>
      'Toque un país para seleccionar: estado, deseo y vivido.';

  @override
  String get tutorialMapList =>
      'Toque el ícono de lista (esquina superior izquierda) para seleccionar por lista.';

  @override
  String get tutorialCountryDetails =>
      'Toque el país y luego \"más\" para seleccionar por región.';

  @override
  String get tutorialItems =>
      'Deslice el interruptor para elegir cómo desea seleccionar los elementos.';

  @override
  String get tutorialInspirations =>
      'Desliza el dedo hacia la derecha o hacia la izquierda para pasar a la siguiente tarjeta.';

  @override
  String get lifetime => 'De por vida';

  @override
  String get chooseYourPlan => 'Elige tu plan';

  @override
  String get requestARefund => 'Solicitar un reembolso';

  @override
  String get noPurchasesFound => 'No se encontraron compras.';

  @override
  String get noProductsAvailable => 'No hay productos disponibles';

  @override
  String get posterLandingAppBar => 'Lleva tus historias a casa';

  @override
  String get posterLandingSubHeading => 'Tus viajes, tu historia';

  @override
  String get posterLandingSubDescription =>
      'Tus viajes son más que viajes; son historias, recuerdos y logros. Convierte esos momentos inolvidables en un mapa del mundo personalizado, tan único como tus aventuras.';

  @override
  String get posterLandingPromoBullet1 =>
      '• Un mapa de tus logros: Destaca cada destino, desde tu primer gran viaje hasta tu aventura más audaz.';

  @override
  String get posterLandingPromoBullet2 =>
      '• Celebra cada viaje: Revive tus viajes a diario con un póster bellamente diseñado para inspirar.';

  @override
  String get posterLandingPromoBullet3 =>
      '• Un regalo que atesorarán: Sorprende a un compañero de viaje con un mapa personalizado que muestra su viaje, perfecto para cumpleaños, hitos o simplemente porque sí.';

  @override
  String get posterLandingHowItWorks => '¡Cómo funciona!';

  @override
  String get posterLandingHowItWorksStep1 =>
      '1. Personaliza tu diseño: Elige colores, estilos y marca tus viajes (¡o los suyos!).';

  @override
  String get posterLandingHowItWorksStep2 =>
      '2. Previsualiza tu mapa: Ve cómo cobra vida antes de realizar tu pedido.';

  @override
  String get posterLandingHowItWorksStep3_iOS =>
      '3. Pago seguro: Rápido y seguro con Apple Pay o Stripe.';

  @override
  String get posterLandingHowItWorksStep3_android =>
      '3. Pago seguro: Rápido y seguro con Google Pay o Stripe. 4. Listo para exhibir: lo enviaremos directamente a su puerta (o a la de ellos).';

  @override
  String get posterLandingHowItWorksStep4 =>
      '4. Listo para exponer: Se lo enviamos directamente a su puerta (o a la de ellos).';

  @override
  String get posterLandingCustomerReviewsHeader =>
      'Experiencias de otros viajeros';

  @override
  String get posterLandingCustomerReview1 =>
      'Este mapa es una forma fantástica de llevar un registro de todos los lugares a los que he viajado y planificar nuestros futuros viajes. La calidad es excelente y queda genial colgado en mi oficina. ¡Incluso le compré uno a mi hermano y no paraba de hablar de lo genial que es! - John C.';

  @override
  String get posterLandingCustomerReview2 =>
      'He viajado a más de 150 puertos mientras trabajaba en un crucero. Este mapa es una gran adición a mi sala de estar como recuerdo de todos los años en el mar. - Betty K.';

  @override
  String get posterLandingCustomerReview3 =>
      'Un regalo genial para el Día de la Madre. ¡Mi madre se conmovió muchísimo! Samantha W.';

  @override
  String get posterLandingCustomerReview4 =>
      'Imprimí un mapa de los lugares que quería visitar con mi novia. Fue un regalo de Navidad genial. Además, de alta calidad. Brad J.';

  @override
  String get posterLandingSpecifications => 'Especificaciones';

  @override
  String get posterLandingSpecification1 =>
      '• Dimensiones: 40,64 cm x 50,8 cm (16\" x 20\")';

  @override
  String get posterLandingSpecification2 => '• Orientación: Horizontal';

  @override
  String get posterLandingSpecification3 =>
      '• Calidad de impresión: Microtinta, gotas para impresiones precisas. Color de 8 bits, calidad de impresión casi fotográfica. • Papel: Papel satinado de 0,22 mm de grosor';

  @override
  String get posterLandingSpecification4 => 'Detalles del envío';

  @override
  String get posterLandingShippingHeader =>
      '• Envío desde Toronto, Canadá, a cualquier parte del mundo mediante Canada Post.';

  @override
  String get posterLandingShipping1 =>
      '• El plazo de entrega en la mayoría de los destinos es de 2 a 4 semanas.';

  @override
  String get posterLandingShipping2 =>
      '• Todos los pedidos se envían enrollados en una caja de cartón a la dirección de envío que indique.';

  @override
  String get posterLandingShipping3 => 'Cancelación/Reembolso:';

  @override
  String get posterLandingCancellationHeader =>
      'Los reembolsos están disponibles antes de que su póster se envíe a la imprenta, lo que puede tardar hasta 24 horas. Una vez procesado su pedido, no se realizarán reembolsos ni cancelaciones. Recibirá un correo electrónico cuando su pedido se haya impreso.';

  @override
  String get posterLandingCancellationBody =>
      'Las devoluciones se realizan antes de que el póster se envíe a la imprenta, lo que puede tardar hasta 24 horas.  Una vez procesado el pedido, no se admiten devoluciones ni cancelaciones.  Recibirá un correo electrónico cuando su pedido se haya impreso.';

  @override
  String get unsubscribe => 'Darse de baja';

  @override
  String get unsubscribeConfirmMessage =>
      '¿Estás seguro de que quieres darte de baja? ¡Te perderás ofertas exclusivas y actualizaciones!';

  @override
  String get updateLive => 'Actualizar Residencia';

  @override
  String get updateLiveDescription =>
      'Para cambiar el país en el que vives, primero debes seleccionar un nuevo país para reemplazarlo.';

  @override
  String get underOneThousand => 'Menos de 1,000';

  @override
  String get oneThousandToTenThousand => '1,000 - 10,000';

  @override
  String get overTenThousand => 'Más de 10,000';

  @override
  String get becomeABrandAmbassador => 'Conviértete en embajador de marca';

  @override
  String get instagram => 'Instagram';

  @override
  String get tiktok => 'TikTok';

  @override
  String get youtube => 'YouTube';

  @override
  String get website => 'Sitio web';

  @override
  String get handle => 'Nombre de usuario';

  @override
  String get followers => 'Seguidores';

  @override
  String get joinBrandAmbassadorProgram =>
      'Únete al programa de embajadores de marca';

  @override
  String get brandAmbassadorProgramDescription =>
      '¿Te encanta Visited? Como embajador de marca, representarás nuestra comunidad de viajes, mostrarás tu mapa y listas de viajes, y ayudarás a otros a descubrir nuevos destinos y características de la aplicación. ¡A cambio, recibirás recompensas, descuentos, productos promocionales y más!';

  @override
  String get fillOutTheFormToGetStarted =>
      'Llena el formulario a continuación para comenzar:';

  @override
  String get yourName => 'Tu nombre';

  @override
  String get yourNameEmptyError => 'Por favor ingresa tu nombre';

  @override
  String get fillInWhereApplicable => 'Llena donde sea aplicable:';

  @override
  String get otherNetworks => 'Otra(s) red(es)';

  @override
  String get anythingElse => '¿Algo más que te gustaría que supiéramos?';

  @override
  String get yourTravelsByContinent => 'Tus viajes por continente';

  @override
  String get territories => 'Territorios';

  @override
  String get couponCode => 'Código de cupón';

  @override
  String get apply => 'Aplicar';

  @override
  String get discount => 'Descuento';

  @override
  String get noCouponCode => 'Por favor ingresa un código de cupón';

  @override
  String get invalidCouponCode => 'Código de cupón inválido';

  @override
  String get couponApplied => 'Cupón aplicado';

  @override
  String discountPercentage(double percentage) {
    return '¡$percentage% de descuento!';
  }

  @override
  String discountAmount(double amount) {
    return '¡$amount de descuento!';
  }

  @override
  String get thankYou => '¡Gracias!';

  @override
  String get formSubmitted =>
      'Hemos recibido tu solicitud para ser embajador de la marca. Nos pondremos en contacto pronto.';
}
