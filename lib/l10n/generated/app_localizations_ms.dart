// ignore: unused_import
import 'package:intl/intl.dart' as intl;
import 'app_localizations.dart';

// ignore_for_file: type=lint

/// The translations for Malay (`ms`).
class AppLocalizationsMs extends AppLocalizations {
  AppLocalizationsMs([String locale = 'ms']) : super(locale);

  @override
  String get appName => 'Visited';

  @override
  String get language => 'Bahasa';

  @override
  String get pickEmailApp => 'Pilih aplikasi e-mel anda';

  @override
  String numberOfCountriesShareMessage(int amount) {
    return 'Saya telah melawat $amount negara! Berapa banyak yang anda telah lawati? www.visitedapp.com';
  }

  @override
  String numberOfCitiesShareMessage(int amount) {
    return 'Saya telah melawat $amount bandar! Berapa banyak yang anda telah lawati? www.visitedapp.com';
  }

  @override
  String numberOfItemsInTodoListShareMessage(int amount, String listName) {
    return 'Saya telah melawat $amount $listName! Berapa banyak yang anda telah lawati? www.visitedapp.com';
  }

  @override
  String get clear => 'Jelas';

  @override
  String get been => 'Telah';

  @override
  String get want => 'Mahu';

  @override
  String get live => 'Tinggal';

  @override
  String get lived => 'Duduk';

  @override
  String get water => 'Air';

  @override
  String get land => 'Tanah';

  @override
  String get borders => 'Sempadan';

  @override
  String get labels => 'Label';

  @override
  String get legend => 'Lagenda';

  @override
  String get inspiration => 'Inspirasi';

  @override
  String get inspirations => 'Inspirasi';

  @override
  String get delete => 'Padam';

  @override
  String get unlockVisitedUpsellTitle => 'Mahu melihat lebih lagi?';

  @override
  String get unlockVisitedUpsellSubtitle =>
      'Buka kunci pada semua ciri yang anda dan nikmati Visited dengan sepenuhnya';

  @override
  String get checkTheDetails => 'Periksa setiap perincian';

  @override
  String get moreInspirationsComingSoon =>
      'Kami sedang berusahan mendapatkan lebih banyak gambar lagi. Periksa semula kemudian!';

  @override
  String get unlockPremiumFeatures => 'Buka kunci pada ciri premium';

  @override
  String get purchased => 'Pembelian';

  @override
  String get buy => 'Beli';

  @override
  String get restorePurchases => 'Kembalikan Pembelian';

  @override
  String get ok => 'Ok';

  @override
  String get areYouSure => 'Adakah anda pasti?';

  @override
  String get deleteInspirationConfirmMessage =>
      'Memadam kad ini adalah kekal. Tidak mempunyai cara lain lagi untuk mendapatkan semula gambar ini';

  @override
  String get cancel => 'Membatalkan';

  @override
  String get map => 'Peta';

  @override
  String get progress => 'Kemajuan';

  @override
  String get myTravelGoal => 'Tujuan Lawatan saya';

  @override
  String goalRemaining(int remaining) {
    return '$remaining masih ada lagi!';
  }

  @override
  String get top => 'Atas';

  @override
  String get ofTheWorld => 'Dunia';

  @override
  String get countries => 'Negara';

  @override
  String topPlacesVisitedFromCountry(String country) {
    return 'Negara Paling Atas yang dikunjungi adalah dari $country:';
  }

  @override
  String get login => 'Log Masuk';

  @override
  String get logout => 'Log Keluar';

  @override
  String get enterYourEmail => 'Masukkan email anda';

  @override
  String get privacyPolicy => 'Dasar Privasi';

  @override
  String get privatePolicyUrl =>
      'https://www.arrivinginhighheels.com/privacy-policy-malay/';

  @override
  String get termsOfUse => 'Syarat - syarat penggunaan';

  @override
  String get termsOfUserUrl =>
      'https://www.arrivinginhighheels.com/terms-of-use-malay/';

  @override
  String get errorTitle => 'Opps!';

  @override
  String get enterValidEmail => 'Sila masukkan email yang sah';

  @override
  String get settings => 'Tetapan';

  @override
  String get whereDoYouLive => 'Di mana awak tinggal?';

  @override
  String get whereHaveYouBeen => 'Ke mana awak pernah pergi?';

  @override
  String get whereDoYouFlyFrom => 'Ke mana awak pernah terbang?';

  @override
  String get next => 'Seterusnya';

  @override
  String get missingAirports =>
      'Tidak dapat melihat apa yang anda cari? Hantarkan e-mel kepada <NAME_EMAIL>';

  @override
  String get missingAirportsEmailTitle => 'Terlepas Lapangan Terbang';

  @override
  String get supportEmailAddress => '<EMAIL>';

  @override
  String get welcomeTitle => 'Selamat Datang ke Visited';

  @override
  String get welcomeSubtitle => 'Pengalaman seumur hidup menanti anda';

  @override
  String get getStarted => 'Mulakan';

  @override
  String get privacyAgreement => 'Perjanjian privasi';

  @override
  String get privacyAgreementSubtitle =>
      'Sila tekan setuju dengan item berikut sebelum menggunakan Visited';

  @override
  String get privacyAgreementTermsMarkdown =>
      'Dengan menanda kotak ini, anda mengaku bahawa anda telah membaca dan bersetuju untuk terikat dengan [Tiba di High Heels \'[Policy Privacy] (https://www.arrivinginhighheels.com/privacy-policy) dan [Syarat Penggunaan] (http: //www.arrivinginhighheels.com/terms-of-use).';

  @override
  String get privacyAgreementOptIn =>
      'Saya setuju untuk menerima pesanan elektronik dari Arriving in High Heelsi yang mengandungi maklumat dan tawaran berkenaan dengan produk, aplikasi dan perkhidmatan yang mungkin menarik bagi saya, termasuk pemberitahuan penjualan, promosi, tawaran dan buletin. Saya boleh menarik balik persetujuan ini pada bila-bila masa seperti yang dijelaskan dalam Dasar Privasi atau dengan mengklik pada pautan \"berhenti melanggan\" dalam mesej elektronik.';

  @override
  String get submit => 'Hantar';

  @override
  String get companyName => 'Arriving In High Heels Corporation';

  @override
  String get companyAddress =>
      '31 Claudview Street\nKing City, Ontario\nCanada\nL7B 0C6';

  @override
  String get privacyAgreementRequired =>
      'Anda perlu bersetuju dengan kedua - dua syarat dan op untuk terus menggunakan Visited';

  @override
  String get deleteAccount => 'Padam Akaun';

  @override
  String get removeAdsUpsell =>
      'Adakan anda ingin memberhentikan iklan dan langganan pemasaran melalui email?';

  @override
  String get deleteAccountWarning =>
      'Memadam akaun anda akan membuang semua maklumat anda dari service kami. Proses ini tidak boleh dikembalikan semula';

  @override
  String get about => 'Mengenai';

  @override
  String get popularity => 'Popular';

  @override
  String get regions => 'Kawasan';

  @override
  String get population => 'Penduduk';

  @override
  String get size => 'Saiz';

  @override
  String get coverage => 'Liputan';

  @override
  String get percentOfCountryVisited => '% negara yang dilawati';

  @override
  String get visited => 'Visited';

  @override
  String get notes => 'Nota';

  @override
  String get kmSquared => 'km²';

  @override
  String get customize => 'Sesuaikan';

  @override
  String get onlyCountSovereign => 'Negara berdaulat diiktiraf PBB';

  @override
  String get countUkSeparately => 'Negara - negara UK dikira secara berasingan';

  @override
  String get showLegend => 'Menunjukkan legenda';

  @override
  String get showLivedPin => 'Menunjukkan Lived Pin';

  @override
  String get useMyColours => 'Gunakan Warna Saya';

  @override
  String get mapColors => 'Peta berwarna';

  @override
  String get traveller => 'Pengembara';

  @override
  String get nightTraveller => 'Pengembara malam';

  @override
  String get original => 'asal';

  @override
  String get explorer => 'Penjelajah';

  @override
  String get weekender => 'Hujung minggu';

  @override
  String get naturalist => 'orang semula jadi';

  @override
  String get historian => 'Sejarahwan';

  @override
  String get thrillSeeker => 'Pencari Keseronokkan';

  @override
  String get culturalBuff => 'Penggemar pada budaya';

  @override
  String get myColors => 'Warna Saya';

  @override
  String get experiences => 'Pengalaman';

  @override
  String get done => 'Selesai';

  @override
  String get experiencesInstructions => 'Tekan Butang + untuk memulakannya!';

  @override
  String get continueText => 'Teruskan';

  @override
  String get experiencesDescription =>
      'apa yang anda mahu lakukan sewaktu mengembara?';

  @override
  String get visitedWebsiteShortLink => 'https://www.visitedapp.com';

  @override
  String get sharingHashtag => '#Visited';

  @override
  String get myTravelMap => 'Peta mengembara saya';

  @override
  String percentOfWorldSeen(int percentage) {
    return 'Saya telah melihat $percentage% dunia';
  }

  @override
  String get requiresOnline =>
      'Maaf, Visited memerlukan sambungan rangkaian yang aktif. Buka aplikasi tetapan anda dan pastikan data Wi-Fi atau Selular diaktifkan dan Mod Pesawat dinyahaktifkan';

  @override
  String get list => 'Senarai';

  @override
  String get more => 'Lagi';

  @override
  String get myCountrySelections => 'Pilihan negara saya';

  @override
  String get cities => 'Bandar';

  @override
  String get citiesInstructions =>
      'Tekan mana - mana negara untuk mula memilih bandar';

  @override
  String get missingCitiesEmailTitle => 'Bandar yang Hilang!';

  @override
  String get lists => 'Senarai';

  @override
  String get disputedTerritories => 'wilayah yang dipertikaikan';

  @override
  String get sponsored => 'Ditaja';

  @override
  String get places => 'Tempat';

  @override
  String get noListsError =>
      'Opps! Senarai ini tidak disediakan buat masa ini, sila cuba sebentar lagi';

  @override
  String get noInspirationsError =>
      'Opps, tiada gambar disediakan buat masa ini, sila cuba sebentar lagi';

  @override
  String get mostFrequentlyVisitedCountries =>
      'Negara anda yang paling kerap dikunjungi:';

  @override
  String get update => 'Kemas kini';

  @override
  String get signup => 'Daftar';

  @override
  String get loginWallSubtitle =>
      'Buat akaun percuma untuk mengalami versi penuh Visited';

  @override
  String get loseAllSelectionsWarning =>
      'Anda akan kehilangan semua pilihan anda selepas menutup aplikasi.';

  @override
  String get createAccount => 'Cipta Akaun';

  @override
  String get continueWithoutAccount => 'Teruskan tanpa Akaun';

  @override
  String get inspirationPromotion =>
      'Dapatkan inspirasi dengan fotografi perjalanan yang indah';

  @override
  String get saveStatsPromotion => 'Simpan Statistik Perjalanan Anda!';

  @override
  String get selectRegionsPromotion => 'Pilih Negeri dan Wilayah';

  @override
  String get experiencesPromotion => 'Pengalaman Trek di seluruh dunia';

  @override
  String get missingListItem =>
      'Adakah kita terlepas sesuatu? Ketik di sini untuk menghantar e -mel kepada kami untuk mendapatkan tempat kegemaran anda.';

  @override
  String missingListItemEmailTitle(String list) {
    return 'Hilang barang dari $list';
  }

  @override
  String listShareMessage(Object amount, Object listName) {
    return 'Saya telah melawat $amount $listName';
  }

  @override
  String get orderPoster => 'Poster';

  @override
  String get shareMap => 'Kongsi peta';

  @override
  String get posterLandingPageTitle => 'Dapatkan poster anda';

  @override
  String get posterNotAvailableError =>
      'Pembelian poster tidak tersedia sekarang. Sila cuba sebentar lagi.';

  @override
  String posterPricePlusShipping(Object price, Object shipping) {
    return '$price + $shipping penghantaran';
  }

  @override
  String get posterDescriptionMarkdownApple =>
      '## Mengenai peta cetakan tersuai kami\nCetak peta dunia peribadi anda. Sesuaikan dengan warna anda sendiri dan hantar terus ke rumah anda.\n \n### Spesifikasi:\n- 16\" x 20\" (40.64cm x 50.8cm)\n- Orientasi landskap.\n- Dakwat mikro, titisan untuk cetakan yang tepat, warna 8 bit, hampir berkualiti cetak foto,\n- Kertas satin tebal 0.22mm\n\n### Butiran Penghantaran:\nPenghantaran dari Toronto, Kanada ke mana sahaja di dunia menggunakan Kanada Post. Sila berikan 2 hingga 4 minggu untuk penghantaran ke kebanyakan destinasi. Semua pesanan dihantar dilancarkan dalam kotak tiub kadbod ke alamat penghantaran yang dikemukakan. Semua pembayaran dikendalikan oleh Apple Pay, atau Stripe.\n\n\n### Pembatalan/Bayaran Balik:\nPesanan diproses sebaik sahaja diserahkan untuk pemulihan yang paling cepat. Oleh itu, tiada bayaran balik/pembatalan yang tersedia.';

  @override
  String get posterDescriptionMarkdown =>
      '## Mengenai peta cetakan tersuai kami\nCetak peta dunia peribadi anda. Sesuaikan dengan warna anda sendiri dan hantar terus ke rumah anda.\n \n### Spesifikasi:\n- 16\" x 20\" (40.64cm x 50.8cm)\n- Orientasi landskap.\n- Dakwat mikro, titisan untuk cetakan yang tepat, warna 8 bit, hampir berkualiti cetak foto,\n- Kertas satin tebal 0.22mm\n\n### Butiran Penghantaran:\nPenghantaran dari Toronto, Kanada ke mana sahaja di dunia menggunakan Kanada Post. Sila berikan 2 hingga 4 minggu untuk penghantaran ke kebanyakan destinasi. Semua pesanan dihantar dilancarkan dalam kotak tiub kadbod ke alamat penghantaran yang dikemukakan. Semua pembayaran dikendalikan oleh Google Pay atau Stripe.\n\n\n### Pembatalan/Bayaran Balik:\nPesanan diproses sebaik sahaja diserahkan untuk pemulihan yang paling cepat. Oleh itu, tiada bayaran balik/pembatalan yang tersedia.';

  @override
  String get posterCustomizeTitle => 'Sesuaikan poster';

  @override
  String get enterShippingAddress => 'Masukkan alamat penghantaran';

  @override
  String get price => 'Harga';

  @override
  String formattedPlusTax(Object formattedPrice) {
    return '$formattedPrice + Cukai';
  }

  @override
  String get showSelections => 'Tunjukkan pemilihan';

  @override
  String get posterNoRefunds =>
      'Tiada bayaran balik yang tersedia selepas poster anda telah dicetak.';

  @override
  String get posterReviewOrder => 'Kaji pesanan anda';

  @override
  String get email => 'E -mel';

  @override
  String get emailEmptyError => 'Sila masukkan e -mel anda';

  @override
  String get fullName => 'Nama penuh';

  @override
  String get fullNameEmptyError => 'Sila masukkan nama penuh anda';

  @override
  String get streetAddressEmptyError => 'Sila masukkan alamat jalan anda';

  @override
  String get cityEmptyError => 'Sila masukkan bandar anda';

  @override
  String fieldEmptyError(Object fieldName) {
    return 'Sila masukkan $fieldName';
  }

  @override
  String get country => 'Negara';

  @override
  String get countryEmptyError => 'Sila masukkan negara anda';

  @override
  String get posterReviewOrderTitle => 'Kaji pesanan anda';

  @override
  String get buyNow => 'Beli sekarang';

  @override
  String get secureCheckoutDisclaimer =>
      'Checkout selamat disediakan oleh Stripe';

  @override
  String get total => 'Jumlah';

  @override
  String get tax => 'Cukai';

  @override
  String get subtotal => 'jumlah kecil';

  @override
  String get posterProductName => 'Poster peta yang dilawati tersuai';

  @override
  String get shipping => 'penghantaran';

  @override
  String get posterOrderReceivedTitle => 'Pesanan Diterima';

  @override
  String get posterOrderReceivedSubtitle => 'Kami menerima pesanan anda!';

  @override
  String get posterOrderReceivedInstructionsMarkdown =>
      'Semak e -mel anda untuk maklumat lanjut. \nSila biarkan sehingga 4 minggu untuk poster anda tiba. \nJika anda mempunyai sebarang pertanyaan, sila e -mel kepada kami di [<EMAIL>] (<EMAIL>)';

  @override
  String get posterOrderReceivedEmailSubject =>
      'Status pesanan poster bercetak';

  @override
  String get moreInfo => 'Maklumat lanjut';

  @override
  String get logoutConfirm => 'Adakah anda ingin log keluar dari aplikasi?';

  @override
  String get emailNotAvailable => 'E -mel itu telah diambil.';

  @override
  String get alphabetical => 'Abjad';

  @override
  String get firstTimeLiveTutorial =>
      'Menyediakan negara asal dan bandar anda akan memperibadikan pengalaman aplikasi anda.';

  @override
  String get firstTimeBeenTutorial =>
      'Memilih di mana anda telah membolehkan anda melihat peta anda dari semua negara yang telah anda lakukan dan melihat statistik peribadi.';

  @override
  String get progressTooltipGoal =>
      'Matlamat perjalanan anda adalah berdasarkan bilangan negara yang anda \"ingin\" perjalanan berbanding dengan negara -negara di mana anda telah \"telah\".';

  @override
  String get progressTooltipRank =>
      'Nombor ini menunjukkan bagaimana anda berpangkat berbanding dengan pelancong di seluruh dunia. Anda boleh meningkatkan pangkat anda dengan perjalanan ke lebih banyak negara.';

  @override
  String get progressTooltipPercentageOfWorld =>
      'Grafik ini didasarkan pada bilangan negara yang telah dibandingkan dengan jumlah negara di dunia.';

  @override
  String get sortBy => 'Disusun mengikut';

  @override
  String get updateWishlist => 'Kemas kini senarai keinginan';

  @override
  String get mapInfo =>
      'Klik pada negara untuk memilih, mahu atau hidup. Anda juga boleh mengklik ikon yang terdapat di sudut kiri atas untuk paparan senarai.';

  @override
  String get oneTimePurchase => 'Semuanya adalah pembelian satu kali!';

  @override
  String get contact => 'Hubungi';

  @override
  String get contactUs => 'Hubungi Kami';

  @override
  String get noCitiesSelected =>
      'Anda belum memilih mana -mana bandar, namun ...';

  @override
  String get updateTravelGoal => 'Kemas kini matlamat perjalanan';

  @override
  String get travelGoalComplete =>
      'Tahniah! \n\ny telah menyelesaikan matlamat perjalanan anda! \n\ntap butang + untuk menambah lebih banyak negara.';

  @override
  String loginEmailNotFoundError(String email) {
    return 'Tiada akaun yang berkaitan dengan e -mel $email. Adakah anda ingin menciptanya sekarang?';
  }

  @override
  String get tryAgain => 'Cuba lagi';

  @override
  String get itineraries => 'Rancangan Perjalanan';

  @override
  String get itinerary => 'Rancangan Perjalanan';

  @override
  String get place => 'tempat';

  @override
  String get itinerariesDescription =>
      'Ini adalah tempat yang anda minati.\nGunakan panduan ini untuk membantu merancang percutian anda yang seterusnya.';

  @override
  String get addMore => 'Tambah Lagi';

  @override
  String get interests => 'Kepentingan';

  @override
  String get selection => 'Pemilihan';

  @override
  String get goal => 'Matlamat';

  @override
  String get noItineraries => 'Tiada Itinerari';

  @override
  String get noItinerariesExplanation =>
      'Sila tambah beberapa tempat, inspirasi atau pengalaman untuk melihat itinerari anda dihasilkan secara automatik.';

  @override
  String get clusterPins => 'Tanda Peta Kluster';

  @override
  String get toggleRegions => 'Tunjuk Wilayah Semasa Zum';

  @override
  String get mapProjection => 'Unjuran Peta';

  @override
  String get mercator => 'Mercator';

  @override
  String get equirectangular => 'Ekuirektangular';

  @override
  String get yourTravellerType => 'Jenis pelancong anda:';

  @override
  String get yourHotelPreferences => 'Keutamaan Hotel Anda:';

  @override
  String get budget => 'Bajet';

  @override
  String get midScale => 'Pertengahan';

  @override
  String get luxury => 'Mewah';

  @override
  String get noTravellerType =>
      'Tambah item ke senarai hajat anda untuk mengetahui jenis pengembara anda.';

  @override
  String get unlockLived => 'Buka Hidup';

  @override
  String get unlockLivedDescription =>
      'Pilih di mana anda pernah tinggal di peta!';

  @override
  String get futureFeaturesDescription => '...dan semua ciri masa depan';

  @override
  String get yourMostFrequentlyVisitedCountry =>
      'Negara Yang Selalu Anda Lawati:';

  @override
  String get departureDate => 'Tarikh Berlepas';

  @override
  String get returnDate => 'Tarikh Pulang';

  @override
  String get hotels => 'Hotel';

  @override
  String get food => 'Makanan';

  @override
  String get travelDates => 'Tarikh Perjalanan';

  @override
  String get posterForMe => 'Untuk Saya';

  @override
  String get posterSendGift => 'Hantar hadiah';

  @override
  String get addSelections => 'Tambah Pilihan';

  @override
  String get posterType => 'Jenis Poster';

  @override
  String get help => 'Tolong';

  @override
  String get tutorialMap =>
      'Ketik pada negara untuk memilih: pernah, mahu dan tinggal.';

  @override
  String get tutorialMapList =>
      'Ketik pada ikon senarai (sudut kiri atas) untuk memilih mengikut senarai.';

  @override
  String get tutorialCountryDetails =>
      'Ketik pada negara dan kemudian \"lagi\" untuk memilih mengikut wilayah.';

  @override
  String get tutorialItems =>
      'Luncurkan togol untuk memilih cara anda mahu memilih item.';

  @override
  String get tutorialInspirations =>
      'Leret ke kanan atau kiri untuk beralih ke kad seterusnya.';

  @override
  String get lifetime => 'Seumur hidup';

  @override
  String get chooseYourPlan => 'Pilih pelan anda';

  @override
  String get requestARefund => 'Minta bayaran balik';

  @override
  String get noPurchasesFound => 'Tiada pembelian ditemui.';

  @override
  String get noProductsAvailable => 'Tiada produk tersedia';

  @override
  String get posterLandingAppBar => 'Bawa Cerita Anda Ke Rumah';

  @override
  String get posterLandingSubHeading => 'Perjalanan Anda, Kisah Anda';

  @override
  String get posterLandingSubDescription =>
      'Perjalanan anda lebih daripada perjalanan, ia adalah cerita, kenangan dan peristiwa penting. Tukar detik-detik yang tidak dapat dilupakan itu menjadi peta dunia yang diperibadikan yang unik seperti pengembaraan anda.';

  @override
  String get posterLandingPromoBullet1 =>
      '• Peta Pencapaian Anda: Serlahkan setiap destinasi, dari perjalanan besar pertama anda hingga pengembaraan paling berani anda.';

  @override
  String get posterLandingPromoBullet2 =>
      '• Raikan Setiap Perjalanan: Hidupkan semula perjalanan anda setiap hari dengan postere yang direka dengan cantik yang direka untuk memberi inspirasi.';

  @override
  String get posterLandingPromoBullet3 =>
      '• Hadiah yang Akan Mereka Hargai: Kejutkan rakan pengembara dengan peta tersuai yang mempamerkan perjalanan mereka, sesuai untuk hari lahir, peristiwa penting atau hanya kerana.';

  @override
  String get posterLandingHowItWorks => 'Bagaimana ia Berfungsi!';

  @override
  String get posterLandingHowItWorksStep1 =>
      '1. Sesuaikan Reka Bentuk Anda: Pilih warna, gaya dan tandai perjalanan anda (atau mereka!)';

  @override
  String get posterLandingHowItWorksStep2 =>
      '2. Pratonton Peta Anda: Lihat ia dihidupkan sebelum pesanan anda.';

  @override
  String get posterLandingHowItWorksStep3_iOS =>
      '3. Pembayaran Selamat: Pantas Dan selamat dengan Apple Pay atau Stripe.';

  @override
  String get posterLandingHowItWorksStep3_android =>
      '3. Pembayaran Selamat: Pantas dan selamat dengan Google Pay atau Stripe.';

  @override
  String get posterLandingHowItWorksStep4 =>
      '4. Sedia untuk Paparan: Kami akan menghantarnya terus ke pintu anda (atau pintu mereka).';

  @override
  String get posterLandingCustomerReviewsHeader =>
      'Pengalaman daripada Rakan Pengembara';

  @override
  String get posterLandingCustomerReview1 =>
      'Peta ini adalah cara yang bagus untuk menjejaki ke mana-mana yang saya telah pergi dan merancang perjalanan masa hadapan kami. Kualitinya mantap dan ia kelihatan hebat tergantung di pejabat saya. Saya juga mendapat satu untuk abang saya dan dia tidak dapat, berhenti bercakap tentang betapa hebatnya! - John C.';

  @override
  String get posterLandingCustomerReview2 =>
      'Saya telah mengembara ke lebih 150 pelabuhan semasa bekerja di pelayaran. Peta ini merupakan tambahan yang bagus untuk ruang tamu saya sebagai kenangan sepanjang tahun di laut. - Betty K.';

  @override
  String get posterLandingCustomerReview3 =>
      'Hadiah hebat untuk hari ibu. Ibu saya sangat terharu! Samantha W.';

  @override
  String get posterLandingCustomerReview4 =>
      'Mencetak peta tempat yang saya ingin lawati bersama gf saya. Ia adalah hadiah Krismas yang hebat. Berkualiti tinggi juga. Brad J.';

  @override
  String get posterLandingSpecifications => 'Spesifikasi';

  @override
  String get posterLandingSpecification1 =>
      '• Dimensi: 16\" x 20\" (40.64cm x 50.8cm)';

  @override
  String get posterLandingSpecification2 => '• Orientasi: Landskap';

  @override
  String get posterLandingSpecification3 =>
      '• Kualiti Cetakan: Dakwat mikro, titisan untuk cetakan yang tepat.  Warna 8 bit, hampir kualiti cetakan foto.';

  @override
  String get posterLandingSpecification4 =>
      '• Kertas: Kertas satin setebal 0.22mm';

  @override
  String get posterLandingShippingHeader => 'Butiran Penghantaran';

  @override
  String get posterLandingShipping1 =>
      '• Penghantaran dari Toronto, Kanada ke mana-mana sahaja di dunia menggunakan Canada Post.';

  @override
  String get posterLandingShipping2 =>
      '• Berikan 2-4 minggu untuk penghantaran ke kebanyakan destinasi.';

  @override
  String get posterLandingShipping3 =>
      '• Semua pesanan digulung dalam kotak tiub kadbod ke alamat penghantaran yang anda serahkan.';

  @override
  String get posterLandingCancellationHeader => 'Pembatalan/Bayaran Balik:';

  @override
  String get posterLandingCancellationBody =>
      'Bayaran balik tersedia sebelum poster anda dihantar ke pencetak, yang boleh mengambil masa sehingga 24 jam.  Selepas pesanan anda telah diproses, tiada bayaran balik/pembatalan tersedia.  Anda akan menerima e-mel apabila pesanan anda telah dicetak.';

  @override
  String get unsubscribe => 'Berhenti melanggan';

  @override
  String get unsubscribeConfirmMessage =>
      'Adakah anda pasti mahu berhenti melanggan? Anda akan terlepas tawaran eksklusif dan kemas kini!';

  @override
  String get updateLive => 'Kemas Kini Tempat Tinggal';

  @override
  String get updateLiveDescription =>
      'Untuk menukar negara tempat anda tinggal, anda mesti memilih negara baru untuk menggantikannya terlebih dahulu.';

  @override
  String get underOneThousand => 'Di bawah 1,000';

  @override
  String get oneThousandToTenThousand => '1,000 – 10,000';

  @override
  String get overTenThousand => 'Lebih daripada 10,000';

  @override
  String get becomeABrandAmbassador => 'Jadi Duta Jenama';

  @override
  String get instagram => 'Instagram';

  @override
  String get tiktok => 'TikTok';

  @override
  String get youtube => 'YouTube';

  @override
  String get website => 'Laman web';

  @override
  String get handle => 'Nama pengguna';

  @override
  String get followers => 'Pengikut';

  @override
  String get joinBrandAmbassadorProgram => 'Sertai Program Duta Jenama';

  @override
  String get brandAmbassadorProgramDescription =>
      'Suka Visited? Sebagai duta jenama, anda akan mewakili komuniti pengembara kami, mempamerkan peta dan senarai perjalanan anda, dan membantu orang lain menemui destinasi baharu serta ciri aplikasi. Sebagai balasan, anda akan mendapat ganjaran, diskaun, barangan, dan banyak lagi!';

  @override
  String get fillOutTheFormToGetStarted => 'Isi borang di bawah untuk bermula:';

  @override
  String get yourName => 'Nama anda';

  @override
  String get yourNameEmptyError => 'Sila masukkan nama anda';

  @override
  String get fillInWhereApplicable => 'Isi jika berkaitan:';

  @override
  String get otherNetworks => 'Rangkaian lain';

  @override
  String get anythingElse => 'Ada apa-apa lagi yang anda ingin kongsikan?';

  @override
  String get yourTravelsByContinent => 'Perjalanan anda mengikut benua';

  @override
  String get territories => 'Wilayah';

  @override
  String get couponCode => 'Kod kupon';

  @override
  String get apply => 'Mohon';

  @override
  String get discount => 'Diskaun';

  @override
  String get noCouponCode => 'Sila masukkan kod kupon';

  @override
  String get invalidCouponCode => 'Kod kupon tidak sah';

  @override
  String get couponApplied => 'Kupon telah digunakan';

  @override
  String discountPercentage(double percentage) {
    return '$percentage% diskaun!';
  }

  @override
  String discountAmount(double amount) {
    return '$amount diskaun!';
  }

  @override
  String get thankYou => 'Terima kasih!';

  @override
  String get formSubmitted =>
      'Kami telah menerima permintaan anda untuk menjadi duta jenama. Kami akan menghubungi anda tidak lama lagi!';
}
