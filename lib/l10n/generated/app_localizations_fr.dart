// ignore: unused_import
import 'package:intl/intl.dart' as intl;
import 'app_localizations.dart';

// ignore_for_file: type=lint

/// The translations for French (`fr`).
class AppLocalizationsFr extends AppLocalizations {
  AppLocalizationsFr([String locale = 'fr']) : super(locale);

  @override
  String get appName => 'Visited';

  @override
  String get language => 'Langue';

  @override
  String get pickEmailApp => 'Choisissez votre application de messagerie';

  @override
  String numberOfCountriesShareMessage(int amount) {
    return 'J\'ai visité $amount pays ! Combien en avez-vous visité ? www.visitedapp.com';
  }

  @override
  String numberOfCitiesShareMessage(int amount) {
    return 'J\'ai visité $amount villes ! Combien en avez-vous visité ? www.visitedapp.com';
  }

  @override
  String numberOfItemsInTodoListShareMessage(int amount, String listName) {
    return 'J\'ai visité $amount $listName ! Combien en avez-vous visité ? www.visitedapp.com';
  }

  @override
  String get clear => 'Effacer';

  @override
  String get been => 'Visité';

  @override
  String get want => 'Désiré';

  @override
  String get live => 'Habite ici';

  @override
  String get lived => 'Vécu';

  @override
  String get water => 'Eau';

  @override
  String get land => 'Terre';

  @override
  String get borders => 'Frontières';

  @override
  String get labels => 'Étiquettes';

  @override
  String get legend => 'Légende';

  @override
  String get inspiration => 'Inspiration';

  @override
  String get inspirations => 'Inspirations';

  @override
  String get delete => 'Supprimer';

  @override
  String get unlockVisitedUpsellTitle => 'Vous voulez en voir plus ?';

  @override
  String get unlockVisitedUpsellSubtitle =>
      'Déverrouillez toutes les fonctionnalités et profitez de Visited dans toute sa puissance.';

  @override
  String get checkTheDetails => 'Vérifiez les détails';

  @override
  String get moreInspirationsComingSoon =>
      'Nous travaillons à l\'obtention de plus d\'images. Revenez bientôt !';

  @override
  String get unlockPremiumFeatures => 'Déverrouiller les fonctions premium';

  @override
  String get purchased => 'Acheté !';

  @override
  String get buy => 'Acheter';

  @override
  String get restorePurchases => 'Rétablir l\'achat';

  @override
  String get ok => 'Ok';

  @override
  String get areYouSure => 'Vous êtes sûr ?';

  @override
  String get deleteInspirationConfirmMessage =>
      'La suppression de cette carte est permanente. Il n\'y a aucun moyen de récupérer cette image.';

  @override
  String get cancel => 'Annuler';

  @override
  String get map => 'Carte';

  @override
  String get progress => 'Progrès';

  @override
  String get myTravelGoal => 'Mon objectif de voyage';

  @override
  String goalRemaining(int remaining) {
    return '$remaining Encore un peu de chemin à parcourir !';
  }

  @override
  String get top => 'MEILLEUR';

  @override
  String get ofTheWorld => 'du monde !';

  @override
  String get countries => 'pays';

  @override
  String topPlacesVisitedFromCountry(String country) {
    return 'Principaux pays visités à partir de $country :';
  }

  @override
  String get login => 'Se connecter';

  @override
  String get logout => 'Se déconnecter';

  @override
  String get enterYourEmail => 'Entrez votre email';

  @override
  String get privacyPolicy => 'Politique de confidentialité';

  @override
  String get privatePolicyUrl =>
      'https://www.arrivinginhighheels.com/privacy-policy-french/';

  @override
  String get termsOfUse => 'Conditions d\'utilisation';

  @override
  String get termsOfUserUrl =>
      'https://www.arrivinginhighheels.com/terms-of-use-french/';

  @override
  String get errorTitle => 'Oups !';

  @override
  String get enterValidEmail => 'Veuillez entrer une adresse e-mail valide';

  @override
  String get settings => 'Configuration';

  @override
  String get whereDoYouLive => 'Où habitez-vous ?';

  @override
  String get whereHaveYouBeen => 'Où avez-vous été ?';

  @override
  String get whereDoYouFlyFrom => 'Quel est votre point de départ ?';

  @override
  String get next => 'Suivant';

  @override
  String get missingAirports =>
      'Vous ne trouvez pas ce que vous cherchez ? Envoyez-nous un courriel à <EMAIL>';

  @override
  String get missingAirportsEmailTitle => 'Aéroports manquants !';

  @override
  String get supportEmailAddress => '<EMAIL>';

  @override
  String get welcomeTitle => 'Bienvenue à Visited';

  @override
  String get welcomeSubtitle => 'L\'aventure de votre vie vous attend';

  @override
  String get getStarted => 'Commencez';

  @override
  String get privacyAgreement => 'Accord de confidentialité';

  @override
  String get privacyAgreementSubtitle =>
      'Veuillez accepter les points suivants avant de continuer à utiliser Visited.';

  @override
  String get privacyAgreementTermsMarkdown =>
      'En cochant cette case, vous reconnaissez avoir lu et accepté d\'être lié par la [Politique de confidentialité] (https://www.arrivinginhighheels.com/privacy-policy) et les [Conditions d\'utilisation] (https://www.arrivinginhighheels.com/terms-of-use) de Arriving in High Heels.';

  @override
  String get privacyAgreementOptIn =>
      'J\'accepte de recevoir des messages électroniques de Arriving in High Heels contenant des informations et des offres concernant des produits, des applications et des services susceptibles de m\'intéresser, y compris des avis de vente, des promotions, des offres et des bulletins d\'information. Je peux retirer ce consentement à tout moment, comme décrit dans la politique de confidentialité ou en cliquant sur le lien \"désinscription\" dans les messages électroniques.';

  @override
  String get submit => 'Soumettre';

  @override
  String get companyName => 'Arriving In High Heels Corporation';

  @override
  String get companyAddress =>
      '31 Claudview Street\nKing City, Ontario\nCanada\nL7B 0C6';

  @override
  String get privacyAgreementRequired =>
      'Vous devez accepter nos deux conditions d\'utilisation et opter pour l\'une d\'entre elles afin de pouvoir continuer à utiliser Visited.';

  @override
  String get deleteAccount => 'Supprimer le compte';

  @override
  String get removeAdsUpsell =>
      'Souhaitez-vous ne plus recevoir de publicités et vous désabonner du marketing par courriel ?';

  @override
  String get deleteAccountWarning =>
      '\"La suppression de votre compte entraîne la suppression de toutes vos informations de nos serveurs. Ce processus ne peut pas être inversé.\"';

  @override
  String get about => 'À propos de';

  @override
  String get popularity => 'Popularité';

  @override
  String get regions => 'Régions';

  @override
  String get population => 'Population';

  @override
  String get size => 'Taille';

  @override
  String get coverage => 'Couverture';

  @override
  String get percentOfCountryVisited => '% du pays visité';

  @override
  String get visited => 'visité';

  @override
  String get notes => 'Notes';

  @override
  String get kmSquared => 'km ²';

  @override
  String get customize => 'Personnaliser';

  @override
  String get onlyCountSovereign => 'Souverain de l’ONU';

  @override
  String get countUkSeparately => 'Comptez les pays du Royaume-Uni séparément';

  @override
  String get showLegend => 'Afficher la légende';

  @override
  String get showLivedPin => 'Afficher l\'épingle d\'habitation';

  @override
  String get useMyColours => 'Utiliser mes couleurs';

  @override
  String get mapColors => 'Couleurs de la carte';

  @override
  String get traveller => 'Le Voyageur';

  @override
  String get nightTraveller => 'Le Voyageur de nuit';

  @override
  String get original => 'L\'original';

  @override
  String get explorer => 'L\'explorateur';

  @override
  String get weekender => 'Le weekender';

  @override
  String get naturalist => 'Le naturaliste';

  @override
  String get historian => 'L\'historien';

  @override
  String get thrillSeeker => 'Le chercheur de frissons';

  @override
  String get culturalBuff => 'Le passionné de culture';

  @override
  String get myColors => 'Mes couleurs';

  @override
  String get experiences => 'Expériences';

  @override
  String get done => 'Terminé';

  @override
  String get experiencesInstructions =>
      'Appuyez sur le bouton + pour commencer !';

  @override
  String get continueText => 'Continuer';

  @override
  String get experiencesDescription =>
      'Qu\'aimez-vous faire lorsque vous voyagez ?';

  @override
  String get visitedWebsiteShortLink =>
      'https://visitedapp.com/visited-application-de-voyage/';

  @override
  String get sharingHashtag => '#Visited';

  @override
  String get myTravelMap => 'Ma carte de voyage';

  @override
  String percentOfWorldSeen(int percentage) {
    return 'J\'ai vu $percentage% du monde';
  }

  @override
  String get requiresOnline =>
      'Désolé, Visited a besoin d\'une connexion réseau active. Ouvrez votre application de configuration et assurez-vous que le Wi-Fi ou les données cellulaires sont activés et que le mode avion est désactivé.';

  @override
  String get list => 'Liste';

  @override
  String get more => 'Plus';

  @override
  String get myCountrySelections => 'Mes sélections de pays';

  @override
  String get cities => 'Villes';

  @override
  String get citiesInstructions =>
      'Tapez sur n\'importe quel pays pour commencer à sélectionner des villes.';

  @override
  String get missingCitiesEmailTitle => 'Villes manquantes !';

  @override
  String get lists => 'Listes';

  @override
  String get disputedTerritories => 'Territoires contestés';

  @override
  String get sponsored => 'Sponsorisé';

  @override
  String get places => 'Lieux';

  @override
  String get noListsError =>
      'Désolé, aucune liste n\'est disponible pour le moment, veuillez essayer un peu plus tard.';

  @override
  String get noInspirationsError =>
      'Désolé, aucune photo n\'est disponible pour le moment, veuillez essayer un peu plus tard.';

  @override
  String get mostFrequentlyVisitedCountries => 'Vos pays les plus visités :';

  @override
  String get update => 'Mettre à jour';

  @override
  String get signup => 'S\'inscrire';

  @override
  String get loginWallSubtitle =>
      'Créez un compte gratuit pour découvrir la version complète d’Visited';

  @override
  String get loseAllSelectionsWarning =>
      'Vous perdrez toutes vos sélections après avoir fermé l’application.';

  @override
  String get createAccount => 'Créer un compte';

  @override
  String get continueWithoutAccount => 'Continuer sans compte';

  @override
  String get inspirationPromotion =>
      'Inspirez-vous de belles photographies de voyage';

  @override
  String get saveStatsPromotion => 'Enregistrez vos statistiques de voyage!';

  @override
  String get selectRegionsPromotion => 'États et provinces sélectionnés';

  @override
  String get experiencesPromotion =>
      'Suivre les expériences dans le monde entier';

  @override
  String get missingListItem =>
      'Avons-nous manqué quelque chose? Appuyez ici pour nous envoyer un e-mail pour faire ajouter votre endroit préféré.';

  @override
  String missingListItemEmailTitle(String list) {
    return 'Élément manquant de $list';
  }

  @override
  String listShareMessage(Object amount, Object listName) {
    return 'J\'ai visité $amount $listName';
  }

  @override
  String get orderPoster => 'Affiche';

  @override
  String get shareMap => 'Cartographie de partage';

  @override
  String get posterLandingPageTitle => 'Obtenez votre affiche';

  @override
  String get posterNotAvailableError =>
      'L\'achat d\'affiches n\'est pas disponible pour le moment. Veuillez réessayer plus tard.';

  @override
  String posterPricePlusShipping(Object price, Object shipping) {
    return '$price + $shipping expédition';
  }

  @override
  String get posterDescriptionMarkdownApple =>
      '## À propos de nos cartes imprimées personnalisées\nImprimez votre carte du monde personnalisée. Personnalisez-le avec vos propres couleurs et faites-la livrer directement à votre maison.\n \n### Caractéristiques:\n- 16\" x 20\" (40.64cm x 50.8cm)\n- Orientation paysage.\n- Micro Ink, gouttelettes pour impressions précises, couleur 8 bits, presque photo de la qualité d\'impression,\n- Papier satiné de 0,22 mm d\'épaisseur\n\n### Les détails d\'expédition:\nExpédition de Toronto, Canada au partout dans le monde en utilisant le Post du Canada. Veuillez prévoir 2 à 4 semaines pour la livraison à la plupart des destinations. Toutes les commandes sont expédiées enroulées dans une boîte à tube en carton à l\'adresse d\'expédition soumise. Tous les paiements sont traités par Apple Pay ou Stripe.\n\n\n### Annulation / remboursement:\nLes commandes sont traitées immédiatement après avoir été soumise pour le revirement le plus rapide possible. Par conséquent, aucun remboursement / annulation n\'est disponible.';

  @override
  String get posterDescriptionMarkdown =>
      '## À propos de nos cartes imprimées personnalisées\nImprimez votre carte du monde personnalisée. Personnalisez-le avec vos propres couleurs et faites-la livrer directement à votre maison.\n \n### Caractéristiques:\n- 16\" x 20\" (40.64cm x 50.8cm)\n- Orientation paysage.\n- Micro Ink, gouttelettes pour impressions précises, couleur 8 bits, presque photo de la qualité d\'impression,\n- Papier satiné de 0,22 mm d\'épaisseur\n\n### Les détails d\'expédition:\nExpédition de Toronto, Canada au partout dans le monde en utilisant le Post du Canada. Veuillez prévoir 2 à 4 semaines pour la livraison à la plupart des destinations. Toutes les commandes sont expédiées enroulées dans une boîte à tube en carton à l\'adresse d\'expédition soumise. Tous les paiements sont traités par Google Pay ou Stripe.\n\n\n### Annulation / remboursement:\nLes commandes sont traitées immédiatement après avoir été soumise pour le revirement le plus rapide possible. Par conséquent, aucun remboursement / annulation n\'est disponible.';

  @override
  String get posterCustomizeTitle => 'Personnaliser l\'affiche';

  @override
  String get enterShippingAddress => 'Entrez l\'adresse d\'expédition';

  @override
  String get price => 'Prix';

  @override
  String formattedPlusTax(Object formattedPrice) {
    return '$formattedPrice + taxe';
  }

  @override
  String get showSelections => 'Sélection de spectacles';

  @override
  String get posterNoRefunds =>
      'Aucun remboursement n\'est disponible après l\'impression de votre affiche.';

  @override
  String get posterReviewOrder => 'Vérifiez votre commande';

  @override
  String get email => 'E-mail';

  @override
  String get emailEmptyError => 'Veuillez saisir votre e-mail';

  @override
  String get fullName => 'Nom et prénom';

  @override
  String get fullNameEmptyError => 'S\'il vous plait entrez votre nom entier';

  @override
  String get streetAddressEmptyError => 'Veuillez saisir votre adresse de rue';

  @override
  String get cityEmptyError => 'Veuillez entrer dans votre ville';

  @override
  String fieldEmptyError(Object fieldName) {
    return 'Veuillez saisir votre $fieldName';
  }

  @override
  String get country => 'Pays';

  @override
  String get countryEmptyError => 'Veuillez entrer dans votre pays';

  @override
  String get posterReviewOrderTitle => 'Vérifiez votre commande';

  @override
  String get buyNow => 'Acheter maintenant';

  @override
  String get secureCheckoutDisclaimer => 'Caisse sécurisée fournie par Stripe';

  @override
  String get total => 'Total';

  @override
  String get tax => 'Impôt';

  @override
  String get subtotal => 'Total';

  @override
  String get posterProductName => 'Affiche de carte personnalisée Visited';

  @override
  String get shipping => 'Expédition';

  @override
  String get posterOrderReceivedTitle => 'Ordre reçu';

  @override
  String get posterOrderReceivedSubtitle => 'Nous avons reçu votre commande!';

  @override
  String get posterOrderReceivedInstructionsMarkdown =>
      'Consultez votre e-mail pour plus de mises à jour.\nplétez autorisez jusqu\'à 4 semaines pour que votre affiche arrive.\nSi vous avez des questions, veuillez nous envoyer un e-mail à [<EMAIL>] (<EMAIL>)';

  @override
  String get posterOrderReceivedEmailSubject =>
      'État de la commande de l\'affiche imprimée';

  @override
  String get moreInfo => 'Plus d\'informations';

  @override
  String get logoutConfirm =>
      'Souhaitez-vous vous déconnecter de l\'application?';

  @override
  String get emailNotAvailable => 'Cet e-mail a été pris.';

  @override
  String get alphabetical => 'Alphabétique';

  @override
  String get firstTimeLiveTutorial =>
      'La fourniture de votre pays d\'origine et de votre ville personnalisera votre expérience d\'application. Soumettre votre pays d\'origine et votre ville personnalisera votre expérience d\'application.';

  @override
  String get firstTimeBeenTutorial =>
      'Sélection de l\'endroit où vous avez été permettant de visualiser votre carte de tous les pays où vous êtes allé et de voir des statistiques personnelles.Sélection de l\'endroit où vous avez été permet de visualiser votre carte de tous les pays où vous êtes allé et de voir des statistiques personnelles.';

  @override
  String get progressTooltipGoal =>
      'Vos objectifs de voyage sont basés sur le nombre de pays que vous \"souhaitez\" voyager par rapport aux pays où vous avez \"été\".';

  @override
  String get progressTooltipRank =>
      'Ce nombre montre comment vous vous classez par rapport aux voyageurs du monde entier. Vous pouvez augmenter votre rang en vous voyageant dans plus de pays. Ce nombre montre comment vous vous classez par rapport aux voyageurs du monde entier. Vous pouvez augmenter votre rang en voyageant dans plus de pays.';

  @override
  String get progressTooltipPercentageOfWorld =>
      'Ce graphique est basé sur le nombre de pays auxquels vous avez été par rapport au total des pays du monde.';

  @override
  String get sortBy => 'Trier par';

  @override
  String get updateWishlist => 'Mettre à jour la liste des souhaits';

  @override
  String get mapInfo =>
      'Cliquez sur le pays pour sélectionner, vouloir ou vivre. Vous pouvez également cliquer sur l\'icône trouvée dans le coin supérieur gauche pour la vue de la liste.';

  @override
  String get oneTimePurchase => 'Tout est un achat unique!';

  @override
  String get contact => 'Contacter';

  @override
  String get contactUs => 'Nous contacter';

  @override
  String get noCitiesSelected =>
      'Vous n\'avez pas encore sélectionné de villes, pas encore ...';

  @override
  String get updateTravelGoal => 'Mettre à jour l\'objectif de voyage';

  @override
  String get travelGoalComplete =>
      'Félicitations! \n\nyou avez complété votre objectif de voyage! \n\ntap le bouton + pour ajouter plus de pays.';

  @override
  String loginEmailNotFoundError(String email) {
    return 'Il n\'y a pas de compte associé l\'e-mail $email. Souhaitez-vous le créer maintenant?';
  }

  @override
  String get tryAgain => 'Réessayer';

  @override
  String get itineraries => 'Plans de voyage';

  @override
  String get itinerary => 'Plan de voyage';

  @override
  String get place => 'Lieu';

  @override
  String get itinerariesDescription =>
      'Il s\'agit des lieux pour lesquels vous avez manifesté de l\'intérêt.\nUtilisez ce guide pour planifier vos prochaines vacances.';

  @override
  String get addMore => 'Plus d\'informations';

  @override
  String get interests => 'Intérêts';

  @override
  String get selection => 'Sélection';

  @override
  String get goal => 'But';

  @override
  String get noItineraries => 'Aucun Itinéraire';

  @override
  String get noItinerariesExplanation =>
      'Ajoutez quelques endroits, inspirations ou expériences pour voir vos itinéraires se générer automatiquement.';

  @override
  String get clusterPins => 'Regrouper les Marqueurs de Carte';

  @override
  String get toggleRegions => 'Afficher les régions';

  @override
  String get mapProjection => 'Projection de carte';

  @override
  String get mercator => 'Mercator';

  @override
  String get equirectangular => 'Équidistante';

  @override
  String get yourTravellerType => 'Votre type de voyageur:';

  @override
  String get yourHotelPreferences => 'Vos préférences d\'hôtel:';

  @override
  String get budget => 'Économique';

  @override
  String get midScale => 'Milieu de gamme';

  @override
  String get luxury => 'Luxe';

  @override
  String get noTravellerType =>
      'Ajoutez des éléments à votre liste de souhaits pour découvrir quel type de voyageur vous êtes.';

  @override
  String get unlockLived => 'Débloquer Vécu';

  @override
  String get unlockLivedDescription =>
      'Sélectionnez sur la carte où vous avez déjà vécu!';

  @override
  String get futureFeaturesDescription =>
      '...et toutes les fonctionnalités futures';

  @override
  String get yourMostFrequentlyVisitedCountry => 'Votre pays le plus visité:';

  @override
  String get departureDate => 'Date de départ';

  @override
  String get returnDate => 'Date de retour';

  @override
  String get hotels => 'Hôtels';

  @override
  String get food => 'Nourriture';

  @override
  String get travelDates => 'Dates de voyage';

  @override
  String get posterForMe => 'Pour moi';

  @override
  String get posterSendGift => 'Envoyer un cadeau';

  @override
  String get addSelections => 'Ajouter des sélections';

  @override
  String get posterType => 'Type de poster';

  @override
  String get help => 'Aide';

  @override
  String get tutorialMap =>
      'Appuyez sur un pays pour sélectionner: été, vouloir et vécu.';

  @override
  String get tutorialMapList =>
      'Appuyez sur l\'icône de liste (coin supérieur gauche) pour sélectionner par liste.';

  @override
  String get tutorialCountryDetails =>
      'Appuyez sur le pays puis sur « plus » pour sélectionner par région.';

  @override
  String get tutorialItems =>
      'Faites glisser la bascule pour choisir la manière dont vous souhaitez sélectionner les éléments.';

  @override
  String get tutorialInspirations =>
      'Faites glisser votre doigt vers la droite ou la gauche pour passer à la carte suivante.';

  @override
  String get lifetime => 'À vie';

  @override
  String get chooseYourPlan => 'Choisissez votre plan';

  @override
  String get requestARefund => 'Demander un remboursement';

  @override
  String get noPurchasesFound => 'Aucun achat trouvé.';

  @override
  String get noProductsAvailable => 'Aucun produit disponible';

  @override
  String get posterLandingAppBar => 'Rapportez vos histoires chez vous';

  @override
  String get posterLandingSubHeading => 'Vos voyages, votre histoire';

  @override
  String get posterLandingSubDescription =>
      'Vos voyages sont bien plus que de simples voyages, ce sont des histoires, des souvenirs et des moments marquants. Transformez ces moments inoubliables en une carte du monde personnalisée, aussi unique que vos aventures.';

  @override
  String get posterLandingPromoBullet1 =>
      '• Une carte de vos réussites : Mettez en valeur chaque destination, de votre premier grand voyage à votre aventure la plus audacieuse.';

  @override
  String get posterLandingPromoBullet2 =>
      '• Célébrez chaque voyage : Revivez vos voyages au quotidien avec un poster magnifiquement conçu et inspirant.';

  @override
  String get posterLandingPromoBullet3 =>
      '• Un cadeau qu\'ils chériront : Surprenez un compagnon de voyage avec une carte personnalisée retraçant son parcours, idéale pour les anniversaires, les événements marquants ou juste pour le plaisir.';

  @override
  String get posterLandingHowItWorks => 'Comment ça marche !';

  @override
  String get posterLandingHowItWorksStep1 =>
      '1. Personnalisez votre design : Choisissez les couleurs, les styles et marquez vos voyages (ou les siens !)';

  @override
  String get posterLandingHowItWorksStep2 =>
      '2. Prévisualisez votre carte : Voyez-la prendre vie avant de commander.';

  @override
  String get posterLandingHowItWorksStep3_iOS =>
      '3. Paiement sécurisé : Rapide et sécurisé avec Apple Pay ou Stripe.';

  @override
  String get posterLandingHowItWorksStep3_android =>
      '3. Paiement sécurisé : Rapide et sécurisé avec Google Pay ou Stripe.';

  @override
  String get posterLandingHowItWorksStep4 =>
      '4. Prêt à être exposé : nous l\'expédierons directement à votre porte (ou à la leur).';

  @override
  String get posterLandingCustomerReviewsHeader => 'Expériences de voyageurs';

  @override
  String get posterLandingCustomerReview1 =>
      '« Cette carte est idéale pour suivre mes voyages et planifier nos futurs voyages. Elle est de qualité et elle est superbe accrochée à mon bureau. J\'en ai même offert une à mon frère, qui n\'arrêtait pas de dire à quel point elle est géniale ! » - John C.';

  @override
  String get posterLandingCustomerReview2 =>
      '« J\'ai visité plus de 150 ports lors de mes croisières. Cette carte est un superbe ajout à mon salon, souvenir de toutes ces années en mer. » - Betty K.';

  @override
  String get posterLandingCustomerReview3 =>
      '« Super cadeau pour la fête des Mères. Ma mère était super touchée ! » Samantha W.';

  @override
  String get posterLandingCustomerReview4 =>
      '« J\'ai imprimé une carte des endroits que je voulais visiter avec ma copine. C\'était un super cadeau de Noël. De plus, elle est de très bonne qualité. » Brad J.';

  @override
  String get posterLandingSpecifications => 'Caractéristiques';

  @override
  String get posterLandingSpecification1 => '• Dimensions : 40,64 cm x 50,8 cm';

  @override
  String get posterLandingSpecification2 => '• Orientation : Paysage';

  @override
  String get posterLandingSpecification3 =>
      '• Qualité d\'impression : Micro-encre, gouttelettes pour des impressions précises. Couleur 8 bits, qualité d\'impression proche de la photo.';

  @override
  String get posterLandingSpecification4 =>
      '• Papier : papier satiné de 0,22 mm d’épaisseur';

  @override
  String get posterLandingShippingHeader => 'Détails de livraison';

  @override
  String get posterLandingShipping1 =>
      '• Expédition depuis Toronto, au Canada, vers n’importe quelle destination dans le monde par Postes Canada.';

  @override
  String get posterLandingShipping2 =>
      '• Prévoir un délai de livraison de 2 à 4 semaines pour la plupart des destinations.';

  @override
  String get posterLandingShipping3 =>
      '• Toutes les commandes sont emballées dans un tube en carton et expédiées à l’adresse de livraison indiquée.';

  @override
  String get posterLandingCancellationHeader => 'Annulation/Remboursement :';

  @override
  String get posterLandingCancellationBody =>
      'Les remboursements sont possibles avant l’envoi de votre affiche à l’imprimeur, ce qui peut prendre jusqu’à 24 heures. Une fois votre commande traitée, aucun remboursement ni annulation ne sera possible. Vous recevrez un courriel une fois votre commande imprimée.';

  @override
  String get unsubscribe => 'Se désabonner';

  @override
  String get unsubscribeConfirmMessage =>
      'Êtes-vous sûr de vouloir vous désabonner ? Vous manquerez des offres exclusives et des mises à jour !';

  @override
  String get updateLive => 'Mettre à Jour Résidence';

  @override
  String get updateLiveDescription =>
      'Pour changer le pays dans lequel vous vivez, vous devez d\'abord sélectionner un nouveau pays pour le remplacer.';

  @override
  String get underOneThousand => 'Moins de 1 000';

  @override
  String get oneThousandToTenThousand => '1 000 – 10 000';

  @override
  String get overTenThousand => 'Plus de 10 000';

  @override
  String get becomeABrandAmbassador => 'Devenez Ambassadeur de la Marque';

  @override
  String get instagram => 'Instagram';

  @override
  String get tiktok => 'TikTok';

  @override
  String get youtube => 'YouTube';

  @override
  String get website => 'Site Web';

  @override
  String get handle => 'Identifiant';

  @override
  String get followers => 'Abonnés';

  @override
  String get joinBrandAmbassadorProgram =>
      'Rejoindre le programme d’ambassadeur de marque';

  @override
  String get brandAmbassadorProgramDescription =>
      'Vous aimez Visited ? En tant qu’ambassadeur de marque, vous représenterez notre communauté de voyageurs, présenterez votre carte et vos listes de voyages, et aiderez les autres à découvrir de nouvelles destinations et fonctionnalités de l’application. En retour, vous recevrez des récompenses, des réductions, des cadeaux et plus encore !';

  @override
  String get fillOutTheFormToGetStarted =>
      'Remplissez le formulaire ci-dessous pour commencer :';

  @override
  String get yourName => 'Votre nom';

  @override
  String get yourNameEmptyError => 'Veuillez entrer votre nom';

  @override
  String get fillInWhereApplicable => 'Remplissez là où c’est applicable :';

  @override
  String get otherNetworks => 'Autres réseaux';

  @override
  String get anythingElse => 'Autre chose que vous souhaiteriez nous dire ?';

  @override
  String get yourTravelsByContinent => 'Vos voyages par continent';

  @override
  String get territories => 'Territoires';

  @override
  String get couponCode => 'Code promo';

  @override
  String get apply => 'Appliquer';

  @override
  String get discount => 'Remise';

  @override
  String get noCouponCode => 'Veuillez entrer un code promo';

  @override
  String get invalidCouponCode => 'Code promo invalide';

  @override
  String get couponApplied => 'Coupon appliqué';

  @override
  String discountPercentage(double percentage) {
    return '$percentage% de réduction !';
  }

  @override
  String discountAmount(double amount) {
    return '$amount remisés !';
  }

  @override
  String get thankYou => 'Merci!';

  @override
  String get formSubmitted =>
      'Nous avons bien reçu votre demande pour devenir ambassadeur de la marque. Nous vous contacterons bientôt!';
}

/// The translations for French, as used in Canada (`fr_CA`).
class AppLocalizationsFrCa extends AppLocalizationsFr {
  AppLocalizationsFrCa() : super('fr_CA');

  @override
  String get appName => 'Visited';

  @override
  String get language => 'Langue';

  @override
  String get pickEmailApp => 'Choisissez votre application de messagerie';

  @override
  String numberOfCountriesShareMessage(int amount) {
    return 'J\'ai visité $amount pays ! Combien en avez-vous visité ? www.visitedapp.com';
  }

  @override
  String numberOfCitiesShareMessage(int amount) {
    return 'J\'ai visité $amount villes ! Combien en avez-vous visité ? www.visitedapp.com';
  }

  @override
  String numberOfItemsInTodoListShareMessage(int amount, String listName) {
    return 'J\'ai visité $amount $listName ! Combien en avez-vous visité ? www.visitedapp.com';
  }

  @override
  String get clear => 'Effacer';

  @override
  String get been => 'Visité';

  @override
  String get want => 'Désiré';

  @override
  String get live => 'Habite ici';

  @override
  String get lived => 'Vécu';

  @override
  String get water => 'Eau';

  @override
  String get land => 'Terre';

  @override
  String get borders => 'Frontières';

  @override
  String get labels => 'Étiquettes';

  @override
  String get legend => 'Légende';

  @override
  String get inspiration => 'Inspiration';

  @override
  String get inspirations => 'Inspirations';

  @override
  String get delete => 'Supprimer';

  @override
  String get unlockVisitedUpsellTitle => 'Vous voulez en voir plus ?';

  @override
  String get unlockVisitedUpsellSubtitle =>
      'Déverrouillez toutes les fonctionnalités et profitez de Visited dans toute sa puissance.';

  @override
  String get checkTheDetails => 'Vérifiez les détails';

  @override
  String get moreInspirationsComingSoon =>
      'Nous travaillons à l\'obtention de plus d\'images. Revenez bientôt !';

  @override
  String get unlockPremiumFeatures => 'Déverrouiller les fonctions premium';

  @override
  String get purchased => 'Acheté !';

  @override
  String get buy => 'Acheter';

  @override
  String get restorePurchases => 'Rétablir l\'achat';

  @override
  String get ok => 'Ok';

  @override
  String get areYouSure => 'Vous êtes sûr ?';

  @override
  String get deleteInspirationConfirmMessage =>
      'La suppression de cette carte est permanente. Il n\'y a aucun moyen de récupérer cette image.';

  @override
  String get cancel => 'Annuler';

  @override
  String get map => 'Carte';

  @override
  String get progress => 'Progrès';

  @override
  String get myTravelGoal => 'Mon objectif de voyage';

  @override
  String goalRemaining(int remaining) {
    return '$remaining  Encore un peu de chemin à parcourir !';
  }

  @override
  String get top => 'MEILLEUR';

  @override
  String get ofTheWorld => 'du monde !';

  @override
  String get countries => 'pays';

  @override
  String topPlacesVisitedFromCountry(String country) {
    return 'Principaux pays visités à partir de $country :';
  }

  @override
  String get login => 'Se connecter';

  @override
  String get logout => 'Se déconnecter';

  @override
  String get enterYourEmail => 'Entrez votre email';

  @override
  String get privacyPolicy => 'Politique de confidentialité';

  @override
  String get privatePolicyUrl =>
      'https://www.arrivinginhighheels.com/privacy-policy-french/';

  @override
  String get termsOfUse => 'Conditions d\'utilisation';

  @override
  String get termsOfUserUrl =>
      'https://www.arrivinginhighheels.com/terms-of-use-french/';

  @override
  String get errorTitle => 'Oups !';

  @override
  String get enterValidEmail => 'Veuillez entrer une adresse e-mail valide';

  @override
  String get settings => 'Configuration';

  @override
  String get whereDoYouLive => 'Où habitez-vous ?';

  @override
  String get whereHaveYouBeen => 'Où avez-vous été ?';

  @override
  String get whereDoYouFlyFrom => 'Quel est votre point de départ ?';

  @override
  String get next => 'Suivant';

  @override
  String get missingAirports =>
      'Vous ne trouvez pas ce que vous cherchez ? Envoyez-nous un courriel à <EMAIL>';

  @override
  String get missingAirportsEmailTitle => 'Aéroports manquants !';

  @override
  String get supportEmailAddress => '<EMAIL>';

  @override
  String get welcomeTitle => 'Bienvenue à Visited';

  @override
  String get welcomeSubtitle => 'L\'aventure de votre vie vous attend';

  @override
  String get getStarted => 'Commencez';

  @override
  String get privacyAgreement => 'Accord de confidentialité';

  @override
  String get privacyAgreementSubtitle =>
      'Veuillez accepter les points suivants avant de continuer à utiliser Visited.';

  @override
  String get privacyAgreementTermsMarkdown =>
      'En cochant cette case, vous reconnaissez avoir lu et accepté d\'être lié par la [Politique de confidentialité] (https://www.arrivinginhighheels.com/privacy-policy) et les [Conditions d\'utilisation] (https://www.arrivinginhighheels.com/terms-of-use) de Arriving in High Heels.';

  @override
  String get privacyAgreementOptIn =>
      'J\'accepte de recevoir des messages électroniques de Arriving in High Heels contenant des informations et des offres concernant des produits, des applications et des services susceptibles de m\'intéresser, y compris des avis de vente, des promotions, des offres et des bulletins d\'information. Je peux retirer ce consentement à tout moment, comme décrit dans la politique de confidentialité ou en cliquant sur le lien \"désinscription\" dans les messages électroniques.';

  @override
  String get submit => 'Soumettre';

  @override
  String get companyName => 'Arriving In High Heels Corporation';

  @override
  String get companyAddress =>
      '31 Claudview Street\nKing City, Ontario\nCanada\nL7B 0C6';

  @override
  String get privacyAgreementRequired =>
      'Vous devez accepter nos deux conditions d\'utilisation et opter pour l\'une d\'entre elles afin de pouvoir continuer à utiliser Visited.';

  @override
  String get deleteAccount => 'Supprimer le compte';

  @override
  String get removeAdsUpsell =>
      'Souhaitez-vous ne plus recevoir de publicités et vous désabonner du marketing par courriel ?';

  @override
  String get deleteAccountWarning =>
      '\"La suppression de votre compte entraîne la suppression de toutes vos informations de nos serveurs. Ce processus ne peut pas être inversé.\"';

  @override
  String get about => 'À propos de';

  @override
  String get popularity => 'Popularité';

  @override
  String get regions => 'Régions';

  @override
  String get population => 'Population';

  @override
  String get size => 'Taille';

  @override
  String get coverage => 'Couverture';

  @override
  String get percentOfCountryVisited => '% du pays visité';

  @override
  String get visited => 'visité';

  @override
  String get notes => 'Notes';

  @override
  String get kmSquared => 'km ²';

  @override
  String get customize => 'Personnaliser';

  @override
  String get onlyCountSovereign => 'Souveraineté de l’ONU';

  @override
  String get countUkSeparately => 'Comptez les pays du Royaume-Uni séparément';

  @override
  String get showLegend => 'Afficher la légende';

  @override
  String get showLivedPin => 'Afficher l\'épingle d\'habitation';

  @override
  String get useMyColours => 'Utiliser mes couleurs';

  @override
  String get mapColors => 'Couleurs de la carte';

  @override
  String get traveller => 'Le Voyageur';

  @override
  String get nightTraveller => 'Le Voyageur de nuit';

  @override
  String get original => 'L\'original';

  @override
  String get explorer => 'L\'explorateur';

  @override
  String get weekender => 'Le weekender';

  @override
  String get naturalist => 'Le naturaliste';

  @override
  String get historian => 'L\'historien';

  @override
  String get thrillSeeker => 'Le chercheur de frissons';

  @override
  String get culturalBuff => 'Le passionné de culture';

  @override
  String get myColors => 'Mes couleurs';

  @override
  String get experiences => 'Expériences';

  @override
  String get done => 'Terminé';

  @override
  String get experiencesInstructions =>
      'Appuyez sur le bouton + pour commencer !';

  @override
  String get continueText => 'Continuer';

  @override
  String get experiencesDescription =>
      'Qu\'aimez-vous faire lorsque vous voyagez ?';

  @override
  String get visitedWebsiteShortLink =>
      'https://visitedapp.com/visited-application-de-voyage/';

  @override
  String get sharingHashtag => '#Visited';

  @override
  String get myTravelMap => 'Ma carte de voyage';

  @override
  String percentOfWorldSeen(int percentage) {
    return 'J\'ai vu $percentage% du monde';
  }

  @override
  String get requiresOnline =>
      'Désolé, Visited a besoin d\'une connexion réseau active. Ouvrez votre application de configuration et assurez-vous que le Wi-Fi ou les données cellulaires sont activés et que le mode avion est désactivé.';

  @override
  String get list => 'Liste';

  @override
  String get more => 'Plus';

  @override
  String get myCountrySelections => 'Mes sélections de pays';

  @override
  String get cities => 'Villes';

  @override
  String get citiesInstructions =>
      'Tapez sur n\'importe quel pays pour commencer à sélectionner des villes.';

  @override
  String get missingCitiesEmailTitle => 'Villes manquantes !';

  @override
  String get lists => 'Listes';

  @override
  String get disputedTerritories => 'Territoires contestés';

  @override
  String get sponsored => 'Sponsorisé';

  @override
  String get places => 'Lieux';

  @override
  String get noListsError =>
      'Désolé, aucune liste n\'est disponible pour le moment, veuillez essayer un peu plus tard.';

  @override
  String get noInspirationsError =>
      'Désolé, aucune photo n\'est disponible pour le moment, veuillez essayer un peu plus tard.';

  @override
  String get mostFrequentlyVisitedCountries => 'Vos pays les plus visités :';

  @override
  String get update => 'Mettre à jour';

  @override
  String get signup => 'S\'inscrire';

  @override
  String get loginWallSubtitle =>
      'Créez un compte gratuit pour découvrir la version complète d’Visited';

  @override
  String get loseAllSelectionsWarning =>
      'Vous perdrez toutes vos sélections après avoir fermé l’application.';

  @override
  String get createAccount => 'Créer un compte';

  @override
  String get continueWithoutAccount => 'Continuer sans compte';

  @override
  String get inspirationPromotion =>
      'Inspirez-vous de belles photographies de voyage';

  @override
  String get saveStatsPromotion => 'Enregistrez vos statistiques de voyage!';

  @override
  String get selectRegionsPromotion => 'États et provinces sélectionnés';

  @override
  String get experiencesPromotion =>
      'Suivre les expériences dans le monde entier';

  @override
  String get missingListItem =>
      'Avons-nous manqué quelque chose? Appuyez ici pour nous envoyer un e-mail pour faire ajouter votre endroit préféré.';

  @override
  String missingListItemEmailTitle(String list) {
    return 'Élément manquant de $list';
  }

  @override
  String listShareMessage(Object amount, Object listName) {
    return 'J\'ai visité $amount $listName';
  }

  @override
  String get orderPoster => 'Affiche';

  @override
  String get shareMap => 'Cartographie de partage';

  @override
  String get posterLandingPageTitle => 'Obtenez votre affiche';

  @override
  String get posterNotAvailableError =>
      'L\'achat d\'affiches n\'est pas disponible pour le moment. Veuillez réessayer plus tard.';

  @override
  String posterPricePlusShipping(Object price, Object shipping) {
    return '$price + $shipping expédition';
  }

  @override
  String get posterDescriptionMarkdownApple =>
      '## À propos de nos cartes imprimées personnalisées\nImprimez votre carte du monde personnalisée. Personnalisez-le avec vos propres couleurs et faites-la livrer directement à votre maison.\n \n### Caractéristiques:\n- 16\" x 20\" (40.64cm x 50.8cm)\n- Orientation paysage.\n- Micro Ink, gouttelettes pour impressions précises, couleur 8 bits, presque photo de la qualité d\'impression,\n- Papier satiné de 0,22 mm d\'épaisseur\n\n### Les détails d\'expédition:\nExpédition de Toronto, Canada au partout dans le monde en utilisant le Post du Canada. Veuillez prévoir 2 à 4 semaines pour la livraison à la plupart des destinations. Toutes les commandes sont expédiées enroulées dans une boîte à tube en carton à l\'adresse d\'expédition soumise. Tous les paiements sont traités par Apple Pay, ou Stripe.\n\n\n### Annulation / remboursement:\nLes commandes sont traitées immédiatement après avoir été soumise pour le revirement le plus rapide possible. Par conséquent, aucun remboursement / annulation n\'est disponible.';

  @override
  String get posterDescriptionMarkdown =>
      '## À propos de nos cartes imprimées personnalisées\nImprimez votre carte du monde personnalisée. Personnalisez-le avec vos propres couleurs et faites-la livrer directement à votre maison.\n \n### Caractéristiques:\n- 16\" x 20\" (40.64cm x 50.8cm)\n- Orientation paysage.\n- Micro Ink, gouttelettes pour impressions précises, couleur 8 bits, presque photo de la qualité d\'impression,\n- Papier satiné de 0,22 mm d\'épaisseur\n\n### Les détails d\'expédition:\nExpédition de Toronto, Canada au partout dans le monde en utilisant le Post du Canada. Veuillez prévoir 2 à 4 semaines pour la livraison à la plupart des destinations. Toutes les commandes sont expédiées enroulées dans une boîte à tube en carton à l\'adresse d\'expédition soumise. Tous les paiements sont traités par Google Pay ou Stripe.\n\n\n### Annulation / remboursement:\nLes commandes sont traitées immédiatement après avoir été soumise pour le revirement le plus rapide possible. Par conséquent, aucun remboursement / annulation n\'est disponible.';

  @override
  String get posterCustomizeTitle => 'Personnaliser l\'affiche';

  @override
  String get enterShippingAddress => 'Entrez l\'adresse d\'expédition';

  @override
  String get price => 'Prix';

  @override
  String formattedPlusTax(Object formattedPrice) {
    return '$formattedPrice + taxe';
  }

  @override
  String get showSelections => 'Sélection de spectacles';

  @override
  String get posterNoRefunds =>
      'Aucun remboursement n\'est disponible après l\'impression de votre affiche.';

  @override
  String get posterReviewOrder => 'Vérifiez votre commande';

  @override
  String get email => 'E-mail';

  @override
  String get emailEmptyError => 'Veuillez saisir votre e-mail';

  @override
  String get fullName => 'Nom et prénom';

  @override
  String get fullNameEmptyError => 'S\'il vous plait entrez votre nom entier';

  @override
  String get streetAddressEmptyError => 'Veuillez saisir votre adresse de rue';

  @override
  String get cityEmptyError => 'Veuillez entrer dans votre ville';

  @override
  String fieldEmptyError(Object fieldName) {
    return 'Veuillez saisir votre $fieldName';
  }

  @override
  String get country => 'Pays';

  @override
  String get countryEmptyError => 'Veuillez entrer dans votre pays';

  @override
  String get posterReviewOrderTitle => 'Vérifiez votre commande';

  @override
  String get buyNow => 'Acheter maintenant';

  @override
  String get secureCheckoutDisclaimer => 'Caisse sécurisée fournie par Stripe';

  @override
  String get total => 'Total';

  @override
  String get tax => 'Impôt';

  @override
  String get subtotal => 'Total';

  @override
  String get posterProductName => 'Affiche de carte personnalisée Visited';

  @override
  String get shipping => 'Expédition';

  @override
  String get posterOrderReceivedTitle => 'Ordre reçu';

  @override
  String get posterOrderReceivedSubtitle => 'Nous avons reçu votre commande!';

  @override
  String get posterOrderReceivedInstructionsMarkdown =>
      'Consultez votre e-mail pour plus de mises à jour.\nplétez autorisez jusqu\'à 4 semaines pour que votre affiche arrive.\nSi vous avez des questions, veuillez nous envoyer un e-mail à [<EMAIL>] (<EMAIL>)';

  @override
  String get posterOrderReceivedEmailSubject =>
      'État de la commande de l\'affiche imprimée';

  @override
  String get moreInfo => 'Plus d\'informations';

  @override
  String get logoutConfirm =>
      'Souhaitez-vous vous déconnecter de l\'application?';

  @override
  String get emailNotAvailable => 'Cet e-mail a été pris.';

  @override
  String get alphabetical => 'Alphabétique';

  @override
  String get firstTimeLiveTutorial =>
      'La fourniture de votre pays d\'origine et de votre ville personnalisera votre expérience d\'application. Soumettre votre pays d\'origine et votre ville personnalisera votre expérience d\'application.';

  @override
  String get firstTimeBeenTutorial =>
      'Sélection de l\'endroit où vous avez été permettant de visualiser votre carte de tous les pays où vous êtes allé et de voir des statistiques personnelles.Sélection de l\'endroit où vous avez été permet de visualiser votre carte de tous les pays où vous êtes allé et de voir des statistiques personnelles.';

  @override
  String get progressTooltipGoal =>
      'Vos objectifs de voyage sont basés sur le nombre de pays que vous \"souhaitez\" voyager par rapport aux pays où vous avez \"été\".';

  @override
  String get progressTooltipRank =>
      'Ce nombre montre comment vous vous classez par rapport aux voyageurs du monde entier. Vous pouvez augmenter votre rang en vous voyageant dans plus de pays. Ce nombre montre comment vous vous classez par rapport aux voyageurs du monde entier. Vous pouvez augmenter votre rang en voyageant dans plus de pays.';

  @override
  String get progressTooltipPercentageOfWorld =>
      'Ce graphique est basé sur le nombre de pays auxquels vous avez été par rapport au total des pays du monde.';

  @override
  String get sortBy => 'Trier par';

  @override
  String get updateWishlist => 'Mettre à jour la liste des souhaits';

  @override
  String get mapInfo =>
      'Cliquez sur le pays pour sélectionner, vouloir ou vivre. Vous pouvez également cliquer sur l\'icône trouvée dans le coin supérieur gauche pour la vue de la liste.';

  @override
  String get oneTimePurchase => 'Tout est un achat unique!';

  @override
  String get contact => 'Contacter';

  @override
  String get contactUs => 'Nous contacter';

  @override
  String get noCitiesSelected =>
      'Vous n\'avez pas encore sélectionné de villes, pas encore ...';

  @override
  String get updateTravelGoal => 'Mettre à jour l\'objectif de voyage';

  @override
  String get travelGoalComplete =>
      'Félicitations! \n\nyou avez complété votre objectif de voyage! \n\ntap le bouton + pour ajouter plus de pays.';

  @override
  String loginEmailNotFoundError(String email) {
    return 'Il n\'y a pas de compte associé l\'e-mail $email. Souhaitez-vous le créer maintenant?';
  }

  @override
  String get tryAgain => 'Réessayer';

  @override
  String get itineraries => 'Plans de voyage';

  @override
  String get itinerary => 'Plan de voyage';

  @override
  String get place => 'Lieu';

  @override
  String get itinerariesDescription =>
      'Il s\'agit des lieux pour lesquels vous avez manifesté de l\'intérêt.\nUtilisez ce guide pour planifier vos prochaines vacances.';

  @override
  String get addMore => 'Plus d\'informations';

  @override
  String get interests => 'Intérêts';

  @override
  String get selection => 'Sélection';

  @override
  String get goal => 'But';

  @override
  String get noItineraries => 'Aucun Itinéraire';

  @override
  String get noItinerariesExplanation =>
      'Ajoutez quelques endroits, inspirations ou expériences pour voir vos itinéraires se générer automatiquement.';

  @override
  String get clusterPins => 'Regrouper les épingles';

  @override
  String get toggleRegions => 'Afficher les régions';

  @override
  String get mapProjection => 'Projection de carte';

  @override
  String get mercator => 'Mercator';

  @override
  String get equirectangular => 'Équidistante';

  @override
  String get yourTravellerType => 'Votre type de voyageur:';

  @override
  String get yourHotelPreferences => 'Vos préférences d\'hôtel:';

  @override
  String get budget => 'Économique';

  @override
  String get midScale => 'Milieu de gamme';

  @override
  String get luxury => 'Luxe';

  @override
  String get noTravellerType =>
      'Ajoutez des éléments à votre liste de souhaits pour découvrir quel type de voyageur vous êtes.';

  @override
  String get unlockLived => 'Débloquer Vécu';

  @override
  String get unlockLivedDescription =>
      'Sélectionnez sur la carte où vous avez déjà vécu!';

  @override
  String get futureFeaturesDescription =>
      '...et toutes les fonctionnalités futures';

  @override
  String get yourMostFrequentlyVisitedCountry => 'Votre pays le plus visitéd:';

  @override
  String get departureDate => 'Date de départ';

  @override
  String get returnDate => 'Date de retour';

  @override
  String get hotels => 'Hôtels';

  @override
  String get food => 'Nourriture';

  @override
  String get travelDates => 'Dates de voyage';

  @override
  String get posterForMe => 'Pour moi';

  @override
  String get posterSendGift => 'Envoyer un cadeau';

  @override
  String get addSelections => 'Ajouter des sélections';

  @override
  String get posterType => 'Type d\'affiche';

  @override
  String get help => 'Aide';

  @override
  String get tutorialMap =>
      'Appuyez sur un pays pour sélectionner : été, vouloir et vécu.';

  @override
  String get tutorialMapList =>
      'Appuyez sur l\'icône de liste (coin supérieur gauche) pour sélectionner par liste.';

  @override
  String get tutorialCountryDetails =>
      'Appuyez sur le pays puis sur « plus » pour sélectionner par région.';

  @override
  String get tutorialItems =>
      'Faites glisser la bascule pour choisir la manière dont vous souhaitez sélectionner les éléments.';

  @override
  String get tutorialInspirations =>
      'Faites glisser votre doigt vers la droite ou la gauche pour passer à la carte suivante.';

  @override
  String get lifetime => 'À vie';

  @override
  String get chooseYourPlan => 'Choisissez votre plan';

  @override
  String get requestARefund => 'Demander un remboursement';

  @override
  String get noPurchasesFound => 'Aucun achat trouvé.';

  @override
  String get noProductsAvailable => 'Aucun produit disponible';

  @override
  String get posterLandingAppBar => 'Rapportez vos histoires chez vous';

  @override
  String get posterLandingSubHeading => 'Vos voyages, votre histoire';

  @override
  String get posterLandingSubDescription =>
      'Vos voyages sont bien plus que de simples voyages, ce sont des histoires, des souvenirs et des moments marquants. Transformez ces moments inoubliables en une carte du monde personnalisée, aussi unique que vos aventures.';

  @override
  String get posterLandingPromoBullet1 =>
      '• Une carte de vos réussites : Mettez en valeur chaque destination, de votre premier grand voyage à votre aventure la plus audacieuse.';

  @override
  String get posterLandingPromoBullet2 =>
      '• Célébrez chaque voyage : Revivez vos voyages au quotidien avec un poster magnifiquement conçu et inspirant.';

  @override
  String get posterLandingPromoBullet3 =>
      '• Un cadeau qu\'ils chériront : Surprenez un compagnon de voyage avec une carte personnalisée retraçant son parcours, idéale pour les anniversaires, les événements marquants ou juste pour le plaisir.';

  @override
  String get posterLandingHowItWorks => 'Comment ça marche !';

  @override
  String get posterLandingHowItWorksStep1 =>
      '1. Personnalisez votre design : Choisissez les couleurs, les styles et marquez vos voyages (ou les siens !)';

  @override
  String get posterLandingHowItWorksStep2 =>
      '2. Prévisualisez votre carte : Voyez-la prendre vie avant de commander.';

  @override
  String get posterLandingHowItWorksStep3_iOS =>
      '3. Paiement sécurisé : Rapide et sécurisé avec Apple Pay ou Stripe.';

  @override
  String get posterLandingHowItWorksStep3_android =>
      '3. Paiement sécurisé : Rapide et sécurisé avec Google Pay ou Stripe.';

  @override
  String get posterLandingHowItWorksStep4 =>
      '4. Prêt à être exposé : nous l\'expédierons directement à votre porte (ou à la leur).';

  @override
  String get posterLandingCustomerReviewsHeader => 'Expériences de voyageurs';

  @override
  String get posterLandingCustomerReview1 =>
      '« Cette carte est idéale pour suivre mes voyages et planifier nos futurs voyages. Elle est de qualité et elle est superbe accrochée à mon bureau. J\'en ai même offert une à mon frère, qui n\'arrêtait pas de dire à quel point elle est géniale ! » - John C.';

  @override
  String get posterLandingCustomerReview2 =>
      '« J\'ai visité plus de 150 ports lors de mes croisières. Cette carte est un superbe ajout à mon salon, souvenir de toutes ces années en mer. » - Betty K.';

  @override
  String get posterLandingCustomerReview3 =>
      '« Super cadeau pour la fête des Mères. Ma mère était super touchée ! » Samantha W.';

  @override
  String get posterLandingCustomerReview4 =>
      '« J\'ai imprimé une carte des endroits que je voulais visiter avec ma copine. C\'était un super cadeau de Noël. De plus, elle est de très bonne qualité. » Brad J.';

  @override
  String get posterLandingSpecifications => 'Caractéristiques';

  @override
  String get posterLandingSpecification1 => '• Dimensions : 40,64 cm x 50,8 cm';

  @override
  String get posterLandingSpecification2 => '• Orientation : Paysage';

  @override
  String get posterLandingSpecification3 =>
      '• Qualité d\'impression : Micro-encre, gouttelettes pour des impressions précises. Couleur 8 bits, qualité d\'impression proche de la photo.';

  @override
  String get posterLandingSpecification4 =>
      '• Papier : papier satiné de 0,22 mm d’épaisseur';

  @override
  String get posterLandingShippingHeader => 'Détails de livraison';

  @override
  String get posterLandingShipping1 =>
      '• Expédition depuis Toronto, au Canada, vers n’importe quelle destination dans le monde par Postes Canada.';

  @override
  String get posterLandingShipping2 =>
      '• Prévoir un délai de livraison de 2 à 4 semaines pour la plupart des destinations.';

  @override
  String get posterLandingShipping3 =>
      '• Toutes les commandes sont emballées dans un tube en carton et expédiées à l’adresse de livraison indiquée.';

  @override
  String get posterLandingCancellationHeader => 'Annulation/Remboursement :';

  @override
  String get posterLandingCancellationBody =>
      'Les remboursements sont possibles avant l’envoi de votre affiche à l’imprimeur, ce qui peut prendre jusqu’à 24 heures. Une fois votre commande traitée, aucun remboursement ni annulation ne sera possible. Vous recevrez un courriel une fois votre commande imprimée.';

  @override
  String get unsubscribe => 'Se désabonner';

  @override
  String get unsubscribeConfirmMessage =>
      'Êtes-vous sûr de vouloir vous désabonner ? Vous manquerez des offres exclusives et des mises à jour !';

  @override
  String get updateLive => 'Mettre à Jour Résidence';

  @override
  String get updateLiveDescription =>
      'Pour changer le pays dans lequel vous vivez, vous devez d\'abord sélectionner un nouveau pays pour le remplacer.';

  @override
  String get underOneThousand => 'Moins de 1 000';

  @override
  String get oneThousandToTenThousand => '1 000 – 10 000';

  @override
  String get overTenThousand => 'Plus de 10 000';

  @override
  String get becomeABrandAmbassador => 'Devenez Ambassadeur de la Marque';

  @override
  String get instagram => 'Instagram';

  @override
  String get tiktok => 'TikTok';

  @override
  String get youtube => 'YouTube';

  @override
  String get website => 'Site Web';

  @override
  String get handle => 'Identifiant';

  @override
  String get followers => 'Abonnés';

  @override
  String get joinBrandAmbassadorProgram =>
      'Rejoindre le programme d’ambassadeur de marque';

  @override
  String get brandAmbassadorProgramDescription =>
      'Vous aimez Visited ? En tant qu’ambassadeur de marque, vous représenterez notre communauté de voyageurs, présenterez votre carte et vos listes de voyages, et aiderez les autres à découvrir de nouvelles destinations et fonctionnalités de l’application. En retour, vous recevrez des récompenses, des réductions, des cadeaux et plus encore !';

  @override
  String get fillOutTheFormToGetStarted =>
      'Remplissez le formulaire ci-dessous pour commencer :';

  @override
  String get yourName => 'Votre nom';

  @override
  String get yourNameEmptyError => 'Veuillez entrer votre nom';

  @override
  String get fillInWhereApplicable => 'Remplissez là où c’est applicable :';

  @override
  String get otherNetworks => 'Autres réseaux';

  @override
  String get anythingElse => 'Autre chose que vous souhaiteriez nous dire ?';

  @override
  String get yourTravelsByContinent => 'Vos voyages par continent';

  @override
  String get territories => 'Territoires';

  @override
  String get couponCode => 'Code promo';

  @override
  String get apply => 'Appliquer';

  @override
  String get discount => 'Remise';

  @override
  String get noCouponCode => 'Veuillez entrer un code promo';

  @override
  String get invalidCouponCode => 'Code promo invalide';

  @override
  String get couponApplied => 'Coupon appliqué';

  @override
  String discountPercentage(double percentage) {
    return '$percentage% de réduction !';
  }

  @override
  String discountAmount(double amount) {
    return '$amount remisés !';
  }

  @override
  String get thankYou => 'Merci!';

  @override
  String get formSubmitted =>
      'Nous avons bien reçu votre demande pour devenir ambassadeur de la marque. Nous vous contacterons bientôt!';
}
