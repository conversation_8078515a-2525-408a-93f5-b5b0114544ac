// ignore: unused_import
import 'package:intl/intl.dart' as intl;
import 'app_localizations.dart';

// ignore_for_file: type=lint

/// The translations for Catalan Valencian (`ca`).
class AppLocalizationsCa extends AppLocalizations {
  AppLocalizationsCa([String locale = 'ca']) : super(locale);

  @override
  String get appName => 'Visited';

  @override
  String get language => 'Idioma';

  @override
  String get pickEmailApp => 'Tria la teva aplicació de correu electrònic';

  @override
  String numberOfCountriesShareMessage(int amount) {
    return 'He visitat $amount països! Quants n\'has visitat tu? www.visitedapp.com';
  }

  @override
  String numberOfCitiesShareMessage(int amount) {
    return 'He visitat $amount ciutats! Quantes n\'has visitat tu? www.visitedapp.com';
  }

  @override
  String numberOfItemsInTodoListShareMessage(int amount, String listName) {
    return 'He visitat $amount $listName! Quants n\'has visitat tu? www.visitedapp.com';
  }

  @override
  String get clear => 'clar';

  @override
  String get been => 'estat';

  @override
  String get want => 'Voler';

  @override
  String get live => 'viu';

  @override
  String get lived => 'viscut';

  @override
  String get water => 'aigua';

  @override
  String get land => 'terra';

  @override
  String get borders => 'Fronteres';

  @override
  String get labels => 'etiquetes';

  @override
  String get legend => 'llegenda';

  @override
  String get inspiration => 'inspiració';

  @override
  String get inspirations => 'inspiracions';

  @override
  String get delete => 'eliminar';

  @override
  String get unlockVisitedUpsellTitle => 'Vols veure més?';

  @override
  String get unlockVisitedUpsellSubtitle =>
      'Desbloquejar totes les característiques i gaudir Visited en tota la seva força';

  @override
  String get checkTheDetails => 'Comproveu els detalls';

  @override
  String get moreInspirationsComingSoon =>
      'Estem treballant en aconseguir més imatges. Torna aviat!';

  @override
  String get unlockPremiumFeatures => 'Desbloqueja les opcions premium';

  @override
  String get purchased => 'Comprat!';

  @override
  String get buy => 'comprar';

  @override
  String get restorePurchases => 'Restaurar la compra';

  @override
  String get ok => 'D\'acord';

  @override
  String get areYouSure => 'Estàs segur?';

  @override
  String get deleteInspirationConfirmMessage =>
      'L\'eliminació d\'aquesta targeta és permanent. No hi ha manera de recuperar aquesta imatge.';

  @override
  String get cancel => 'Cancel · lar';

  @override
  String get map => 'mapa';

  @override
  String get progress => 'Progrés';

  @override
  String get myTravelGoal => 'el meu objectiu de viatge';

  @override
  String goalRemaining(int remaining) {
    return '$remaining més per anar!';
  }

  @override
  String get top => 'SUPERIOR';

  @override
  String get ofTheWorld => 'del món!';

  @override
  String get countries => 'països';

  @override
  String topPlacesVisitedFromCountry(String country) {
    return 'Top països Visited de $country:';
  }

  @override
  String get login => 'Iniciar Sessió';

  @override
  String get logout => 'Tancar sessió';

  @override
  String get enterYourEmail => 'Introdueix el teu correu electrònic';

  @override
  String get privacyPolicy => 'Política de privacitat';

  @override
  String get privatePolicyUrl =>
      'https://www.arrivinginhighheels.com/privacy-policy-spanish/';

  @override
  String get termsOfUse => 'Condicions d\'ús';

  @override
  String get termsOfUserUrl =>
      'https://www.arrivinginhighheels.com/terms-of-use-spanish/';

  @override
  String get errorTitle => 'Vaja!';

  @override
  String get enterValidEmail => 'Si us plau, introdueixi un correu vàlid';

  @override
  String get settings => 'ajustos';

  @override
  String get whereDoYouLive => 'On vius?';

  @override
  String get whereHaveYouBeen => 'On has estat?';

  @override
  String get whereDoYouFlyFrom => 'D\'on voles?';

  @override
  String get next => 'Pròxim';

  @override
  String get missingAirports =>
      'No troba el que busca? Envia un correu electrò<NAME_EMAIL>';

  @override
  String get missingAirportsEmailTitle => 'Aeroports que falten!';

  @override
  String get supportEmailAddress => '<EMAIL>';

  @override
  String get welcomeTitle => 'Benvingut a Visited';

  @override
  String get welcomeSubtitle => 'L\'aventura de la seva vida li espera';

  @override
  String get getStarted => 'Començar';

  @override
  String get privacyAgreement => 'Acord de privacitat';

  @override
  String get privacyAgreementSubtitle =>
      'Si us plau, estar d\'acord amb els següents elements abans de seguir utilitzant Visited.';

  @override
  String get privacyAgreementTermsMarkdown =>
      'A l\'marcar aquesta casella, vostè reconeix que ha llegit i accepta que quedarà vinculat amb Arriving in High Heels [Política de Privacitat] (https://www.arrivinginhighheels.com/privacy-policy) i [Condicions d\'ús] (http: //www.arrivinginhighheels.com/terms-of-use).';

  @override
  String get privacyAgreementOptIn =>
      'Estic d\'acord en rebre missatges electrònics de Arriving in High Heels que contenen informació i ofertes pel que fa a productes, aplicacions i serveis que puguin ser d\'interès per a mi, incloent la notificació de vendes, promocions, ofertes i butlletins de notícies. Puc retirar aquest consentiment en qualsevol moment segons es descriu en la política de privacitat o fent clic a l\'enllaç \"donar-se de baixa\" en els missatges electrònics.';

  @override
  String get submit => 'Presentar';

  @override
  String get companyName => 'Arriving In High Heels Corporation';

  @override
  String get companyAddress =>
      '31 Claudview Street\nKing City, Ontario\nCanada\nL7B 0C6';

  @override
  String get privacyAgreementRequired =>
      'Vostè ha d\'estar d\'acord amb els nostres termes i LOP per tal de seguir utilitzant Visited.';

  @override
  String get deleteAccount => 'Esborrar compte';

  @override
  String get removeAdsUpsell =>
      'Voleu desactivar els anuncis i cancel·lar la subscripció de correu electrònic de màrqueting en el seu lloc?';

  @override
  String get deleteAccountWarning =>
      'L\'eliminació del seu compte eliminarà tota la informació dels nostres servidors.\n  Aquest procés no es pot desfer.';

  @override
  String get about => 'Sobre';

  @override
  String get popularity => 'Popularitat';

  @override
  String get regions => 'regions';

  @override
  String get population => 'població';

  @override
  String get size => 'mida';

  @override
  String get coverage => 'cobertura';

  @override
  String get percentOfCountryVisited => '% Del país visitat';

  @override
  String get visited => 'visitat';

  @override
  String get notes => 'notes';

  @override
  String get kmSquared => 'km²';

  @override
  String get customize => 'personalitzar';

  @override
  String get onlyCountSovereign => 'Sobirania reconeguda per l’ONU';

  @override
  String get countUkSeparately => 'Comptar Països U.K. separat';

  @override
  String get showLegend => 'Mostra llegenda';

  @override
  String get showLivedPin => 'Mostra Pin Actiu';

  @override
  String get useMyColours => 'Fes servir els meus colors';

  @override
  String get mapColors => 'els colors de mapa';

  @override
  String get traveller => 'El Viatjer';

  @override
  String get nightTraveller => 'El viatjer nocturn';

  @override
  String get original => 'L\'original';

  @override
  String get explorer => 'l\'Explorador';

  @override
  String get weekender => 'el de caps de setmana';

  @override
  String get naturalist => 'el naturalista';

  @override
  String get historian => 'historiador';

  @override
  String get thrillSeeker => 'El buscador d\'emocions';

  @override
  String get culturalBuff => 'L\'aficionat cultural';

  @override
  String get myColors => 'Els meus colors';

  @override
  String get experiences => 'experiències';

  @override
  String get done => 'fet';

  @override
  String get experiencesInstructions => 'Toca el botó + per començar!';

  @override
  String get continueText => 'continuar';

  @override
  String get experiencesDescription => 'Què li agrada fer quan viatja?';

  @override
  String get visitedWebsiteShortLink => 'https://www.visitedapp.com';

  @override
  String get sharingHashtag => '#Visited';

  @override
  String get myTravelMap => 'El meu Mapa de viatges';

  @override
  String percentOfWorldSeen(int percentage) {
    return 'He vist $percentage % de l\'món';
  }

  @override
  String get requiresOnline =>
      'Ho sentim, Visited requereix una connexió de xarxa activa. Si us plau, obriu la seva configuració d\'aplicació i assegureu-vos que sigui \\ r Wi-Fi o de dades mòvils està activada i el mode d\'avió està desactivat';

  @override
  String get list => 'llista';

  @override
  String get more => 'més';

  @override
  String get myCountrySelections => 'Les meves seleccions de País';

  @override
  String get cities => 'Ciutats';

  @override
  String get citiesInstructions =>
      'Toqueu en qualsevol país per iniciar la selecció de les ciutats.';

  @override
  String get missingCitiesEmailTitle => 'Les ciutats que falten!';

  @override
  String get lists => 'Llistes';

  @override
  String get disputedTerritories => 'Territors en disputa';

  @override
  String get sponsored => 'Patrocinat';

  @override
  String get places => 'llocs';

  @override
  String get noListsError =>
      'Opps, no hi ha llistes disponibles en aquest moment, si us plau intenti una mica més tard';

  @override
  String get noInspirationsError =>
      'Opps, no hi ha fotos disponibles en aquest moment, si us plau intenti-ho una mica més tard';

  @override
  String get mostFrequentlyVisitedCountries =>
      'Els vostres països més visitats:';

  @override
  String get update => 'Actualització';

  @override
  String get signup => 'Registra\'t';

  @override
  String get loginWallSubtitle =>
      'Crear un compte gratuït per experimentar la versió completa de l\'Visited';

  @override
  String get loseAllSelectionsWarning =>
      'Perdràs totes les seleccions després de tancar l\'aplicació.';

  @override
  String get createAccount => 'Crea un compte';

  @override
  String get continueWithoutAccount => 'Continuar sense un compte';

  @override
  String get inspirationPromotion =>
      'Inspira\'t amb una bella fotografia de viatges';

  @override
  String get saveStatsPromotion => 'Deseu les vostres estadístiques de viatge!';

  @override
  String get selectRegionsPromotion => 'Selecciona estats i províncies';

  @override
  String get experiencesPromotion => 'Seguiment d\'experiències arreu del món';

  @override
  String get missingListItem =>
      'Hem trobat a faltar alguna cosa? Toqueu aquí per enviar -nos un correu electrònic per obtenir el vostre lloc favorit afegit.';

  @override
  String missingListItemEmailTitle(String list) {
    return 'Falta element de $list';
  }

  @override
  String listShareMessage(Object amount, Object listName) {
    return 'He visitat $amount $listName';
  }

  @override
  String get orderPoster => 'Cartell';

  @override
  String get shareMap => 'Mapa compartit';

  @override
  String get posterLandingPageTitle => 'Aconsegueix el teu pòster';

  @override
  String get posterNotAvailableError =>
      'La compra de pòsters no està disponible ara mateix. Siusplau, intenta-ho més tard.';

  @override
  String posterPricePlusShipping(Object price, Object shipping) {
    return '$price + $shipping enviament';
  }

  @override
  String get posterDescriptionMarkdownApple =>
      '## Sobre els nostres mapes d\'impressió personalitzats\nImprimiu el vostre mapa mundial personalitzat. Personalitzeu -lo amb els vostres colors i feu -lo lliurar directament a casa vostra.\n \n### Especificacions:\n- 16\" x 20\" (40.64cm x 50.8cm)\n- Orientació del paisatge.\n- Micro tinta, gotetes per a estampats precisos, color de 8 bits, gairebé impressió fotogràfica, qualitat,\n- Paper de setí de 0,22 mm de gruix\n\n### Detalls d\'enviament:\nEnviament de Toronto, Canadà a qualsevol lloc del món mitjançant Canada Post. Si us plau, permeteu de 2 a 4 setmanes per lliurar a la majoria de destinacions. Totes les comandes s\'envien enrotllades en una caixa de tub de cartró a l\'adreça d\'enviament enviada. Tot el pagament és gestionat per Apple Pay, o Stripe.\n\n\n### Cancel·lació/devolució:\nLes comandes es processen immediatament després de ser enviades per al canvi més ràpid possible. Per tant, no hi ha cap reemborsament/cancel·lació disponible.';

  @override
  String get posterDescriptionMarkdown =>
      '## Sobre els nostres mapes d\'impressió personalitzats\nImprimiu el vostre mapa mundial personalitzat. Personalitzeu -lo amb els vostres colors i feu -lo lliurar directament a casa vostra.\n \n### Especificacions:\n- 16\" x 20\" (40.64cm x 50.8cm)\n- Orientació del paisatge.\n- Micro tinta, gotetes per a estampats precisos, color de 8 bits, gairebé impressió fotogràfica, qualitat,\n- Paper de setí de 0,22 mm de gruix\n\n### Detalls d\'enviament:\nEnviament de Toronto, Canadà a qualsevol lloc del món mitjançant Canada Post. Si us plau, permeteu de 2 a 4 setmanes per lliurar a la majoria de destinacions. Totes les comandes s\'envien enrotllades en una caixa de tub de cartró a l\'adreça d\'enviament enviada. Tot el pagament és gestionat per Google Pay o Stripe.\n\n\n### Cancel·lació/devolució:\nLes comandes es processen immediatament després de ser enviades per al canvi més ràpid possible. Per tant, no hi ha cap reemborsament/cancel·lació disponible.';

  @override
  String get posterCustomizeTitle => 'Personalitzeu el cartell';

  @override
  String get enterShippingAddress => 'Introduïu l\'adreça d\'enviament';

  @override
  String get price => 'Preu';

  @override
  String formattedPlusTax(Object formattedPrice) {
    return '$formattedPrice + Impost';
  }

  @override
  String get showSelections => 'Mostra la selecció';

  @override
  String get posterNoRefunds =>
      'No hi ha reemborsaments disponibles un cop imprès el vostre pòster.';

  @override
  String get posterReviewOrder => 'Revisa la teva comanda';

  @override
  String get email => 'Correu electrònic';

  @override
  String get emailEmptyError => 'Introduïu el vostre correu electrònic';

  @override
  String get fullName => 'Nom complet';

  @override
  String get fullNameEmptyError => 'Introduïu el vostre nom complet';

  @override
  String get streetAddressEmptyError => 'Introduïu la vostra adreça de carrer';

  @override
  String get cityEmptyError => 'Introduïu la vostra ciutat';

  @override
  String fieldEmptyError(Object fieldName) {
    return 'Introduïu el vostre $fieldName';
  }

  @override
  String get country => 'Camp';

  @override
  String get countryEmptyError => 'Introduïu el vostre país';

  @override
  String get posterReviewOrderTitle => 'Revisa la teva comanda';

  @override
  String get buyNow => 'Compra ara';

  @override
  String get secureCheckoutDisclaimer => 'Colló segur proporcionat per Stripe';

  @override
  String get total => 'Total';

  @override
  String get tax => 'Impost';

  @override
  String get subtotal => 'Subtotal';

  @override
  String get posterProductName => 'Cartell de mapes visitat personalitzat';

  @override
  String get shipping => 'Enviament';

  @override
  String get posterOrderReceivedTitle => 'ordre rebuda';

  @override
  String get posterOrderReceivedSubtitle => 'Hem rebut la vostra comanda!';

  @override
  String get posterOrderReceivedInstructionsMarkdown =>
      'Comproveu el vostre correu electrònic per obtenir més actualitzacions. \nPetelleu fins a 4 setmanes perquè arribi el vostre pòster. \nSi teniu cap pregunta, envieu -nos un correu electrònic a [<EMAIL>] (<EMAIL>)';

  @override
  String get posterOrderReceivedEmailSubject =>
      'Estat de la comanda del pòster imprès';

  @override
  String get moreInfo => 'Més informació';

  @override
  String get logoutConfirm => 'Voleu iniciar la sessió de l\'aplicació?';

  @override
  String get emailNotAvailable => 'S\'ha fet aquest correu electrònic.';

  @override
  String get alphabetical => 'Alfabètic';

  @override
  String get firstTimeLiveTutorial =>
      'Proporcionar el vostre país d\'origen i la vostra ciutat personalitzarà la vostra experiència d\'aplicacions.';

  @override
  String get firstTimeBeenTutorial =>
      'Seleccionant on heu estat per permetre veure el vostre mapa de tots els països on heu estat i veure estadístiques personals.';

  @override
  String get progressTooltipGoal =>
      'Els vostres objectius de viatge es basen en el nombre de països que \"voleu\" viatjar en comparació amb els països on heu estat.';

  @override
  String get progressTooltipRank =>
      'Aquest número mostra com es classifica en comparació amb els viatgers de tot el món. Podeu augmentar el vostre rang viatjant a més països.';

  @override
  String get progressTooltipPercentageOfWorld =>
      'Aquest gràfic es basa en el nombre de països que heu comparat amb els països totals del món.';

  @override
  String get sortBy => 'Ordenar per';

  @override
  String get updateWishlist => 'Llista de desitjos d\'actualització';

  @override
  String get mapInfo =>
      'Feu clic al país per seleccionar, desitjar o viure. També podeu fer clic a la icona que es troba a la part superior esquerra de la vista de la llista.';

  @override
  String get oneTimePurchase => 'Tot és una compra única.';

  @override
  String get contact => 'Contacte';

  @override
  String get contactUs => 'Poseu -vos en contacte amb nosaltres';

  @override
  String get noCitiesSelected => 'No heu seleccionat cap ciutat, però ...';

  @override
  String get updateTravelGoal => 'Actualitzar l\'objectiu de viatge';

  @override
  String get travelGoalComplete =>
      'Enhorabona! \n\nyou heu completat el vostre objectiu de viatge. \n\ntap el botó + per afegir més països.';

  @override
  String loginEmailNotFoundError(String email) {
    return 'No hi ha cap compte associat al correu electrònic $email. Voleu crear -lo ara?';
  }

  @override
  String get tryAgain => 'Torna-ho a provar';

  @override
  String get itineraries => 'Plans de viatge';

  @override
  String get itinerary => 'Pla de viatge';

  @override
  String get place => 'Lloc';

  @override
  String get itinerariesDescription =>
      'Aquests són els llocs en què heu expressat interès.\nUtilitzeu aquesta guia per planificar les vostres properes vacances.';

  @override
  String get addMore => 'Afegir més';

  @override
  String get interests => 'Interessos';

  @override
  String get selection => 'Selecció';

  @override
  String get goal => 'Objectiu';

  @override
  String get noItineraries => 'Sense Itineraris';

  @override
  String get noItinerariesExplanation =>
      'Si us plau, afegiu alguns llocs, inspiracions o experiències per veure com es generen els vostres itineraris automàticament.';

  @override
  String get clusterPins => 'Agrupar Pins';

  @override
  String get toggleRegions => 'Mostra regions';

  @override
  String get mapProjection => 'Projecció de mapa';

  @override
  String get mercator => 'Mercator';

  @override
  String get equirectangular => 'Equirectangular';

  @override
  String get yourTravellerType => 'El teu tipus de viatger:';

  @override
  String get yourHotelPreferences => 'Les teves preferències d\'hotel:';

  @override
  String get budget => 'Pressupost';

  @override
  String get midScale => 'Mitjana Gamma';

  @override
  String get luxury => 'Luxe';

  @override
  String get noTravellerType =>
      'Afegeix articles a la teva llista de desitjos per descobrir quin tipus de viatger ets.';

  @override
  String get unlockLived => 'Desbloqueja Viscut';

  @override
  String get unlockLivedDescription =>
      'Selecciona on has viscut anteriorment al mapa!';

  @override
  String get futureFeaturesDescription => '...i totes les futures funcions';

  @override
  String get yourMostFrequentlyVisitedCountry => 'El vostre país més visitat:';

  @override
  String get departureDate => 'Data de sortida';

  @override
  String get returnDate => 'Data de tornada';

  @override
  String get hotels => 'Hotels';

  @override
  String get food => 'Menjar';

  @override
  String get travelDates => 'Dates de viatge';

  @override
  String get posterForMe => 'Per a mi';

  @override
  String get posterSendGift => 'Envia un regal';

  @override
  String get addSelections => 'Afegeix seleccions';

  @override
  String get posterType => 'Tipus de pòster';

  @override
  String get help => 'Ajuda';

  @override
  String get tutorialMap =>
      'Toqueu un país per seleccionar: estat, vol i viscut.';

  @override
  String get tutorialMapList =>
      'Toqueu la icona de llista (extrem superior esquerre) per seleccionar per llista.';

  @override
  String get tutorialCountryDetails =>
      'Toqueu el país i després \"més\" per seleccionar per regió.';

  @override
  String get tutorialItems =>
      'Feu lliscar el commutador per triar com voleu seleccionar els elements.';

  @override
  String get tutorialInspirations =>
      'Fes lliscar el dit cap a la dreta o l\'esquerra per passar a la següent targeta.';

  @override
  String get lifetime => 'De per vida';

  @override
  String get chooseYourPlan => 'Tria el teu pla';

  @override
  String get requestARefund => 'Sol·licita un reemborsament';

  @override
  String get noPurchasesFound => 'No s\'han trobat compres.';

  @override
  String get noProductsAvailable => 'No hi ha productes disponibles';

  @override
  String get posterLandingAppBar => 'Porta les teves històries a casa';

  @override
  String get posterLandingSubHeading => 'El teu viatge, la teva història';

  @override
  String get posterLandingSubDescription =>
      'Els teus viatges són més que viatges, són històries, records i fites. Converteix aquests moments inoblidables en un mapa del món personalitzat que és tan únic com les teves aventures.';

  @override
  String get posterLandingPromoBullet1 =>
      '• Un mapa dels teus èxits: destaca totes les destinacions, des del teu primer gran viatge fins a la teva aventura més atrevida.';

  @override
  String get posterLandingPromoBullet2 =>
      '• Celebra cada viatge: reviu els teus viatges diàriament amb un pòster ben dissenyat per inspirar.';

  @override
  String get posterLandingPromoBullet3 =>
      '• Un regal que atresoraran: sorprèn a un company de viatge amb un mapa personalitzat que mostra el seu viatge, perfecte per a aniversaris, fites o simplement perquè.';

  @override
  String get posterLandingHowItWorks => 'Com funciona!';

  @override
  String get posterLandingHowItWorksStep1 =>
      '1. Personalitza el teu disseny: tria colors, estils i marca els teus viatges (o els seus!)';

  @override
  String get posterLandingHowItWorksStep2 =>
      '2. Previsualitza el teu mapa: mira\'l com cobra vida abans de la teva comanda.';

  @override
  String get posterLandingHowItWorksStep3_iOS =>
      '3. Pagament segur: ràpid i segur amb Apple Pay o Stripe.';

  @override
  String get posterLandingHowItWorksStep3_android =>
      '3. Pagament segur: ràpid i segur amb Google Pay o Stripe.';

  @override
  String get posterLandingHowItWorksStep4 =>
      '4. A punt per mostrar: l\'enviarem directament a la teva porta (o a la seva).';

  @override
  String get posterLandingCustomerReviewsHeader =>
      'Experiencias de otros viajeros';

  @override
  String get posterLandingCustomerReview1 =>
      'Aquest mapa és una manera fantàstica de fer un seguiment de tots els llocs on he viatjat i planificar els nostres viatges futurs. La qualitat és sòlida i es veu increïble penjat a la meva oficina. Fins i tot en vaig aconseguir un per al meu germà i no va poder, deixeu de parlar de com és genial! - Joan C.';

  @override
  String get posterLandingCustomerReview2 =>
      'He viatjat a més de 150 ports mentre treballava en creuers. Aquest mapa és una gran addició a la meva sala d\'estar com a record de tots els anys al mar. - Betty K.';

  @override
  String get posterLandingCustomerReview3 =>
      'Un gran regal per al dia de la mare. La meva mare estava molt emocionada! Samantha W.';

  @override
  String get posterLandingCustomerReview4 =>
      'Va imprimir un mapa dels llocs que volia visitar amb la meva novia. Va ser un gran regal de Nadal. També d\'alta qualitat. Brad J.';

  @override
  String get posterLandingSpecifications => 'Especificacions';

  @override
  String get posterLandingSpecification1 =>
      '• Mides: 16\" x 20\" (40,64 cm x 50,8 cm)';

  @override
  String get posterLandingSpecification2 => '• Orientació: Paisatge';

  @override
  String get posterLandingSpecification3 =>
      '• Qualitat d\'impressió: Microtinta, gotes per a impressions precises.  Color de 8 bits, qualitat d\'impressió gairebé fotogràfica.';

  @override
  String get posterLandingSpecification4 =>
      '• Paper: paper setinat de 0,22 mm de gruix';

  @override
  String get posterLandingShippingHeader => 'Detalls d\'enviament';

  @override
  String get posterLandingShipping1 =>
      '• Enviament des de Toronto, Canadà a qualsevol part del món mitjançant Canada Post.';

  @override
  String get posterLandingShipping2 =>
      '• Permet entre 2 i 4 setmanes per al lliurament a la majoria de destinacions.';

  @override
  String get posterLandingShipping3 =>
      '• Totes les comandes s\'enrotllen en una caixa de tub de cartró a l\'adreça d\'enviament que envieu.';

  @override
  String get posterLandingCancellationHeader => 'Cancel·lació/Devolució:';

  @override
  String get posterLandingCancellationBody =>
      'Els reemborsaments estan disponibles abans que el vostre pòster s\'hagi enviat a la impressora, cosa que pot trigar fins a 24 hores.  Després de processar la vostra comanda, no hi ha cap reemborsament/cancel·lació disponible.  Rebràs un correu electrònic quan la teva comanda hagi estat impresa.';

  @override
  String get unsubscribe => 'Donar-se de baixa';

  @override
  String get unsubscribeConfirmMessage =>
      'Estàs segur que vols donar-te de baixa? Et perdràs ofertes exclusives i actualitzacions!';

  @override
  String get updateLive => 'Actualitzar Residència';

  @override
  String get updateLiveDescription =>
      'Per canviar el país on vius, primer has de seleccionar un nou país per substituir-lo.';

  @override
  String get underOneThousand => 'Menys de 1.000';

  @override
  String get oneThousandToTenThousand => '1.000 – 10.000';

  @override
  String get overTenThousand => 'Més de 10.000';

  @override
  String get becomeABrandAmbassador => 'Converteix-te en ambaixador de marca';

  @override
  String get instagram => 'Instagram';

  @override
  String get tiktok => 'TikTok';

  @override
  String get youtube => 'YouTube';

  @override
  String get website => 'Lloc web';

  @override
  String get handle => 'Usuari';

  @override
  String get followers => 'Seguidors';

  @override
  String get joinBrandAmbassadorProgram =>
      'Uneix-te al programa d’ambaixadors de marca';

  @override
  String get brandAmbassadorProgramDescription =>
      'T’agrada Visited? Com a ambaixador de marca, representaràs la nostra comunitat viatgera, mostraràs el teu mapa i les teves llistes de viatges, i ajudaràs altres a descobrir nous destins i funcions de l’app. A canvi, rebràs recompenses, descomptes, marxandatge i molt més!';

  @override
  String get fillOutTheFormToGetStarted =>
      'Omple el formulari següent per començar:';

  @override
  String get yourName => 'El teu nom';

  @override
  String get yourNameEmptyError => 'Si us plau, introdueix el teu nom';

  @override
  String get fillInWhereApplicable => 'Omple on sigui aplicable:';

  @override
  String get otherNetworks => 'Altres xarxes';

  @override
  String get anythingElse => 'Hi ha res més que vulguis que sapiguem?';

  @override
  String get yourTravelsByContinent => 'Els teus viatges per continent';

  @override
  String get territories => 'Territoris';

  @override
  String get couponCode => 'Codi de descompte';

  @override
  String get apply => 'Aplica';

  @override
  String get discount => 'Descompte';

  @override
  String get noCouponCode => 'Si us plau, introdueix un codi de descompte';

  @override
  String get invalidCouponCode => 'Codi de descompte no vàlid';

  @override
  String get couponApplied => 'Codi aplicat';

  @override
  String discountPercentage(double percentage) {
    return '$percentage% de descompte!';
  }

  @override
  String discountAmount(double amount) {
    return 'Descompte de $amount!';
  }

  @override
  String get thankYou => 'Gràcies!';

  @override
  String get formSubmitted =>
      'Hem rebut la teva sol·licitud per convertir-te en ambaixador de la marca. Ens posarem en contacte aviat!';
}
