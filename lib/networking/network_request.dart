class NetworkRequest {
  final String path;
  final Map<String, dynamic>? parameters;
  final bool retry;
  final bool cacheable;
  final bool requiredAuthentication;

  const NetworkRequest(
    this.path, {
    this.parameters,
    this.retry = true,
    this.cacheable = true,
    this.requiredAuthentication = true,
  });

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is NetworkRequest &&
          runtimeType == other.runtimeType &&
          path == other.path &&
          parameters == other.parameters &&
          retry == other.retry &&
          cacheable == other.cacheable &&
          requiredAuthentication == other.requiredAuthentication;

  @override
  int get hashCode => path.hashCode;

  @override
  String toString() => path;
}
