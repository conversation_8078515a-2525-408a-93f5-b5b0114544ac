class EnvironmentData {
  final String name;
  final String baseUrl;
  final String baseUrlFallback;
  final String version;
  final double pixelDensity;
  final String stripePublishKey;

  const EnvironmentData({
    required this.name,
    required this.baseUrl,
    required this.baseUrlFallback,
    required this.version,
    required this.pixelDensity,
    required this.stripePublishKey,
  });

  @override
  String toString() {
    return 'Environment{name: $name}';
  }
}
