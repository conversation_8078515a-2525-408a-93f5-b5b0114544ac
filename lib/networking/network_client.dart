import 'dart:async';
import 'dart:convert';
import 'dart:io';

import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:flutter/foundation.dart';
import 'package:http/http.dart' as http;
import 'package:sentry_flutter/sentry_flutter.dart';

import '../dependency_injection/dependency_injector.dart';
import '../logger.dart';
import 'client.dart';
import 'environment.dart';
import 'network_request.dart';
import 'network_request_queue.dart';

enum _HttpMethod { get, delete, post, put }

class NetworkClient implements Client {
  @override
  final EnvironmentData environment;

  @override
  late SessionRefresher sessionRefresher;

  @override
  final Future<void> Function() onOfflineCallback;

  final _connectivity = Connectivity();

  NetworkClient(this.environment, this.onOfflineCallback);

  late final _requestQueue = NetworkRequestQueue(
    requestProcessor: _processGetRequest,
  );

  @override
  Future delete(String path, {bool retry = true}) {
    return _request(method: _HttpMethod.delete, path: path, retry: retry);
  }

  @override
  Future get(
    String path, {
    Map<String, dynamic>? parameters,
    bool retry = true,
    bool cacheable = true,
    bool requiredAuthentication = true,
  }) {
    return _requestQueue.add(
      NetworkRequest(
        path,
        parameters: parameters,
        retry: retry,
        cacheable: cacheable,
        requiredAuthentication: requiredAuthentication,
      ),
    );
  }

  Future _processGetRequest(NetworkRequest request) {
    if (request.requiredAuthentication && !authenticated) {
      throw Exception('You must be authenticated to make this request');
    }

    return _request(
      method: _HttpMethod.get,
      path: request.path,
      parameters: request.parameters,
      retry: request.retry,
      extraHeaders: {
        'Cache-Control': request.cacheable
            ? 'max-age=${const Duration(days: 1).inSeconds}'
            : 'no-cache',
      },
    );
  }

  @override
  Future post(String path, {JsonMap? body, bool retry = true}) {
    return _request(
      method: _HttpMethod.post,
      path: path,
      body: body,
      retry: retry,
    );
  }

  @override
  Future put(String path, {JsonMap? body, bool retry = true}) {
    return _request(
      method: _HttpMethod.put,
      path: path,
      body: body,
      retry: retry,
    );
  }

  Future _request({
    required _HttpMethod method,
    required String path,
    JsonMap? parameters,
    body,
    required bool retry,
    Map<String, String>? extraHeaders,
    bool useFallbackHost = false,
  }) async {
    final online = await _isOnline();
    if (!online) {
      await onOfflineCallback();
      return _request(
        method: method,
        path: path,
        parameters: parameters,
        body: body,
        retry: retry,
        extraHeaders: extraHeaders,
        useFallbackHost: useFallbackHost,
      );
    }

    final url = _buildUrl(
      path,
      parameters: parameters,
      useFallbackHost: useFallbackHost,
    );
    final postBody = body != null ? jsonEncode(body) : null;
    _logRequest(url, postBody);

    final headers = await _buildHeaders();
    if (extraHeaders != null) {
      headers.addAll(extraHeaders);
    }

    final transaction = Sentry.startTransaction(
      'webrequest',
      'request',
      bindToScope: true,
    );

    final sentryTracingClient = SentryHttpClient();

    try {
      final response = await _sendRequest(method, url, headers, postBody);

      return _handleResponse(response, retry: retry);
    } on http.ClientException catch (_) {
      // Try again with the fallback url (skips Cloudfront
      return _request(
        method: method,
        path: path,
        parameters: parameters,
        body: body,
        retry: retry,
        extraHeaders: extraHeaders,
        useFallbackHost: true,
      );
    } finally {
      sentryTracingClient.close();
      await transaction.finish(status: const SpanStatus.ok());
    }
  }

  Future<http.Response> _sendRequest(
    _HttpMethod method,
    Uri url,
    Map<String, String> headers,
    String? postBody,
  ) {
    Sentry.addBreadcrumb(
      Breadcrumb(message: '${method.name}: $url', category: 'http.request'),
    );

    switch (method) {
      case _HttpMethod.get:
        return http.get(url, headers: headers);

      case _HttpMethod.put:
        return http.put(url, headers: headers, body: postBody);

      case _HttpMethod.delete:
        return http.delete(url, headers: headers);

      case _HttpMethod.post:
        return http.post(url, headers: headers, body: postBody);
    }
  }

  Future<bool> _isOnline() async {
    final results = await _connectivity.checkConnectivity();
    return results.contains(ConnectivityResult.none) == false;
  }

  void _logRequest(Uri url, [String? body]) {
    if (!kDebugMode) {
      return;
    }
    log('');
    log('Sending Request');
    log(url);
    if (body != null) {
      log(body);
    }
  }

  FutureOr _handleResponse(http.Response response, {required bool retry}) {
    _logResponse(response);

    if (response.statusCode ~/ 100 != 2) {
      return _handleFailure(response, retry);
    }

    try {
      final body = response.body;

      if (body.isEmpty) {
        return null;
      }

      final json = jsonDecode(body);

      // ENABLE IF YOU WANT TO SEE THE RESPONSE IN THE LOG.
      // BUT IT MOSTLY JUST SLOWS DOWN PERFORMANCE WHILE DEBUGGING
      // if (kDebugMode) {
      //   const prettyPrintEncoder = JsonEncoder.withIndent('  ');
      //   final prettyPrinted = prettyPrintEncoder.convert(json);
      //   log(prettyPrinted);
      // }

      return json;
    } catch (error) {
      log(error);
      rethrow;
    }
  }

  void _logResponse(http.Response response) {
    if (kDebugMode) {
      log('');
      final request = response.request;
      if (request != null) {
        log('${request.method} - ${request.url}');
      }
      log(response.headers);
      log(response.statusCode);
    }
  }

  Future<Map<String, String>> _buildHeaders() async {
    final headers = {
      'Content-Type': 'application/json;charset=UTF-8',
      'Accept-Encoding': 'gzip, deflate, br',
      'Accept-Language':
          DependencyInjector.settingsBloc.currentLanguage.localizationKey,
      'x-app-platform': Platform.isIOS ? 'iOS' : 'Android',
      'x-app-version': environment.version,
      'x-app-density': environment.pixelDensity.toString(),
    };

    headers.addAll(customHeaders);
    return headers;
  }

  Uri _buildUrl(String path, {Map? parameters, bool useFallbackHost = false}) {
    //TODO: Just use the Uri's constructor instead of a StringBuffer
    final buffer = StringBuffer();
    buffer.write(
      useFallbackHost ? environment.baseUrlFallback : environment.baseUrl,
    );
    buffer.write('/');
    buffer.write(path);

    if (parameters != null) {
      buffer.write('?');
      final query = parameters.keys
          .map((key) => '$key=${parameters[key]}')
          .join('&');
      buffer.write(query);
    }

    final url = buffer.toString();
    return Uri.parse(url);
  }

  @override
  final customHeaders = <String, String>{};

  @override
  void setHeader({required String key, required String value}) {
    customHeaders[key] = value;
  }

  Future _handleFailure(http.Response response, bool retry) {
    if (retry &&
        response.request != null &&
        response.statusCode == HttpStatus.unauthorized) {
      return _refreshSessionThenRetryRequest(response.request as http.Request);
    }

    throw NetworkException(
      response.statusCode,
      response.body,
      response.request?.url.path,
    );
  }

  Future _refreshSessionThenRetryRequest(http.Request request) async {
    customHeaders.remove(kAuthorizationHeader);
    await sessionRefresher();

    final retryRequest = http.Request(request.method, request.url);
    retryRequest.headers.addAll(await _buildHeaders());
    retryRequest.body = request.body;

    final updatedResponse = await http.Response.fromStream(
      await retryRequest.send(),
    );
    return _handleResponse(updatedResponse, retry: false);
  }

  @override
  bool get authenticated => customHeaders.containsKey(kAuthorizationHeader);

  @override
  void clearCache() {
    _requestQueue.clear();
  }

  @override
  void dispose() {
    _requestQueue.dispose();
  }
}

class NetworkException implements Exception {
  const NetworkException(this.statusCode, this.errorMessage, [this.path]);

  final int statusCode;
  final String errorMessage;
  final String? path;

  Map? get errorJson {
    try {
      return jsonDecode(errorMessage);
    } catch (e) {
      return null;
    }
  }
}
