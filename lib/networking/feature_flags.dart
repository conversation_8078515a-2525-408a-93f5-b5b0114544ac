import 'dart:convert';

import 'package:firebase_remote_config/firebase_remote_config.dart';

import '../features/authentication/onboarding_cards.dart';

class FeatureFlags {
  static const _kAccountRequired = 'account_required';
  static const _kPosterEnabled = 'poster_enabled';
  static const _kOrganizeListsByTags = 'organize_lists_by_tag';
  static const _kEnableSearchInTodoLists = 'enable_search_in_todo_lists';
  static const _kShowBookLinks = 'show_book_links';
  static const _kLoginButtonFirst = 'login_button_first';
  static const _kPosterMessage = 'poster_message';
  static const _kEnableSubscriptions = 'enable_subscriptions';
  static const _kLimitCountrySelections = 'limit_country_selections';
  static const _kLimitCitySelections = 'limit_city_selections';
  static const _kMaxFreeCountries = 'max_free_countries';
  static const _kMaxFreeCities = 'max_free_cities';

  static const _onboardingShowImage = 'onboarding_show_images';
  static const _onboardingShowImagesPremium = 'onboarding_show_images_premium';
  static const _onboardingShowImagesWithAutoScroll =
      'onboarding_show_images_with_autoscroll';
  static const _onboardingShowImagesWithText =
      'onboarding_show_images_with_text';
  static const _onboardingShowImagesWithTextPremium =
      'onboarding_show_images_with_text_premium';

  FirebaseRemoteConfig get _config => FirebaseRemoteConfig.instance;
  bool _activated = false;

  Future<void> _activate() async {
    if (_activated) {
      return;
    }

    _config.setDefaults({
      _kLoginButtonFirst: false,
      _kAccountRequired: true,
      _kEnableSearchInTodoLists: false,
      'iap_enabled': true,
      _kOrganizeListsByTags: false,
      _kPosterEnabled: true,
      _kShowBookLinks: true,
      _kLimitCitySelections: false,
      _kLimitCountrySelections: false,
      _kEnableSubscriptions: false,
      _kMaxFreeCities: 20,
      _kMaxFreeCountries: 10,
      _onboardingShowImage: false,
      _onboardingShowImagesPremium: false,
      _onboardingShowImagesWithAutoScroll: false,
      _onboardingShowImagesWithText: false,
      _onboardingShowImagesWithTextPremium: false,
    });

    try {
      await _config.fetchAndActivate();
      await _config.ensureInitialized();
    } catch (e) {
      // Don't worry about failure, just use built in app defaults
    }
    _activated = true;
  }

  Future<bool> accountRequiredOnAppLaunch() => _fetchFlag(_kAccountRequired);
  Future<bool> posterEnabled() => _fetchFlag(_kPosterEnabled);
  Future<bool> organizeListByTag() => _fetchFlag(_kOrganizeListsByTags);
  Future<bool> enableSearchInTodoListScreen() =>
      _fetchFlag(_kEnableSearchInTodoLists);
  Future<bool> showBookLinks() => _fetchFlag(_kShowBookLinks);
  Future<bool> loginButtonFirst() => _fetchFlag(_kLoginButtonFirst);
  Future<bool> enableSubscriptions() =>
      Future.value(true); // _fetchFlag(_kEnableSubscriptions);
  Future<bool> limitCitySelections() => _fetchFlag(_kLimitCitySelections);
  Future<bool> limitCountrySelections() => _fetchFlag(_kLimitCountrySelections);
  Future<int> maxFreeCities() => _fetchInt(_kMaxFreeCities);
  Future<int> maxFreeCountries() => _fetchInt(_kMaxFreeCountries);

  Future<String?> posterMessage(String languageCode) async {
    await _activate();
    final fullText = _config.getString(_kPosterMessage);
    if (fullText.isEmpty) {
      return null;
    }

    try {
      final json = jsonDecode(fullText);
      final translation = json[languageCode];
      return translation;
    } catch (_) {
      return null;
    }
  }

  Future<bool> _fetchFlag(String flag) async {
    await _activate();
    final value = _config.getBool(flag);
    return value;
  }

  Future<int> _fetchInt(String flag) async {
    await _activate();
    final value = _config.getInt(flag);
    return value;
  }

  Future<OnboardingStyle> onboardingStyle() async {
    await _activate();

    if (_config.getBool(_onboardingShowImage)) {
      return OnboardingStyle.appImages;
    }

    if (_config.getBool(_onboardingShowImagesWithText)) {
      return OnboardingStyle.appImagesWithText;
    }

    if (_config.getBool(_onboardingShowImagesWithAutoScroll)) {
      return OnboardingStyle.appImagesWithAutoScroll;
    }

    if (_config.getBool(_onboardingShowImagesWithTextPremium)) {
      return OnboardingStyle.appImagesWithPremiumUpsellAndText;
    }

    if (_config.getBool(_onboardingShowImagesPremium)) {
      return OnboardingStyle.appImagesWithPremiumUpsell;
    }

    return OnboardingStyle.none;
  }
}
