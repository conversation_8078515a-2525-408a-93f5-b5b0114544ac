import 'dart:async';

import '../dependency_injection/dependency_injector.dart';
import 'network_request.dart';

class NetworkRequestQueue {
  NetworkRequestQueue({required this.requestProcessor}) {
    _languageSubscription ??= DependencyInjector.settingsBloc.language.listen((
      _,
    ) {
      _cachedResults.clear();
    });
  }

  StreamSubscription? _languageSubscription;
  final Future Function(NetworkRequest request) requestProcessor;

  Future add(NetworkRequest request) {
    if (request.cacheable) {
      final cachedResult = _cachedResults[request.path];
      if (cachedResult != null) {
        return Future.value(cachedResult);
      }
    }

    final future = _cache(request);
    if ((_activeRequests[request.path]?.length ?? 0) == 1) {
      _processRequests(request);
    }

    return future;
  }

  final _activeRequests = <String, Set<Completer>>{};
  final _cachedResults = <String, dynamic>{};

  Future _cache(NetworkRequest request) {
    final completers = {..._activeRequests[request.path] ?? {}};
    final completer = Completer();
    completers.add(completer);
    _activeRequests[request.path] = completers;
    return completer.future;
  }

  void _processRequests(NetworkRequest request) async {
    try {
      final result = await requestProcessor(request);
      _activeRequests[request.path]?.forEach(
        (completer) => completer.complete(result),
      );

      if (request.cacheable) {
        _cachedResults[request.path] = result;
      }
    } catch (e, stack) {
      _activeRequests[request.path]?.forEach(
        (completer) => completer.completeError(e, stack),
      );
    }

    _activeRequests.remove(request.path);
  }

  void clear() {
    _activeRequests.clear();
    _cachedResults.clear();
  }

  void dispose() {
    _languageSubscription?.cancel();
  }
}
