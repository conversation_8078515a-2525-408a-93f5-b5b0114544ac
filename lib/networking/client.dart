import 'environment.dart';

typedef SessionRefresher = Future<void> Function();
typedef JsonMap = Map<String, dynamic>;
const kAuthorizationHeader = 'Authorization';

abstract class Client {
  EnvironmentData get environment;
  late final SessionRefresher sessionRefresher;
  final Future<void> Function() onOfflineCallback;

  Map<String, String> get customHeaders;

  Client(this.onOfflineCallback);

  Future get(
    String path, {
    Map<String, dynamic>? parameters,
    bool retry,
    bool cacheable = true,
    bool requiredAuthentication = true,
  });

  Future post(String path, {JsonMap? body, bool retry});

  Future put(String path, {JsonMap? body, bool retry});

  Future delete(String path, {bool retry});

  void setHeader({
    required String key,
    required String value,
  });

  bool get authenticated;

  void clearCache();

  void dispose();
}
