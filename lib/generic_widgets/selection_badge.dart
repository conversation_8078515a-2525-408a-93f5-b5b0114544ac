import 'package:flutter/material.dart';

import '../dependency_injection/dependency_injector.dart';
import '../l10n/generated/app_localizations.dart';
import '../models/color_extensions.dart';
import '../models/selection.dart';

class SelectionBadge extends StatelessWidget {
  final Selection selection;

  const SelectionBadge({
    super.key,
    required this.selection,
  });

  @override
  Widget build(BuildContext context) {
    if (selection == Selection.clear) {
      return const SizedBox();
    }

    final palette = DependencyInjector.settingsBloc.currentPalette;

    final color = palette.colorForSelection(selection);

    return Container(
      decoration: ShapeDecoration(color: color, shape: const StadiumBorder()),
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        child: Text(
          selection.localized(AppLocalizations.of(context)!),
          style: TextStyle(
            color: color.legibleForegroundColor(),
            fontWeight: FontWeight.w500,
          ),
        ),
      ),
    );
  }
}
