import 'package:collection/collection.dart';
import 'package:flutter/material.dart';

import '../models/color_extensions.dart';

class ToggleItem<T> {
  final T value;
  final String label;
  final Color? color;

  const ToggleItem({required this.value, required this.label, this.color});
}

class VisitedToggleBar<T> extends StatelessWidget {
  const VisitedToggleBar({
    super.key,
    required this.items,
    required this.onSelected,
    required this.selected,
    this.height,
    this.expand = false,
  });

  final List<ToggleItem<T>> items;
  final T selected;
  final ValueChanged<T> onSelected;
  final double? height;
  final bool expand;

  static const _itemSize = Size(110, 33);
  static const _animDuration = Duration(milliseconds: 250);

  @override
  Widget build(BuildContext context) {
    return Container(
      width: expand ? double.infinity : _itemSize.width * items.length,
      height: _itemSize.height,
      decoration: ShapeDecoration(
        shape: const StadiumBorder(),
        color: _backgroundColour(context),
      ),
      child: LayoutBuilder(
        builder: (context, constraints) {
          return Stack(
            children: [
              _buildSelectionIndicator(context, constraints),
              _buildItems(context),
            ],
          );
        },
      ),
    );
  }

  Widget _buildSelectionIndicator(
    BuildContext context,
    BoxConstraints constraints,
  ) {
    final width = expand
        ? constraints.maxWidth / items.length
        : _itemSize.width;

    return AnimatedPositioned(
      duration: _animDuration,
      left: width * selectedIndex,
      child: SwipeDetector(
        onSwipeDetected: (direction) {
          final newIndex = selectedIndex + direction;
          if (newIndex >= 0 && newIndex < items.length) {
            onSelected(items[newIndex].value);
          }
        },
        child: AnimatedContainer(
          duration: _animDuration,
          decoration: ShapeDecoration(
            shape: const StadiumBorder(),
            color: _selectedColour(context),
          ),
          width: width,
          height: _itemSize.height,
        ),
      ),
    );
  }

  Color _backgroundColour(BuildContext context) {
    return _selectedItem(selected)?.color?.withBrightness(0.85) ??
        Theme.of(context).dividerColor;
  }

  Color _selectedColour(BuildContext context) {
    final theme = Theme.of(context);
    return _selectedItem(selected)?.color ??
        (theme.brightness == Brightness.light
            ? theme.primaryColor
            : theme.colorScheme.primary);
  }

  ToggleItem<T>? _selectedItem(T value) {
    return items.firstWhereOrNull((element) => element.value == value);
  }

  Widget _buildItems(BuildContext context) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        for (final item in items)
          expand
              ? Expanded(child: _buildItem(item, context))
              : _buildItem(item, context),
      ],
    );
  }

  Widget _buildItem(ToggleItem<T> item, BuildContext context) {
    if (_isSelected(item)) {
      return IgnorePointer(child: _buildLabel(item, context));
    }

    return GestureDetector(
      behavior: HitTestBehavior.opaque,
      onTap: () {
        if (_isSelected(item)) {
          return;
        }
        onSelected(item.value);
      },
      child: _buildLabel(item, context),
    );
  }

  Widget _buildLabel(ToggleItem<T> item, BuildContext context) {
    return SizedBox.fromSize(
      size: _itemSize,
      child: Center(
        child: AnimatedDefaultTextStyle(
          duration: _animDuration,
          style: TextStyle(
            fontSize: 12,
            fontWeight: FontWeight.w700,
            color: _buildTextColor(context, item),
          ),
          child: Text(item.label),
        ),
      ),
    );
  }

  bool _isSelected(ToggleItem<T> item) => selected == item.value;

  Color _buildTextColor(BuildContext context, ToggleItem<T> item) {
    final theme = Theme.of(context);

    if (!_isSelected(item)) {
      return _backgroundColour(context).legibleForegroundColor();
    }

    final selectedTextColour = theme.brightness == Brightness.dark
        ? Colors.black
        : Colors.white;

    return item.color?.legibleForegroundColor() ?? selectedTextColour;
  }

  int get selectedIndex {
    for (var i = 0; i < items.length; i++) {
      if (_isSelected(items[i])) {
        return i;
      }
    }

    throw Exception('Selected value is not present in the item list!');
  }
}

class SwipeDetector extends StatefulWidget {
  const SwipeDetector({
    super.key,
    required this.onSwipeDetected,
    required this.child,
  });

  final ValueChanged<int> onSwipeDetected;
  final Widget child;

  @override
  State<SwipeDetector> createState() => _SwipeDetectorState();
}

class _SwipeDetectorState extends State<SwipeDetector> {
  DragStartDetails? _dragStartDetails;

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      behavior: HitTestBehavior.opaque,
      onPanStart: (details) {
        _dragStartDetails = details;
      },
      onPanEnd: (endDetails) {
        final startDetails = _dragStartDetails;
        if (startDetails == null) {
          return;
        }

        final diff = endDetails.globalPosition - startDetails.globalPosition;
        if (diff.dx.abs() > 3) {
          final direction = diff.dx > 0 ? 1 : -1;
          widget.onSwipeDetected(direction);
        }

        _dragStartDetails = null;
      },
      child: widget.child,
    );
  }
}
