import 'package:flutter/material.dart';

import '../helpers/searcher.dart';
import 'platform_aware/platform_icon_button.dart';
import 'selectable_item.dart';

class SearchBar<T extends SelectableItem> extends StatefulWidget {
  const SearchBar({
    super.key,
    this.items,
    this.onQuery,
    this.controller,
    this.onCleared,
  }) : assert(controller != null || (items != null && onQuery != null));

  final List<T>? items;
  final ValueChanged<List<T>?>? onQuery;
  final VoidCallback? onCleared;
  final TextEditingController? controller;

  @override
  State<StatefulWidget> createState() => _SearchBarState<T>();
}

class _SearchBarState<T extends SelectableItem> extends State<SearchBar<T>> {
  bool selected = false;
  late final FocusNode focus = FocusNode()
    ..addListener(() {
      setState(() {
        selected = focus.hasFocus;
      });
    });

  bool get useInternalSearcher =>
      widget.items != null && widget.onQuery != null;

  late final _searcher = useInternalSearcher
      ? Searcher(
          widget.items!,
          widget.onQuery!,
        )
      : null;

  late final _textController = widget.controller ?? TextEditingController();

  @override
  Widget build(BuildContext context) {
    return Material(
      elevation: selected ? 8 : 0,
      borderRadius: radius,
      child: Stack(
        children: [
          _buildTextField(context),
          if (focus.hasFocus) _buildClearButton()
        ],
      ),
    );
  }

  Widget _buildTextField(BuildContext context) {
    return TextField(
      controller: _textController,
      focusNode: focus,
      maxLines: 1,
      autocorrect: false,
      decoration: _buildDecoration(context),
      onChanged: _onQuery,
    );
  }

  Widget _buildClearButton() {
    return Positioned(
      top: 0,
      bottom: 0,
      right: 4,
      child: PlatformIconButton(
          icon: Icons.clear,
          onTapped: () {
            _textController.clear();
            focus.unfocus();
            widget.onCleared?.call();
            widget.onQuery?.call(null);
          }),
    );
  }

  InputDecoration _buildDecoration(BuildContext context) {
    final theme = Theme.of(context);

    return InputDecoration(
      hintText: focus.hasFocus
          ? null
          : MaterialLocalizations.of(context).searchFieldLabel,
      isCollapsed: true,
      fillColor: theme.brightness == Brightness.light
          ? Colors.black12
          : theme.cardTheme.color?.withValues(alpha: 0.9),
      filled: true,
      contentPadding: const EdgeInsets.all(8.0),
      border: OutlineInputBorder(
        borderRadius: radius,
        borderSide: BorderSide.none,
      ),
      focusedBorder: OutlineInputBorder(
        borderSide: BorderSide(color: theme.primaryColor),
        borderRadius: radius,
      ),
    );
  }

  void _onQuery(String query) {
    if (query.isEmpty) {
      widget.onQuery?.call(null);
      return;
    }

    _searcher?.search(query);
  }

  BorderRadius get radius => BorderRadius.circular(8);

  @override
  void dispose() {
    focus.dispose();
    if (widget.controller == null) {
      _textController.dispose();
    }

    super.dispose();
  }
}
