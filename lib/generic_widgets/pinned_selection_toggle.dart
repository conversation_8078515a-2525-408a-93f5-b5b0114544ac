import 'package:flutter/material.dart';
import 'package:sliver_tools/sliver_tools.dart';

import '../dependency_injection/dependency_injector.dart';
import '../l10n/generated/app_localizations.dart';
import '../models/selection.dart';
import 'visited_toggle_bar.dart';

class PinnedSelectionToggle extends StatelessWidget {
  const PinnedSelectionToggle({
    super.key,
    required this.active,
    required this.onChanged,
    this.selections = const [Selection.been, Selection.want],
    this.expand = false,
  });

  final bool expand;
  final Selection active;
  final ValueChanged<Selection> onChanged;
  final List<Selection> selections;

  @override
  Widget build(BuildContext context) {
    final localizations = AppLocalizations.of(context)!;
    final palette = DependencyInjector.settingsBloc.currentPalette;
    return SliverPinnedHeader(
      child: Center(
        child: Padding(
          padding: const EdgeInsets.all(8.0),
          child: VisitedToggleBar<Selection>(
            selected: active,
            expand: expand,
            items: [
              for (final selection in selections)
                ToggleItem(
                  value: selection,
                  label: selection.localized(localizations),
                  color: palette.colorForSelection(selection),
                ),
            ],
            onSelected: onChanged,
          ),
        ),
      ),
    );
  }
}
