import 'package:flutter/cupertino.dart';
import '../models/device.dart';

class ResponsiveSliverPadding extends SliverPadding
    with ResponsivePaddingShared {
  ResponsiveSliverPadding({
    super.key,
    required BuildContext context,
    required Widget sliver,
    bool fillToEdgeOnPhone = true,
  }) : super(
            padding: ResponsivePaddingShared.responsivePadding(
              context,
              fillToEdgeOnPhone: fillToEdgeOnPhone,
            ),
            sliver: sliver);
}

class ResponsivePadding extends Padding with ResponsivePaddingShared {
  ResponsivePadding({
    super.key,
    required BuildContext context,
    required Widget child,
    bool fillToEdgeOnPhone = true,
  }) : super(
          padding: ResponsivePaddingShared.responsivePadding(
            context,
            fillToEdgeOnPhone: fillToEdgeOnPhone,
          ),
          child: child,
        );
}

mixin ResponsivePaddingShared {
  static EdgeInsets responsivePadding(
    BuildContext context, {
    bool fillToEdgeOnPhone = true,
  }) {
    return Device.isTablet(context)
        ? const EdgeInsets.symmetric(horizontal: Device.tabletMargin)
        : _phoneMargin(fillToEdgeOnPhone);
  }

  static EdgeInsets _phoneMargin(bool fillToEdgeOnPhone) {
    return fillToEdgeOnPhone
        ? EdgeInsets.zero
        : const EdgeInsets.symmetric(horizontal: 16.0);
  }
}
