import 'package:flutter/material.dart';

import '../bridges/environment_fetcher.dart';
import '../dependency_injection/dependency_injector.dart';
import '../features/sharing/email_sender.dart';
import '../l10n/generated/app_localizations.dart';
import 'platform_aware/platform_arrow.dart';

class MissingItemSliverTile extends StatelessWidget {
  const MissingItemSliverTile({
    super.key,
    required this.title,
    required this.emailTitle,
    this.style,
  });

  final String title;
  final String emailTitle;
  final TextStyle? style;

  @override
  Widget build(BuildContext context) {
    return SliverToBoxAdapter(
      child: GestureDetector(
        behavior: HitTestBehavior.opaque,
        onTap: () => _onEmailTapped(context),
        child: SafeArea(
          top: false,
          child: Padding(
            padding: const EdgeInsets.all(16.0),
            child: Row(
              children: [
                Expanded(
                    child: Text(
                  title,
                  style: style,
                )),
                PlatformArrow(
                  color: style?.color ??
                      Theme.of(context).textTheme.bodyLarge?.color,
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  void _onEmailTapped(BuildContext context) async {
    final localization = AppLocalizations.of(context)!;

    final mailer = EmailSender(
      environment: Environment.of(context)!,
      user: DependencyInjector.sessionBloc.user,
      selections: DependencyInjector.areaSelectionBloc,
      localizations: localization,
    );

    mailer.send(
      context: context,
      subject: emailTitle,
    );
  }
}
