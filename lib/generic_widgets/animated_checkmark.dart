import 'package:flutter/material.dart';

class AnimatedC<PERSON><PERSON>mark extends StatelessWidget {
  final double? width;
  final bool checked;
  final Color? color;

  const AnimatedCheckmark(
      {super.key, required this.checked, this.width, this.color});

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      width: width,
      child: AnimatedSwitcher(
        duration: const Duration(milliseconds: 200),
        transitionBuilder: (child, animation) => ScaleTransition(
          scale: animation,
          child: RotationTransition(
            turns: Tween(begin: 0.5, end: 0.0).animate(animation),
            child: child,
          ),
        ),
        child: checked
            ? Icon(
                Icons.check,
                color: color,
              )
            : const SizedBox(),
      ),
    );
  }
}
