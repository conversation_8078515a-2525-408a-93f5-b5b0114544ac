import 'package:flutter/material.dart';

class SeparatedTile extends StatelessWidget {
  final Widget child;
  final bool divideTop;
  final bool divideBottom;
  final Color? color;

  const SeparatedTile({
    super.key,
    required this.child,
    this.divideTop = false,
    this.divideBottom = true,
    this.color,
  });

  @override
  Widget build(BuildContext context) {
    return AnimatedContainer(
      duration: const Duration(milliseconds: 400),
      decoration: BoxDecoration(
        color: color ?? Theme.of(context).cardColor,
        border: Border(
          top: divideTop ? _buildDivider(context) : BorderSide.none,
          bottom: divideBottom ? _buildDivider(context) : BorderSide.none,
        ),
      ),
      child: child,
    );
  }

  BorderSide _buildDivider(BuildContext context) => BorderSide(
        color: Theme.of(context).dividerColor,
        width: 1,
      );
}
