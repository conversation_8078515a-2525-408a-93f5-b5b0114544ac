import 'dart:ui';

import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';

sealed class ImageLocation {
  final String location;
  final Object? heroId;

  const ImageLocation(this.location, [this.heroId]);
}

class NetworkImageLocation extends ImageLocation {
  const NetworkImageLocation(String location, {Object? heroId})
    : super(location, heroId);
}

class PushInDetector extends StatefulWidget {
  const PushInDetector({
    super.key,
    required this.child,
    required this.onTapped,
    this.shadow,
  });

  final Widget child;
  final VoidCallback onTapped;
  final BoxShadow? shadow;

  @override
  State<PushInDetector> createState() => _PushInDetectorState();
}

class _PushInDetectorState extends State<PushInDetector> {
  bool _isTapped = false;
  final animDuration = const Duration(milliseconds: 250);

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTapDown: (_) => setState(() => _isTapped = true),
      onTapUp: (_) => setState(() => _isTapped = false),
      onTapCancel: () => setState(() => _isTapped = false),
      onTap: widget.onTapped,
      child: AnimatedContainer(
        alignment: Alignment.center,
        duration: animDuration,
        curve: Curves.decelerate,
        transform: _isTapped ? _scaledMatrix() : Matrix4.identity(),
        decoration: _buildDecoration(context),
        child: widget.child,
      ),
    );
  }

  Decoration _buildDecoration(BuildContext context) {
    final theme = Theme.of(context);
    return BoxDecoration(
      borderRadius: BorderRadius.circular(6),
      boxShadow: [
        if (!_isTapped)
          widget.shadow ??
              BoxShadow(
                blurRadius: 8,
                offset: const Offset(0, 4),
                color: theme.brightness == Brightness.light
                    ? theme.disabledColor
                    : theme.canvasColor,
              ),
      ],
    );
  }

  Matrix4 _scaledMatrix() {
    final matrix = Matrix4.identity().scaled(0.98, 0.98);
    matrix.translate(2.5, 2.5, 0.0);
    return matrix;
  }
}

class AssetImageLocation extends ImageLocation {
  const AssetImageLocation(String location, {Object? heroId})
    : super(location, heroId);
}

class NoImageLocation extends ImageLocation {
  const NoImageLocation() : super('no_image');
}

class PushableTile extends StatelessWidget {
  const PushableTile({
    super.key,
    this.title,
    this.titleWidget,
    required this.imageLocation,
    this.onTapped,
    this.topRightIcon,
    this.imageFit = BoxFit.fitWidth,
    this.imageHeight,
    this.titleStyle = const TextStyle(
      fontWeight: FontWeight.w600,
      fontSize: 15,
    ),
    this.trailing,
  }) : assert(title != null || titleWidget != null);

  final String? title;
  final Widget? titleWidget;
  final ImageLocation imageLocation;
  final VoidCallback? onTapped;
  final TextStyle titleStyle;
  final Widget? topRightIcon;
  final Widget? trailing;
  final BoxFit? imageFit;
  final double? imageHeight;

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4.0),
      child: onTapped != null
          ? PushInDetector(onTapped: onTapped!, child: _buildButton(context))
          : _buildButton(context),
    );
  }

  Widget _buildButton(BuildContext context) {
    return topRightIcon != null
        ? Stack(
            children: [
              _buildImageAndText(context),
              Positioned(top: 10, right: 16, child: topRightIcon!),
            ],
          )
        : _buildImageAndText(context);
  }

  Widget _buildImageAndText(BuildContext context) {
    return Stack(
      children: [
        _buildThumbnail(),
        Positioned(
          bottom: 0,
          left: 0,
          right: 0,
          child: ClipRect(
            child: BackdropFilter(
              filter: ImageFilter.blur(sigmaX: 2, sigmaY: 2),
              child: Container(
                width: double.infinity,
                color: Theme.of(
                  context,
                ).secondaryHeaderColor.withValues(alpha: 0.75),
                child: Padding(
                  padding: const EdgeInsets.all(8.0),
                  child: _buildTitleBarContent(context),
                ),
              ),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildTitleBarContent(BuildContext context) {
    if (trailing == null) {
      return buildTitle(context);
    }

    return Row(
      children: [
        Expanded(child: buildTitle(context)),
        trailing!,
      ],
    );
  }

  Widget buildTitle(BuildContext context) {
    if (titleWidget != null) {
      final style = titleStyle.color == null
          ? titleStyle.copyWith(
              color: Theme.of(context).textTheme.bodyMedium?.color,
            )
          : titleStyle;
      return DefaultTextStyle(style: style, child: titleWidget!);
    }

    return Text(title!, style: titleStyle);
  }

  Widget _buildThumbnail() {
    final heroTag = imageLocation.heroId;
    if (heroTag != null) {
      return Hero(tag: heroTag, child: _buildImage(imageLocation));
    }

    return _buildImage(imageLocation);
  }

  Widget _buildImage(ImageLocation imageLocation) {
    return switch (imageLocation) {
      final NetworkImageLocation e => _buildNetworkImage(e),
      final AssetImageLocation e => _buildAssetImage(e),
      final NoImageLocation _ => _buildNoImage(),
    };
  }

  Widget _buildNoImage() {
    return SizedBox(
      width: double.infinity,
      height: imageHeight,
      child: const Center(
        child: Icon(
          Icons.image_not_supported_outlined,
          color: Colors.grey,
          size: 50,
        ),
      ),
    );
  }

  Widget _buildAssetImage(AssetImageLocation image) {
    return Image.asset(
      image.location,
      width: double.infinity,
      height: imageHeight,
      fit: imageFit,
      // alignment: Alignment.topLeft,
    );
  }

  Widget _buildNetworkImage(NetworkImageLocation image) {
    return CachedNetworkImage(
      imageUrl: image.location,
      width: double.infinity,
      height: imageHeight,
      fit: imageFit,
      placeholder: (context, text) =>
          Container(height: 220, color: Theme.of(context).cardColor),
    );
  }
}
