import 'package:flutter/material.dart';
import '../helpers/string_extensions.dart';

import 'selectable_item.dart';

class SelectableItemGenericIcon extends StatelessWidget {
  const SelectableItemGenericIcon({
    super.key,
    required this.item,
    this.useInitials = false,
  });

  final SelectableItem item;
  final bool useInitials;

  @override
  Widget build(BuildContext context) {
    return CircleAvatar(
      backgroundColor: Theme.of(context).colorScheme.primary,
      radius: 16,
      child: FittedBox(
        fit: BoxFit.scaleDown,
        child: Text(
          _buildTitle(),
          textAlign: TextAlign.center,
          style: Theme.of(context).textTheme.titleLarge?.copyWith(
                color: Colors.white,
                fontSize: 16,
                fontWeight: FontWeight.bold,
              ),
        ),
      ),
    );
  }

  String _buildTitle() {
    if (useInitials) {
      return item.name.initials;
    }
    return item.name.isNotEmpty ? item.name[0].toUpperCase() : '';
  }
}
