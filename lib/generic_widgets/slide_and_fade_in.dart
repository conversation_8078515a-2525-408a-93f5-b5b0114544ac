import 'package:flutter/material.dart';

class SlideAndFadeIn extends StatefulWidget {
  final Offset offset;
  final Duration duration;
  final Duration delay;
  final Widget child;
  final Curve curve;

  const SlideAndFadeIn({
    super.key,
    required this.offset,
    required this.duration,
    this.delay = Duration.zero,
    this.curve = Curves.easeOutCubic,
    required this.child,
  });

  @override
  State createState() => _SlideAndFadeInState();
}

class _SlideAndFadeInState extends State<SlideAndFadeIn>
    with SingleTickerProviderStateMixin {
  AnimationController? _controller;
  late final Animation<Offset> _offset;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(vsync: this, duration: widget.duration);
    _offset = Tween(
      begin: widget.offset,
      end: Offset.zero,
    ).animate(
      CurvedAnimation(
        parent: _controller!,
        curve: widget.curve,
      ),
    );
    Future.delayed(widget.delay).then((_) {
      if (mounted) {
        _controller?.forward();
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    final controller = _controller;
    if (controller == null) {
      return widget.child;
    }

    return AnimatedBuilder(
      animation: controller,
      builder: (context, child) => FadeTransition(
        opacity: controller,
        child: SlideTransition(
          position: _offset,
          child: child,
        ),
      ),
      child: widget.child,
    );
  }

  @override
  void dispose() {
    _controller?.dispose();
    super.dispose();
  }
}
