import 'package:flutter/material.dart' hide SearchBar;

import '../helpers/margin.dart';
import 'search_bar.dart';
import 'selectable_item.dart';
import 'sliver_sticky_bar.dart';

class SliverStickySearchBar<T extends SelectableItem> extends StatelessWidget {
  const SliverStickySearchBar({
    super.key,
    this.items,
    this.onQuery,
    this.searchController,
    this.onCleared,
  });

  final List<T>? items;
  final ValueChanged<List<T>?>? onQuery;
  final TextEditingController? searchController;
  final VoidCallback? onCleared;

  @override
  Widget build(BuildContext context) {
    return SliverStickyBar(
      child: Padding(
        padding: const EdgeInsets.only(
          left: Margin.small,
          right: Margin.small,
          bottom: Margin.small,
        ),
        child: SearchBar(
          items: items,
          onQuery: onQuery,
          controller: searchController,
          onCleared: onCleared,
        ),
      ),
    );
  }
}
