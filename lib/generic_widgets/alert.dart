import 'dart:io';

import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';

import '../l10n/generated/app_localizations.dart';

mixin Presentable<T> implements Widget {
  Future<T?> show(
    BuildContext context, {
    bool? barrierDismissible,
  }) {
    if (Platform.isIOS) {
      return showCupertinoDialog<T>(
          context: context,
          builder: (context) => this,
          barrierDismissible: barrierDismissible ?? this.barrierDismissible);
    } else {
      return showDialog<T>(
        context: context,
        barrierDismissible: barrierDismissible ?? this.barrierDismissible,
        builder: (context) => this,
      );
    }
  }

  bool get barrierDismissible => false;
}

class AlertAction {
  final String title;
  final bool destructive;
  final void Function(BuildContext context) onTap;

  const AlertAction({
    required this.title,
    this.destructive = false,
    required this.onTap,
  });
}

class Alert<T> extends StatelessWidget with Presentable<T> {
  final String? title;
  final String? message;
  final Widget? body;
  final List<AlertAction>? actions;

  const Alert({
    super.key,
    this.title,
    this.message,
    this.body,
    this.actions,
  });

  @override
  Widget build(BuildContext context) {
    return Platform.isIOS
        ? _buildIOSDialog(context)
        : _buildAndroidDialog(context);
  }

  Widget _buildIOSDialog(BuildContext context) {
    final actions = this.actions ?? _defaultActions(context);

    return CupertinoAlertDialog(
      title: _buildTitle(),
      content: _buildContent(),
      actions: actions
          .map((action) => CupertinoButton(
                child: Text(
                  action.title,
                  textAlign: TextAlign.center,
                  style: action.destructive
                      ? TextStyle(color: Theme.of(context).colorScheme.error)
                      : null,
                ),
                onPressed: () => action.onTap(context),
              ))
          .toList(),
    );
  }

  Widget? _buildContent() {
    if (body != null) {
      return body;
    }

    if (message != null) {
      return Text(message!);
    }

    return null;
  }

  Widget _buildAndroidDialog(BuildContext context) {
    final actions = this.actions ?? _defaultActions(context);
    return AlertDialog(
      title: _buildTitle(),
      content: _buildContent(),
      actions: actions
          .map(
            (action) => TextButton(
              style: ButtonStyle(
                foregroundColor: WidgetStateProperty.all(
                  action.destructive ? Colors.red : null,
                ),
              ),
              child: Text(action.title),
              onPressed: () => action.onTap(context),
            ),
          )
          .toList(),
    );
  }

  Widget? _buildTitle() {
    return title != null ? Text(title!) : null;
  }

  List<AlertAction> _defaultActions(BuildContext context) => [
        AlertAction(
          title: AppLocalizations.of(context)!.ok,
          onTap: (context) => Navigator.of(context).pop(),
        )
      ];
}

class ConfirmDialog extends Alert<bool> {
  ConfirmDialog({
    super.key,
    required super.title,
    required super.message,
    required String confirmText,
    required String cancelText,
    bool confirmDestructive = false,
    bool cancelDestructive = false,
  }) : super(
          actions: [
            AlertAction(
              title: cancelText,
              destructive: cancelDestructive,
              onTap: (context) => Navigator.of(context).pop(false),
            ),
            AlertAction(
              title: confirmText,
              destructive: confirmDestructive,
              onTap: (context) => Navigator.of(context).pop(true),
            )
          ],
        );
}
