import 'dart:math';

import 'package:flutter/material.dart';

// <PERSON><PERSON> from ListView.separated
class SliverChildSeparatedDelegate extends SliverChildBuilderDelegate {
  SliverChildSeparatedDelegate({
    required int itemCount,
    required IndexedWidgetBuilder itemBuilder,
    required IndexedWidgetBuilder separatorBuilder,
  }) : super(
         (BuildContext context, int index) {
           final itemIndex = index ~/ 2;
           final Widget widget;
           if (index.isEven) {
             widget = itemBuilder(context, itemIndex);
           } else {
             widget = separatorBuilder(context, itemIndex);
           }
           return widget;
         },
         childCount: _computeActualChildCount(itemCount),
         semanticIndexCallback: (Widget _, int index) {
           return index.isEven ? index ~/ 2 : null;
         },
       );

  // Helper method to compute the actual child count for the separated constructor.
  static int _computeActualChildCount(int itemCount) {
    return max(0, itemCount * 2 - 1);
  }
}
