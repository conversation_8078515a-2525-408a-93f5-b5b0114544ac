import 'dart:ui';

import 'package:flutter/material.dart';

class ListViewHeader extends StatelessWidget {
  const ListViewHeader({
    super.key,
    required this.title,
    this.trailing,
    this.style = const TextStyle(
      fontWeight: FontWeight.bold,
      fontSize: 18,
    ),
  });

  final String title;
  final TextStyle? style;
  final Widget? trailing;

  @override
  Widget build(BuildContext context) {
    return ClipRect(
      child: BackdropFilter(
        filter: ImageFilter.blur(sigmaX: 10.0, sigmaY: 10.0),
        child: Container(
          color:
              Theme.of(context).scaffoldBackgroundColor.withValues(alpha: 0.86),
          child: Padding(
            padding: const EdgeInsets.only(
              left: 8,
              bottom: 8,
              top: 24,
              right: 8,
            ),
            child: trailing != null
                ? Row(
                    children: [
                      Expanded(
                        child: _buildTitle(),
                      ),
                      trailing!
                    ],
                  )
                : _buildTitle(),
          ),
        ),
      ),
    );
  }

  Widget _buildTitle() {
    return Text(
      title,
      style: style,
    );
  }
}
