import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';

class PlatformTextField extends StatelessWidget {
  const PlatformTextField({
    super.key,
    this.focusNode,
    this.controller,
    this.autofocus = false,
    this.onSubmitted,
    this.onTapped,
    this.textStyle,
    this.textAlign = TextAlign.start,
  });

  final FocusNode? focusNode;
  final TextEditingController? controller;
  final bool autofocus;
  final ValueChanged<String>? onSubmitted;
  final VoidCallback? onTapped;
  final TextStyle? textStyle;
  final TextAlign textAlign;

  @override
  Widget build(BuildContext context) {
    return Theme.of(context).platform == TargetPlatform.iOS
        ? CupertinoTextField(
            focusNode: focusNode,
            controller: controller,
            autofocus: autofocus,
            onSubmitted: onSubmitted,
            onTap: onTapped,
            style: textStyle,
            textAlign: textAlign,
          )
        : TextField(
            focusNode: focusNode,
            controller: controller,
            autofocus: autofocus,
            onSubmitted: onSubmitted,
            onTap: onTapped,
            style: textStyle,
            textAlign: textAlign,
          );
  }
}
