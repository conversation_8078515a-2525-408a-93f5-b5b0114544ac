import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';

class PlatformTextButton extends StatelessWidget {
  final String title;
  final VoidCallback? onTapped;
  final TextStyle? textStyle;
  final EdgeInsets? padding;

  const PlatformTextButton({
    super.key,
    required this.title,
    this.onTapped,
    this.textStyle,
    this.padding,
  });

  @override
  Widget build(BuildContext context) {
    return Theme.of(context).platform == TargetPlatform.iOS
        ? CupertinoButton(
            padding: padding,
            onPressed: onTapped,
            child: Text(title, style: textStyle),
          )
        : TextButton(
            onPressed: onTapped,
            child: Text(
              title,
              style: textStyle,
            ),
          );
  }
}
