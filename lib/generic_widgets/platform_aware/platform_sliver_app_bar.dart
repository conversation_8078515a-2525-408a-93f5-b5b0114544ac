import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';

import '../../models/color_extensions.dart';
import 'platform_icon_button.dart';

class PlatformSliverAppBar extends StatelessWidget {
  const PlatformSliverAppBar({
    super.key,
    required this.title,
    this.action,
    this.backgroundColour,
    this.titleColour,
    this.leading,
    this.elevation,
  });
  final String title;
  final Widget? action;
  final Color? backgroundColour;
  final Color? titleColour;
  final Widget? leading;
  final double? elevation;
  @override
  Widget build(BuildContext context) {
    return Theme.of(context).platform == TargetPlatform.iOS
        ? _buildCupertinoAppBar(context)
        : _buildMaterialAppBar(context);
  }

  Widget _buildCupertinoAppBar(BuildContext context) {
    final brightness = Theme.of(context).brightness;
    return CupertinoTheme(
      data: CupertinoThemeData(
        primaryColor:
            titleColour ?? Theme.of(context).textTheme.titleLarge!.color,
      ),
      child: CupertinoSliverNavigationBar(
        backgroundColor:
            backgroundColour ??
            (brightness == Brightness.dark
                ? Theme.of(
                    context,
                  ).scaffoldBackgroundColor.withValues(alpha: 0.8)
                : null),
        trailing: action,
        leading: leading ?? _buildLocalizedCloseButton(context),
        largeTitle: FittedBox(
          fit: BoxFit.scaleDown,
          alignment: Alignment.centerLeft,
          child: Text(
            title,
            style: TextStyle(
              color:
                  titleColour ?? Theme.of(context).textTheme.titleLarge!.color,
            ),
          ),
        ),
      ),
    );
  }

  Widget? _buildLocalizedCloseButton(BuildContext context) {
    final route = ModalRoute.of(context);
    if (route is! MaterialPageRoute) {
      return null;
    }

    if (route.fullscreenDialog) {
      return PlatformIconButton(
        icon: Icons.close,
        iconColour: titleColour,
        onTapped: () => route.navigator?.maybePop(),
      );
    }

    return null;
  }

  Widget _buildMaterialAppBar(BuildContext context) {
    return SliverAppBar(
      pinned: true,
      backgroundColor: backgroundColour,
      leading: leading,
      elevation: elevation,
      title: Text(
        title,
        style: backgroundColour != null
            ? TextStyle(color: backgroundColour!.legibleForegroundColor())
            : null,
      ),
      actions: [if (action != null) action!],
    );
  }
}
