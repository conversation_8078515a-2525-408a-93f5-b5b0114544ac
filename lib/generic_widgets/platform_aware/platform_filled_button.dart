import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';

class PlatformFilledButton extends StatelessWidget {
  const PlatformFilledButton({
    super.key,
    required this.title,
    this.onTapped,
    this.color,
    this.textColor,
    this.disabledTextColor,
    this.fitText = false,
    this.fontSize,
    this.trailing,
  });

  final VoidCallback? onTapped;
  final bool fitText;
  final String title;
  final Color? color;
  final Color? textColor;
  final Color? disabledTextColor;
  final Widget? trailing;
  final double? fontSize;

  @override
  Widget build(BuildContext context) {
    return Theme.of(context).platform == TargetPlatform.iOS
        ? _buildCupertinoButton(context)
        : _buildMaterialButton(context);
  }

  Widget _buildCupertinoButton(BuildContext context) {
    return CupertinoButton(
      color: color ?? Theme.of(context).colorScheme.primary,
      // disabledColor: disabledTextColor ?? CupertinoColors.quaternarySystemFill,
      onPressed: onTapped,
      padding: const EdgeInsets.all(8),
      child: _buildContent(context),
    );
  }

  Widget _buildMaterialButton(BuildContext context) {
    return ElevatedButton(
      style: ElevatedButton.styleFrom(
        backgroundColor: color ?? Theme.of(context).colorScheme.primary,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16.0),
        ),
      ),
      onPressed: onTapped,
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 16),
        child: _buildContent(context),
      ),
    );
  }

  Widget _buildContent(BuildContext context) {
    return SizedBox(
      width: double.infinity,
      child: fitText
          ? FittedBox(
              fit: BoxFit.scaleDown,
              child: _buildContent(context),
            )
          : _buildTrailingAndText(context),
    );
  }

  Widget _buildTrailingAndText(BuildContext context) {
    if (trailing == null) {
      return _buildText(context);
    }

    return Row(
      spacing: 8.0,
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        _buildText(context),
        trailing!,
      ],
    );
  }

  Widget _buildText(BuildContext context) {
    return Text(
      title,
      textAlign: TextAlign.center,
      style: TextStyle(
          fontSize: fontSize ?? 18,
          fontWeight: FontWeight.bold,
          color: _determineTextColor(context)),
    );
  }

  Color _determineTextColor(BuildContext context) {
    if (!enabled) {
      return disabledTextColor ?? Theme.of(context).disabledColor;
    }

    if (textColor != null) {
      return textColor!;
    }

    return Colors.white;
  }

  bool get enabled => onTapped != null;
}
