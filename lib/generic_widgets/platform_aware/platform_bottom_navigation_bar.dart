import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import '../../models/color_extensions.dart';

class PlatformBottomNavigationBar extends StatelessWidget {
  final List<BottomNavigationBarItem> items;
  final int currentIndex;
  final ValueChanged<int> onTap;

  const PlatformBottomNavigationBar({
    super.key,
    required this.items,
    required this.onTap,
    this.currentIndex = 0,
  }) : assert(currentIndex < items.length);

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Theme.of(context).platform == TargetPlatform.iOS
        ? CupertinoTabBar(
            activeColor: theme.primaryColor,
            inactiveColor: theme.disabledColor,
            currentIndex: currentIndex,
            onTap: onTap,
            items: items,
          )
        : BottomNavigationBar(
            backgroundColor: theme.brightness == Brightness.light
                ? Colors.white
                : theme.scaffoldBackgroundColor.withBrightness(0.02),
            items: items,
            onTap: onTap,
            currentIndex: currentIndex,
            showUnselectedLabels: true,
            selectedItemColor: theme.primaryColor,
            unselectedItemColor: theme.disabledColor,
            type: BottomNavigationBarType.fixed,
          );
  }
}
