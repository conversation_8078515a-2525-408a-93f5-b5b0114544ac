import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';

class PlatformSwitch extends StatelessWidget {
  final bool checked;
  final ValueChanged<bool>? onChanged;
  final Color? trackColor;

  const PlatformSwitch({
    super.key,
    required this.checked,
    required this.onChanged,
    this.trackColor,
  });

  @override
  Widget build(BuildContext context) {
    return Theme.of(context).platform == TargetPlatform.iOS
        ? CupertinoSwitch(
            inactiveTrackColor: trackColor,
            value: checked,
            activeTrackColor: Theme.of(context).colorScheme.primary,
            onChanged: onChanged,
          )
        : Switch(
            trackColor: trackColor != null
                ? WidgetStatePropertyAll(trackColor)
                : null,
            value: checked,
            activeColor: Theme.of(context).colorScheme.primary,
            onChanged: onChanged,
          );
  }
}
