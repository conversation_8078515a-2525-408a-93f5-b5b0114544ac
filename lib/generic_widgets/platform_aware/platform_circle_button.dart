import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';

class PlatformCircleButton extends StatelessWidget {
  final Widget child;
  final VoidCallback? onTapped;
  final Color? fillColour;
  final Color? disabledColour;

  const PlatformCircleButton({
    super.key,
    this.onTapped,
    this.fillColour,
    this.disabledColour,
    required this.child,
  });

  @override
  Widget build(BuildContext context) {
    return Theme.of(context).platform == TargetPlatform.iOS
        ? _buildCupertino()
        : _buildMaterial();
  }

  Widget _buildMaterial() {
    return ElevatedButton(
      style: ButtonStyle(
          backgroundColor: WidgetStateProperty.all(fillColour),
          shape: WidgetStateProperty.all(const CircleBorder()),
          padding: WidgetStateProperty.all(EdgeInsets.zero)),
      onPressed: onTapped,
      child: child,
    );
  }

  Widget _buildCupertino() {
    return Padding(
      padding: const EdgeInsets.all(4.0),
      child: CupertinoTheme(
        data: CupertinoThemeData(primaryColor: fillColour),
        child: CupertinoButton.filled(
          borderRadius: BorderRadius.circular(100),
          padding: EdgeInsets.zero,
          onPressed: onTapped,
          disabledColor: disabledColour ?? CupertinoColors.quaternarySystemFill,
          child: child,
        ),
      ),
    );
  }
}
