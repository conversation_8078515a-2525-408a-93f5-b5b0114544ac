import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';

class PlatformArrow extends StatelessWidget {
  const PlatformArrow({
    super.key,
    this.color,
  });

  final Color? color;

  @override
  Widget build(BuildContext context) {
    return Icon(_icon(context), color: color);
  }

  IconData _icon(BuildContext context) =>
      Theme.of(context).platform == TargetPlatform.iOS
          ? CupertinoIcons.chevron_forward
          : Icons.arrow_forward_rounded;
}
