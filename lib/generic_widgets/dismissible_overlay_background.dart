import 'package:flutter/material.dart';

class DismissibleOverlayBackground extends StatelessWidget {
  const DismissibleOverlayBackground({
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: Navigator.of(context).pop,
      behavior: HitTestBehavior.opaque,
      child: Container(
        width: double.infinity,
        height: double.infinity,
        color: Colors.black12,
      ),
    );
  }
}
