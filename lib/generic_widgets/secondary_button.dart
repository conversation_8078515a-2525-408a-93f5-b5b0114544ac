import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';

class SecondaryButton extends StatelessWidget {
  final String? title;
  final Widget? child;
  final VoidCallback? onTapped;

  const SecondaryButton({
    super.key,
    this.title,
    this.child,
    this.onTapped,
  });

  @override
  Widget build(BuildContext context) {
    return Theme.of(context).platform == TargetPlatform.iOS
        ? _buildCupertinoButton(context)
        : _buildMaterialButton(context);
  }

  Widget _buildMaterialButton(BuildContext context) {
    return TextButton(
      onPressed: onTapped,
      child: _buildTitle(context),
    );
  }

  Widget _buildCupertinoButton(BuildContext context) {
    return CupertinoButton(
      onPressed: onTapped,
      child: _buildTitle(context),
    );
  }

  Widget _buildTitle(BuildContext context) {
    if (child != null) {
      return child!;
    }

    final textTheme = Theme.of(context).textTheme;
    return Text(
      title ?? '',
      style: textTheme.bodyLarge?.copyWith(color: textTheme.titleLarge?.color),
    );
  }
}
