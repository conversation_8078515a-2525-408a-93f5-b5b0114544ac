import 'dart:ui';

import 'package:flutter/material.dart';

class FrostedGlassDialog extends StatelessWidget {
  const FrostedGlassDialog({
    super.key,
    required this.child,
  });

  final Widget child;

  @override
  Widget build(BuildContext context) {
    return Dialog(
      shape: RoundedRectangleBorder(
          side: BorderSide(
            color: Theme.of(context).cardColor,
            width: 0.5,
          ),
          borderRadius: BorderRadius.circular(16.0)),
      backgroundColor: Theme.of(context).cardColor.withValues(alpha: 0.8),
      elevation: 2,
      child: ConstrainedBox(
        constraints: const BoxConstraints(maxWidth: 400),
        child: _buildBlurryBackground(
          context: context,
          child: child,
        ),
      ),
    );
  }

  Widget _buildBlurryBackground({
    required BuildContext context,
    required Widget child,
  }) {
    return ClipRRect(
      borderRadius: BorderRadius.circular(16.0),
      child: BackdropFilter(
        filter: ImageFilter.compose(
            outer: ColorFilter.mode(
              Theme.of(context).brightness == Brightness.light
                  ? Colors.white38
                  : Colors.black38,
              BlendMode.luminosity,
            ),
            inner: ImageFilter.blur(sigmaY: 4, sigmaX: 4)),
        child: child,
      ),
    );
  }
}
