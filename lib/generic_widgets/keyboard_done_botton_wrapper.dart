import 'package:flutter/material.dart';
import 'package:keyboard_actions/keyboard_actions.dart';

import '../l10n/generated/app_localizations.dart';
import 'platform_aware/platform_text_button.dart';

class KeyboardDoneButtonWrapper extends StatelessWidget {
  const KeyboardDoneButtonWrapper({
    super.key,
    required this.focus,
    required this.child,
  });

  final FocusNode focus;
  final Widget child;

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final doneText = AppLocalizations.of(context)!.done;

    return KeyboardActions(
      config: KeyboardActionsConfig(
        nextFocus: false,
        keyboardBarColor: theme.brightness == Brightness.light
            ? Colors.grey[200]
            : Colors.black87,
        actions: [
          KeyboardActionsItem(
            focusNode: focus,
            displayDoneButton: false,
            toolbarButtons: [
              (focus) => FittedBox(
                    fit: BoxFit.fitHeight,
                    child: PlatformTextButton(
                      title: doneText,
                      textStyle: const TextStyle(
                        fontSize: 20,
                        fontWeight: FontWeight.bold,
                      ),
                      onTapped: FocusManager.instance.primaryFocus?.unfocus,
                    ),
                  ),
            ],
          ),
        ],
      ),
      child: child,
    );
  }
}
