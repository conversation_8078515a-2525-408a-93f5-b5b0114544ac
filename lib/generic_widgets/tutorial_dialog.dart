import 'package:flutter/material.dart';

import '../dependency_injection/dependency_injector.dart';
import '../l10n/generated/app_localizations.dart';
import 'dismissible_overlay_background.dart';
import 'frosted_glass_dialog.dart';
import 'platform_aware/platform_filled_button.dart';

class TutorialDialog extends StatefulWidget {
  const TutorialDialog({
    super.key,
    this.items,
    this.itemBuilder,
  }) : assert(items != null || itemBuilder != null);

  final List<TutorialItem>? items;
  final List<TutorialItem> Function(BuildContext context)? itemBuilder;

  @override
  State<TutorialDialog> createState() => _TutorialDialogState();

  Future<void> showOnlyOnce({
    required BuildContext context,
    required String hasShownKey,
  }) async {
    final prefs = DependencyInjector.sharedPrefsStorage;
    final hasShown = await prefs.getBool(hasShownKey);

    if (hasShown) {
      return;
    }

    prefs.putBool(hasShownKey, true);

    WidgetsBinding.instance.addPostFrameCallback((_) {
      showAdaptiveDialog(context: context, builder: (_) => this);
    });
  }
}

class _TutorialDialogState extends State<TutorialDialog> {
  int index = 0;
  bool loaded = false;

  final animDuration = const Duration(milliseconds: 350);

  late final List<TutorialItem> items;

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) async {
      final items = widget.items ?? widget.itemBuilder?.call(context);
      if (items == null) {
        return;
      }

      this.items = items;
      await Future.delayed(const Duration(milliseconds: 250));
      setState(() {
        loaded = true;
      });
    });
  }

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        const DismissibleOverlayBackground(),
        AnimatedSwitcher(
          duration: animDuration,
          switchInCurve: Curves.easeIn,
          switchOutCurve: Curves.easeOut,
          transitionBuilder: (child, animation) => FadeTransition(
            opacity: animation,
            child: ScaleTransition(
              scale: animation.drive(Tween(begin: 0.9, end: 1.0)),
              child: SlideTransition(
                position: animation.drive(
                  Tween(begin: const Offset(0, 0.1), end: Offset.zero),
                ),
                child: child,
              ),
            ),
          ),
          child: loaded ? _buildCard(context) : const SizedBox(),
        ),
      ],
    );
  }

  Widget _buildCard(BuildContext context) {
    return FrostedGlassDialog(
      key: ValueKey(index),
      child: Padding(
        padding: const EdgeInsets.symmetric(vertical: 16, horizontal: 8),
        child: Column(
          spacing: 16,
          mainAxisSize: MainAxisSize.min,
          children: [
            Row(
              spacing: 16,
              children: [
                items[index].icon,
                Expanded(
                  child: Text(
                    items[index].label,
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.w700,
                    ),
                  ),
                ),
              ],
            ),
            PlatformFilledButton(
              title: index == items.length - 1
                  ? AppLocalizations.of(context)!.done
                  : AppLocalizations.of(context)!.continueText,
              onTapped: () async {
                if (index >= items.length - 1) {
                  setState(() {
                    loaded = false;
                  });
                  await Future.delayed(animDuration);
                  if (context.mounted) {
                    Navigator.of(context).pop();
                  }
                  return;
                }

                setState(() {
                  index = index + 1;
                });
              },
            ),
          ],
        ),
      ),
    );
  }
}

class TutorialItem {
  final String label;
  final Widget icon;

  const TutorialItem({
    required this.label,
    required this.icon,
  });
}
