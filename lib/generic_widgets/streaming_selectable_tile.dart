import 'package:flutter/material.dart';

import '../dependency_injection/dependency_injector.dart';
import '../models/geo_area.dart';
import '../models/selection.dart';
import 'area_flag.dart';
import 'selectable_item.dart';
import 'selection_badge.dart';
import 'separated_tile.dart';

class SelectableTile extends StatelessWidget {
  const SelectableTile({
    super.key,
    required this.item,
    required this.currentSelection,
    this.nameBuilder,
    this.onTapped,
    this.leading,
  });

  final SelectableItem item;
  final Selection currentSelection;
  final Future<String>? nameBuilder;
  final VoidCallback? onTapped;
  final Widget? leading;

  @override
  Widget build(BuildContext context) {
    return SeparatedTile(
      color: currentSelection != Selection.clear
          ? DependencyInjector.settingsBloc.currentPalette
              .colorForSelection(currentSelection)
              .withValues(alpha: 0.25)
          : null,
      child: ListTile(
        leading: leading ??
            (item is GeoArea ? AreaFlag(area: item as GeoArea) : null),
        title: _buildTitle(),
        onTap: onTapped,
        trailing: SelectionBadge(
          selection: currentSelection,
        ),
      ),
    );
  }

  Widget _buildTitle() {
    const style = TextStyle(fontWeight: FontWeight.w600);

    if (nameBuilder == null) {
      return Text(item.name, style: style);
    }

    return FutureBuilder<String>(
      future: nameBuilder,
      builder: (context, snapshot) =>
          Text(snapshot.data ?? item.name, style: style),
    );
  }
}

class StreamingSelectableTile extends StatelessWidget {
  const StreamingSelectableTile({
    super.key,
    required this.selectionStream,
    required this.item,
    this.nameBuilder,
    this.onTapped,
  });

  final Stream<Selection> selectionStream;
  final SelectableItem item;
  final Future<String>? nameBuilder;
  final VoidCallback? onTapped;

  @override
  Widget build(BuildContext context) {
    return StreamBuilder<Selection>(
      stream: selectionStream,
      initialData: Selection.clear,
      builder: (context, snapshot) {
        final selection = snapshot.requireData;
        return SelectableTile(
          item: item,
          currentSelection: selection,
          onTapped: onTapped,
          nameBuilder: nameBuilder,
        );
      },
    );
  }
}
