import 'package:flutter/material.dart';

import '../helpers/margin.dart';

class HelperTooltip extends StatelessWidget {
  const HelperTooltip({
    super.key,
    required this.message,
    this.color,
    this.child,
    this.preferBelow,
  });

  final String message;
  final Widget? child;
  final Color? color;
  final bool? preferBelow;

  @override
  Widget build(BuildContext context) {
    return Tooltip(
      margin: const EdgeInsets.symmetric(horizontal: Margin.standard),
      showDuration: const Duration(seconds: 30),
      message: message,
      preferBelow: preferBelow,
      padding: const EdgeInsets.all(Margin.standard),
      triggerMode: TooltipTriggerMode.tap,
      textStyle: TextStyle(
        fontWeight: FontWeight.w500,
        fontSize: 14,
        color: Theme.of(context).textTheme.bodyMedium?.color ?? Colors.black,
      ),
      decoration: _buildTooltipDecoration(context),
      child:
          child ??
          Icon(
            Icons.help_outline,
            size: 30,
            color: color ?? Theme.of(context).primaryColor,
          ),
    );
  }

  BoxDecoration _buildTooltipDecoration(BuildContext context) {
    return BoxDecoration(
      color: Theme.of(context).cardColor,
      borderRadius: BorderRadius.circular(8),
      boxShadow: const [
        BoxShadow(offset: Offset(0, 2), blurRadius: 2, color: Colors.black38),
      ],
    );
  }
}
