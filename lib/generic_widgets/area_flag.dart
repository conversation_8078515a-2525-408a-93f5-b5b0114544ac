import 'package:flutter/material.dart';
import '../dependency_injection/dependency_injector.dart';
import '../models/geo_area.dart';

import 'selectable_item_generic_icon.dart';

class AreaFlag extends StatelessWidget {
  const AreaFlag({
    super.key,
    required this.area,
    this.size = 32,
    this.useCachedSize = true,
  });

  final GeoArea area;
  final double size;

  final bool useCachedSize;

  @override
  Widget build(BuildContext context) {
    if (area.flagName == null) {
      return _buildParentFlag(context);
    }

    return Image.asset(
      'assets/flags/${area.flagName}.png',
      width: size,
      semanticLabel: '${area.name} flag',
      errorBuilder: (context, error, stack) => _buildFallback(),
      cacheWidth: useCachedSize ? 64 : null,
      cacheHeight: useCachedSize ? 40 : null,
    );
  }

  Widget _buildFallback() {
    return SelectableItemGenericIcon(
      item: area,
      useInitials: true,
    );
  }

  Widget _buildParentFlag(BuildContext context) {
    return FutureBuilder<GeoArea?>(
      future: DependencyInjector.areaBloc.fetchParent(area),
      builder: (context, snapshot) {
        if (snapshot.hasData == false) {
          return SizedBox(height: size);
        }

        return AreaFlag(size: size, area: snapshot.data!);
      },
    );
  }
}
