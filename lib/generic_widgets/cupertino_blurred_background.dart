import 'dart:ui';

import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

class CupertinoBlurredBackground extends StatelessWidget {
  const CupertinoBlurredBackground({
    super.key,
    required this.child,
    this.backgroundColor,
    this.forceOnAllPlatforms = false,
  });

  final Color? backgroundColor;
  final Widget child;

  final bool forceOnAllPlatforms;

  @override
  Widget build(BuildContext context) {
    if (!forceOnAllPlatforms &&
        Theme.of(context).platform != TargetPlatform.iOS) {
      return DecoratedBox(
        decoration: BoxDecoration(color: backgroundColor),
        child: child,
      );
    }

    final color =
        CupertinoDynamicColor.maybeResolve(backgroundColor, context) ??
            CupertinoTheme.of(context).barBackgroundColor;

    return _wrapWithBackground(
      backgroundColor: color,
      child: child,
    );
  }

  Widget _wrapWithBackground({
    Border? border,
    required Color backgroundColor,
    Brightness? brightness,
    required Widget child,
    bool updateSystemUiOverlay = true,
  }) {
    var result = child;
    if (updateSystemUiOverlay) {
      final isDark = backgroundColor.computeLuminance() < 0.179;
      final newBrightness =
          brightness ?? (isDark ? Brightness.dark : Brightness.light);
      final SystemUiOverlayStyle overlayStyle;
      switch (newBrightness) {
        case Brightness.dark:
          overlayStyle = SystemUiOverlayStyle.light;
          break;
        case Brightness.light:
          overlayStyle = SystemUiOverlayStyle.dark;
          break;
      }
      result = AnnotatedRegion<SystemUiOverlayStyle>(
        value: overlayStyle,
        child: result,
      );
    }
    final childWithBackground = DecoratedBox(
      decoration: BoxDecoration(
        border: border,
        color: backgroundColor,
      ),
      child: result,
    );

    if (backgroundColor.a == 0xFF) {
      return childWithBackground;
    }

    return ClipRect(
      child: BackdropFilter(
        filter: ImageFilter.blur(sigmaX: 10.0, sigmaY: 10.0),
        child: childWithBackground,
      ),
    );
  }
}
