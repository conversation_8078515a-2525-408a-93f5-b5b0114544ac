import 'package:flutter/material.dart';
import 'package:sliver_tools/sliver_tools.dart';

class SliverStickyBar extends StatelessWidget {
  final Widget? child;

  final Color? color;

  const SliverStickyBar({super.key, this.child, this.color});

  @override
  Widget build(BuildContext context) {
    return SliverPinnedHeader(
      child: Container(
        color: color ??
            Theme.of(context).scaffoldBackgroundColor.withValues(alpha: 0.9),
        child: Padding(
          padding: const EdgeInsets.all(2),
          child: child,
        ),
      ),
    );
  }
}
