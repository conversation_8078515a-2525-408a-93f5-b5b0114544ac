import 'package:flutter/material.dart';

import '../dependency_injection/dependency_injector.dart';
import '../features/in_app_purchase/iap_product.dart';
import '../features/in_app_purchase/iap_tile.dart';

class PayWall extends StatefulWidget {
  const PayWall({
    super.key,
    required this.feature,
  });

  final IAPFeature feature;

  @override
  State<PayWall> createState() => _PayWallState();
}

class _PayWallState extends State<PayWall> {
  List<IAPProduct>? _products;

  @override
  void initState() {
    super.initState();
    fetchData();
  }

  void fetchData() async {
    final bloc = DependencyInjector.iapBloc;
    final products = await bloc.fetchProducts({
      IAPFeature.visitedPro,
      widget.feature,
    });
    if (products == null) {
      return;
    }

    if (!mounted) {
      return;
    }

    setState(() {
      _products = products;
    });
  }

  @override
  Widget build(BuildContext context) {
    if (_products == null) {
      return const SizedBox();
    }

    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        for (final product in _products!) IAPTile(product: product),
      ],
    );
  }
}
