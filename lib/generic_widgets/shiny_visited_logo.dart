import 'package:flutter/material.dart';

class ShinyVisitedLogo extends StatelessWidget {
  const ShinyVisitedLogo({
    super.key,
    this.size = 100,
  });

  final double size;

  @override
  Widget build(BuildContext context) {
    return DecoratedBox(
      decoration: BoxDecoration(
        shape: BoxShape.circle,
        boxShadow: [
          const BoxShadow(color: Colors.white54, blurRadius: 15),
          BoxShadow(
              color: Theme.of(context).colorScheme.primary, blurRadius: 30),
        ],
      ),
      child: Image.asset(
        'assets/images/visited_logo.png',
        height: size,
        width: size,
      ),
    );
  }
}
