import 'dart:ui';

import 'package:flutter/material.dart';
import 'package:get_it/get_it.dart';

import '../caching/database.dart';
import '../caching/shared_prefs_storage.dart';
import '../caching/storage.dart';
import '../features/areas/area_bloc.dart';
import '../features/areas/area_repository.dart';
import '../features/areas/area_services.dart';
import '../features/areas/geometry_bloc.dart';
import '../features/authentication/session_bloc.dart';
import '../features/books/book_service.dart';
import '../features/cities/city_bloc.dart';
import '../features/dashboard/stats_bloc.dart';
import '../features/disputed_territories/disputed_territories_bloc.dart';
import '../features/disputed_territories/disputed_territories_service.dart';
import '../features/experiences/experience_bloc.dart';
import '../features/in_app_purchase/iap_bloc.dart';
import '../features/inspiration/inspiration_service.dart';
import '../features/itineraries/itinerary_bloc.dart';
import '../features/map/tiles/internal/geometry_repository.dart';
import '../features/map/tiles/internal/tile_rendering_service.dart';
import '../features/map/tiles/internal/tile_repository.dart';
import '../features/poster_printing/poster_printing_bloc.dart';
import '../features/selection/area_selection_bloc.dart';
import '../features/selection/area_selection_repository.dart';
import '../features/selection/area_selection_service.dart';
import '../features/settings/settings_bloc.dart';
import '../features/todo_lists/todo_list_bloc.dart';
import '../networking/client.dart';
import '../networking/environment.dart';
import '../networking/feature_flags.dart';

class DependencyInjector {
  static EnvironmentData get environment => _get<EnvironmentData>();
  static Client get client => _get<Client>();
  static VisitedDatabase get database => _get<VisitedDatabase>();

  static IAPBloc get iapBloc => _get<IAPBloc>();
  static DisputedTerritoriesBloc get disputedTerritoriesBloc =>
      _get<DisputedTerritoriesBloc>();
  static AreaBloc get areaBloc => _get<AreaBloc>();
  static SettingsBloc get settingsBloc => _get<SettingsBloc>();
  static AreaSelectionBloc get areaSelectionBloc => _get<AreaSelectionBloc>();
  static GeometryRepository get geometryRepository =>
      _get<GeometryRepository>();
  static GeometryBloc get geometryBloc => _get<GeometryBloc>();
  static CityBloc get cityBloc => _get<CityBloc>();
  static ItineraryBloc get itineraryBloc => _get<ItineraryBloc>();
  static ExperienceBloc get experienceBloc => _get<ExperienceBloc>();
  static TodoListBloc get todoListBloc => _get<TodoListBloc>();
  static PosterPrintingBloc get posterPrintingBloc =>
      _get<PosterPrintingBloc>();
  static SessionBloc get sessionBloc => _get<SessionBloc>();

  static FeatureFlags get featureFlags => _get<FeatureFlags>();
  static AreaRepository get areaRepository => _get<AreaRepository>();
  static AreaService get areaService => _get<AreaService>();
  static TileRepository get tileRepository => _get<TileRepository>();
  static AreaSelectionRepository get areaSelectionRepository =>
      _get<AreaSelectionRepository>();
  static AreaSelectionService get areaSelectionService =>
      _get<AreaSelectionService>();
  static InspirationService get inspirationService =>
      _get<InspirationService>();
  static DisputedTerritoriesService get disputedTerritoriesService =>
      _get<DisputedTerritoriesService>();
  static BookService get bookService => _get<BookService>();
  static TileRenderingService get tileRenderingService =>
      _get<TileRenderingService>();
  static StatsBloc get statsBloc => _get<StatsBloc>();
  static Storage get sharedPrefsStorage => _get<Storage>();

  static void register<T extends Object>(T Function() serviceFactory) {
    // Avoid double registration
    if (GetIt.instance.isRegistered<T>()) {
      return;
    }

    GetIt.instance.registerLazySingleton(serviceFactory);
  }

  static T _get<T extends Object>() => GetIt.instance.get<T>();

  static void configure(
    Client client,
    VisitedDatabase database,
    Brightness brightness, {
    Storage? sharedPrefsStorage,
  }) {
    register(() => sharedPrefsStorage ?? const SharedPrefsStorage());

    late final iap = IAPBloc();
    late final disputed = DisputedTerritoriesBloc();
    late final areaBloc = AreaBloc();
    late final settings = SettingsBloc(brightness);
    late final selections = AreaSelectionBloc(
      areaBloc: areaBloc,
      settingsBloc: settings,
    );

    late final city = CityBloc(
      areaBloc: areaBloc,
      iap: iap,
    );

    late final itineraryBloc = ItineraryBloc();
    late final experiences = ExperienceBloc(
      areaBloc: areaBloc,
      delegate: itineraryBloc,
    );

    late final lists = TodoListBloc(iap: iap, delegate: itineraryBloc);

    final geometry = DependencyInjector.geometryBloc;

    late final poster = PosterPrintingBloc(
      areaBloc: areaBloc,
      disputedTerritoryBloc: disputed,
      settingsBloc: settings,
      geometryBloc: geometry,
      selectionBloc: selections,
    );

    final statsBloc = StatsBloc(settings);

    late final auth = SessionBloc(
      iapBloc: iap,
      selectionBloc: selections,
      areaBloc: areaBloc,
      cityBloc: city,
      allBlocs: {
        areaBloc,
        selections,
        iap,
        geometry,
        settings,
        experiences,
        city,
        lists,
        disputed,
        poster,
        itineraryBloc,
        statsBloc,
      },
    );

    register(() => iap);
    register(() => disputed);
    register(() => areaBloc);
    register(() => settings);
    register(() => selections);
    register(() => geometry);
    register(() => city);
    register(() => itineraryBloc);
    register(() => experiences);
    register(() => lists);
    register(() => poster);
    register(() => auth);

    register(() => client.environment);
    register(() => client);
    register(() => database);
    register(() => FeatureFlags());
    register(() => AreaRepository(database));
    register(() => AreaService());
    register(() => DisputedTerritoriesService());
    register(() => AreaSelectionService());
    register(() => AreaSelectionRepository(database));
    register(() => InspirationService());
    register(() => BookService());
    register(() => TileRenderingService());
    register(() => GeometryRepository());
    register(() => TileRepository());
    register(() => statsBloc);
  }

  static void disposeBlocs() {
    iapBloc.dispose();
    disputedTerritoriesBloc.dispose();
    areaBloc.dispose();
    settingsBloc.dispose();
    areaSelectionBloc.dispose();
    geometryBloc.dispose();
    cityBloc.dispose();
    itineraryBloc.dispose();
    experienceBloc.dispose();
    todoListBloc.dispose();
    posterPrintingBloc.dispose();
    sessionBloc.dispose();
  }
}
