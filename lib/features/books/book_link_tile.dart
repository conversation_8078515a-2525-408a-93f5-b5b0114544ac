import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import '../../dependency_injection/dependency_injector.dart';
import 'book_link.dart';
import '../../generic_widgets/responsive_padding.dart';
import '../../generic_widgets/stadium_button.dart';

import '../../helpers/url_dispatcher.dart';

class BookLinkTile extends StatelessWidget {
  const BookLinkTile({
    super.key,
    required this.book,
  });

  final BookLink book;

  @override
  Widget build(BuildContext context) {
    return FutureBuilder<bool>(
      future: DependencyInjector.featureFlags.showBookLinks(),
      builder: (context, snapshot) {
        final show = snapshot.data ?? false;
        if (!show) {
          return const SizedBox();
        }

        return _buildTile(context);
      },
    );
  }

  ResponsivePadding _buildTile(BuildContext context) {
    return ResponsivePadding(
      context: context,
      fillToEdgeOnPhone: false,
      child: GestureDetector(
        onTap: () => UrlDispatcher.launchUri(book.url),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Travel Guide',
              style: Theme.of(context).textTheme.titleLarge,
            ),
            SizedBox(
              height: 240,
              child: Stack(
                children: [
                  Positioned.fill(
                    child: CachedNetworkImage(
                      imageUrl: book.thumbnailUrl,
                      fit: BoxFit.fitWidth,
                      alignment: Alignment.topCenter,
                    ),
                  ),
                  Positioned(
                    bottom: 0,
                    right: 0,
                    child: Padding(
                      padding: const EdgeInsets.all(8),
                      child: StadiumButton(
                        title: 'Buy Now',
                        onTapped: () => UrlDispatcher.launchUri(book.url),
                      ),
                    ),
                  )
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
