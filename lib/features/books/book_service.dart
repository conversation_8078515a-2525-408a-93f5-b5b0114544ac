import '../../dependency_injection/dependency_injector.dart';
import 'book_link.dart';
import '../experiences/models/experience.dart';
import '../todo_lists/models/todo_list.dart';
import '../../models/geo_area.dart';

class BookService {
  final _client = DependencyInjector.client;

  Future<BookLink?> fetchAreaBookLink(GeoArea area) {
    final path = 'books/areas/${area.isoCode}';
    return _fetchBook(path);
  }

  Future<BookLink?> fetchTodoListBookLink(TodoList list) {
    final path = 'books/lists/${list.id}';
    return _fetchBook(path);
  }

  Future<BookLink?> fetchExperienceBookLink(Experience experience) {
    final path = 'books/experiences/${experience.id}';
    return _fetchBook(path);
  }

  Future<BookLink?> _fetchBook(String path) async {
    try {
      final response = await _client.get(path);
      return BookLink.fromJson(response);
    } catch (e) {
      return null;
    }
  }
}
