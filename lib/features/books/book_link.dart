class BookLink {
  const BookLink({
    required this.name,
    required this.thumbnailUrl,
    required this.url,
  });

  BookLink.fromJson(Map json)
      : name = json['name'],
        url = Uri.parse(json['url']),
        thumbnailUrl = json['imageUrl'];

  final String name;
  final String thumbnailUrl;
  final Uri url;

  @override
  String toString() {
    return 'BookLink{name: $name}';
  }

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is BookLink &&
          runtimeType == other.runtimeType &&
          name == other.name &&
          thumbnailUrl == other.thumbnailUrl &&
          url == other.url;

  @override
  int get hashCode => name.hashCode ^ thumbnailUrl.hashCode ^ url.hashCode;
}
