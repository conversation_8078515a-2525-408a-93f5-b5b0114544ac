import 'package:drift/drift.dart';

import '../../caching/database.dart';
import '../../models/geo_area.dart';
import '../../models/selection.dart';

part 'city_database.g.dart';

@DriftAccessor(tables: [Cities, CitySelections])
class CityDatabase extends DatabaseAccessor<VisitedDatabase>
    with _$CityDatabaseMixin {
  CityDatabase(super.db);

  Future<List<CityDto>> fetchCities(GeoArea area) {
    return (select(cities)..where((tbl) => tbl.areaId.equals(area.id))).get();
  }

  Future<void> saveCities(List<CityDto> cityDtos) async {
    return batch((batch) => batch.insertAllOnConflictUpdate(cities, cityDtos));
  }

  Future<void> saveCity(CityDto dto) {
    return into(cities).insertOnConflictUpdate(dto);
  }

  Stream<List<(CitySelection, CityDto)>> listenToCitySelections() {
    return select(citySelections)
        .join([leftOuterJoin(cities, cities.id.equalsExp(citySelections.id))])
        .watch()
        .map(
          (event) => event.map((e) {
            return (e.readTable(citySelections), e.readTable(cities));
          }).toList(),
        );
  }

  Future<List<(CitySelection, CityDto)>> fetchCitySelectionsForArea(
    GeoArea area,
  ) async {
    final query = await (select(citySelections).join([
      leftOuterJoin(cities, cities.id.equalsExp(citySelections.id)),
    ])..where(cities.areaId.equals(area.id))).get();

    return query
        .map((e) => (e.readTable(citySelections), e.readTable(cities)))
        .toList();
  }

  Future<void> saveCitySelection(CitySelection selection) {
    return into(citySelections).insertOnConflictUpdate(selection);
  }

  Future<void> saveCitySelections(Iterable<CitySelection> dtos) {
    return batch(
      (batch) => batch.insertAllOnConflictUpdate(citySelections, dtos),
    );
  }

  Future<CityDto?> fetchLivedCity() async {
    try {
      final query = select(citySelections).join([
        leftOuterJoin(cities, cities.id.equalsExp(citySelections.id)),
      ])..where(citySelections.selection.equals(Selection.live.backendKey));

      final results = await query.getSingleOrNull();
      if (results == null) {
        return null;
      }

      return results.readTable(cities);
    } catch (e) {
      return null;
    }
  }

  Future<Map<CityDto, Selection>> fetchSelections() async {
    final query = await (select(citySelections).join([
      leftOuterJoin(cities, cities.id.equalsExp(citySelections.id)),
    ])).get();

    final parsed = <CityDto, Selection>{};
    for (final row in query) {
      final citySelection = row.readTable(citySelections);
      final cityDto = row.readTable(cities);
      final selection = Selection.fromBackendKey(citySelection.selection);

      parsed[cityDto] = selection;
    }

    return parsed;
  }

  Future<List<CitySelection>> fetchPendingCityChanges() {
    return (select(
      citySelections,
    )..where((tbl) => tbl.synced.equals(false))).get();
  }

  Future<void> clearSelections() {
    return delete(citySelections).go();
  }

  Future<void> deleteClearedCities() {
    return (delete(
      citySelections,
    )..where((tbl) => tbl.selection.equals(Selection.clear.backendKey))).go();
  }

  Future<void> flagCitySelectionsAsSynced() {
    return update(
      citySelections,
    ).write(const CitySelectionsCompanion(synced: Value(true)));
  }

  // Stream<int> numberOfCitiesVisited() {
  //   final count = countAll(
  //     filter: citySelections.selection.equals(Selection.been.backendKey) |
  //         citySelections.selection.equals(Selection.live.backendKey),
  //   );
  //
  //   return (selectOnly(citySelections)..addColumns([count]))
  //       .watchSingle()
  //       .map((row) => row.read(count) ?? 0);
  // }

  // Stream<int> numberOfCitiesSelectedOfType(Selection selection) {
  //   final count =
  //       countAll(filter: citySelections.selection.equals(selection.backendKey));
  //
  //   return (selectOnly(citySelections)..addColumns([count]))
  //       .watchSingle()
  //       .map((row) => row.read(count) ?? 0);
  // }

  Future<void> sync(CityDto city) {
    return (update(citySelections)..where((row) => row.id.equals(city.id)))
        .write(const CitySelectionsCompanion(synced: Value(true)));
  }

  Future<CityDto?> fetch(int cityId) {
    final query = select(cities)..where((tbl) => tbl.id.equals(cityId));
    return query.getSingleOrNull();
  }

  Future<void> clearCities() {
    return delete(cities).go();
  }
}
