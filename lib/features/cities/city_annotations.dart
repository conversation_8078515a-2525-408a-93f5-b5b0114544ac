// import 'package:flutter/material.dart';
//
// import '../../dependency_injection/dependency_injector.dart';
// import '../../models/geo_bounds.dart';
// import '../../models/selection.dart';
// import '../map/map_annotation.dart';
// import 'city.dart';
// import 'map_annotation.dart';
// import 'city_selection_dialog.dart';
//
// class CityAnnotations extends StatelessWidget {
//   final Map<City, Selection> cities;
//   final GeoBounds bounds;
//   final TransformationController mapController;
//   final Size mapSize;
//   final int maxZoom;
//
//   const CityAnnotations({
//     super.key,
//     required this.cities,
//     required this.bounds,
//     required this.mapController,
//     required this.mapSize,
//     required this.maxZoom,
//   });
//
//   @override
//   Widget build(BuildContext context) {
//     return Stack(
//       children: [
//         for (final city in cities.keys)
//           MapAnnotation(
//             coordinate: city.coordinate,
//             mapController: mapController,
//             mapSize: mapSize,
//             bounds: bounds,
//             builder: (context) {
//               final selection = cities[city]!;
//               return MapAnnotation(
//                 city: city,
//                 selection: selection,
//                 onTapped: () async {
//                   await CitySelectionDialog(city: city).show(context);
//                   if (context.mounted) {
//                     DependencyInjector.cityBloc.savePendingSelections();
//                   }
//                 },
//               );
//             },
//           )
//       ],
//     );
//   }
// }
