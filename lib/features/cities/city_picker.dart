import 'package:flutter/material.dart';

import '../../dependency_injection/dependency_injector.dart';
import '../../generic_widgets/pinned_selection_toggle.dart';
import '../../generic_widgets/platform_aware/platform_sliver_app_bar.dart';
import '../../generic_widgets/sliver_sticky_search_bar.dart';
import '../../generic_widgets/streaming_selectable_tile.dart';
import '../../models/geo_area.dart';
import '../../models/selection.dart';
import '../in_app_purchase/buy_visited_pro_soft_sell.dart';
import '../in_app_purchase/iap_product.dart';
import 'city.dart';
import 'city_listenable.dart';
import 'city_selection_dialog.dart';
import 'free_cities_remaining_counter.dart';
import 'paywall_dialog.dart';

class CityPicker extends StatefulWidget {
  const CityPicker({
    super.key,
    required this.area,
    this.initialSelection = Selection.been,
    this.selectionTypes = const [
      Selection.lived,
      Selection.been,
      Selection.want,
    ],
    this.onSelectedOverride,
    this.fetchSelectionsOverride,
  });

  static String routeName(GeoArea area) => 'cities/${area.isoCode}';

  final GeoArea area;
  final Selection initialSelection;
  final List<Selection> selectionTypes;
  final Future<Map<City, Selection>>? fetchSelectionsOverride;
  final void Function(City city, Selection selection)? onSelectedOverride;

  @override
  State createState() => _CityPickerState();
}

class _CityPickerState extends State<CityPicker>
    with CityListenable<CityPicker> {
  List<City>? _searchResults;
  final _selections = <City, Selection>{};

  late Selection selection;

  @override
  void initState() {
    super.initState();

    selection = widget.selectionTypes.contains(widget.initialSelection)
        ? widget.initialSelection
        : widget.selectionTypes.firstWhere(
            (element) => element != Selection.lived,
          );

    WidgetsBinding.instance.addPostFrameCallback((_) async {
      final cityBloc = DependencyInjector.cityBloc;
      final selections =
          await (widget.fetchSelectionsOverride ??
              cityBloc.fetchSelections(widget.area));
      _selections.addAll(selections);

      loadCities(widget.area);
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Scrollbar(
        child: CustomScrollView(
          keyboardDismissBehavior: ScrollViewKeyboardDismissBehavior.onDrag,
          slivers: [
            PlatformSliverAppBar(
              title: widget.area.name,
              action: const FreeCitiesRemainingCounter(),
            ),
            if (error != null)
              buildError(widget.area)
            else if (!loaded)
              buildLoading()
            else ...[
              SliverStickySearchBar<City>(
                items: cities,
                onQuery: (results) => setState(() => _searchResults = results),
              ),
              if (widget.selectionTypes.length > 1 &&
                  !DependencyInjector
                      .settingsBloc
                      .currentSettings
                      .showSelectionDialogOnPickerLists)
                PinnedSelectionToggle(
                  active: selection,
                  selections: widget.selectionTypes,
                  expand: true,
                  onChanged: (selection) async {
                    if (selection == Selection.lived &&
                        DependencyInjector.iapBloc.canSelectLived == false) {
                      final purchased = await BuyVisitedProSoftSell.present(
                        context,
                      );
                      if (context.mounted == false) {
                        return;
                      }

                      setState(() {
                        selection = purchased == true
                            ? Selection.lived
                            : this.selection;
                      });
                      return;
                    }

                    setState(() => this.selection = selection);
                  },
                ),
              _buildCities(),
              buildMissingCities(),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildCities() {
    final bloc = DependencyInjector.cityBloc;

    return SliverList(
      key: ValueKey(cities),
      delegate: SliverChildBuilderDelegate(
        childCount: _searchResults?.length ?? cities?.length ?? 0,
        (context, index) {
          final city = _searchResults?[index] ?? cities?[index];
          if (city == null) {
            return const SizedBox();
          }

          return SelectableTile(
            currentSelection: _selections[city] ?? Selection.clear,
            item: city,
            nameBuilder: bloc.fullCityName(city),
            onTapped: () => _onTapped(city),
          );
        },
      ),
    );
  }

  void _onTapped(City city) async {
    // Clear the selection if the tile is already selected with the value as the selection toggle
    final selectionToUpdate = _selections[city] == selection
        ? Selection.clear
        : selection;

    if (widget.onSelectedOverride == null) {
      final canAddNewCities = await DependencyInjector.cityBloc
          .isAllowedToUpdateSelections(city, selectionToUpdate);

      if (!mounted) {
        return;
      }

      if (!canAddNewCities) {
        final paid = await PaywallDialog(
          feature: IAPFeature.unlockCities,
        ).show(context);
        if (paid != true) {
          return;
        }
      }
    }

    if (mounted &&
        DependencyInjector
            .settingsBloc
            .currentSettings
            .showSelectionDialogOnPickerLists) {
      final dialog = CitySelectionDialog(
        city: city,
        availableSelections: widget.selectionTypes,
        showAddMore: false,
      );
      await dialog.show(context);

      if (!mounted) {
        return;
      }
      final actualSelection = await DependencyInjector.cityBloc
          .selection(city)
          .first;

      if (!mounted) {
        return;
      }

      setState(() {
        _selections[city] = actualSelection;
      });
      return;
    }

    setState(() {
      _selections[city] = selectionToUpdate;
    });

    if (widget.onSelectedOverride != null) {
      widget.onSelectedOverride!(city, selectionToUpdate);
      return;
    }

    DependencyInjector.cityBloc.select(city, selectionToUpdate);
  }

  @override
  void dispose() {
    cancelCitySubscription();
    super.dispose();
  }
}
