import 'dart:async';

import 'package:flutter/material.dart';

import '../../dependency_injection/dependency_injector.dart';
import '../../generic_widgets/missing_item_sliver_tile.dart';
import '../../generic_widgets/platform_aware/platform_text_button.dart';
import '../../generic_widgets/spinner.dart';
import '../../l10n/generated/app_localizations.dart';
import '../../models/geo_area.dart';
import 'city.dart';

mixin CityListenable<T extends StatefulWidget> on State<T> {
  List<City>? cities;
  var loaded = false;
  Object? error;

  StreamSubscription? _citySubscription;

  void loadCities(GeoArea area) async {
    if (!mounted) {
      return;
    }

    setState(() {
      loaded = false;
      error = null;
    });

    _citySubscription ??= DependencyInjector.cityBloc
        .listenToCities(area)
        .listen(
          (cities) {
            if (!mounted) {
              return;
            }

            setState(() {
              this.cities = cities;
              loaded = true;
            });
          },
          onError: (e) {
            if (mounted) {
              setState(() {
                error = e;
              });
            }
          },
        );
  }

  Widget buildLoading() {
    return const SliverFillRemaining(child: Center(child: Spinner()));
  }

  Widget buildError(GeoArea area) {
    return SliverFillRemaining(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.center,
        mainAxisAlignment: MainAxisAlignment.center,
        mainAxisSize: MainAxisSize.min,
        children: [
          Text(error?.toString() ?? ''),
          PlatformTextButton(
            title: AppLocalizations.of(context)!.tryAgain,
            onTapped: () {
              setState(() {
                error = null;
                loaded = false;
              });
              loadCities(area);
            },
          ),
        ],
      ),
    );
  }

  Widget buildMissingCities() {
    final localization = AppLocalizations.of(context)!;
    return MissingItemSliverTile(
      title: localization.missingAirports,
      emailTitle: localization.missingCitiesEmailTitle,
    );
  }

  /// Classes that use this mixin must call this on their dispose function
  void cancelCitySubscription() {
    _citySubscription?.cancel();
  }
}
