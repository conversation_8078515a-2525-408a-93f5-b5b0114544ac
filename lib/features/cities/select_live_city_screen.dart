import 'package:flutter/material.dart';
import 'package:sliver_tools/sliver_tools.dart';

import '../../dependency_injection/dependency_injector.dart';
import '../../generic_widgets/animated_checkmark.dart';
import '../../generic_widgets/platform_aware/platform_sliver_app_bar.dart';
import '../../generic_widgets/responsive_padding.dart';
import '../../generic_widgets/separated_tile.dart';
import '../../generic_widgets/sliver_sticky_search_bar.dart';
import '../../generic_widgets/spinner.dart';
import '../../l10n/generated/app_localizations.dart';
import '../../models/selection.dart';
import 'city.dart';
import 'city_listenable.dart';

class SelectLiveCityScreen extends StatefulWidget {
  static const routeName = 'selectLiveCity';
  const SelectLiveCityScreen({super.key});

  @override
  State createState() => _SelectLiveCityScreenState();
}

class _SelectLiveCityScreenState extends State<SelectLiveCityScreen>
    with CityListenable<SelectLiveCityScreen> {
  City? _selected;
  List<City>? _searchResults;
  var changed = false;

  @override
  void initState() {
    super.initState();
    _fetchData();
  }

  void _fetchData() async {
    final bloc = DependencyInjector.cityBloc;
    final country = await DependencyInjector.areaSelectionBloc
        .currentCountryUserLivesIn();

    if (country == null) {
      throw Exception(
        'You cannot select a city without first selecting a country',
      );
    }

    _selected = await bloc.fetchLivedCity();
    loadCities(country);
  }

  @override
  Widget build(BuildContext context) {
    return PopScope(
      onPopInvokedWithResult: (_, _) {
        if (changed) {
          DependencyInjector.cityBloc.select(_selected!, Selection.live);
        }
      },
      child: Scaffold(
        body: Scrollbar(
          child: CustomScrollView(
            keyboardDismissBehavior: ScrollViewKeyboardDismissBehavior.onDrag,
            slivers: [
              PlatformSliverAppBar(
                title: AppLocalizations.of(context)!.whereDoYouLive,
              ),
              if (!loaded)
                const SliverFillRemaining(child: Center(child: Spinner()))
              else
                _buildContent(context),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildContent(BuildContext context) {
    return ResponsiveSliverPadding(
      context: context,
      sliver: MultiSliver(
        children: [
          SliverStickySearchBar<City>(
            items: _citiesToBuild,
            onQuery: (results) => setState(() => _searchResults = results),
          ),
          SliverList(
            delegate: SliverChildBuilderDelegate((context, index) {
              final city = _citiesToBuild[index];
              return _buildTile(city);
            }, childCount: _citiesToBuild.length),
          ),
        ],
      ),
    );
  }

  Widget _buildTile(City city) {
    final selected = _selected == city;
    return SeparatedTile(
      key: ValueKey(city.id),
      color: selected
          ? Theme.of(context).primaryColor.withValues(alpha: 0.05)
          : Theme.of(context).scaffoldBackgroundColor,
      child: ListTile(
        selected: selected,
        title: Text(city.name),
        trailing: AnimatedCheckmark(checked: selected),
        onTap: () => setState(() {
          _selected = city;
          changed = true;
        }),
      ),
    );
  }

  List<City> get _citiesToBuild {
    return (_searchResults?.isNotEmpty ?? false ? _searchResults : cities) ??
        [];
  }

  @override
  void dispose() {
    cancelCitySubscription();
    super.dispose();
  }
}
