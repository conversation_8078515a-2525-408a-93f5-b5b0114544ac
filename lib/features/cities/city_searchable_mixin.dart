import 'dart:async';

import 'package:debounce_throttle/debounce_throttle.dart';
import 'package:flutter/material.dart';
import 'package:sliver_tools/sliver_tools.dart';

import '../../dependency_injection/dependency_injector.dart';
import '../../generic_widgets/responsive_padding.dart';
import '../../generic_widgets/spinner.dart';
import '../../helpers/searcher.dart';
import '../../helpers/string_extensions.dart';
import '../../l10n/generated/app_localizations.dart';
import '../../models/geo_area.dart';
import '../first_time/missing_cities_tile.dart';
import 'city.dart';

mixin CitySearchableMixin<T extends StatefulWidget> on State<T> {
  final searchController = TextEditingController();
  StreamSubscription? _debounceSubscription;
  late final Debouncer<String> _debouncer;
  List<City>? cities;
  bool _showSpinner = false;
  Searcher<GeoArea>? countrySearcher;
  List<GeoArea>? countrySearchResults;

  void onNoCityFound();
  Widget buildCityTile(BuildContext context, City city);
  Widget buildCountryTile(BuildContext context, GeoArea country);

  @override
  void initState() {
    super.initState();
    _debouncer = Debouncer<String>(
      const Duration(milliseconds: 200),
      initialValue: searchController.text,
    );

    _configureCountrySearcher();

    searchController.addListener(_searchListener);
    _debounceSubscription ??= _debouncer.values.listen(_debounceListener);
  }

  void _searchListener() async {
    _debouncer.value = searchController.text;

    if (searchController.text.isEmpty) {
      final allCountries = await DependencyInjector.areaBloc.allCountries();
      setState(() {
        countrySearchResults = allCountries;
        cities = null;
        _showSpinner = false;
      });
    }
  }

  void _debounceListener(String query) async {
    countrySearcher?.search(query);

    if (cities?.isEmpty ?? false) {
      setState(() {
        _showSpinner = true;
      });
    }

    final results = await DependencyInjector.cityBloc.search(query);
    setState(() {
      cities = results;
      _showSpinner = false;
    });
  }

  void _configureCountrySearcher() {
    DependencyInjector.areaBloc.allCountries().then((countries) {
      if (mounted) {
        setState(() {
          countrySearchResults = countries;
        });
      }

      countrySearcher = Searcher(score: 0.1, countries, (results) {
        if (searchController.text.isEmpty) {
          return;
        }

        if (mounted) {
          setState(() {
            countrySearchResults = results;
          });
        }
      });
    });
  }

  List<Widget> buildSlivers(BuildContext context) => [
    if (_showSpinner)
      const SliverFillRemaining(child: Center(child: Spinner())),
    if (cities != null) _buildSearchResults(context),
    if (countrySearchResults?.isNotEmpty ?? false)
      _buildCountryResults(context),
    if (searchController.text.isNotEmpty && cities != null)
      const MissingCitiesSliverTile(),
  ];

  Widget _buildSearchResults(BuildContext context) {
    return ResponsiveSliverPadding(
      context: context,
      fillToEdgeOnPhone: false,
      sliver: SliverList.builder(
        itemCount: cities!.length,
        itemBuilder: (context, index) {
          final city = cities![index];
          return buildCityTile(context, city);
        },
      ),
    );
  }

  Widget _buildCountryResults(BuildContext context) {
    return ResponsiveSliverPadding(
      context: context,
      fillToEdgeOnPhone: false,
      sliver: MultiSliver(
        children: [
          SliverList.list(
            children: [
              const SizedBox(height: 16),
              Text(
                AppLocalizations.of(context)!.countries.capitalized,
                style: Theme.of(
                  context,
                ).textTheme.titleLarge?.copyWith(color: Colors.white),
              ),
            ],
          ),
          SliverList.builder(
            itemCount: countrySearchResults!.length,
            itemBuilder: (context, index) {
              final country = countrySearchResults![index];
              return buildCountryTile(context, country);
            },
          ),
        ],
      ),
    );
  }

  @override
  void dispose() {
    _debounceSubscription?.cancel();
    searchController.removeListener(_searchListener);
    searchController.dispose();
    super.dispose();
  }
}
