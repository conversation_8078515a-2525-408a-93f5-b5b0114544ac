import 'package:flutter/material.dart';

import '../../dependency_injection/dependency_injector.dart';
import '../../generic_widgets/area_flag.dart';
import '../../generic_widgets/navigation_extensions.dart';
import '../../generic_widgets/presentable.dart';
import '../../generic_widgets/stadium_button.dart';
import '../../l10n/generated/app_localizations.dart';
import '../../models/selection.dart';
import '../selection/selection_dialog.dart';
import 'city.dart';
import 'city_picker.dart';

class CitySelectionDialog extends StatelessWidget with Presentable {
  final City city;
  final List<Selection> availableSelections;
  final bool showAddMore;

  const CitySelectionDialog({
    super.key,
    required this.city,
    this.availableSelections = const [
      Selection.been,
      Selection.want,
      Selection.clear,
    ],
    this.showAddMore = true,
  });

  @override
  Widget build(BuildContext context) {
    final bloc = DependencyInjector.cityBloc;
    final area = DependencyInjector.areaBloc.areaByIsoCodeSync(
      city.geoAreaIsoCode,
    );

    return SelectionDialog(
      title: city.name,
      centerContent: const SizedBox(height: 16),
      selection: bloc.selection(city),
      onChanged: (selection) => bloc.select(city, selection),
      availableSelections: const [
        Selection.been,
        Selection.want,
        Selection.clear,
      ],
      trailing: (area != null && showAddMore)
          ? Padding(
              padding: const EdgeInsets.only(
                left: 8.0,
                right: 8.0,
                bottom: 16.0,
              ),
              child: Row(
                children: [
                  AreaFlag(area: area),
                  const SizedBox(width: 16),
                  Expanded(
                    child: StadiumButton(
                      title: AppLocalizations.of(context)!.addMore,
                      onTapped: () {
                        Navigator.of(context).pushMaterialReplacement(
                          fullscreen: true,
                          builder: (context) => CityPicker(
                            area: area,
                          ),
                          name: CityPicker.routeName(area),
                        );
                      },
                    ),
                  ),
                ],
              ),
            )
          : null,
    );
  }
}
