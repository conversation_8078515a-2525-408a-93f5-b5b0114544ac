import 'dart:async';
import 'dart:convert';
import 'dart:io';
import 'dart:math';

import 'package:path/path.dart';
import 'package:path_provider/path_provider.dart';
import 'package:rxdart/rxdart.dart';
import 'package:sentry_flutter/sentry_flutter.dart';

import '../../caching/resettable_behaviour_subject.dart';
import '../../caching/storage.dart';
import '../../dependency_injection/dependency_injector.dart';
import '../../generic_widgets/bloc.dart';
import '../../models/geo_area.dart';
import '../../models/selection.dart';
import '../areas/area_bloc.dart';
import '../in_app_purchase/iap_bloc.dart';
import '../localization/supported_language.dart';
import 'city.dart';
import 'city_repository.dart';
import 'city_service.dart';

class CityBloc implements Bloc {
  CityBloc({required this.areaBloc, required this.iap})
    : _service = CityService() {
    _languageSubscription ??= DependencyInjector.settingsBloc.language.listen((
      language,
    ) async {
      if (language == _lastLanguage) {
        return;
      }

      _lastLanguage = language;

      // ignore the language change on the first load
      if (_clearCacheOnLanguageChange) {
        await _repo.clear();
      }

      _clearCacheOnLanguageChange = true;

      _selectionController.reset();
      _liveCityController.reset();

      fetchLivedCity();
      _fetchSelections();
    });
  }

  SupportedLanguage? _lastLanguage;
  bool _clearCacheOnLanguageChange = false;

  StreamSubscription? _languageSubscription;

  static const kShownCityInstructions =
      'com.highheels.visited.cities.shownInstructions';

  late final _liveCityController = ResettableBehaviorSubject<City?>()
    ..onListen = () => fetchLivedCity();

  late final _selectionController =
      ResettableBehaviorSubject<Map<City, Selection>>()
        ..onListen = _fetchSelections;

  final _repo = CityRepository();
  Storage get storage => DependencyInjector.sharedPrefsStorage;
  final AreaBloc areaBloc;
  final CityService _service;
  final IAPBloc iap;

  Stream<City?> get livedCity => _liveCityController.stream;
  Stream<Map<City, Selection>> get selections => _selectionController.stream;

  Future<Map<City, Selection>> currentSelections() async {
    await _fetchSelections();

    final selections = {...(_selectionController.value)};

    final livedCity = _liveCityController.valueOrNull ?? await fetchLivedCity();
    if (livedCity != null) {
      selections[livedCity] = Selection.live;
    }

    return selections;
  }

  Stream<Map<GeoArea, List<City>>> get sortedSelections =>
      selections.asyncMap((event) async {
        final codesToAreas = <String, List<City>>{};
        for (final entry in event.entries) {
          final iso = entry.key.geoAreaIsoCode;
          final sorted = codesToAreas[iso] ?? [];
          sorted.add(entry.key);
          codesToAreas[iso] = sorted;
        }

        final areaLookup = <GeoArea, List<City>>{};
        for (final entry in codesToAreas.entries) {
          final area = await areaBloc.areaByIsoCode(entry.key);
          if (area == null) {
            continue;
          }
          areaLookup[area] = entry.value..sort();
        }
        return areaLookup;
      });

  Stream<int?> freeSelectionsRemaining() {
    final flags = DependencyInjector.featureFlags;

    final maxAmountSelectable = Stream.fromFuture(() async {
      final limitSelections = await flags.limitCitySelections();
      if (!limitSelections) {
        return null;
      }

      return flags.maxFreeCities();
    }());

    return Rx.combineLatest3(
      selections,
      DependencyInjector.iapBloc.status,
      maxAmountSelectable,
      (selections, purchases, maxFreeCitySelections) {
        if (maxFreeCitySelections == null) {
          return null;
        }

        if (purchases.purchasedPro) {
          return null;
        }

        final totalCitySelections = selections.entries.where((entry) {
          return entry.value != Selection.live;
        }).length;

        return max(maxFreeCitySelections - totalCitySelections, 0);
      },
    );
  }

  /// Fetch selections from backend if not already done
  Future<void> initialize() => _fetchSelections();

  Future<City?> fetchLivedCity() async {
    final inMemoryCity = _liveCityController.valueOrNull;
    if (inMemoryCity != null) {
      return inMemoryCity;
    }

    final locallyCached = await _repo.fetchLivedCity();
    if (locallyCached != null) {
      _liveCityController.add(locallyCached);
      return locallyCached;
    }

    if (!_service.client.authenticated) {
      return null;
    }

    final city = await _service.fetchLivedCity();
    if (city != null) {
      _repo.cacheSelection(city, Selection.live);
      _liveCityController.add(city);
    }
    return city;
  }

  Stream<int> get numberOfCitiesVisited =>
      _selectionController.map((selections) => selections.beenCount);

  Stream<double> get percentOfCitiesVisited =>
      _selectionController.map((selections) {
        final total = selections.length;
        final beenCount = selections.beenCount;

        return beenCount / total;
      });

  // TODO: check if this is efficient.  Maybe a single query is better?
  Stream<Selection> selection(City city) =>
      selections.map((selections) => selections[city] ?? Selection.clear);

  /// PLEASE DE-BOUNCE THIS METHOD!!!!
  Future<List<City>?> search(String query) => _service.search(query);

  /// Fetches the cities associated with this area's topmost parent.
  /// Results are memoized.
  Stream<List<City>> listenToCities(GeoArea area) async* {
    final topLevel = (await areaBloc.fetchParent(area) ?? area);

    final cached = await _repo.fetchCities(area);
    if (cached != null) {
      yield cached;
    }

    if (_repo.synced(area)) {
      return;
    }

    final cities = await _service.fetchCities(topLevel);
    cities.sort((a, b) => a.name.compareTo(b.name));
    await _repo.saveCities(area, cities);
    yield cities;
  }

  Future<City?> fetchCity(int cityId) async {
    final cached = await _repo.fetchCity(cityId);
    if (cached != null) {
      return cached;
    }

    return _service.fetchCity(cityId);
  }

  Stream<List<City>> listenToSelectedCities({
    required GeoArea area,
    required List<Selection> validSelections,
  }) {
    return selections.map((selections) {
      return [
        for (final entry in selections.entries)
          if (entry.key.geoAreaIsoCode == area.isoCode &&
              validSelections.contains(entry.value))
            entry.key,
      ];
    });
  }

  Future<List<City>> fetchCities(GeoArea area) async {
    final topLevel = (await areaBloc.fetchParent(area) ?? area);

    if (_repo.synced(topLevel)) {
      final cached = await _repo.fetchCities(topLevel);
      if (cached != null && cached.isNotEmpty) {
        return cached;
      }
    }

    final cities = await _service.fetchCities(topLevel);
    cities.sort((a, b) => a.name.compareTo(b.name));
    await _repo.saveCities(topLevel, cities);
    return cities;
  }

  /// Updates the city selection both locally and on the server
  /// If the selection for a city is higher than its GeoArea,
  /// then that GeoArea selection will also be updated
  /// May throw a CityException if not allowed to make selections
  Future<void> select(City city, Selection selection) async {
    final allowed = await isAllowedToUpdateSelections(city, selection);

    if (!allowed) {
      throw CityException.freeCityAmountReached;
    }

    // First Update Locally
    if (selection == Selection.live) {
      _clearPreviouslyLiveCity(city);
      _service.select(city, selection);
    }

    var selectionsOriginal = _selectionController.valueOrNull;
    if (selectionsOriginal == null) {
      await _fetchSelections();
      selectionsOriginal = _selectionController.value;
    }

    // Create a copy to avoid unexpected mutations from other processes
    final selections = {...selectionsOriginal};

    if (selection == Selection.clear) {
      selections.remove(city);
    } else {
      selections[city] = selection;
    }

    _cacheBackupOfCitySelections(selections);

    _selectionController.add(selections);
    await _repo.cacheSelection(city, selection);
    await _service.select(city, selection);
    await _repo.markAsSynced(city);

    return _syncAreaSelection(city, selection);
  }

  Future<void> _cacheBackupOfCitySelections(
    Map<City, Selection> selections,
  ) async {
    try {
      final dirDirectory = await getApplicationDocumentsDirectory();
      final cityFile = File(join(dirDirectory.path, 'city_selections.json'));

      final jsonable = {
        for (final entry in selections.entries)
          entry.key.toJson(): entry.value.backendKey,
      };

      cityFile.writeAsStringSync(jsonEncode(jsonable));
    } catch (e) {
      // Capture exception, but try to continue with city selection process
      // I think the bug might be in the local database?
      Sentry.captureException(e);
    }
  }

  Future<Map<City, Selection>?> _loadCitySelectionsFromFileBackup() async {
    final dirDirectory = await getApplicationDocumentsDirectory();
    final cityFile = File(join(dirDirectory.path, 'city_selections.json'));

    if (!cityFile.existsSync()) {
      return null;
    }

    final json = jsonDecode(cityFile.readAsStringSync()) as Map?;
    if (json == null) {
      return null;
    }

    return json.map((key, value) {
      final city = City.fromJson(key);
      final selection = Selection.fromBackendKey(value);
      return MapEntry(city, selection);
    });
  }

  Future<Map<City, Selection>> fetchSelections(GeoArea area) async {
    await _fetchSelections();
    return _repo.fetchSelections(area);
  }

  Future<void> _syncAreaSelection(City city, Selection selection) async {
    await _updateAreaSelection(city.secondLevelGeoAreaIso, selection);
    await _updateAreaSelection(city.geoAreaIsoCode, selection);
  }

  Future<void> _updateAreaSelection(
    String? isoCode,
    Selection selection,
  ) async {
    if (isoCode == null) {
      return;
    }

    final area = await areaBloc.areaByIsoCode(isoCode);

    if (area == null) {
      return;
    }

    final currentSelection = await DependencyInjector.areaSelectionRepository
        .fetchSelection(area);

    // If the area has a lower selection than one of its cities
    // then bump up the area selections
    if (selection <= currentSelection) {
      return;
    }

    await DependencyInjector.areaSelectionService.select(
      area: area,
      selection: selection,
    );

    await DependencyInjector.tileRenderingService.updateAreas([area]);
  }

  void _clearPreviouslyLiveCity(City city) async {
    // Check if nothing has changed and exit early
    if (_liveCityController.valueOrNull == city) {
      return;
    }

    final previousLive =
        _liveCityController.valueOrNull ?? (await _repo.fetchLivedCity());

    if (previousLive != null) {
      select(previousLive, Selection.clear);
    }

    _liveCityController.add(city);
  }

  // Future<void> _savePendingSelections() async {
  //   final pending = await _repo.fetchPendingChanges();
  //
  //   if (pending.isEmpty) {
  //     return;
  //   }
  //
  //   await _service.batchSelect(pending);
  //   await _repo.syncChanges();
  // }

  Future<void> _fetchSelections() async {
    if (_selectionController.hasValue) {
      return;
    }

    if (!_service.client.authenticated) {
      return;
    }

    final fileSelections = await _loadCitySelectionsFromFileBackup();
    if (fileSelections != null) {
      _selectionController.add(fileSelections);
      try {
        _repo.cacheSelections(fileSelections, synced: false);
      } catch (e) {
        Sentry.captureException(e);
      }
    }

    final cachedSelections = await _repo.fetchAllSelections();
    if (cachedSelections.isNotEmpty) {
      _selectionController.add(cachedSelections);
    }

    final selections = await _service.getSelections();

    final differences = <City, Selection>{};
    for (final entry in selections.entries) {
      final city = entry.key;
      final selection = entry.value;

      if (cachedSelections[city] == selection) {
        continue;
      }

      differences[city] = selection;
    }

    if (differences.isEmpty) {
      return;
    }

    final updatedSelections = {
      ..._selectionController.valueOrNull ?? {},
      ...differences,
    };
    _selectionController.add(updatedSelections);

    await _repo.syncChanges();
    await _repo.cacheSelections(differences, synced: true);
  }

  Future<bool> hasShowedInstructions() async {
    final shown = await storage.getBool(kShownCityInstructions);
    return shown;
  }

  void saveInstructionsHaveBeenShown() {
    storage.putBool(kShownCityInstructions, true);
  }

  Future<String> fullCityName(City city) async {
    if (city.secondLevelGeoAreaIso == null) {
      return city.name;
    }

    final secondLevel = await areaBloc.areaByIsoCode(
      city.secondLevelGeoAreaIso!,
    );
    return '${city.name}, ${secondLevel?.name}';
  }

  @override
  void clear() {
    _repo.clear();
    _liveCityController.reset();
    _selectionController.reset();
    storage.delete(kShownCityInstructions);
  }

  @override
  void dispose() {
    _languageSubscription?.cancel();
    _selectionController.close();
    _liveCityController.close();
  }

  Future<bool> isAllowedToUpdateSelections(
    City city,
    Selection selection,
  ) async {
    // Clear or Live will never increase the selection count
    if (selection == Selection.clear || selection == Selection.live) {
      return true;
    }

    if (!_selectionController.hasValue) {
      await _fetchSelections();
    }

    // If you are changing the selection value, that won't change the count, so its allowed
    if (_selectionController.valueOrNull?.containsKey(city) ?? false) {
      return true;
    }

    final hasPurchased = DependencyInjector.iapBloc.hasUnlockedCities;
    if (hasPurchased) {
      return true;
    }

    final flags = DependencyInjector.featureFlags;
    final limitCitySelections = await flags.limitCitySelections();
    if (!limitCitySelections) {
      return true;
    }

    final maxFreeCitySelections = await flags.maxFreeCities();
    final currentSelectionCount =
        _selectionController.valueOrNull?.entries
            .where((entry) => entry.value != Selection.live)
            .length ??
        0;

    return currentSelectionCount < maxFreeCitySelections;
  }
}

extension on Map<City, Selection> {
  int get beenCount => entries.where((e) {
    final selection = e.value;
    return switch (selection) {
      Selection.want => false,
      Selection.clear => false,
      _ => true,
    };
  }).length;
}

enum CityException implements Exception { freeCityAmountReached }
