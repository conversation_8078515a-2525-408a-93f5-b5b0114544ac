import 'package:collection/collection.dart';
import 'package:sentry_flutter/sentry_flutter.dart';

import '../../caching/database.dart';
import '../../dependency_injection/dependency_injector.dart';
import '../../models/coordinate.dart';
import '../../models/geo_area.dart';
import '../../models/selection.dart';
import 'city.dart';
import 'city_database.dart';

class CityRepository {
  final _cityDatabase = CityDatabase(DependencyInjector.database);
  final _hasSynced = <GeoArea>{};

  bool synced(GeoArea area) => _hasSynced.contains(area);

  Future<List<City>?> fetchCities(GeoArea area) async {
    final dtos = await _cityDatabase.fetchCities(area);
    if (dtos.isEmpty) {
      return null;
    }

    final cities = <City>[];
    for (final dto in dtos) {
      final city = await _buildCity(dto);
      cities.add(city);
    }

    cities.sort();

    return cities;
  }

  Future<void> saveCities(GeoArea area, List<City> cities) async {
    final dtos = <CityDto>[];
    for (final city in cities) {
      final dto = await _buildCityDto(city);
      dtos.add(dto);
    }

    await _cityDatabase.saveCities(dtos);
    _hasSynced.add(area);
  }

  Future<Map<City, Selection>> fetchSelections(GeoArea area) async {
    final results = await _cityDatabase.fetchCitySelectionsForArea(area);
    return _parseCitySelectionsResults(results);
  }

  Future<Map<City, Selection>> _parseCitySelectionsResults(
    List<(CitySelection, CityDto)> results,
  ) async {
    final output = <City, Selection>{};
    for (final record in results) {
      final (citySelection, dto) = record;
      final selection = Selection.fromBackendKey(citySelection.selection);

      if (selection == Selection.clear) {
        continue;
      }

      final city = await _buildCity(dto);
      output[city] = selection;
    }

    return output;
  }

  Future<City?> fetchLivedCity() async {
    final live = await _cityDatabase.fetchLivedCity();
    if (live == null) {
      return null;
    }

    return _buildCity(live);
  }

  Future<void> cacheSelection(City city, Selection selection) async {
    // Confirm that city there is only one live city
    if (selection == Selection.live) {
      await _clearOldLivedCity(city);
      final dto = await _buildCityDto(city);
      await _cityDatabase.saveCity(dto);
    }

    final citySelection = CitySelection(
      id: city.id,
      selection: selection.backendKey,
      synced: false,
    );

    return _cityDatabase.saveCitySelection(citySelection);
  }

  Future<void> _clearOldLivedCity(City updatedLiveCity) async {
    final oldCity = await _cityDatabase.fetchLivedCity();
    if (oldCity == null || oldCity.id == updatedLiveCity.id) {
      return;
    }

    await _cityDatabase.saveCitySelection(
      CitySelection(
        id: oldCity.id,
        selection: Selection.clear.backendKey,
        synced: false,
      ),
    );
  }

  Future<Map<Selection, List<int>>> fetchPendingChanges() async {
    final pending = await _cityDatabase.fetchPendingCityChanges();
    final grouped = pending.groupListsBy((element) => element.selection);
    return grouped.map(
      (key, value) => MapEntry(
        Selection.fromBackendKey(key),
        value.map((e) => e.id).toList(),
      ),
    );
  }

  Future<Map<City, Selection>> fetchAllSelections() async {
    final selections = await _cityDatabase.fetchSelections();
    final parsed = <City, Selection>{};
    for (final entry in selections.entries) {
      final selection = entry.value;
      if (selection == Selection.clear || selection == Selection.live) {
        continue;
      }

      final city = await _buildCity(entry.key);
      parsed[city] = entry.value;
    }

    return parsed;
  }

  Future<void> markAsSynced(City city) async {
    final dto = await _buildCityDto(city);
    return _cityDatabase.sync(dto);
  }

  Future<void> syncChanges() async {
    await _cityDatabase.deleteClearedCities();
    await _cityDatabase.flagCitySelectionsAsSynced();
  }

  Future<void> cacheSelections(
    Map<City, Selection> selections, {
    required bool synced,
  }) async {
    final cityDtos = <CityDto>[];
    final citySelections = <CitySelection>[];
    final safeToIterate = [...selections.entries];
    for (final entry in safeToIterate) {
      final city = entry.key;
      final selection = entry.value;
      final dto = await _buildCityDto(city);
      cityDtos.add(dto);
      citySelections.add(
        CitySelection(
          id: city.id,
          selection: selection.backendKey,
          synced: synced,
        ),
      );
    }

    try {
      await _cityDatabase.saveCities(cityDtos);
      await _cityDatabase.saveCitySelections(citySelections);
    } catch (e) {
      Sentry.captureException(e);
    }
  }

  Future<City> _buildCity(CityDto dto) async {
    final db = DependencyInjector.database;

    final firstLevel = await db.fetchAreaById(dto.areaId);
    final secondLevel = dto.secondLevelAreaId != null
        ? (await db.fetchAreaById(dto.secondLevelAreaId!))
        : null;
    return City(
      id: dto.id,
      name: dto.name,
      geoAreaIsoCode: firstLevel!.isoCode,
      secondLevelGeoAreaIso: secondLevel?.isoCode,
      coordinate: Coordinate(latitude: dto.lat, longitude: dto.long),
    );
  }

  Future<CityDto> _buildCityDto(City city) async {
    final areaBloc = DependencyInjector.areaBloc;
    final db = DependencyInjector.database;

    final firstLevel = await areaBloc.areaByIsoCode(city.geoAreaIsoCode);
    final secondLevelIso = city.secondLevelGeoAreaIso;

    final secondLevel = secondLevelIso != null
        ? (await db.fetchAreaByIsoCode(secondLevelIso))
        : null;

    return CityDto(
      id: city.id,
      name: city.name,
      areaId: firstLevel!.id,
      secondLevelAreaId: secondLevel?.id,
      lat: city.coordinate.latitude.toDouble(),
      long: city.coordinate.longitude.toDouble(),
    );
  }

  Future<City?> fetchCity(int cityId) async {
    final dto = await _cityDatabase.fetch(cityId);
    if (dto == null) {
      return null;
    }
    return _buildCity(dto);
  }

  Future<void> clear() async {
    _hasSynced.clear();
    await _cityDatabase.clearSelections();
    await _cityDatabase.clearCities();
  }
}
