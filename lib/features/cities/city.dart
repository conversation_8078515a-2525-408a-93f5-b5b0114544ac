import 'package:sentry_flutter/sentry_flutter.dart';

import '../../generic_widgets/selectable_item.dart';
import '../../models/coordinate.dart';

class City
    implements SelectableItem, Searchable, MapDisplayable, Comparable<City> {
  @override
  final int id;

  @override
  final String name;

  @override
  final Coordinate coordinate;

  final String geoAreaIsoCode;
  final String? secondLevelGeoAreaIso;

  factory City.fromJson(Map json) {
    try {
      final id = json['id'];
      final name = json['name'];
      final coordinate = Coordinate.fromList(json['coordinate']);
      final secondLevelGeoAreaIso = json['levelTwoIso'];
      final geoAreaIsoCode = json['levelOneIso'];

      return City(
        id: id,
        name: name,
        coordinate: coordinate,
        geoAreaIsoCode: geoAreaIsoCode,
        secondLevelGeoAreaIso: secondLevelGeoAreaIso,
      );
    } catch (e) {
      Sentry.captureException(
        e,
        hint: Hint.withMap(json.cast<String, Object>()),
      );
      rethrow;
    }
  }

  Map toJson() => {
    'id': id,
    'name': name,
    'coordinate': coordinate.toList(),
    'levelOneIso': geoAreaIsoCode,
    if (secondLevelGeoAreaIso != null) 'levelTwoIso': secondLevelGeoAreaIso,
  };

  const City({
    required this.id,
    required this.name,
    required this.geoAreaIsoCode,
    required this.coordinate,
    this.secondLevelGeoAreaIso,
  });

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is City && runtimeType == other.runtimeType && id == other.id;

  @override
  int get hashCode => id.hashCode;

  @override
  String toString() {
    return 'City{id: $id, name: $name}';
  }

  City copyWith({String? name, Coordinate? coordinate}) {
    return City(
      id: id,
      name: name ?? this.name,
      coordinate: coordinate ?? this.coordinate,
      geoAreaIsoCode: geoAreaIsoCode,
    );
  }

  @override
  List<String> get searchKeywords => [name];

  @override
  int compareTo(City other) => name.compareTo(other.name);
}
