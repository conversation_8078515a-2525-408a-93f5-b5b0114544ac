import 'package:flutter/material.dart';

import '../../generic_widgets/navigation_extensions.dart';
import '../../generic_widgets/presentable.dart';
import '../../generic_widgets/stadium_button.dart';
import '../../helpers/string_extensions.dart';
import '../../l10n/generated/app_localizations.dart';
import '../../models/selection.dart';
import '../selection/selection_dialog.dart';
import 'city.dart';
import 'select_live_city_screen.dart';

class LiveCityDialog extends StatelessWidget with Presentable {
  final City city;

  const LiveCityDialog({super.key, required this.city});

  @override
  Widget build(BuildContext context) {
    return SelectionDialog(
      title: city.name,
      selection: Stream.value(Selection.live),
      availableSelections: const [Selection.live],
      centerContent: const SizedBox(height: 8),
      onChanged: (selection) async {},
      trailing: Padding(
        padding: const EdgeInsets.all(8.0),
        child: SizedBox(
          width: double.infinity,
          child: StadiumButton(
            title: AppLocalizations.of(context)!.update.capitalized,
            onTapped: () {
              Navigator.of(context).pushMaterialReplacement(
                name: SelectLiveCityScreen.routeName,
                builder: (_) => const SelectLiveCityScreen(),
                fullscreen: true,
              );
            },
          ),
        ),
      ),
    );
  }
}
