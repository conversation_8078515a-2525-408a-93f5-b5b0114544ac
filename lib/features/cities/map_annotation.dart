import 'package:flutter/material.dart';

import '../../dependency_injection/dependency_injector.dart';
import '../../models/selection.dart';

class MapAnnotation extends StatelessWidget {
  const MapAnnotation({
    super.key,
    required this.selection,
    this.onTapped,
  });

  final Selection selection;
  final VoidCallback? onTapped;

  @override
  Widget build(BuildContext context) {
    final palette = DependencyInjector.settingsBloc.currentPalette;
    final color = palette.colorForSelection(selection);
    final textColour = palette.labelColorForSelection(selection);

    return GestureDetector(
      behavior: HitTestBehavior.opaque,
      onTap: onTapped ??
          () {}, // Dummy Detector to eat taps and prevent the city picker from appearing
      child: SizedBox(
        width: 30,
        height: 30,
        child: Center(
          child: Container(
            width: 15,
            height: 15,
            decoration: BoxDecoration(
                shape: BoxShape.circle,
                color: color,
                gradient: RadialGradient(
                  colors: [
                    Color.lerp(color, Colors.white, 0.25)!,
                    color,
                  ],
                  focal: const Alignment(0.25, -0.25),
                ),
                border: Border.all(color: textColour),
                boxShadow: const [
                  BoxShadow(blurRadius: 2, offset: Offset(0, 1))
                ]),
          ),
        ),
      ),
    );
  }
}
