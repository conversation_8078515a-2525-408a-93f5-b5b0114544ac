import 'dart:async';

import 'package:flutter/material.dart';

import '../../dependency_injection/dependency_injector.dart';
import '../../generic_widgets/navigation_extensions.dart';
import '../../generic_widgets/pay_wall.dart';
import '../../generic_widgets/platform_aware/platform_icon_button.dart';
import '../../generic_widgets/presentable.dart';
import '../in_app_purchase/iap_product.dart';
import '../in_app_purchase/iap_status.dart';
import '../in_app_purchase/visted_subscription_soft_sell.dart';

class PaywallDialog extends StatefulWidget with Presentable<bool> {
  PaywallDialog({
    super.key,
    required this.feature,
  });

  final IAPFeature feature;

  @override
  State createState() => _PaywallDialogState();

  bool get barrierDismissible => true;

  @override
  Future<bool?> show(
    BuildContext context, {
    bool barrierDismissible = false,
  }) async {
    if (await DependencyInjector.featureFlags.enableSubscriptions()) {
      if (context.mounted == false) {
        return false;
      }

      return Navigator.of(context, rootNavigator: true).pushMaterialRoute(
          builder: (_) => const VisitedSubscriptionSoftSell(),
          name: 'subscription_paywall',
          fullscreen: true);
    }

    if (context.mounted == false) {
      return false;
    }

    return super.show(context, barrierDismissible: barrierDismissible);
  }
}

class _PaywallDialogState extends State<PaywallDialog> {
  StreamSubscription? _purchaseSubscription;

  @override
  void initState() {
    super.initState();
    _fetchData();
  }

  void _fetchData() async {
    final bloc = DependencyInjector.iapBloc;
    _purchaseSubscription = bloc.status.listen(_listenForPurchaseUpdates);
  }

  void _listenForPurchaseUpdates(IAPStatus status) {
    if (status.containsFeature(widget.feature)) {
      Navigator.of(context).pop(true);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      backgroundColor: Theme.of(context).cardColor,
      insetPadding: const EdgeInsets.all(32.0),
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: Stack(
        children: [
          Padding(
              padding: const EdgeInsets.all(24.0),
              child: PayWall(feature: widget.feature)),
          Positioned(
            right: 0,
            child: PlatformIconButton(
              icon: Icons.close,
              onTapped: Navigator.of(context).pop,
            ),
          )
        ],
      ),
    );
  }

  @override
  void dispose() {
    _purchaseSubscription?.cancel();
    super.dispose();
  }
}
