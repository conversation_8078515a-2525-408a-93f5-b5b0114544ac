import 'package:flutter/material.dart';

import '../../bridges/environment_fetcher.dart';
import '../../dependency_injection/dependency_injector.dart';
import '../../l10n/generated/app_localizations.dart';
import '../sharing/email_sender.dart';

class MissingCitiesTile extends StatelessWidget {
  const MissingCitiesTile({
    super.key,
    this.backgroundColor,
  });

  final Color? backgroundColor;

  @override
  Widget build(BuildContext context) {
    final localization = AppLocalizations.of(context)!;

    return SliverToBoxAdapter(
      child: GestureDetector(
        behavior: HitTestBehavior.opaque,
        onTap: () async {
          final mailer = EmailSender(
            environment: Environment.of(context)!,
            user: DependencyInjector.sessionBloc.user,
            selections: DependencyInjector.areaSelectionBloc,
            localizations: localization,
          );

          mailer.send(
            context: context,
            subject: localization.missingCitiesEmailTitle,
          );
        },
        child: DecoratedBox(
          decoration: BoxDecoration(color: backgroundColor),
          child: Padding(
            padding: const EdgeInsets.all(16.0),
            child: Text(localization.missingAirports),
          ),
        ),
      ),
    );
  }
}
