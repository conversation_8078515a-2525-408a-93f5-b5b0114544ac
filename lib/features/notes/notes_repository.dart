import 'dart:convert';
import 'dart:io';

import 'package:path/path.dart';
import 'package:path_provider/path_provider.dart';

import '../../models/geo_area.dart';

class NotesRepository {
  Map<String, String>? _cache;

  Future<String?> get(GeoArea area) async {
    final notes = await _fetchNotes();
    return notes[area.isoCode];
  }

  void update(GeoArea area, String note) async {
    final notes = await _fetchNotes();

    if (note.isNotEmpty) {
      notes[area.isoCode] = note;
    } else {
      notes.remove(area.isoCode);
    }
    _saveNotes();
  }

  Future<void> clear() async {
    _cache = null;
    final file = await _fetchFile();

    if (file.existsSync()) {
      file.deleteSync();
    }
  }

  Future<Map<String, String>> _fetchNotes() async {
    if (_cache != null) {
      return _cache!;
    }

    final file = await _fetchFile();

    if (!file.existsSync()) {
      _cache = <String, String>{};
      return _cache!;
    }

    final json =
        (jsonDecode(file.readAsStringSync()) as Map).cast<String, String>();
    _cache = json;
    return _cache!;
  }

  Future<void> _saveNotes() async {
    final json = jsonEncode(_cache ?? {});
    final file = await _fetchFile();
    file.writeAsStringSync(json);
  }

  Future<File> _fetchFile() async {
    final doc = await getApplicationDocumentsDirectory();
    final path = join(doc.path, 'notes.json');
    return File(path);
  }
}
