import 'package:flutter/material.dart';
import 'package:flutter_markdown/flutter_markdown.dart';

import '../../generic_widgets/platform_aware/platform_sliver_app_bar.dart';
import '../../generic_widgets/platform_aware/platform_text_button.dart';
import '../../generic_widgets/responsive_padding.dart';
import '../../helpers/url_dispatcher.dart';
import '../../l10n/generated/app_localizations.dart';

class AboutScreen extends StatelessWidget {
  const AboutScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: CustomScrollView(
        slivers: [
          PlatformSliverAppBar(
            title: '${AppLocalizations.of(context)!.about} Visited',
          ),
          ResponsiveSliverPadding(
            context: context,
            fillToEdgeOnPhone: false,
            sliver: SliverList(
              delegate: SliverChildListDelegate(
                [
                  const Text(
                      'Visited is a mobile application created for the propose of ranking how many countries and states you have visited compared to your goals as a traveler as well as how you rank against other travelers. Its perfect for anyone who loves to travel and wants to see what percent of the world they have explored so far, and for anyone who wants to keep track of their yearly travelling progress.'),
                  const SizedBox(
                    height: 16,
                  ),
                  Text(
                    'About Arriving in High Heels',
                    style: Theme.of(context).textTheme.titleLarge,
                  ),
                  const Text(
                      'Arriving in High Heels is dedicated to travel inspiration. We started as a Travel Blog but has expanded into creating travel mobile apps.'),
                  const SizedBox(height: 16),
                  const Text(
                      'Our vision is to bring inspiration via digital and mobile platforms to those who love and dream of travel and make the journey a bit easier with helpful travel tips and itineraries.'),
                  const SizedBox(height: 16),
                  const Text(
                      'The blog: The Arriving in High Heels blog is a compilation of travel and love stories from around the world. This includes but is not limited to photography, itineraries, travel tips and advice, and romantic tales from abroad.'),
                  const SizedBox(height: 16),
                  Text(
                    'Privacy Policy',
                    style: Theme.of(context).textTheme.titleLarge,
                  ),
                  MarkdownBody(
                    data:
                        '''On our website, you can find our [Terms of Use](http://www.arrivinginhighheels.com/terms-of-use/) and our [Privacy Policy](http://www.arrivinginhighheels.com/privacy-policy/) . The following sums up a few key points:

    *   When you log in with your email we store some of your personal information on our servers.
    *   We publish your rank as a number or as a percentage as it compares to the rest of the users on this site.
    *   We store usage statistics anonymously.''',
                    onTapLink: _onLinkTapped,
                  ),
                  const Text(
                      'This privacy policy is subject to change without notice.'),
                  const SizedBox(height: 16),
                  Text('Data Set',
                      style: Theme.of(context).textTheme.titleLarge),
                  MarkdownBody(
                    data:
                        'The data for Visited was provided by [Natural Earth.](http://www.naturalearthdata.com)',
                    onTapLink: _onLinkTapped,
                  ),
                  Padding(
                    padding: const EdgeInsets.symmetric(vertical: 16),
                    child: PlatformTextButton(
                      title: MaterialLocalizations.of(context)
                          .viewLicensesButtonLabel,
                      onTapped: () => showLicensePage(
                          context: context,
                          applicationIcon:
                              Image.asset('assets/images/visited_logo.png')),
                    ),
                  ),
                  const SafeArea(
                    top: false,
                    child: SizedBox(),
                  ),
                ],
              ),
            ),
          )
        ],
      ),
    );
  }

  void _onLinkTapped(String text, String? href, String title) {
    UrlDispatcher.launch(href);
  }
}
