import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import '../../generic_widgets/separated_tile.dart';

class MoreTile extends StatelessWidget {
  final String title;
  final VoidCallback? onTapped;

  const MoreTile({
    super.key,
    required this.title,
    this.onTapped,
  });
  @override
  Widget build(BuildContext context) {
    return SeparatedTile(
      color: Theme.of(context).scaffoldBackgroundColor,
      child: ListTile(
        title: Text(
          title,
        ),
        onTap: onTapped,
        trailing: const Icon(CupertinoIcons.forward),
      ),
    );
  }
}
