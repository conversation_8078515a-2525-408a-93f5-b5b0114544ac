import 'package:flutter/material.dart';

import '../../bridges/environment_fetcher.dart';
import '../../dependency_injection/dependency_injector.dart';
import '../../generic_widgets/alert.dart';
import '../../generic_widgets/legal_links.dart';
import '../../generic_widgets/navigation_extensions.dart';
import '../../generic_widgets/platform_aware/platform_sliver_app_bar.dart';
import '../../generic_widgets/responsive_padding.dart';
import '../../l10n/generated/app_localizations.dart';
import '../areas/my_country_selections_screen.dart';
import '../in_app_purchase/iap_screen.dart';
import '../in_app_purchase/visted_subscription_soft_sell.dart';
import '../settings/customize_screen.dart';
import '../settings/delete_confirmation_dialog.dart';
import '../sharing/brand_ambassador_screen.dart';
import '../sharing/email_sender.dart';
import '../tutorial/tutorial_screen.dart';
import 'about_screen.dart';
import 'more_tile.dart';

class MoreScreen extends StatelessWidget {
  const MoreScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: SafeArea(
        child: Column(
          children: [
            Expanded(
              child: Scrollbar(
                child: CustomScrollView(
                  slivers: [
                    PlatformSliverAppBar(
                      title: AppLocalizations.of(context)!.more,
                    ),
                    ResponsiveSliverPadding(
                      context: context,
                      sliver: SliverList(delegate: _buildTiles(context)),
                    ),
                  ],
                ),
              ),
            ),
            _buildVersionAndLegalLinks(context),
          ],
        ),
      ),
    );
  }

  SliverChildDelegate _buildTiles(BuildContext context) {
    final localizations = AppLocalizations.of(context)!;
    final navigator = Navigator.of(context);
    return SliverChildListDelegate([
      if (Localizations.localeOf(context).languageCode == 'en')
        _buildTutorialTile(localizations, context),
      _buildInAppPurchases(localizations, navigator),
      _buildCustomize(localizations, navigator),
      _buildBrandAmbassador(localizations, navigator),
      _buildCountrySelections(localizations, navigator),
      _buildAbout(localizations, navigator),
      _buildContact(localizations),
      _buildDeleteAccount(context, localizations),
      _buildLogout(localizations, context),
    ]);
  }

  Widget _buildTutorialTile(
    AppLocalizations localizations,
    BuildContext context,
  ) {
    return MoreTile(
      title: localizations.getStarted,
      onTapped: () => Navigator.of(context).pushMaterialRoute(
        name: TutorialScreen.routeName,
        builder: (_) => const TutorialScreen(),
        fullscreen: true,
      ),
    );
  }

  Widget _buildCustomize(
    AppLocalizations localizations,
    NavigatorState navigator,
  ) {
    return MoreTile(
      title: localizations.customize,
      onTapped: () => navigator.pushMaterialRoute(
        name: CustomizeScreen.routeName,
        builder: (_) => const CustomizeScreen(),
      ),
    );
  }

  Widget _buildCountrySelections(
    AppLocalizations localizations,
    NavigatorState navigator,
  ) {
    return MoreTile(
      title: localizations.myCountrySelections,
      onTapped: () => navigator.pushMaterialRoute(
        name: MyCountrySelectionsScreen.routeName,
        builder: (_) => const MyCountrySelectionsScreen(),
      ),
    );
  }

  Widget _buildInAppPurchases(
    AppLocalizations localizations,
    NavigatorState navigator,
  ) {
    return FutureBuilder<bool>(
      initialData: false,
      future: DependencyInjector.featureFlags.enableSubscriptions(),
      builder: (context, snapshot) => MoreTile(
        title: localizations.unlockPremiumFeatures,
        onTapped: () => navigator.pushMaterialRoute(
          name: IAPScreen.routeName,
          builder: (_) => snapshot.data == true
              ? const VisitedSubscriptionSoftSell()
              : const IAPScreen(),
        ),
      ),
    );
  }

  Widget _buildAbout(AppLocalizations localizations, NavigatorState navigator) {
    return MoreTile(
      title: '${localizations.about} ${localizations.appName}',
      onTapped: () => navigator.pushMaterialRoute(
        name: 'about',
        builder: (_) => const AboutScreen(),
      ),
    );
  }

  Widget _buildContact(AppLocalizations localizations) {
    return Builder(
      builder: (context) {
        return MoreTile(
          title: localizations.contactUs,
          onTapped: () async {
            final mailer = EmailSender(
              environment: Environment.of(context)!,
              user: DependencyInjector.sessionBloc.user,
              selections: DependencyInjector.areaSelectionBloc,
              localizations: localizations,
            );

            final success = await mailer.send(
              context: context,
              subject: localizations.appName,
            );
            if (!success && context.mounted) {
              Alert(
                title: localizations.errorTitle,
                message: 'No Email Apps Available',
              ).show(context);
            }
          },
        );
      },
    );
  }

  Widget _buildBrandAmbassador(
    AppLocalizations localizations,
    NavigatorState navigator,
  ) {
    return MoreTile(
      title: localizations.becomeABrandAmbassador,
      onTapped: () => navigator.pushMaterialRoute(
        name: '/brandAmbassadorForm',
        builder: (_) => const BrandAmbassadorScreen(),
      ),
    );
  }

  Widget _buildDeleteAccount(
    BuildContext context,
    AppLocalizations localizations,
  ) {
    return MoreTile(
      title: localizations.deleteAccount,
      onTapped: () {
        showAdaptiveDialog(
          context: context,
          builder: (_) => const DeleteConfirmationDialog(),
          barrierDismissible: true,
        );
      },
    );
  }

  Widget _buildLogout(AppLocalizations localizations, BuildContext context) {
    return MoreTile(
      title: localizations.logout,
      onTapped: () async {
        final bloc = DependencyInjector.sessionBloc;
        final confirmed = await ConfirmDialog(
          title: localizations.areYouSure,
          message: localizations.logoutConfirm,
          cancelText: localizations.cancel,
          confirmText: localizations.logout,
          confirmDestructive: true,
        ).show(context);

        if (confirmed == true) {
          bloc.logout();
        }
      },
    );
  }

  Widget _buildVersionAndLegalLinks(BuildContext context) {
    final version = Environment.of(context)?.version;

    return Padding(
      padding: const EdgeInsets.all(8.0),
      child: Column(
        children: [
          if (version != null)
            Text(
              '${AppLocalizations.of(context)!.appName}: $version',
              style: Theme.of(context).textTheme.titleSmall,
            ),
          Text(
            DependencyInjector.sessionBloc.user?.email ?? '',
            style: Theme.of(context).textTheme.titleSmall,
          ),
          LegalLinks(
            textColour: Theme.of(context).colorScheme.primary,
            showDropShadows: false,
          ),
        ],
      ),
    );
  }
}
