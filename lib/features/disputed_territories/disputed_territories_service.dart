import '../../dependency_injection/dependency_injector.dart';
import 'disputed_territories_repository.dart';
import 'disputed_territory.dart';
import '../../models/geo_area.dart';
import '../../models/simple_geo_area.dart';

typedef _AdjustmentsFilter = Future<_DisputedAdjustmentType> Function(
  DisputedTerritory territory,
);

class DisputedTerritoriesService {
  DisputedTerritoriesService();
  late final _client = DependencyInjector.client;
  final _repo = DisputedTerritoryRepository();
  List<DisputedTerritory>? _territories;

  Future<List<DisputedTerritory>> fetchDisputedTerritories() async {
    if (_territories != null) {
      return _territories!;
    }

    final response = await _client.get('areas/disputed');
    _territories = response
        .map<DisputedTerritory>((json) => DisputedTerritory.fromJson(json))
        .toList(growable: false);

    return _territories!;
  }

  Future<DisputedTerritoryAdjustments> topLevelDisputedPreferences() async {
    return _buildAdjustments((territory) async {
      final selectedOption = await _repo.selected(territory);

      // Selected Sovereign
      if (selectedOption.area == territory.area) {
        return _DisputedAdjustmentType.add;
      }

      // Unselected Sovereign
      if (territory.options
              .where((option) => option.area == territory.area)
              .length >
          1) {
        return _DisputedAdjustmentType.remove;
      }

      // Sovereign was never an option;
      return _DisputedAdjustmentType.ignore;
    });
  }

  Future<DisputedTerritoryAdjustments> subdivisionAdjustments(GeoArea area) {
    return _buildAdjustments(
      (territory) => _isDependantOnArea(territory, area),
    );
  }

  Future<DisputedTerritoryAdjustments> _buildAdjustments(
    _AdjustmentsFilter filter,
  ) async {
    final territories = await fetchDisputedTerritories();
    final add = <SimpleGeoArea>{};
    final remove = <SimpleGeoArea>{};

    for (final territory in territories) {
      final toggle = await filter(territory);
      if (toggle == _DisputedAdjustmentType.add) {
        add.add(territory.area);
      } else if (toggle == _DisputedAdjustmentType.remove) {
        remove.add(territory.area);
      }
    }

    return DisputedTerritoryAdjustments(
      add: add,
      remove: remove,
    );
  }

  Future<_DisputedAdjustmentType> _isDependantOnArea(
      DisputedTerritory territory, GeoArea area) async {
    if (territory.options
        .where((element) => element.area.isoCode == area.isoCode)
        .isEmpty) {
      return _DisputedAdjustmentType.ignore;
    }

    final option = await _repo.selected(territory);
    return option.area.isoCode == area.isoCode
        ? _DisputedAdjustmentType.add
        : _DisputedAdjustmentType.remove;
  }

  Future<Set<SimpleGeoArea>> requestedAlternativeGeometry() async {
    final territories = await fetchDisputedTerritories();
    final alternativeSelections = <SimpleGeoArea>{};

    for (final territory in territories) {
      final selection = await _repo.selected(territory);
      if (_isBundledGeographyConfiguration(territory, selection)) {
        continue;
      }

      alternativeSelections.add(territory.area);
    }

    return alternativeSelections;
  }

  /// Represents the geography that is included in this app bundle,
  /// in case the backend decides to change the configuration
  /// Key are disputed territory -> bundled owner
  static const _bundledDisputedTerritoryConfigurations = {
    411: 217, // Crimea -> Russia
    149: 149, // Hong Kong -> Hong Kong
    122: 122, // Taiwan -> Taiwan
    480: 154, // Jammu and Kashmir -> India
    1332: 201, // Abkhazia -> Georgia
  };

  bool _isBundledGeographyConfiguration(
    DisputedTerritory territory,
    DisputedTerritoryOption option,
  ) {
    return _bundledDisputedTerritoryConfigurations[territory.area.id] ==
        option.area.id;
  }
}

enum _DisputedAdjustmentType {
  add,
  remove,
  ignore,
}

class DisputedTerritoryAdjustments {
  final Set<SimpleGeoArea> add;
  final Set<SimpleGeoArea> remove;
  late final Set<String> addIsoCodes = add.map((e) => e.isoCode).toSet();
  late final Set<String> removeIsoCodes = remove.map((e) => e.isoCode).toSet();

  DisputedTerritoryAdjustments({
    required this.add,
    required this.remove,
  });

  DisputedTerritoryAdjustments.fromJson(Map json)
      : add = json['add']
            .map<SimpleGeoArea>((area) => SimpleGeoArea.fromJson(area))
            .toSet(),
        remove = json['remove']
            .map<SimpleGeoArea>((area) => SimpleGeoArea.fromJson(area))
            .toSet();

  Map toJson() => {
        'add': add.map((area) => area.toJson()).toList(),
        'remove': remove.map((area) => area.toJson()).toList()
      };
}
