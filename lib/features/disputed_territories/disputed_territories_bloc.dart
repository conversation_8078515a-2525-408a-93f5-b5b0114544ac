import 'package:rxdart/rxdart.dart';

import '../../dependency_injection/dependency_injector.dart';
import '../../generic_widgets/bloc.dart';
import 'disputed_territories_repository.dart';
import 'disputed_territory.dart';

typedef DisputedSelectionsLookup
    = Map<DisputedTerritory, DisputedTerritoryOption>;

class DisputedTerritoriesBloc implements Bloc {
  final _controller = BehaviorSubject<DisputedSelectionsLookup>();
  final _updateController = BehaviorSubject<bool>();

  final _repo = DisputedTerritoryRepository();

  Stream<DisputedSelectionsLookup> get selections => _controller.stream;
  DisputedSelectionsLookup? get currentSelections => _controller.valueOrNull;

  List<DisputedTerritory>? get territories => _controller.value.keys.toList();

  Future<void> initialize() async {
    if (_controller.hasValue) {
      return;
    }

    final territories =
        await DependencyInjector.areaBloc.fetchDisputedTerritories();
    return _restoreSelections(territories);
  }

  Stream<bool> selected(
    DisputedTerritory territory,
    DisputedTerritoryOption option,
  ) {
    return _controller.asyncMap((event) async {
      var selected = event[territory];

      if (selected == null) {
        selected = await _repo.selected(territory);
        select(territory, selected);
      }

      return selected == option;
    });
  }

  Stream<bool> get disputedTerritoriesChanged => _updateController.stream;

  void select(
    DisputedTerritory territory,
    DisputedTerritoryOption option,
  ) async {
    await _repo.select(territory, option);
    final value = _controller.valueOrNull ?? {};
    value[territory] = option;
    _controller.add(value);
    _updateController.add(true);

    DependencyInjector.geometryRepository.applyDisputedTerritoryAdjustments();
    final areaBloc = DependencyInjector.areaBloc;
    DependencyInjector.tileRenderingService.clearTilesWithAreas([
      areaBloc.areaByIsoCodeSync(territory.area.isoCode)!,
      ...[
        for (final option in territory.options)
          areaBloc.areaByIsoCodeSync(option.area.isoCode)!
      ]
    ]);
  }

  Future<void> _restoreSelections(List<DisputedTerritory> list) async {
    final selections = <DisputedTerritory, DisputedTerritoryOption>{};
    for (final territory in list) {
      final selection = await _repo.selected(territory);
      selections[territory] = selection;
    }

    _controller.sink.add(selections);
  }

  @override
  void clear() {
    final territories = _controller.valueOrNull?.keys.toList();
    if (territories == null) {
      return;
    }

    _repo.clear(territories);
    _restoreSelections(territories);
  }

  @override
  void dispose() {
    _updateController.close();
    _controller.close();
  }
}
