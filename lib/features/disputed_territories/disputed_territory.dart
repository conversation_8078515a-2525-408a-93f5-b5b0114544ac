import '../../models/simple_geo_area.dart';

class DisputedTerritory {
  final SimpleGeoArea area;
  final List<DisputedTerritoryOption> options;

  const DisputedTerritory({
    required this.area,
    required this.options,
  });

  @override
  String toString() {
    return 'DisputedTerritory{area: ${area.name}';
  }

  DisputedTerritory.fromJson(Map json)
      : area = SimpleGeoArea.fromJson(json['area']),
        options = json['options']
            .map<DisputedTerritoryOption>(
                (j) => DisputedTerritoryOption.fromJson(j))
            .toList(growable: false);

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is DisputedTerritory &&
          runtimeType == other.runtimeType &&
          area == other.area;

  @override
  int get hashCode => area.hashCode;
}

class DisputedTerritoryOption {
  final SimpleGeoArea area;
  final bool isDefault;

  const DisputedTerritoryOption({
    required this.area,
    required this.isDefault,
  });

  DisputedTerritoryOption.fromJson(Map json)
      : area = SimpleGeoArea.fromJson(json['area']),
        isDefault = json['defaultSelection'];

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is DisputedTerritoryOption &&
          runtimeType == other.runtimeType &&
          area == other.area;

  @override
  int get hashCode => area.hashCode;

  @override
  String toString() {
    return 'DisputedTerritoryOption{area: ${area.name}';
  }
}
