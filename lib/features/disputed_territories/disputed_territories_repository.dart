import '../../caching/storage.dart';
import '../../dependency_injection/dependency_injector.dart';
import 'disputed_territory.dart';

class DisputedTerritoryRepository {
  Storage get _storage => DependencyInjector.sharedPrefsStorage;

  Future<DisputedTerritoryOption> selected(DisputedTerritory territory) async {
    final selection = await _storage.get(_buildStorageKey(territory));
    if (selection == null) {
      return territory.options.firstWhere(
        (option) => option.isDefault,
        orElse: () => territory.options.first,
      );
    }

    return territory.options.firstWhere(
      (option) => option.area.isoCode == selection,
    );
  }

  Future<void> select(
    DisputedTerritory territory,
    DisputedTerritoryOption option,
  ) {
    return _storage.put(_buildStorageKey(territory), option.area.isoCode);
  }

  String _buildStorageKey(DisputedTerritory territory) =>
      'com.visited.disuputed.${territory.area.isoCode}';

  void clear(List<DisputedTerritory> territories) {
    for (final territory in territories) {
      _storage.delete(_buildStorageKey(territory));
    }
  }
}
