import 'package:flutter/material.dart';
import 'package:sliver_tools/sliver_tools.dart';

import '../../dependency_injection/dependency_injector.dart';
import '../../generic_widgets/animated_checkmark.dart';
import '../../generic_widgets/platform_aware/platform_sliver_app_bar.dart';
import '../../generic_widgets/responsive_padding.dart';
import '../../generic_widgets/separated_tile.dart';
import '../../generic_widgets/sliver_bottom_safe_area.dart';
import '../../generic_widgets/spinner.dart';
import '../../l10n/generated/app_localizations.dart';
import 'disputed_territory.dart';

class DisputedTerritoriesScreen extends StatefulWidget {
  static const routeName = 'disputedTerritories';

  const DisputedTerritoriesScreen({super.key});

  @override
  State createState() => _DisputedTerritoriesScreenState();
}

class _DisputedTerritoriesScreenState extends State<DisputedTerritoriesScreen> {
  List<DisputedTerritory>? _territories;

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.scheduleFrameCallback((_) => _loadData());
  }

  void _loadData() async {
    final disputedBloc = DependencyInjector.disputedTerritoriesBloc;
    await disputedBloc.initialize();
    _territories = disputedBloc.territories;

    setState(() {});
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: _territories == null
          ? _buildPlaceholderUI(context)
          : _buildTerritories(context, _territories!),
    );
  }

  Widget _buildPlaceholderUI(BuildContext context) {
    return CustomScrollView(
      slivers: [
        _buildAppBar(context),
        const SliverFillRemaining(
          child: Center(
            child: Spinner(),
          ),
        )
      ],
    );
  }

  Widget _buildAppBar(BuildContext context) {
    return PlatformSliverAppBar(
      title: AppLocalizations.of(context)!.disputedTerritories,
    );
  }

  Widget _buildTerritories(
      BuildContext context, List<DisputedTerritory> territories) {
    return Scrollbar(
      child: CustomScrollView(
        slivers: [
          _buildAppBar(context),
          for (final territory in territories)
            ResponsiveSliverPadding(
              context: context,
              fillToEdgeOnPhone: true,
              sliver: MultiSliver(children: [
                _buildHeader(context, territory),
                _buildOptions(territory)
              ]),
            ),
          const SliverBottomSafeArea(),
        ],
      ),
    );
  }

  SliverList _buildOptions(DisputedTerritory territory) {
    return SliverList(
      delegate: SliverChildBuilderDelegate((context, index) {
        final option = territory.options[index];
        final bloc = DependencyInjector.disputedTerritoriesBloc;
        return StreamBuilder(
          stream: bloc.selected(territory, option),
          initialData: false,
          builder: (context, snapshot) => SeparatedTile(
            child: ListTile(
              title: Text(option.area.name),
              selected: snapshot.data == true,
              onTap: () => bloc.select(territory, option),
              trailing: AnimatedCheckmark(checked: snapshot.data == true),
            ),
          ),
        );
      }, childCount: territory.options.length),
    );
  }

  SliverToBoxAdapter _buildHeader(
      BuildContext context, DisputedTerritory territory) {
    return SliverToBoxAdapter(
      child: Container(
        height: 50,
        color: Theme.of(context).scaffoldBackgroundColor,
        child: Align(
            alignment: Alignment.bottomLeft,
            child: Padding(
              padding: const EdgeInsets.only(bottom: 4, left: 8),
              child: Text(
                territory.area.name,
                style: Theme.of(context)
                    .textTheme
                    .bodySmall
                    ?.copyWith(fontWeight: FontWeight.w700),
              ),
            )),
      ),
    );
  }

  @override
  void dispose() {
    super.dispose();
  }
}
