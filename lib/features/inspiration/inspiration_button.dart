import 'package:flutter/material.dart';

import '../../generic_widgets/platform_aware/platform_icon_button.dart';
import '../../models/color_extensions.dart';

class InspirationButton extends StatelessWidget {
  final String iconPath;
  final String label;
  final VoidCallback onTapped;
  final Color? textColor;
  final bool selected;
  final Color? selectedColor;

  const InspirationButton({
    super.key,
    required this.iconPath,
    required this.label,
    required this.onTapped,
    this.textColor,
    this.selected = false,
    this.selectedColor,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        PlatformIconButton(
          svgAsset: iconPath,
          borderRadius: BorderRadius.circular(100),
          color: _determineBackgroundColour(context),
          iconColour: _determineForegroundColour(context),
          padding: const EdgeInsets.all(16.0),
          onTapped: onTapped,
        ),
        _buildLabel()
      ],
    );
  }

  Color? _determineBackgroundColour(BuildContext context) {
    if (selected && selectedColor != null) {
      return selectedColor;
    }

    return Theme.of(context).primaryColor.withValues(alpha: 0.1);
  }

  Widget _buildLabel() {
    return Padding(
      padding: const EdgeInsets.all(8.0),
      child: Text(
        label,
        style: TextStyle(
          fontSize: 18,
          color: textColor,
        ),
      ),
    );
  }

  Color? _determineForegroundColour(BuildContext context) {
    var color = textColor ?? Theme.of(context).textTheme.titleLarge!.color;
    final selectedColor = this.selectedColor;
    if (selected && selectedColor != null) {
      color = color?.legibleForegroundColor();
    }

    return color;
  }
}
