import '../../models/simple_geo_area.dart';

class Inspiration {
  final int id;
  final String name;
  final String imageUrl;
  final SimpleGeoArea area;

  const Inspiration({
    required this.id,
    required this.name,
    required this.imageUrl,
    required this.area,
  });

  Inspiration.fromJson(Map json)
      : id = json['id'],
        name = json['name'],
        imageUrl = json['imageUrl'],
        area = SimpleGeoArea.fromJson(json['geoArea']);

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is Inspiration &&
          runtimeType == other.runtimeType &&
          id == other.id &&
          name == other.name &&
          imageUrl == other.imageUrl &&
          area == other.area;

  @override
  int get hashCode =>
      id.hashCode ^ name.hashCode ^ imageUrl.hashCode ^ area.hashCode;
}
