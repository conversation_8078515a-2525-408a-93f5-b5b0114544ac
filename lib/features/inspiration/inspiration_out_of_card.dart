import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';

import '../../dependency_injection/dependency_injector.dart';
import '../../generic_widgets/spinner.dart';
import '../../generic_widgets/stadium_button.dart';
import '../../l10n/generated/app_localizations.dart';
import '../in_app_purchase/iap_product.dart';
import '../in_app_purchase/iap_status.dart';
import 'inspiration_bloc.dart';

class InspirationOutOfCards extends StatelessWidget {
  const InspirationOutOfCards({super.key, required this.inspirationBloc});
  final InspirationBloc inspirationBloc;

  @override
  Widget build(BuildContext context) {
    return StreamBuilder<InspirationEvent>(
        stream: inspirationBloc.events,
        builder: (context, snapshot) {
          if (snapshot.data is! InspirationCardsEvent) {
            return const SizedBox();
          }

          return StreamBuilder<IAPStatus>(
            stream: DependencyInjector.iapBloc.status,
            builder: (context, snapshot) {
              if (snapshot.data?.hasUnlockedInspirations ?? false) {
                return _buildMoreCardsComingSoon(context);
              }

              return _buildUnlockInspirations(context);
            },
          );
        });
  }

  Widget _buildUnlockInspirations(BuildContext context) {
    final localizations = AppLocalizations.of(context)!;
    return _buildEndCard(
      context: context,
      subtitle: localizations.unlockVisitedUpsellSubtitle,
      cta: StadiumButton(
        title: localizations.checkTheDetails,
        onTapped: () => _buyInspirations(context),
      ),
    );
  }

  Widget _buildMoreCardsComingSoon(BuildContext context) {
    return _buildEndCard(
      context: context,
      subtitle: AppLocalizations.of(context)!.moreInspirationsComingSoon,
    );
  }

  Widget _buildEndCard({
    required BuildContext context,
    required String subtitle,
    Widget? cta,
  }) {
    final textTheme = Theme.of(context).textTheme;
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          Text(
            AppLocalizations.of(context)!.unlockVisitedUpsellTitle,
            style: textTheme.titleLarge,
          ),
          Padding(
            padding: const EdgeInsets.symmetric(vertical: 16.0),
            child: SizedBox(
              width: 180,
              child: Text(
                subtitle,
                textAlign: TextAlign.center,
                style: textTheme.titleSmall,
              ),
            ),
          ),
          SvgPicture.asset('assets/images/unlock_inspirations_product.svg'),
          if (cta != null) cta
        ],
      ),
    );
  }

  void _buyInspirations(BuildContext context) async {
    const spinner = SpinnerDialog();
    final nav = Navigator.maybeOf(context);

    spinner.show(context);

    final bloc = DependencyInjector.iapBloc;
    final unlockInspirations =
        await bloc.fetchProduct(IAPFeature.unlockInspirations);

    if (unlockInspirations == null) {
      nav?.pop();
      return;
    }

    try {
      await bloc.purchase(unlockInspirations);
      inspirationBloc.fetchMoreInspirations();
    } finally {
      nav?.pop();
    }
  }
}
