import 'dart:async';

import 'package:firebase_analytics/firebase_analytics.dart';
import 'package:flutter/material.dart';
import 'package:flutter_tindercard/flutter_tindercard.dart';

import '../../dependency_injection/dependency_injector.dart';
import '../../generic_widgets/spinner.dart';
import '../../models/geo_area.dart';
import 'inspiration.dart';
import 'inspiration_bloc.dart';
import 'inspiration_card.dart';
import 'inspiration_selection_type.dart';

class InspirationCardDeck extends StatefulWidget {
  const InspirationCardDeck({
    super.key,
    this.area,
    required this.inspirationBloc,
  });

  final GeoArea? area;
  final InspirationBloc inspirationBloc;

  @override
  State createState() => _InspirationCardDeckState();
}

class _InspirationCardDeckState extends State<InspirationCardDeck> {
  final _pageSize = 50;
  final _cardController = CardController();
  StreamSubscription? _languageSubscription;
  final _cards = <Inspiration>[];

  var _isLoading = true;
  var _hasError = false;
  var _noMoreCardsToFetch = false;

  final _fetchMoreRemainingSize = 10;

  @override
  void initState() {
    super.initState();
    _languageSubscription ??= widget.inspirationBloc.events.listen(
      _onInspirationEvent,
      onError: (_) {
        setState(() {
          _isLoading = false;
          _hasError = true;
        });
      },
    );

    DependencyInjector.settingsBloc.language.listen((_) {
      _cards.clear();
      setState(() {
        _isLoading = true;
      });
      // widget.inspirationBloc.fetchMoreInspirations();
    });
  }

  @override
  Widget build(BuildContext context) {
    return LayoutBuilder(
      builder: (context, constraints) {
        if (_isLoading) {
          return const Center(
            child: Spinner(),
          );
        }

        if (_hasError || _cards.isEmpty) {
          return const SizedBox();
        }

        return TinderSwapCard(
          cardController: _cardController,
          totalNum: _cards.length,
          swipeEdge: 4.0,
          stackNum: 2,
          orientation: AmassOrientation.top,
          swipeEdgeVertical: 16.0,
          animDuration: 200,
          maxWidth: constraints.maxWidth,
          minWidth: constraints.maxWidth * 0.8,
          maxHeight: constraints.maxHeight,
          minHeight: constraints.maxHeight * 0.99,
          allowSwiping: true,
          swipeCompleteCallback: _onCardSwiped,
          cardBuilder: (context, index) {
            final inspiration = _cards[index];
            return InspirationCard(
              inspirationBloc: widget.inspirationBloc,
              inspiration: inspiration,
              onWant: () => _onConfirmSelection(
                inspiration,
                InspirationSelectionType.want,
              ),
              onBeen: () => _onConfirmSelection(
                inspiration,
                InspirationSelectionType.been,
              ),
            );
          },
        );
      },
    );
  }

  void _onConfirmSelection(
      Inspiration inspiration, InspirationSelectionType selection) {
    final bloc = widget.inspirationBloc;
    bloc.select(inspiration, selection);

    if (!_noMoreCardsToFetch && _cards.length <= _fetchMoreRemainingSize) {
      bloc.fetchMoreInspirations();
    }

    if (selection == InspirationSelectionType.want) {
      _cardController.triggerLeft();
    } else if (selection == InspirationSelectionType.been) {
      _cardController.triggerRight();
    }
  }

  void _onInspirationEvent(InspirationEvent event) {
    if (event is InspirationLoadingEvent) {
      setState(() {
        _isLoading = true;
      });
      return;
    }

    if (event is! InspirationCardsEvent) {
      return;
    }

    final inspirations = event.inspirations;

    for (final newCard in inspirations) {
      if (!_cards.contains(newCard)) {
        _cards.add(newCard);
      }
    }
    setState(() {
      _isLoading = false;
      _noMoreCardsToFetch = inspirations.length < _pageSize;
    });
  }

  void _onCardSwiped(CardSwipeOrientation orientation, int index) {
    if (index == _cards.length - 1) {
      final boughtInspirations =
          DependencyInjector.iapBloc.hasUnlockedInspirations;
      final eventName = boughtInspirations ? 'completed' : 'paywall';
      FirebaseAnalytics.instance.logEvent(name: 'inspirations/$eventName');
    }
  }

  @override
  void dispose() {
    _languageSubscription?.cancel();
    super.dispose();
  }
}
