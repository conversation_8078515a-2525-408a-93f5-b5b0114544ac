import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import '../../generic_widgets/platform_aware/platform_switch.dart';

class PlatformToggle extends StatelessWidget {
  final bool checked;
  final Widget child;
  final ValueChanged<bool> onChanged    ;
  final Color? trackColor;

  const PlatformToggle({
    super.key,
    required this.checked,
    required this.onChanged,
    required this.child,
    this.trackColor,
  });

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8.0),
      child: GestureDetector(
        behavior: HitTestBehavior.opaque,
        onTap: () => onChanged(!checked),
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            PlatformSwitch(
              checked: checked,
              onChanged: onChanged,
              trackColor: trackColor,
            ),
            const SizedBox(width: 16),
            Expanded(child: child),
          ],
        ),
      ),
    );
  }
}
