class PrivacyAgreement {
  final bool required;
  bool? optedIn;
  bool? agreedToTerms;

  PrivacyAgreement() : required = true;

  PrivacyAgreement.fromJson(Map json)
      : required = json['required'],
        optedIn = _privacyStatus(json['optIn']),
        agreedToTerms = _privacyStatus(json['terms']);

  static bool? _privacyStatus(String key) {
    switch (key) {
      case 'YES':
        return true;
      case 'NO':
        return false;
      default:
        return null;
    }
  }

  bool get requiresInput {
    if (!required) {
      return false;
    }

    return !accepted;
  }

  bool get accepted => (optedIn ?? false) && (agreedToTerms ?? false);
}
