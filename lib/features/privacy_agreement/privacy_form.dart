import 'package:flutter/material.dart';
import 'package:flutter_markdown/flutter_markdown.dart';

import '../../generic_widgets/legal_links.dart';
import '../../generic_widgets/platform_aware/platform_icon_button.dart';
import '../../generic_widgets/responsive_padding.dart';
import '../../generic_widgets/stadium_button.dart';
import '../../helpers/url_dispatcher.dart';
import '../../l10n/generated/app_localizations.dart';
import '../authentication/login_background.dart';
import '../authentication/login_screen.dart';
import 'platform_toggle.dart';
import 'privacy_agreement.dart';

class PrivacyForm extends StatefulWidget {
  final PrivacyAgreement agreement;
  final VoidCallback onSubmitTapped;
  final VoidCallback? onCancelled;

  const PrivacyForm({
    super.key,
    required this.agreement,
    required this.onSubmitTapped,
    this.onCancelled,
  });

  @override
  State createState() => _PrivacyFormState();
}

class _PrivacyFormState extends State<PrivacyForm> {
  @override
  Widget build(BuildContext context) {
    return PopScope(
      canPop: false,
      child: Scaffold(
        body: Stack(
          children: [
            const VisitedGradientBackground(),
            _buildContent(context),
            if (widget.onCancelled != null)
              Positioned(
                top: 0,
                right: 0,
                child: SafeArea(
                  child: PlatformIconButton(
                    iconColour: Colors.white,
                    icon: Icons.close,
                    onTapped: widget.onCancelled!,
                  ),
                ),
              )
          ],
        ),
      ),
    );
  }

  Widget _buildContent(BuildContext context) {
    final localizations = AppLocalizations.of(context)!;
    return Scrollbar(
      child: CustomScrollView(
        slivers: [
          ResponsiveSliverPadding(
            fillToEdgeOnPhone: false,
            context: context,
            sliver: SliverList.list(
              children: [
                const SafeArea(
                    child: SizedBox(
                  height: 24,
                )),
                Text(
                  localizations.privacyAgreement,
                  style: const TextStyle(
                    fontSize: 30,
                    fontWeight: FontWeight.w900,
                    color: Colors.white,
                  ),
                ),
                Padding(
                  padding: const EdgeInsets.symmetric(vertical: 14.0),
                  child: Text(
                    localizations.privacyAgreementSubtitle,
                    style: const TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.w600,
                      color: Colors.white,
                    ),
                  ),
                ),
                _buildTerms(),
                _builtOptIn(),
                _buildSubmitButton(),
                ..._buildContactInfo(context)
              ],
            ),
          )
        ],
      ),
    );
  }

  double getFontSize(BuildContext context) {
    return MediaQuery.of(context).size.width < 600 ? 16 : 22;
  }

  Widget _buildTerms() {
    return PlatformToggle(
      trackColor: Colors.grey,
      checked: widget.agreement.agreedToTerms ?? false,
      onChanged: (agreed) {
        setState(() {
          widget.agreement.agreedToTerms = agreed;
        });
      },
      child: MarkdownBody(
        data: AppLocalizations.of(context)!.privacyAgreementTermsMarkdown,
        styleSheet: MarkdownStyleSheet(
            p: baseStyle(context),
            a: baseStyle(context).copyWith(
              decoration: TextDecoration.underline,
              color: Colors.white,
              fontWeight: FontWeight.w700,
            )),
        onTapLink: (text, href, title) => UrlDispatcher.launch(href),
      ),
    );
  }

  Widget _builtOptIn() {
    return PlatformToggle(
      trackColor: Colors.grey,
      checked: widget.agreement.optedIn ?? false,
      onChanged: (agreed) {
        setState(() {
          widget.agreement.optedIn = agreed;
        });
      },
      child: Text(
        AppLocalizations.of(context)!.privacyAgreementOptIn,
        style: baseStyle(context),
      ),
    );
  }

  TextStyle baseStyle(BuildContext context) {
    return TextStyle(color: Colors.white, fontSize: getFontSize(context));
  }

  Widget _buildSubmitButton() {
    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Center(
        child: StadiumButton(
          title: AppLocalizations.of(context)!.submit,
          fillColour: LoginScreen.confirmButtonColour,
          textColour: Colors.black,
          onTapped: widget.onSubmitTapped,
        ),
      ),
    );
  }

  Iterable<Widget> _buildContactInfo(BuildContext context) {
    final localizations = AppLocalizations.of(context)!;
    return [
      Text(
        localizations.companyName,
        textAlign: TextAlign.center,
        style: Theme.of(context)
            .textTheme
            .titleSmall
            ?.copyWith(color: Colors.white),
      ),
      Padding(
        padding: const EdgeInsets.symmetric(vertical: 8),
        child: Text(
          localizations.companyAddress,
          textAlign: TextAlign.center,
          style: Theme.of(context)
              .textTheme
              .titleSmall
              ?.copyWith(color: Colors.white),
        ),
      ),
      UnderlinedButton(
        title: localizations.supportEmailAddress,
        addDropShadow: false,
        url: 'mailto:${localizations.supportEmailAddress}',
      )
    ];
  }
}
