import 'package:flutter/material.dart';
import 'privacy_agreement.dart';
import 'privacy_form.dart';
import '../../generic_widgets/alert.dart';

class PrivacyDialog extends StatelessWidget with Presentable<PrivacyAgreement> {
  final PrivacyAgreement agreement;
  final ValueChanged<PrivacyAgreement>? onSubmitted;

  const PrivacyDialog({
    super.key,
    required this.agreement,
    this.onSubmitted,
  });

  @override
  Widget build(BuildContext context) {
    return PrivacyForm(
      agreement: agreement,
      onSubmitTapped: () {
        if (agreement.accepted) {
          Navigator.of(context).pop(agreement);
        }
      },
    );
  }

  @override
  bool get barrierDismissible => false;
}
