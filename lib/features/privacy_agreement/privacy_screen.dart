import 'package:flutter/material.dart';

import '../../dependency_injection/dependency_injector.dart';
import '../../generic_widgets/alert.dart';
import '../../generic_widgets/status_bar_tinter.dart';
import '../../l10n/generated/app_localizations.dart';
import '../settings/delete_confirmation_dialog.dart';
import 'privacy_agreement.dart';
import 'privacy_form.dart';

class PrivacyScreen extends StatelessWidget {
  static const routeName = 'privacyAgreement';

  final PrivacyAgreement agreement;

  const PrivacyScreen({
    super.key,
    required this.agreement,
  });

  @override
  Widget build(BuildContext context) {
    return StatusBarTinter(
      child: PopScope(
        canPop: false,
        child: PrivacyForm(
          agreement: agreement,
          onSubmitTapped: () => _onSubmitTapped(context),
        ),
      ),
    );
  }

  void _onSubmitTapped(BuildContext context) {
    if (agreement.accepted) {
      DependencyInjector.sessionBloc.submitPrivacyAgreement(agreement);
      Navigator.of(context).pop();
      return;
    }

    _showPrivacyRequiredDialog(context);
  }

  void _showPrivacyRequiredDialog(BuildContext context) async {
    final localizations = AppLocalizations.of(context)!;

    final alert = ConfirmDialog(
      title: localizations.errorTitle,
      message: localizations.privacyAgreementRequired,
      cancelText: localizations.deleteAccount,
      confirmText: localizations.ok,
      cancelDestructive: true,
    );

    final confirmed = await alert.show(context);
    if (confirmed == true) {
      return;
    }

    if (context.mounted) {
      _presentDeleteScreen(context);
    }
  }

  void _presentDeleteScreen(BuildContext context) {
    showAdaptiveDialog(
      context: context,
      builder: (_) => const DeleteConfirmationDialog(),
    );
  }
}
