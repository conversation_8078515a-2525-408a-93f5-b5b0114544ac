import 'package:flutter/material.dart';

import '../../dependency_injection/dependency_injector.dart';
import '../../generic_widgets/navigation_extensions.dart';
import '../../generic_widgets/platform_aware/platform_app_bar.dart';
import '../../generic_widgets/pushable_tile.dart';
import '../../generic_widgets/responsive_padding.dart';
import '../../l10n/generated/app_localizations.dart';
import '../cities/paywall_dialog.dart';
import '../experiences/widgets/experience_dashboard_screen.dart';
import '../in_app_purchase/iap_product.dart';
import '../inspiration/inspiration_screen.dart';
import '../itineraries/itinerary_screen.dart';
import '../todo_lists/todo_list_screen.dart';

class ListsScreen extends StatelessWidget {
  static const routeName = 'places';

  const ListsScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final localizations = AppLocalizations.of(context)!;
    return Scaffold(
      appBar: PlatformAppBar(
        title: localizations.places,
      ),
      body: SafeArea(
        child: ResponsivePadding(
          context: context,
          fillToEdgeOnPhone: true,
          child: Column(
            children: [
              _buildItineraries(localizations, context),
              _buildLists(localizations, context),
              _buildInspirations(localizations, context),
              _buildExperiences(localizations, context),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildItineraries(
    AppLocalizations localizations,
    BuildContext context,
  ) {
    return _buildTile(
      context,
      title: localizations.itineraries,
      imageLocation: 'assets/images/itineraries.jpg',
      routeName: ItineraryScreen.routeName,
      onTapped: () async {
        if (DependencyInjector.iapBloc.hasUnlockedItineraries) {
          _pushItineraries(context);
          return;
        }

        final purchased =
            await PaywallDialog(feature: IAPFeature.unlockItineraries)
                .show(context);

        if (!context.mounted) {
          return;
        }

        if (purchased == true) {
          _pushItineraries(context);
        }
      },
    );
  }

  void _pushItineraries(BuildContext context) {
    Navigator.of(context).pushMaterialRoute(
      name: ItineraryScreen.routeName,
      builder: (_) => const ItineraryScreen(),
    );
  }

  Widget _buildLists(AppLocalizations localizations, BuildContext context) {
    return _buildTile(
      context,
      title: localizations.lists,
      imageLocation: 'assets/images/lists.jpg',
      routeName: TodoListScreen.routeName,
      routeBuilder: (_) => const TodoListScreen(),
    );
  }

  Widget _buildExperiences(
      AppLocalizations localizations, BuildContext context) {
    return _buildTile(
      context,
      title: localizations.experiences,
      imageLocation: 'assets/images/experiences.jpg',
      routeName: 'experiences',
      routeBuilder: (_) => const ExperienceDashboardScreen(),
    );
  }

  Widget _buildInspirations(
      AppLocalizations localizations, BuildContext context) {
    return _buildTile(
      context,
      title: localizations.inspiration,
      imageLocation: 'assets/images/inspirations.jpg',
      routeName: 'inspirations',
      routeBuilder: (_) => const InspirationScreen(),
    );
  }

  Widget _buildTile(
    BuildContext context, {
    required String title,
    required String imageLocation,
    required String routeName,
    VoidCallback? onTapped,
    WidgetBuilder? routeBuilder,
  }) {
    assert(onTapped != null || routeBuilder != null);
    return Flexible(
      child: PushableTile(
        title: title,
        trailing: Icon(Icons.adaptive.arrow_forward),
        titleStyle: tileTextStyle,
        imageLocation: AssetImageLocation(imageLocation),
        onTapped: onTapped ??
            () => _onTapped(
                  context,
                  routeName: routeName,
                  routeBuilder: routeBuilder!,
                ),
      ),
    );
  }

  TextStyle get tileTextStyle => const TextStyle(
        fontSize: 17,
        fontWeight: FontWeight.w600,
      );

  void _onTapped(
    BuildContext context, {
    required String routeName,
    required WidgetBuilder routeBuilder,
  }) =>
      Navigator.of(context).pushMaterialRoute(
        name: routeName,
        builder: routeBuilder,
      );
}
