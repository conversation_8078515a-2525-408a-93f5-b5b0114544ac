import 'package:flutter/material.dart';

import '../../dependency_injection/dependency_injector.dart';
import '../../generic_widgets/navigation_extensions.dart';
import '../../generic_widgets/platform_aware/platform_icon_button.dart';
import '../../generic_widgets/platform_aware/platform_sliver_app_bar.dart';
import '../../generic_widgets/responsive_padding.dart';
import '../../generic_widgets/sliver_bottom_safe_area.dart';
import '../../generic_widgets/spinner.dart';
import '../../l10n/generated/app_localizations.dart';
import '../../models/geo_area.dart';
import '../../models/selection.dart';
import '../areas/area_list_picker.dart';
import '../areas/area_tile.dart';
import 'travel_goal_graph.dart';

class TravelGoalScreen extends StatelessWidget {
  static const routeName = '/travelGoal';

  const TravelGoalScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      floatingActionButton: Theme.of(context).platform != TargetPlatform.iOS
          ? FloatingActionButton(
              child: const Icon(Icons.add),
              onPressed: () => _pushListPicker(context),
            )
          : null,
      body: Scrollbar(
        child: CustomScrollView(
          slivers: [
            _buildAppBar(context),
            _buildStats(),
            ResponsiveSliverPadding(
              context: context,
              fillToEdgeOnPhone: true,
              sliver: StreamBuilder<List<GeoArea>>(
                stream: DependencyInjector.areaSelectionBloc
                    .listenToCountriesByType(Selection.want),
                builder: (context, snapshot) {
                  final areas = snapshot.data;

                  if (snapshot.connectionState == ConnectionState.waiting ||
                      areas == null) {
                    return _buildLoading();
                  }

                  if (areas.isEmpty) {
                    return _buildGoalComplete(context);
                  }

                  return _buildWantList(areas);
                },
              ),
            ),
            const SliverBottomSafeArea(),
          ],
        ),
      ),
    );
  }

  Widget _buildWantList(List<GeoArea> areas) {
    return SliverList(
      delegate: SliverChildBuilderDelegate(
        childCount: areas.length,
        (context, index) {
          final area = areas[index];
          return AreaTile(area: area);
        },
      ),
    );
  }

  Widget _buildGoalComplete(BuildContext context) {
    return SliverFillRemaining(
      child: Center(
        child: SizedBox(
          width: 300,
          child: Text(
            AppLocalizations.of(context)!.travelGoalComplete,
            textAlign: TextAlign.center,
            style: Theme.of(context).textTheme.headlineSmall,
          ),
        ),
      ),
    );
  }

  Widget _buildLoading() {
    return const SliverFillRemaining(
      child: Center(
        child: Spinner(),
      ),
    );
  }

  Widget _buildStats() {
    return const SliverToBoxAdapter(
      child: SizedBox(height: 300, child: TravelGoalGraph(animate: false)),
    );
  }

  Widget _buildAppBar(BuildContext context) {
    return PlatformSliverAppBar(
      title: AppLocalizations.of(context)!.myTravelGoal,
      action: (Theme.of(context).platform == TargetPlatform.iOS)
          ? PlatformIconButton(
              icon: Icons.add,
              onTapped: () => _pushListPicker(context),
            )
          : null,
    );
  }

  void _pushListPicker(BuildContext context) {
    Navigator.of(context).pushMaterialRoute(
        name: "/areaListPicker", builder: (_) => const AreaListPicker());
  }
}
