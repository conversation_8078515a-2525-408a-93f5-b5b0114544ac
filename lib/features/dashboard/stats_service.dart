import '../../dependency_injection/dependency_injector.dart';

class StatsService {
  Future<List<(String, int)>> fetchTravelerType() async {
    final results = await DependencyInjector.client.get(
      'stats/travellerType',
      cacheable: false,
    );
    if (results == null) {
      return [];
    }

    return [
      for (final json in results) (json['name'] as String, json['count'] as int)
    ];
  }

  Future<HotelPreference?> fetchHotelPreferences() async {
    final results = await DependencyInjector.client.get(
      'stats/hotelPreference',
      cacheable: false,
    );

    if (results == null) {
      return null;
    }

    return HotelPreference.fromJson(results);
  }
}

class HotelPreference {
  HotelPreference.fromJson(Map json)
      : rank = json['rank'],
        budget = json['budget'],
        mid = json['mid'],
        luxury = json['luxury'];

  final double rank;
  final int budget;
  final int mid;
  final int luxury;

  double normalized(double value) {
    final min = budget;
    final max = luxury;

    return (value - min) / (max - min);
  }
}
