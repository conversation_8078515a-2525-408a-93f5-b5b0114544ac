import 'dart:async';

import 'package:collection/collection.dart';

import '../../caching/resettable_behaviour_subject.dart';
import '../../dependency_injection/dependency_injector.dart';
import '../../generic_widgets/bloc.dart';
import '../../models/color_extensions.dart';
import '../../models/geo_area.dart';
import '../../models/selection.dart';
import '../localization/supported_language.dart';
import '../settings/settings_bloc.dart';
import '../todo_lists/models/todo_list.dart';
import 'stats_service.dart';
import 'traveller_type_view_model.dart';

class StatsBloc implements Bloc {
  final _service = StatsService();

  StatsBloc(SettingsBloc settingsBloc) {
    _languageSubscription = settingsBloc.language.listen((languages) async {
      if (_typeController.hasValue) {
        _typeController.reset();
        _fetchTravelerType();
      }

      if (_hotelController.hasValue) {
        _hotelController.reset();
        _fetchHotelType();
      }

      if (_mostFrequentlyVisitedCountryController.hasValue) {
        final currentCountry = _mostFrequentlyVisitedCountryController.value;
        await Future.delayed(const Duration(milliseconds: 100));
        final localizedCountry = await DependencyInjector.areaBloc
            .areaByIsoCode(currentCountry?.isoCode ?? '');
        _mostFrequentlyVisitedCountryController.add(localizedCountry);
      }
    });
  }

  late final _typeController =
      ResettableBehaviorSubject<List<TravelTypeViewModel>>()
        ..onListen = _listenToSelection;

  late final _mostFrequentlyVisitedCountryController =
      ResettableBehaviorSubject<GeoArea?>()
        ..onListen = _listenToMostFrequentlyVisitedCountry;
  StreamSubscription? _mostFrequentlyVisitedCountrySubscription;

  late final _hotelController = ResettableBehaviorSubject<HotelPreference>()
    ..onListen = _listenToSelection;

  late StreamSubscription<SupportedLanguage?> _languageSubscription;
  StreamSubscription? _listSelectionSubscription;

  Stream<List<TravelTypeViewModel>> get travelTypes => _typeController.stream;

  Stream<HotelPreference> get hotelPreference => _hotelController.stream;

  static const _hotelListsIds = {305, 326, 323};
  var _lastHotelSelectionCount = 0;

  Stream<GeoArea?> mostFrequentlyVisitedCountry() =>
      _mostFrequentlyVisitedCountryController.stream;

  void _listenToMostFrequentlyVisitedCountry() {
    _mostFrequentlyVisitedCountrySubscription ??= DependencyInjector
        .areaSelectionBloc
        .listenToMostFrequentlyVisitedCountries()
        .listen((areas) {
          _mostFrequentlyVisitedCountryController.add(areas.firstOrNull);
        });
  }

  Stream<bool> shouldRowUsRegionsOnDashboard() {
    return DependencyInjector.areaSelectionBloc.selections.asyncMap((
      allSelections,
    ) async {
      final usa = await DependencyInjector.areaBloc.areaByIsoCode('US');
      if (usa == null) {
        return false;
      }

      final selection = allSelections[usa];
      if (selection != Selection.live) {
        return false;
      }

      final statesBeen = allSelections.entries
          .where(
            (entry) =>
                entry.key.parentId == usa.parentId &&
                entry.value == Selection.been,
          )
          .length;

      return statesBeen > 2;
    });
  }

  void _listenToSelection() {
    _listSelectionSubscription ??= DependencyInjector.todoListBloc.selections
        .listen(_onSelectionsUpdated);

    if (!_typeController.hasValue) {
      _fetchTravelerType();
    }

    if (!_hotelController.hasValue) {
      _fetchHotelType();
    }
  }

  Future<void> _fetchTravelerType() async {
    if (_typeController.hasValue) {
      return;
    }

    final stats = await _service.fetchTravelerType();
    final total = stats.fold(0, (total, tuple) => total + tuple.$2);

    final been = DependencyInjector.settingsBloc.currentPalette.been;

    final viewModels = stats
        .map(
          (tuple) => TravelTypeViewModel(
            name: tuple.$1,
            amount: tuple.$2,
            color: been.withBrightness(tuple.$2 / total),
          ),
        )
        .toList();

    _typeController.add(viewModels);
  }

  Future<void> _fetchHotelType() async {
    if (_hotelController.hasValue) {
      return;
    }

    final preference = await _service.fetchHotelPreferences();
    if (preference == null) {
      return;
    }

    _hotelController.add(preference);
  }

  @override
  void clear() {
    _hotelController.reset();
    _typeController.reset();
  }

  void _onSelectionsUpdated(
    Map<TodoList, Map<Selection, Set<int>>> selections,
  ) {
    // Prevents Infinite loop when logging out
    if (!DependencyInjector.sessionBloc.isAuthenticated) {
      return;
    }

    final hotelSelectionCount = selections.entries
        .where((entry) => _hotelListsIds.contains(entry.key.id))
        .fold(
          0,
          (total, entry) => total + (entry.value[Selection.been]?.length ?? 0),
        );

    if (hotelSelectionCount != _lastHotelSelectionCount) {
      _fetchHotelType();
      _lastHotelSelectionCount = hotelSelectionCount;
    }

    _fetchTravelerType();
  }

  Stream<TodoList> prepareWondersOfTheWorldTile() {
    return DependencyInjector.settingsBloc.language.asyncMap((_) async {
      final todoListBloc = DependencyInjector.todoListBloc;

      final lists = await todoListBloc.fetchLists();
      final wondersOfTheWorld = lists.firstWhere(
        (lists) => lists.id == TodoList.worldWondersId,
      );
      final _ = await todoListBloc.fetchItems(wondersOfTheWorld);
      return wondersOfTheWorld;
    });
  }

  @override
  void dispose() {
    _mostFrequentlyVisitedCountrySubscription?.cancel();
    _languageSubscription.cancel();
    _typeController.close();
    _hotelController.close();
    _listSelectionSubscription?.cancel();
  }
}
