import 'package:flutter/material.dart';
import 'package:rxdart/rxdart.dart';

import '../../../helpers/margin.dart';
import 'graph_theme.dart';

class CounterGraph extends StatelessWidget with GraphTheme {
  final int count;
  final int? total;
  final String? label;

  const CounterGraph({super.key, required this.count, this.total, this.label});

  @override
  Widget build(BuildContext context) {
    if (label == null) {
      return _buildCounter(context);
    }

    return Column(
      children: [
        _buildCounter(context),
        Padding(
          padding: const EdgeInsets.all(Margin.small),
          child: Text(label!, style: subtitleStyle(context)),
        ),
      ],
    );
  }

  Widget _buildCounter(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        shape: BoxShape.circle,
        border: Border.all(
          color: Theme.of(context).colorScheme.primary,
          width: 1,
        ),
      ),
      child: _buildContent(context),
    );
  }

  Widget _buildContent(BuildContext context) {
    if (total == null) {
      return Padding(
        padding: const EdgeInsets.all(24.0),
        child: _buildNumber(context, count),
      );
    }

    return Padding(
      padding: const EdgeInsets.all(20),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        mainAxisSize: MainAxisSize.min,
        children: [
          _buildNumber(context, count),
          Container(
            height: 1,
            width: 30,
            margin: const EdgeInsets.symmetric(vertical: Margin.small),
            color: graphStyle(context)?.color ?? Colors.black,
          ),
          _buildNumber(context, total!, fontSize: 14),
        ],
      ),
    );
  }

  Widget _buildNumber(BuildContext context, int number, {double? fontSize}) {
    return Text(
      number.toString(),
      style: graphStyle(context, fontSize: fontSize),
    );
  }
}

class ListeningCounterGraph extends StatelessWidget with GraphTheme {
  const ListeningCounterGraph({
    super.key,
    required this.count,
    required this.label,
    this.total,
  });

  final Stream<int> count;
  final Stream<int>? total;
  final Stream<String> label;

  @override
  Widget build(BuildContext context) {
    return StreamBuilder<(int, int?, String)>(
      stream: total == null
          ? Rx.combineLatest2(
              count,
              label,
              (count, label) => (count, null, label),
            )
          : Rx.combineLatest3(
              count,
              total!,
              label,
              (count, total, label) => (count, total, label),
            ),
      builder: (context, snapshot) {
        final tuple = snapshot.data;
        if (tuple == null) {
          return const SizedBox();
        }
        final (count, total, label) = tuple;
        return CounterGraph(count: count, total: total, label: label);
      },
    );
  }
}
