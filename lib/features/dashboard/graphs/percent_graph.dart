import 'dart:math';

import 'package:flutter/material.dart';

class PercentGraph extends StatelessWidget {
  final double percentage;
  final double animationPercentage;
  final Widget? child;
  final double lineWidth;

  const PercentGraph({
    super.key,
    required this.percentage,
    this.animationPercentage = 1.0,
    this.lineWidth = 16.0,
    this.child,
  });

  @override
  Widget build(BuildContext context) {
    return AspectRatio(
      aspectRatio: 1.0,
      child: CustomPaint(
        foregroundPainter: percentage > 0
            ? PercentagePainter(
                color: Theme.of(context).colorScheme.primary,
                percentage: percentage,
                percentAnimated: animationPercentage,
                lineWidth: lineWidth,
              )
            : null,
        painter: DashCirclePainter(
          dashColour: Theme.of(context).dividerColor,
        ),
        child: child,
      ),
    );
  }
}

mixin _CirclePainter {
  Path circlePath(Size size) => Path()..addOval(frame(size));

  Rect frame(Size size) => Rect.fromLTWH(0, 0, size.height, size.height);
}

class PercentagePainter extends CustomPainter with _CirclePainter {
  final Color color;
  final double percentage;
  final double percentAnimated;
  final double lineWidth;

  const PercentagePainter({
    required this.color,
    required this.percentage,
    this.percentAnimated = 1.0,
    this.lineWidth = 16.0,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = color
      ..style = PaintingStyle.stroke
      ..strokeCap = StrokeCap.round
      ..strokeWidth = lineWidth;

    final start = degreesToRadians(-90);
    final end = degreesToRadians(450);

    final fullPath = Path()..addArc(frame(size), start, end);
    final metric = fullPath.computeMetrics().first;
    final arc =
        metric.extractPath(0, metric.length * percentage * percentAnimated);
    canvas.drawPath(arc, paint);
  }

  @override
  bool shouldRepaint(covariant PercentagePainter oldDelegate) => true;

  double degreesToRadians(double degrees) {
    return degrees * pi / 180;
  }
}

class DashCirclePainter extends CustomPainter with _CirclePainter {
  final Color dashColour;

  DashCirclePainter({required this.dashColour});

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = dashColour
      ..style = PaintingStyle.stroke
      ..strokeWidth = 2;
    final circle = circlePath(size);

    final dashedPath = Path();

    const dashWidth = 5;
    const dashSpace = 8;
    var distance = 0.0;

    for (final pathMetric in circle.computeMetrics()) {
      while (distance < pathMetric.length) {
        dashedPath.addPath(
          pathMetric.extractPath(distance, distance + dashWidth),
          Offset.zero,
        );
        distance += dashWidth;
        distance += dashSpace;
      }
    }
    canvas.drawPath(dashedPath, paint);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}
