import 'package:flutter/material.dart';

import 'graph_theme.dart';
import 'percent_graph.dart';

class ListeningPercentGraph extends StatelessWidget with GraphTheme {
  final Stream<double> percentage;
  final String? label;
  final Stream<String>? labelStream;

  const ListeningPercentGraph({
    super.key,
    required this.percentage,
    this.label,
    this.labelStream,
  }) : assert(label != null || labelStream != null);

  @override
  Widget build(BuildContext context) {
    return StreamBuilder<double>(
      stream: percentage,
      initialData: 0,
      builder: (context, snapshot) {
        var percent = snapshot.data ?? 0;
        if (percent.isInfinite || percent.isNaN || percent.isNegative) {
          percent = 0;
        }

        return Column(
          children: [
            SizedBox(
              height: 80,
              child: PercentGraph(
                percentage: percent,
                lineWidth: 2,
                child: Center(
                  child: FittedBox(
                    fit: BoxFit.scaleDown,
                    child: Text(
                      percent == 0 ? '0%' : '${(percent * 100).round()}%',
                      style: graphStyle(context),
                    ),
                  ),
                ),
              ),
            ),
            Padding(
              padding: const EdgeInsets.all(8.0),
              child: FittedBox(
                fit: BoxFit.scaleDown,
                child: labelStream != null
                    ? StreamBuilder<String>(
                        stream: labelStream,
                        builder: (context, snapshot) =>
                            _buildLabel(context, snapshot.data),
                      )
                    : _buildLabel(context, label),
              ),
            ),
          ],
        );
      },
    );
  }

  Widget _buildLabel(BuildContext context, String? label) {
    return Text(
      label ?? '',
      textAlign: TextAlign.center,
      style: subtitleStyle(context),
    );
  }
}
