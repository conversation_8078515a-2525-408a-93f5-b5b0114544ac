import 'package:flutter/material.dart';

import '../../../generic_widgets/responsive_padding.dart';

mixin GraphTheme {
  Widget buildVerticalDivider() => const Padding(
    padding: EdgeInsets.symmetric(horizontal: 3.0),
    child: <PERSON><PERSON><PERSON><PERSON>(height: 100, child: VerticalDivider(thickness: 1)),
  );

  Widget buildDashboardDivider(BuildContext context) {
    final theme = Theme.of(context);
    return ResponsivePadding(
      context: context,
      child: Padding(
        padding: const EdgeInsets.only(bottom: 16, top: 8),
        child: Container(
          width: double.infinity,
          height: 1,
          color: theme.brightness == Brightness.light
              ? theme.primaryColor
              : theme.colorScheme.primary,
        ),
      ),
    );
  }

  Widget buildHorizontalDivider() => const Divider(thickness: 1, height: 24);

  TextStyle? graphStyle(BuildContext context, {double? fontSize}) {
    final theme = Theme.of(context);
    return theme.textTheme.headlineSmall?.copyWith(
      color: theme.colorScheme.primary,
      fontWeight: FontWeight.w700,
      fontSize: fontSize ?? 18,
    );
  }

  TextStyle? subtitleStyle(BuildContext context) {
    final theme = Theme.of(context);
    if (theme.brightness == Brightness.light) {
      return null;
    }

    return TextStyle(
      color: theme.colorScheme.primary,
      fontSize: 12,
      fontWeight: FontWeight.w500,
    );
  }
}
