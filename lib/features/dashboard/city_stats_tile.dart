import 'package:flutter/material.dart';

import '../../dependency_injection/dependency_injector.dart';
import '../../l10n/generated/app_localizations.dart';
import 'graphs/counter_graph.dart';
import 'graphs/graph_theme.dart';
import 'graphs/listening_percent_graph.dart';

class CityStatisticTile extends StatelessWidget with GraphTheme {
  const CityStatisticTile({super.key});

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 1,
      child: Padding(
        padding: const EdgeInsets.symmetric(vertical: 16.0),
        child: Row(
          children: [
            Expanded(
              child: ListeningCounterGraph(
                count: DependencyInjector.cityBloc.numberOfCitiesVisited,
                label: Stream.value(AppLocalizations.of(context)!.cities),
              ),
            ),
            buildVerticalDivider(),
            Expanded(
              child: ListeningPercentGraph(
                percentage: DependencyInjector.cityBloc.percentOfCitiesVisited,
                label: AppLocalizations.of(context)!.been,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
