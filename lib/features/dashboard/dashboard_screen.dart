import 'dart:async';

import 'package:flutter/material.dart';

import '../../dependency_injection/dependency_injector.dart';
import '../../generic_widgets/navigation_extensions.dart';
import '../../generic_widgets/platform_aware/platform_text_button.dart';
import '../../generic_widgets/responsive_padding.dart';
import '../../generic_widgets/sliver_bottom_safe_area.dart';
import '../../helpers/margin.dart';
import '../../l10n/generated/app_localizations.dart';
import '../../models/continent.dart';
import '../../models/device.dart';
import '../../models/geo_area.dart';
import '../ads/ad_manager.dart';
import '../areas/widgets/area_details_screen.dart';
import '../areas/widgets/region_sliver.dart';
import '../cities/city_selections_screen.dart';
import '../todo_lists/components/list_stats_tile.dart';
import '../todo_lists/models/todo_list.dart';
import '../todo_lists/todo_list_details_screen.dart';
import 'city_stats_tile.dart';
import 'continent_tile.dart';
import 'country_stats_tile.dart';
import 'dashboard_header.dart';
import 'graphs/graph_theme.dart';
import 'hotel_preferences_graph.dart';
import 'most_frequently_visited_country_graph.dart';
import 'top_places_tile.dart';
import 'travel_goal_graph.dart';
import 'travel_goal_screen.dart';
import 'traveller_type_graph.dart';

class DashboardScreen extends StatefulWidget {
  const DashboardScreen({super.key});

  @override
  State<DashboardScreen> createState() => _DashboardScreenState();
}

class _DashboardScreenState extends State<DashboardScreen> with GraphTheme {
  bool _showAds = false;
  StreamSubscription? _adsSubscription;

  @override
  void initState() {
    super.initState();
    _adsSubscription ??= DependencyInjector.iapBloc.status.listen((value) {
      setState(() {
        _showAds = !value.hasRemovedAds;
      });
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: SafeArea(
        bottom: false,
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: Device.margin),
          child: Scrollbar(
            child: CustomScrollView(
              slivers: [
                _buildGlobalStatistics(context),
                StreamBuilder<List<Continent>>(
                  stream: DependencyInjector.areaBloc.fetchContinents(),
                  builder: (context, snapshot) {
                    final continents = snapshot.data;
                    if (continents == null || continents.isEmpty) {
                      return const SliverToBoxAdapter(child: SizedBox());
                    }

                    return ResponsiveSliverPadding(
                      context: context,
                      sliver: SliverList.builder(
                        itemCount: continents.length + 1,
                        itemBuilder: (context, index) {
                          if (index == 0) {
                            return DashboardHeader(
                              title: AppLocalizations.of(
                                context,
                              )!.yourTravelsByContinent,
                            );
                          }

                          final continent = continents[index - 1];
                          return ContinentTile(
                            continent: continent,
                            showDivider: index < continents.length,
                          );
                        },
                      ),
                    );
                  },
                ),
                if (_showAds)
                  _buildAdSliver(key: const ValueKey('before_traveler_type')),
                SliverList.list(
                  children: [
                    const TravellerTypeGraph(),
                    const SizedBox(height: Margin.standard),
                    const HotelPreferencesGraph(),
                    const SizedBox(height: 20),
                  ],
                ),
                if (_showAds) _buildAdSliver(key: const ValueKey('ad1')),
                _buildUnitedStatesDetails(),
                const MostFrequentlyVisitedCountrySliver(),
                _buildWondersOfTheWorld(),
                _buildTopCountries(context),
                if (_showAds) _buildAdSliver(key: const ValueKey('ad2')),
                _buildCitySliver(context),
                const SliverBottomSafeArea(),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildWondersOfTheWorld() {
    return StreamBuilder<TodoList>(
      stream: DependencyInjector.statsBloc.prepareWondersOfTheWorldTile(),
      builder: (context, snapshot) {
        final worldWonders = snapshot.data;
        if (worldWonders == null) {
          return const SliverToBoxAdapter(child: SizedBox(height: 100));
        }
        return SliverList.list(
          children: [
            ResponsivePadding(
              context: context,
              child: DashboardHeader(title: worldWonders.name),
            ),
            ResponsivePadding(
              context: context,
              child: Card(child: ListStatsTile(list: worldWonders)),
            ),
            PlatformTextButton(
              title: AppLocalizations.of(context)!.update,
              onTapped: () => Navigator.of(context).pushMaterialRoute(
                fullscreen: true,
                name: TodoListDetailsScreen.routeName(worldWonders),
                builder: (_) => TodoListDetailsScreen(list: worldWonders),
              ),
            ),
          ],
        );
      },
    );
  }

  Widget _buildUnitedStatesDetails() {
    return StreamBuilder<bool>(
      stream: DependencyInjector.statsBloc.shouldRowUsRegionsOnDashboard(),
      builder: (context, snapshot) {
        if (snapshot.data != true) {
          return const SliverToBoxAdapter(child: SizedBox());
        }

        final usa = DependencyInjector.areaBloc.areaByIsoCodeSync('US')!;

        return SliverList.list(
          key: ValueKey(usa),
          children: [
            DashboardHeader(title: usa.name),
            PercentCountrySeenGraph(area: usa),
            RegionMap(area: usa, onTapped: null, showHeader: false),
            PlatformTextButton(
              title: AppLocalizations.of(context)!.update,
              onTapped: () {
                Navigator.of(context).pushMaterialRoute(
                  name: AreaDetailsScreen.routeName(usa),
                  fullscreen: true,
                  builder: (_) => AreaDetailsScreen(area: usa),
                );
              },
            ),
            buildDashboardDivider(context),
          ],
        );
      },
    );
  }

  Widget _buildAdSliver({required ValueKey<String> key}) {
    return SliverPadding(
      key: key,
      padding: const EdgeInsets.symmetric(vertical: Device.margin),
      sliver: SliverToBoxAdapter(child: AdView(location: AdLocation.dashboard)),
    );
  }

  Widget _buildGlobalStatistics(BuildContext context) {
    return SliverList.list(
      children: [
        const TravelGoalGraph(),
        PlatformTextButton(
          title: AppLocalizations.of(context)!.updateTravelGoal,
          onTapped: () {
            Navigator.of(context).pushMaterialRoute(
              name: TravelGoalScreen.routeName,
              fullscreen: true,
              builder: (context) => const TravelGoalScreen(),
            );
          },
        ),
        if (_showAds)
          Padding(
            key: const ValueKey('ad3'),
            padding: const EdgeInsets.symmetric(vertical: Margin.standard),
            child: AdView(location: AdLocation.dashboard),
          ),
        const CountryStatsBar(),
        Row(
          mainAxisSize: MainAxisSize.min,
          mainAxisAlignment: MainAxisAlignment.end,
          spacing: Margin.small,
          children: [
            Text(
              AppLocalizations.of(context)!.onlyCountSovereign,
              style: Theme.of(context).textTheme.bodySmall,
            ),
            StreamBuilder(
              stream: DependencyInjector.settingsBloc.countSovereign,
              builder: (context, asyncSnapshot) {
                return Switch.adaptive(
                  value: asyncSnapshot.data ?? false,
                  activeColor: Theme.of(context).colorScheme.primary,
                  onChanged: DependencyInjector.settingsBloc.updateSovereign,
                );
              },
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildTopCountries(BuildContext context) {
    return StreamBuilder<(GeoArea, List<GeoArea>)>(
      stream: DependencyInjector
          .areaSelectionBloc
          .topVisitedCountriesFromCurrentLivedCountry,
      builder: (context, snapshot) {
        final tuple = snapshot.data;
        if (tuple == null) {
          return const SliverToBoxAdapter(child: SizedBox());
        }

        final (liveCountry, topPlaces) = tuple;

        return ResponsiveSliverPadding(
          context: context,
          sliver: SliverList(
            delegate: SliverChildBuilderDelegate((context, index) {
              if (index == 0) {
                return DashboardHeader(
                  title: AppLocalizations.of(
                    context,
                  )!.topPlacesVisitedFromCountry(liveCountry.name),
                );
              }

              final area = topPlaces[index - 1];
              return TopPlacesTile(
                area: area,
                index: index,
                showDivider: index < topPlaces.length,
              );
            }, childCount: topPlaces.length + 1),
          ),
        );
      },
    );
  }

  Widget _buildCitySliver(BuildContext context) {
    return StreamBuilder<bool>(
      stream: DependencyInjector.iapBloc.status.map(
        (purchases) => purchases.hasUnlockedCities,
      ),
      builder: (context, snapshot) {
        if (snapshot.data != true) {
          return const SliverToBoxAdapter(child: SizedBox());
        }

        return ResponsiveSliverPadding(
          context: context,
          sliver: SliverList.list(
            children: [
              Padding(
                padding: const EdgeInsets.only(top: Margin.standard),
                child: DashboardHeader(
                  title: AppLocalizations.of(context)!.cities,
                ),
              ),
              const CityStatisticTile(),
              PlatformTextButton(
                title: AppLocalizations.of(context)!.update,
                onTapped: () => Navigator.of(context).pushMaterialRoute(
                  name: CitySelectionsScreen.routeName,
                  fullscreen: true,
                  builder: (_) => const CitySelectionsScreen(),
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  @override
  void dispose() {
    _adsSubscription?.cancel();
    super.dispose();
  }
}
