import 'package:bubble_chart/bubble_chart.dart';
import 'package:flutter/material.dart';

import '../../dependency_injection/dependency_injector.dart';
import '../../generic_widgets/navigation_extensions.dart';
import '../../generic_widgets/platform_aware/platform_text_button.dart';
import '../../generic_widgets/responsive_padding.dart';
import '../../l10n/generated/app_localizations.dart';
import '../../models/color_extensions.dart';
import '../todo_lists/todo_list_screen.dart';
import 'dashboard_header.dart';
import 'traveller_type_view_model.dart';

class TravellerTypeGraph extends StatelessWidget {
  const TravellerTypeGraph({super.key});

  @override
  Widget build(BuildContext context) {
    return StreamBuilder(
      stream: DependencyInjector.statsBloc.travelTypes,
      builder: (context, snapshot) {
        final types = snapshot.data;

        return ResponsivePadding(
          context: context,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              DashboardHeader(
                title: AppLocalizations.of(context)!.yourTravellerType,
              ),
              _buildBubbleGraph(context, types),
              _buildUpdateButton(context),
            ],
          ),
        );
      },
    );
  }

  Widget _buildBubbleGraph(
    BuildContext context,
    List<TravelTypeViewModel>? types,
  ) {
    return SizedBox(
      width: MediaQuery.of(context).size.width,
      height: 300,
      child: types == null || types.isEmpty
          ? _buildNoTravellerType(context)
          : IgnorePointer(
              child: BubbleChartLayout(
                duration: const Duration(milliseconds: 500),
                children: [
                  for (final viewModel in types)
                    _buildBubble(viewModel, context),
                ],
              ),
            ),
    );
  }

  Widget _buildNoTravellerType(BuildContext context) {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(32.0),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              AppLocalizations.of(context)!.noTravellerType,
              textAlign: TextAlign.center,
              style: Theme.of(context).textTheme.titleMedium,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildUpdateButton(BuildContext context) {
    return Center(
      child: PlatformTextButton(
        title: AppLocalizations.of(context)!.update,
        onTapped: () {
          Navigator.of(context).pushMaterialRoute(
            name: TodoListScreen.routeName,
            fullscreen: true,
            builder: (_) => const TodoListScreen(),
          );
        },
      ),
    );
  }

  BubbleNode _buildBubble(TravelTypeViewModel viewModel, BuildContext context) {
    return BubbleNode.leaf(
      value: viewModel.amount,
      options: BubbleOptions(
        color: viewModel.color,
        border: Border.all(
          color: Theme.of(context).scaffoldBackgroundColor,
          width: 4,
        ),
        child: _buildTitle(viewModel),
      ),
    );
  }

  Widget _buildTitle(TravelTypeViewModel viewModel) {
    return Padding(
      padding: const EdgeInsets.all(4.0),
      child: FittedBox(
        fit: BoxFit.scaleDown,
        child: Text(
          viewModel.name,
          style: TextStyle(
            fontSize: 15,
            fontWeight: FontWeight.bold,
            color: viewModel.color.legibleForegroundColor(),
          ),
        ),
      ),
    );
  }
}
