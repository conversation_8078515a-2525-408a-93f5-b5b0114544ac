import 'package:flutter/material.dart';
import 'package:rxdart/rxdart.dart';

import '../../dependency_injection/dependency_injector.dart';
import '../../generic_widgets/navigation_extensions.dart';
import '../../helpers/margin.dart';
import '../../helpers/string_extensions.dart';
import '../../l10n/generated/app_localizations.dart';
import '../../models/continent.dart';
import '../areas/area_list_picker.dart';
import '../areas/selected_areas_screen.dart';
import 'graphs/counter_graph.dart';
import 'graphs/graph_theme.dart';
import 'graphs/percent_graph.dart';

class ContinentsSelectionScreen extends StatelessWidget {
  static String routeName(Continent continent) =>
      '/continentSelections/${continent.name}';

  const ContinentsSelectionScreen({
    super.key,
    required this.continent,
  });

  final Continent continent;

  @override
  Widget build(BuildContext context) {
    return SelectedAreasScreen(
      title: continent.name,
      header: ContinentStatsTile(continent: continent),
      sortedSelectionStream: DependencyInjector.areaSelectionBloc
          .sortedContinentSelections(continent),
      onEditTapped: () => Navigator.of(context).pushMaterialRoute(
        name: ContinentListPicker.routeName(continent),
        builder: (_) => ContinentListPicker(continent: continent),
      ),
    );
  }
}

class ContinentStatsTile extends StatelessWidget with GraphTheme {
  const ContinentStatsTile({
    super.key,
    required this.continent,
  });

  final Continent continent;

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(Margin.standard),
      child: StreamBuilder<((int, int), bool)>(
        stream: percentOfContinentSeenAndSovereignty(continent),
        builder: (context, snapshot) {
          final (fraction, onlySovereignty) = snapshot.data ?? ((0, 0), true);

          return Column(
            children: [
              _buildGraphs(fraction, context),
              _buildLabels(onlySovereignty, context),
            ],
          );
        },
      ),
    );
  }

  Widget _buildLabels(
    bool onlySovereignty,
    BuildContext context,
  ) {
    final localizations = AppLocalizations.of(context)!;
    final textStyle = Theme.of(context).textTheme.bodySmall;

    return Row(
      children: [
        Expanded(
          child: Text(
            (onlySovereignty
                    ? localizations.countries
                    : localizations.territories)
                .capitalized,
            textAlign: TextAlign.center,
            style: textStyle,
          ),
        ),
        const SizedBox(width: Margin.large),
        Expanded(
          child: Text(
            localizations.been,
            textAlign: TextAlign.center,
            style: textStyle,
          ),
        ),
      ],
    );
  }

  Widget _buildGraphs(
    (int, int) fraction,
    BuildContext context,
  ) {
    final (numerator, denominator) = fraction;
    final percent = denominator == 0 ? 0.0 : numerator / denominator;

    return Row(
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        _buildAbsoluteCountGraph(
          numerator,
          denominator,
          context,
        ),
        buildVerticalDivider(),
        _buildPercentageGraph(percent, context),
      ],
    );
  }

  Stream<((int, int), bool)> percentOfContinentSeenAndSovereignty(
    Continent continent,
  ) {
    return Rx.combineLatest2(
      DependencyInjector.areaSelectionBloc.percentOfContinentSeen(continent),
      DependencyInjector.settingsBloc.countSovereign,
      (percent, countSovereign) => (percent, countSovereign),
    );
  }

  Widget _buildPercentageGraph(double percent, BuildContext context) {
    return Expanded(
      child: Center(
        child: Column(
          children: [
            SizedBox(
              height: 80,
              child: PercentGraph(
                percentage: percent,
                lineWidth: 3,
                child: Center(
                  child: FittedBox(
                    fit: BoxFit.scaleDown,
                    child: Text(
                      percent == 0 ? '0%' : '${(percent * 100).round()}%',
                      style: graphStyle(context, fontSize: 16),
                    ),
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildAbsoluteCountGraph(
    int numerator,
    int denominator,
    BuildContext context,
  ) {
    return Expanded(
      child: Center(
        child: CounterGraph(
          count: numerator,
          total: denominator,
        ),
      ),
    );
  }
}

class ContinentListPicker extends StatelessWidget {
  static String routeName(Continent continent) =>
      '/continentListPicker/${continent.name}';

  final Continent continent;

  const ContinentListPicker({super.key, required this.continent});

  @override
  Widget build(BuildContext context) {
    return GenericAreaListPicker(
      title: DependencyInjector.areaBloc.fetchContinents().map((continents) {
        return continents.firstWhere((c) => c.id == continent.id).name;
      }),
      areaStream: DependencyInjector.areaBloc.listenToCountriesInContinent(
        continent,
      ),
      showAppBar: true,
    );
  }
}
