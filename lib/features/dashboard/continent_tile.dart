import 'package:flutter/material.dart';

import '../../dependency_injection/dependency_injector.dart';
import '../../generic_widgets/navigation_extensions.dart';
import '../../helpers/margin.dart';
import '../../models/continent.dart';
import 'continents_screen.dart';
import 'graphs/graph_theme.dart';
import 'graphs/percent_graph.dart';

class ContinentTile extends StatelessWidget with GraphTheme {
  const ContinentTile({
    super.key,
    required this.continent,
    required this.showDivider,
  });

  final Continent continent;
  final bool showDivider;

  @override
  Widget build(BuildContext context) {
    if (showDivider) {
      return Column(children: [_buildTile(context), const Divider()]);
    }
    return _buildTile(context);
  }

  ListTile _buildTile(BuildContext context) {
    return ListTile(
      title: Text(continent.name),
      trailing: Row(
        spacing: Margin.standard,
        mainAxisSize: MainAxisSize.min,
        children: [
          SizedBox(
            height: 40,
            child: StreamBuilder<(int, int)>(
              stream: DependencyInjector.areaSelectionBloc
                  .percentOfContinentSeen(continent),
              builder: (context, asyncSnapshot) {
                final fraction = asyncSnapshot.data;
                if (fraction == null || fraction.$2 == 0) {
                  return const SizedBox();
                }
                final percent = fraction.$1 / fraction.$2;

                return PercentGraph(
                  percentage: percent,
                  lineWidth: 1,
                  child: Center(
                    child: FittedBox(
                      fit: BoxFit.scaleDown,
                      child: Text(
                        percent == 0 ? '0%' : '${(percent * 100).round()}%',
                        style: graphStyle(context, fontSize: 10),
                      ),
                    ),
                  ),
                );
              },
            ),
          ),
          Icon(Icons.adaptive.arrow_forward),
        ],
      ),
      onTap: () => Navigator.of(context).pushMaterialRoute(
        name: ContinentsSelectionScreen.routeName(continent),
        builder: (_) => ContinentsSelectionScreen(continent: continent),
      ),
    );
  }
}
