import 'package:flutter/material.dart';

import '../../dependency_injection/dependency_injector.dart';
import '../../generic_widgets/area_flag.dart';
import '../../generic_widgets/responsive_padding.dart';
import '../../l10n/generated/app_localizations.dart';
import '../../models/geo_area.dart';
import 'dashboard_header.dart';
import 'graphs/graph_theme.dart';

class MostFrequentlyVisitedCountrySliver extends StatelessWidget
    with GraphTheme {
  const MostFrequentlyVisitedCountrySliver({
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return StreamBuilder<GeoArea?>(
      stream: DependencyInjector.statsBloc.mostFrequentlyVisitedCountry(),
      builder: (context, snapshot) {
        final country = snapshot.data;
        if (country == null) {
          return const SliverToBoxAdapter(child: SizedBox());
        }

        final primaryColour = Theme.of(context).colorScheme.primary;

        return SliverList.list(
          children: [
            ResponsivePadding(
              context: context,
              child: DashboardHeader(
                title: AppLocalizations.of(context)!
                    .yourMostFrequentlyVisitedCountry,
              ),
            ),
            Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.center,
                mainAxisAlignment: MainAxisAlignment.center,
                mainAxisSize: MainAxisSize.min,
                children: [
                  _buildFlag(primaryColour, country),
                  _buildCountryName(context, country),
                ],
              ),
            ),
          ],
        );
      },
    );
  }

  Widget _buildCountryName(
    BuildContext context,
    GeoArea country,
  ) {
    return Text(
      country.name,
      style: const TextStyle(
        fontSize: 20,
        fontWeight: FontWeight.bold,
      ),
    );
  }

  Widget _buildFlag(Color colour, GeoArea country) {
    return AreaFlag(
      size: 200,
      useCachedSize: false,
      area: country,
    );
  }
}
