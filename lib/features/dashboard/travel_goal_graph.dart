import 'package:flutter/material.dart';

import '../../dependency_injection/dependency_injector.dart';
import '../../generic_widgets/helper_tooltip.dart';
import '../../l10n/generated/app_localizations.dart';
import 'graphs/percent_graph.dart';

class TravelGoalGraph extends StatefulWidget {
  final double? forcedPercent;
  final int? forcedRemainder;
  final bool animate;

  const TravelGoalGraph({
    super.key,
    this.forcedPercent,
    this.forcedRemainder,
    this.animate = true,
  });

  @override
  State createState() => _TravelGoalGraphState();
}

class _TravelGoalGraphState extends State<TravelGoalGraph>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 650),
    );
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final bloc = DependencyInjector.areaSelectionBloc;

    return SizedBox(
      height: 320,
      child: StreamBuilder<double>(
        stream: bloc.percentOfTravelGoalCompleted(),
        initialData: 0,
        builder: (context, snapshot) {
          _controller.forward();
          return AnimatedBuilder(
            animation: _controller,
            builder: (context, child) {
              return HelperTooltip(
                message: AppLocalizations.of(context)!.progressTooltipGoal,
                child: Stack(children: [
                  Positioned(
                    top: 5,
                    right: 5,
                    child: Icon(
                      Icons.help_outline,
                      color: Theme.of(context).colorScheme.primary,
                    ),
                  ),
                  Center(
                    child: Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: PercentGraph(
                        percentage: widget.forcedPercent ?? snapshot.data ?? 0,
                        animationPercentage: _controller.value,
                        child: child,
                      ),
                    ),
                  ),
                ]),
              );
            },
            child: _buildDescription(context),
          );
        },
      ),
    );
  }

  Widget _buildDescription(BuildContext context) {
    final theme = Theme.of(context);
    final textTheme = theme.textTheme;

    final colour = theme.colorScheme.primary;

    final localizations = AppLocalizations.of(context)!;
    return MediaQuery.withNoTextScaling(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 32),
            child: Text(
              localizations.myTravelGoal,
              textAlign: TextAlign.center,
              style: textTheme.headlineSmall?.copyWith(
                color: colour,
              ),
            ),
          ),
          _buildAnimatedPercentage(textTheme, theme),
          StreamBuilder<int>(
            stream: DependencyInjector
                .areaSelectionBloc.numberOfCountriesWantToVisit,
            initialData: 0,
            builder: (context, snapshot) {
              return Text(
                localizations.goalRemaining(
                    widget.forcedRemainder ?? snapshot.data ?? 0),
                style: textTheme.titleMedium?.copyWith(color: colour),
              );
            },
          )
        ],
      ),
    );
  }

  Widget _buildAnimatedPercentage(TextTheme textTheme, ThemeData theme) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 16.0),
      child: StreamBuilder<double>(
        stream:
            DependencyInjector.areaSelectionBloc.percentOfTravelGoalCompleted(),
        initialData: 0,
        builder: (context, snapshot) {
          final percent = ((widget.forcedPercent ?? snapshot.data ?? 0) * 100);
          return AnimatedBuilder(
            animation: _controller,
            builder: (context, child) {
              final displayPercent = percent * _controller.value;
              final stringified = displayPercent.toStringAsFixed(1);
              return Text(
                '$stringified%',
                style: textTheme.displayMedium?.copyWith(
                  color: theme.colorScheme.primary,
                  fontWeight: FontWeight.w600,
                ),
              );
            },
          );
        },
      ),
    );
  }
}
