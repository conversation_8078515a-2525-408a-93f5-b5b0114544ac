import 'package:flutter/material.dart';

import '../../dependency_injection/dependency_injector.dart';
import '../../generic_widgets/responsive_padding.dart';
import '../../l10n/generated/app_localizations.dart';
import 'dashboard_header.dart';
import 'stats_service.dart';

class HotelPreferencesGraph extends StatelessWidget {
  const HotelPreferencesGraph({super.key});

  @override
  Widget build(BuildContext context) {
    return StreamBuilder<HotelPreference?>(
      stream: DependencyInjector.statsBloc.hotelPreference,
      builder: (context, snapshot) {
        final preference = snapshot.data;

        return ResponsivePadding(
          context: context,
          child: Column(
            children: [
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  DashboardHeader(
                    title: AppLocalizations.of(context)!.yourHotelPreferences,
                  ),
                  const SizedBox(height: 8),
                  Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 8.0),
                    child: _buildGraph(preference),
                  ),
                  _buildLabels(context),
                  const SizedBox(height: 16),
                ],
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildGraph(HotelPreference? preference) {
    return SizedBox(
      width: double.infinity,
      height: 40,
      child: LayoutBuilder(builder: (context, constraints) {
        return Stack(
          children: [
            ..._buildGraphLines(context, constraints),
            _buildIndicator(constraints, preference, context),
          ],
        );
      }),
    );
  }

  Iterable<Widget> _buildGraphLines(
      BuildContext context, BoxConstraints constraints) {
    const thickness = 4.0;
    final theme = Theme.of(context);
    final colour = theme.brightness == Brightness.light
        ? theme.primaryColor
        : theme.dividerColor;

    return [
      // Centre Line
      Positioned(
        left: 0,
        right: 0,
        height: thickness,
        top: constraints.minHeight / 2 - (thickness / 2),
        child: Container(color: colour),
      ),
      // Budget Indicator
      Positioned(
        left: 0,
        top: 0,
        width: thickness,
        bottom: 0,
        child: Container(color: colour),
      ),
      // Luxury Indicator
      Positioned(
        right: 0,
        top: 0,
        width: thickness,
        bottom: 0,
        child: Container(color: colour),
      ),
      // Mid Scale Indicator
      Positioned(
        left: (constraints.maxWidth / 2) - (thickness / 2),
        top: 0,
        width: thickness,
        bottom: 0,
        child: Container(color: colour),
      ),
    ];
  }

  Widget _buildIndicator(BoxConstraints constraints,
      HotelPreference? preference, BuildContext context) {
    const diameter = 30.0;

    final indicatorPosition = preference?.normalized(preference.rank) ?? 0;

    return Positioned(
      top: (constraints.maxHeight / 2) - (diameter / 2),
      left: constraints.maxWidth * indicatorPosition - (diameter / 2),
      child: Container(
        decoration: BoxDecoration(
          shape: BoxShape.circle,
          color: Theme.of(context).colorScheme.primary,
        ),
        width: diameter,
        height: diameter,
      ),
    );
  }

  Widget _buildLabels(BuildContext context) {
    final localizations = AppLocalizations.of(context)!;
    return Padding(
      padding: const EdgeInsets.only(top: 4),
      child: DefaultTextStyle(
        style: const TextStyle(
          fontSize: 14,
          fontWeight: FontWeight.w600,
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceEvenly,
          children: [
            Expanded(child: Text(localizations.budget)),
            Expanded(
                child: Text(
              localizations.midScale,
              textAlign: TextAlign.center,
            )),
            Expanded(
                child: Text(
              localizations.luxury,
              textAlign: TextAlign.end,
            )),
          ],
        ),
      ),
    );
  }
}
