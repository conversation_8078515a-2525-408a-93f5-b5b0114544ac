import '../../generic_widgets/selectable_item.dart';
import '../../models/coordinate.dart';
import '../../models/geo_area.dart';
import '../cities/city.dart';
import '../experiences/models/experience.dart';
import '../inspiration/inspiration.dart';
import '../todo_lists/models/todo_list_item.dart';

class Itinerary {
  const Itinerary({
    required this.area,
    this.places,
    this.food,
    this.inspirations,
    this.experiences,
    this.cities,
  });

  final GeoArea area;
  final List<TodoListItem>? places;
  final List<TodoListItem>? food;
  final List<City>? cities;
  final List<Inspiration>? inspirations;
  final List<Experience>? experiences;

  Itinerary.fromJson(this.area, Map json)
      : places = _buildContent(
          data: json['places'],
          constructor: TodoListItem.fromJson,
        ),
        food = _buildContent(
          data: json['food'],
          constructor: TodoListItem.fromJson,
        ),
        cities = _buildContent(
          data: json['cities'],
          constructor: City.fromJson,
        ),
        experiences = _buildContent(
          data: json['experiences'],
          constructor: Experience.fromJson,
        ),
        inspirations = _buildContent(
          data: json['inspirations'],
          constructor: Inspiration.fromJson,
        );

  static List<T>? _buildContent<T>({
    required List? data,
    required T Function(Map json) constructor,
  }) {
    return data?.cast<Map>().map<T>(constructor).toList();
  }

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is Itinerary &&
          runtimeType == other.runtimeType &&
          places == other.places &&
          inspirations == other.inspirations &&
          experiences == other.experiences;

  @override
  int get hashCode =>
      places.hashCode ^ inspirations.hashCode ^ experiences.hashCode;

  List<MapDisplayable> get displayableItems => [
        if (cities?.isNotEmpty ?? false) ...cities!,
        if (places?.isNotEmpty ?? false)
          ...places!.where((place) => !place.coordinate.isCenter),
        if (food?.isNotEmpty ?? false)
          ...food!.where((place) => !place.coordinate.isCenter),
      ];

  List<Coordinate> get coordinates =>
      [for (final displayable in displayableItems) displayable.coordinate];

  Itinerary copyWith({
    List<City>? cities,
    List<TodoListItem>? places,
    List<TodoListItem>? food,
    List<Experience>? experiences,
    List<Inspiration>? inspirations,
  }) {
    return Itinerary(
      area: area,
      cities: cities ?? this.cities,
      places: places ?? this.places,
      food: food ?? this.food,
      experiences: experiences ?? this.experiences,
      inspirations: inspirations ?? this.inspirations,
    );
  }
}
