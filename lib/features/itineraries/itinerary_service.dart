import '../../dependency_injection/dependency_injector.dart';
import '../../models/geo_area.dart';
import '../../networking/client.dart';
import '../areas/area_repository.dart';
import 'itinerary.dart';
import 'itinerary_notes.dart';
import 'itinerary_summary.dart';

class ItineraryService {
  final Client client;
  final AreaRepository areaRepository;

  ItineraryService(this.client, this.areaRepository);

  Future<List<ItinerarySummary>> fetchSummaries() async {
    try {
      final List results = await client.get(
        'itineraries/summary/v2',
        parameters: {'resolution': DependencyInjector.environment.pixelDensity},
        cacheable: false,
      );

      final summaries = <ItinerarySummary>[];
      for (final result in results) {
        try {
          final amount = result['amount'];
          final areaJson = result['area'];
          final iso = areaJson['isoCode'];

          final thumbnail = areaJson['thumbnail'];
          final area = await areaRepository.fetchByIsoCode(iso);

          if (area == null || thumbnail == null) {
            continue;
          }

          summaries.add(
            ItinerarySummary(
              area: area,
              amount: amount,
              thumbnailUrl: thumbnail,
            ),
          );
        } catch (e) {
          rethrow;
        }
      }

      return summaries;
    } catch (e) {
      return [];
    }
  }

  Future<Itinerary> fetchItinerary(GeoArea area) async {
    final path = 'itineraries/areas/${area.isoCode}/v2';

    final response = await client.get(
      path,
      cacheable: false,
      parameters: {'resolution': DependencyInjector.environment.pixelDensity},
    );

    return Itinerary.fromJson(area, response);
  }

  Future<ItineraryNotes?> fetchNotes(GeoArea area) async {
    final path = _buildItineraryNotesPath(area);
    final response = await client.get(path, cacheable: false);

    if (response is! Map) {
      return null;
    }

    return ItineraryNotes.fromJson(response);
  }

  Future<bool> saveItineraryNotes(GeoArea area, ItineraryNotes notes) async {
    final path = _buildItineraryNotesPath(area);

    final _ = await client.post(path, body: notes.toJson());

    return true;
  }

  String _buildItineraryNotesPath(GeoArea area) =>
      'itineraries/areas/${area.isoCode}/notes';
}
