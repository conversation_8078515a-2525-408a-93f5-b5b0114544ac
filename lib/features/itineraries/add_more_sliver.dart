import 'package:flutter/material.dart';

import '../../l10n/generated/app_localizations.dart';

class AddMoreSliver extends StatelessWidget {
  const AddMoreSliver({
    super.key,
    required this.onTapped,
  });

  final VoidCallback onTapped;

  @override
  Widget build(BuildContext context) {
    return SliverList(
      delegate: SliverChildListDelegate.fixed(
        [
          ListTile(
            title: Text(
              AppLocalizations.of(context)!.addMore,
              style: const TextStyle(
                fontWeight: FontWeight.w600,
                fontSize: 15,
              ),
            ),
            trailing: Icon(
              Icons.add_circle_outlined,
              color: Theme.of(context).primaryColor,
            ),
            onTap: onTapped,
          ),
          const SizedBox(height: 16),
        ],
      ),
    );
  }
}
