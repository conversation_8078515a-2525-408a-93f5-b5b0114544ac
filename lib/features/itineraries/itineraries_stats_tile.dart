import 'package:flutter/material.dart';

import '../../dependency_injection/dependency_injector.dart';
import '../../l10n/generated/app_localizations.dart';
import '../../models/selection.dart';
import '../dashboard/graphs/counter_graph.dart';
import '../dashboard/graphs/graph_theme.dart';
import 'itinerary.dart';

class ItinerariesStatsTile extends StatelessWidget with GraphTheme {
  final Itinerary itinerary;

  const ItinerariesStatsTile({super.key, required this.itinerary});

  @override
  Widget build(BuildContext context) {
    final localizations = AppLocalizations.of(context)!;
    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          Expanded(
            child: ListeningCounterGraph(
              count: DependencyInjector.cityBloc
                  .listenToSelectedCities(
                    area: itinerary.area,
                    validSelections: [Selection.want],
                  )
                  .map((cities) => cities.length),
              label: Stream.value(localizations.cities),
            ),
          ),
          buildVerticalDivider(),
          Expanded(
            child: CounterGraph(
              count: itinerary.places?.length ?? 0,
              label: localizations.places,
            ),
          ),
          buildVerticalDivider(),
          Expanded(
            child: CounterGraph(
              count: itinerary.experiences?.length ?? 0,
              label: localizations.experiences,
            ),
          ),
        ],
      ),
    );
  }
}
