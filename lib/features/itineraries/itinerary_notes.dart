import 'package:intl/intl.dart';

import '../../networking/client.dart';

class ItineraryNotes {
  DateTime? startDate;
  DateTime? endDate;

  var hotels = <String>[];
  var notes = <String>[];

  ItineraryNotes.emtpy();

  ItineraryNotes._({
    this.startDate,
    this.endDate,
    required this.hotels,
    required this.notes,
  });

  factory ItineraryNotes.fromJson(Map json) {
    final startDateString = json['startDate'];
    final startDate =
        startDateString is String ? DateTime.tryParse(startDateString) : null;

    final endDateString = json['endDate'];
    final endDate =
        endDateString is String ? DateTime.tryParse(endDateString) : null;

    final maybeHotels = json['hotels'];
    final hotels = maybeHotels is List
        ? [for (final e in maybeHotels) e.toString()]
        : <String>[];

    final maybeNotes = json['notes'];
    final notes = maybeNotes is List
        ? [for (final e in maybeNotes) e.toString()]
        : <String>[];

    return ItineraryNotes._(
      startDate: startDate,
      endDate: endDate,
      hotels: hotels,
      notes: notes,
    );
  }

  JsonMap toJson() {
    final formatter = DateFormat.yMd();
    return {
      if (startDate != null) 'startDate': formatter.format(startDate!),
      if (endDate != null) 'endDate': formatter.format(endDate!),
      if (hotels.isNotEmpty) 'hotels': hotels,
      if (notes.isNotEmpty) 'notes': notes,
    };
  }
}
