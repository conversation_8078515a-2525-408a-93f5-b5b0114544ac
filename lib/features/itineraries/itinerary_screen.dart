import 'package:flutter/material.dart';

import '../../dependency_injection/dependency_injector.dart';
import '../../generic_widgets/area_flag.dart';
import '../../generic_widgets/navigation_extensions.dart';
import '../../generic_widgets/pay_wall.dart';
import '../../generic_widgets/platform_aware/platform_sliver_app_bar.dart';
import '../../generic_widgets/pushable_tile.dart';
import '../../generic_widgets/responsive_padding.dart';
import '../../generic_widgets/sliver_bottom_safe_area.dart';
import '../../generic_widgets/spinner.dart';
import '../../l10n/generated/app_localizations.dart';
import '../in_app_purchase/iap_product.dart';
import '../in_app_purchase/iap_status.dart';
import 'itinerary_details_screen.dart';
import 'itinerary_summary.dart';

class ItineraryScreen extends StatelessWidget {
  static const routeName = 'itineraries';

  const ItineraryScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return StreamBuilder<IAPStatus>(
      stream: DependencyInjector.iapBloc.status,
      builder: (context, snapshot) {
        final status =
            snapshot.data ?? DependencyInjector.iapBloc.currentStatus;

        if (status.hasUnlockedItineraries) {
          return _buildItineraryUnlockedContent(context);
        }

        return _buildItineraryPaywall(context);
      },
    );
  }

  Widget _buildItineraryPaywall(BuildContext context) {
    return Scaffold(
      body: CustomScrollView(
        slivers: [
          PlatformSliverAppBar(
            title: AppLocalizations.of(context)!.itineraries,
          ),
          ResponsiveSliverPadding(
            context: context,
            fillToEdgeOnPhone: false,
            sliver: const SliverToBoxAdapter(
              child: PayWall(
                feature: IAPFeature.unlockItineraries,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildItineraryUnlockedContent(BuildContext context) {
    return Scaffold(
      body: Scrollbar(
        child: CustomScrollView(
          slivers: [
            PlatformSliverAppBar(
              title: AppLocalizations.of(context)!.itineraries,
            ),
            _buildSummaries(context),
            const SliverBottomSafeArea()
          ],
        ),
      ),
    );
  }

  Widget _buildSummaries(BuildContext context) {
    return StreamBuilder<List<ItinerarySummary>>(
      stream: DependencyInjector.itineraryBloc.summaries,
      builder: (context, snapshot) {
        final data = snapshot.data;

        if (data == null) {
          return const SliverFillRemaining(
            child: Center(
              child: Spinner(),
            ),
          );
        }

        if (data.isEmpty) {
          return _buildNoItineraries(context);
        }

        return SliverList(
          delegate: SliverChildBuilderDelegate(
            childCount: data.length,
            (context, index) {
              final summary = data[index];
              return _buildItineraryTile(summary, context);
            },
          ),
        );
      },
    );
  }

  Widget _buildNoItineraries(BuildContext context) {
    final localizations = AppLocalizations.of(context)!;

    return SliverFillRemaining(
      child: ResponsivePadding(
        context: context,
        fillToEdgeOnPhone: false,
        child: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            mainAxisSize: MainAxisSize.min,
            children: [
              const Icon(
                Icons.no_luggage,
                size: 40,
              ),
              Text(
                localizations.noItineraries,
                style: Theme.of(context)
                    .textTheme
                    .headlineSmall
                    ?.copyWith(fontWeight: FontWeight.w600),
              ),
              Text(
                localizations.noItinerariesExplanation,
                textAlign: TextAlign.center,
              )
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildItineraryTile(ItinerarySummary summary, BuildContext context) {
    final area = summary.area;
    return PushableTile(
      title: summary.area.name,
      titleWidget: Row(
        children: [
          AreaFlag(area: area),
          const SizedBox(width: 8),
          Text(area.name),
        ],
      ),
      titleStyle: TextStyle(
          fontSize: 18,
          fontWeight: FontWeight.bold,
          color: Theme.of(context).textTheme.bodyMedium?.color),
      trailing: _buildNumberOfPlacesBadge(context, summary),
      imageLocation: NetworkImageLocation(
        summary.thumbnailUrl,
        heroId: area,
      ),
      onTapped: () => Navigator.of(context).pushMaterialRoute(
        name: ItineraryDetailsScreen.routeName(area),
        builder: (_) => ItineraryDetailsScreen(
          summary: summary,
        ),
      ),
    );
  }

  Widget _buildNumberOfPlacesBadge(
    BuildContext context,
    ItinerarySummary summary,
  ) {
    final localizations = AppLocalizations.of(context)!;
    return Row(
      children: [
        Container(
          decoration: BoxDecoration(
            shape: BoxShape.circle,
            color: Theme.of(context).colorScheme.primary,
          ),
          padding: const EdgeInsets.all(12),
          child: Text(
            (summary.amount).toString(),
            style: const TextStyle(
              color: Colors.white,
              fontWeight: FontWeight.bold,
              fontSize: 18,
            ),
          ),
        ),
        const SizedBox(width: 8),
        Text(
          summary.amount == 1 ? localizations.place : localizations.places,
          style: Theme.of(context).textTheme.labelLarge,
        )
      ],
    );
  }
}
