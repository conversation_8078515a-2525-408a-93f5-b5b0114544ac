import '../../models/geo_area.dart';

class ItinerarySummary implements Comparable<ItinerarySummary> {
  final GeoArea area;
  final int amount;
  final String thumbnailUrl;

  const ItinerarySummary({
    required this.area,
    required this.amount,
    required this.thumbnailUrl,
  });

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is ItinerarySummary &&
          runtimeType == other.runtimeType &&
          area == other.area &&
          thumbnailUrl == other.thumbnailUrl;

  ItinerarySummary copyWith({
    GeoArea? area,
    int? amount,
    String? thumbnailUrl,
  }) {
    return ItinerarySummary(
      area: area ?? this.area,
      amount: amount ?? this.amount,
      thumbnailUrl: thumbnailUrl ?? this.thumbnailUrl,
    );
  }

  @override
  int get hashCode => area.hashCode ^ thumbnailUrl.hashCode;

  @override
  String toString() {
    return 'ItinerarySummary{area: $area, amount: $amount, thumbnailUrl: $thumbnailUrl}';
  }

  @override
  int compareTo(ItinerarySummary other) => area.compareTo(other.area);
}
