import 'package:flutter/material.dart';
import 'package:flutter_sticky_header/flutter_sticky_header.dart';
import 'package:intl/intl.dart';
import 'package:sliver_tools/sliver_tools.dart';

import '../../dependency_injection/dependency_injector.dart';
import '../../generic_widgets/list_view_header.dart';
import '../../generic_widgets/platform_aware/platform_date_picker.dart';
import '../../generic_widgets/platform_aware/platform_text_field.dart';
import '../../generic_widgets/separated_tile.dart';
import '../../l10n/generated/app_localizations.dart';
import 'itinerary.dart';
import 'itinerary_notes.dart';
import 'multiple_notes_sliver.dart';

class ItineraryNotesSliver extends StatefulWidget {
  const ItineraryNotesSliver({super.key, required this.itinerary});

  final Itinerary itinerary;

  @override
  State<ItineraryNotesSliver> createState() => _ItineraryNotesSliverState();
}

class _ItineraryNotesSliverState extends State<ItineraryNotesSliver> {
  ItineraryNotes? _notes;
  late TextEditingController _startDateController;
  late TextEditingController _endDateController;
  final _formatter = DateFormat.yMMMMd();

  @override
  void initState() {
    super.initState();

    _fetchNotes();
  }

  void _fetchNotes() async {
    final notes = await DependencyInjector.itineraryBloc.fetchNotes(
      widget.itinerary.area,
    );

    setState(() {
      _notes = notes;
      _startDateController = TextEditingController(
        text: notes.startDate != null
            ? _formatter.format(notes.startDate!)
            : null,
      );

      _endDateController = TextEditingController(
        text: notes.endDate != null ? _formatter.format(notes.endDate!) : null,
      );
    });
  }

  @override
  Widget build(BuildContext context) {
    final notes = _notes;

    if (notes == null) {
      return const SliverToBoxAdapter(child: SizedBox());
    }

    final localizations = AppLocalizations.of(context)!;
    return PopScope(
      onPopInvokedWithResult: (_, _) {
        DependencyInjector.itineraryBloc.saveNotes(
          widget.itinerary.area,
          notes,
        );
      },
      child: MultiSliver(
        children: [
          _buildDateSliver(context),
          MultipleNotesSliver(
            key: const ValueKey('Hotels'),
            title: localizations.hotels,
            notes: notes.hotels,
            maxLength: 50,
          ),
          MultipleNotesSliver(
            key: const ValueKey('Notes'),
            title: localizations.notes,
            maxLines: 4,
            notes: notes.notes,
            maxLength: 128,
          ),
        ],
      ),
    );
  }

  Widget _buildDateSliver(BuildContext context) {
    final localizations = AppLocalizations.of(context)!;
    return SliverStickyHeader(
      header: ListViewHeader(title: localizations.travelDates),
      sliver: SliverList.list(
        children: [
          _buildDatePickerRow(
            context,
            title: localizations.departureDate,
            controller: _startDateController,
            onDateSubmitted: (date) => _notes?.startDate = date,
          ),
          _buildDatePickerRow(
            context,
            title: localizations.returnDate,
            controller: _endDateController,
            onDateSubmitted: (date) => _notes?.endDate = date,
          ),
        ],
      ),
    );
  }

  Widget _buildDatePickerRow(
    BuildContext context, {
    required String title,
    required TextEditingController controller,
    required ValueChanged<DateTime> onDateSubmitted,
  }) {
    return SeparatedTile(
      child: ListTile(
        title: Text(title),
        trailing: SizedBox(
          width: 200,
          child: GestureDetector(
            behavior: HitTestBehavior.opaque,
            onTap: () async {
              FocusManager.instance.primaryFocus?.unfocus();
              final now = DateTime.now();

              final date = await showPlatformDatePicker(
                context,
                initialDate: now,
                minDate: now.subtract(const Duration(days: 5 * 365)),
                maxDate: now.add(const Duration(days: 50 * 365)),
              );

              FocusManager.instance.primaryFocus?.unfocus();

              if (date == null) {
                return;
              }

              setState(() {
                controller.text = _formatter.format(date);
                onDateSubmitted(date);
              });
            },
            child: IgnorePointer(
              child: PlatformTextField(
                controller: controller,
                textAlign: TextAlign.end,
                textStyle: TextStyle(
                  color: Theme.of(context).brightness == Brightness.light
                      ? Colors.black
                      : Colors.white,
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }

  @override
  void dispose() {
    super.dispose();
  }
}
