import 'dart:async';

import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_sticky_header/flutter_sticky_header.dart';
import 'package:sliver_tools/sliver_tools.dart';

import '../../dependency_injection/dependency_injector.dart';
import '../../generic_widgets/list_view_header.dart';
import '../../generic_widgets/navigation_extensions.dart';
import '../../generic_widgets/platform_aware/platform_icon_button.dart';
import '../../generic_widgets/platform_aware/platform_sliver_app_bar.dart';
import '../../generic_widgets/pushable_tile.dart';
import '../../generic_widgets/separated_tile.dart';
import '../../generic_widgets/sliver_bottom_safe_area.dart';
import '../../generic_widgets/spinner.dart';
import '../../l10n/generated/app_localizations.dart';
import '../../models/geo_area.dart';
import '../../models/selection.dart';
import '../books/book_link.dart';
import '../books/book_link_tile.dart';
import '../cities/city.dart';
import '../cities/city_picker.dart';
import '../experiences/models/experience.dart';
import '../map/sliver_annotated_map.dart';
import '../sharing/content/itinerary_sharable_content.dart';
import '../sharing/sharing_service.dart';
import '../todo_lists/models/todo_list.dart';
import '../todo_lists/models/todo_list_item.dart';
import '../todo_lists/models/todo_list_type.dart';
import '../todo_lists/todo_list_details_screen.dart';
import 'add_more_sliver.dart';
import 'area_experience_picker.dart';
import 'itineraries_stats_tile.dart';
import 'itinerary.dart';
import 'itinerary_map_screen.dart';
import 'itinerary_notes_sliver.dart';
import 'itinerary_summary.dart';

class ItineraryDetailsScreen extends StatefulWidget {
  static String routeName(GeoArea area) => 'itineraries/${area.isoCode}';

  const ItineraryDetailsScreen({
    super.key,
    required this.summary,
  });

  final ItinerarySummary summary;

  @override
  State<ItineraryDetailsScreen> createState() => _ItineraryDetailsScreenState();
}

class _ItineraryDetailsScreenState extends State<ItineraryDetailsScreen> {
  Itinerary? _itinerary;
  StreamSubscription? subscription;

  @override
  void initState() {
    super.initState();
    subscription ??= DependencyInjector.itineraryBloc
        .fetch(widget.summary.area)
        .listen((value) {
      setState(() {
        _itinerary = value;
      });
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Scrollbar(
        child: CustomScrollView(
          slivers: [
            PlatformSliverAppBar(
              title: widget.summary.area.name,
              action: _itinerary != null
                  ? PlatformIconButton(
                      icon: Icons.adaptive.share,
                      onTapped: () => shareItinerary(context),
                    )
                  : null,
            ),
            SliverToBoxAdapter(
              child: ItineraryCarousel(
                summary: widget.summary,
                itinerary: _itinerary,
              ),
            ),
            if (_itinerary == null)
              const SliverFillRemaining(
                child: Center(
                  child: Spinner(),
                ),
              )
            else
              ..._buildContent(_itinerary!),
            const SliverBottomSafeArea(),
          ],
        ),
      ),
    );
  }

  Iterable<Widget> _buildContent(Itinerary itinerary) {
    return [
      _buildHeader(itinerary),
      _buildMap(itinerary),
      _buildBookLink(itinerary),
      ItineraryNotesSliver(key: ValueKey(itinerary), itinerary: itinerary),
      _buildCities(itinerary),
      _buildPlaces(itinerary),
      _buildFood(itinerary),
      // _buildInspiration(itinerary.inspirations),
      _buildExperiences(itinerary.experiences)
    ];
  }

  SliverAnnotatedMap _buildMap(Itinerary itinerary) {
    return SliverAnnotatedMap(
      coordinates: itinerary.coordinates,
      onTapped: () => Navigator.of(context).pushMaterialRoute(
          name: 'itinerary/${itinerary.area.isoCode}/map',
          builder: (BuildContext context) => ItineraryMapScreen(
                itinerary: itinerary,
              )),
    );
  }

  Widget _buildPlaces(Itinerary itinerary) {
    return _buildListItemSection(
      items: itinerary.places,
      title: AppLocalizations.of(context)!.places,
      type: TodoListType.place,
    );
  }

  Widget _buildFood(Itinerary itinerary) {
    return _buildListItemSection(
      items: itinerary.food,
      title: AppLocalizations.of(context)!.food,
      type: TodoListType.food,
    );
  }

  Widget _buildListItemSection({
    required List<TodoListItem>? items,
    required String title,
    required TodoListType type,
  }) {
    return FutureBuilder<List<(TodoListItem, TodoList?)>?>(
      future: DependencyInjector.itineraryBloc.pairListItemsWithParentList(
        items,
        DependencyInjector.todoListBloc,
      ),
      builder: (context, snapshot) {
        return _buildSection(
          title: title,
          items: snapshot.data,
          itemBuilder: (record) {
            final (listItem, list) = record;
            final url = listItem.thumbnailUrl;
            final listName = list?.name;

            return PushableTile(
              imageLocation: url != null
                  ? NetworkImageLocation(url)
                  : const NoImageLocation(),
              imageHeight: double.infinity,
              imageFit: BoxFit.fitHeight,
              title: listName == null ? listItem.name : null,
              titleWidget: listName != null
                  ? _buildTitleWithSubtitle(listItem.name, listName)
                  : null,
            );
          },
          onAddMoreTapped: () => _onAddListItemsTapped(type),
        );
      },
    );
  }

  Widget _buildBookLink(Itinerary itinerary) {
    return SliverToBoxAdapter(
      child: FutureBuilder<BookLink?>(
        future: DependencyInjector.itineraryBloc.fetchLink(itinerary),
        builder: (context, snapshot) {
          final book = snapshot.data;
          if (book == null) {
            return const SizedBox();
          }

          return BookLinkTile(book: book);
        },
      ),
    );
  }

  Widget _buildTitleWithSubtitle(String title, String subtitle) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(title),
        Text(
          subtitle,
          style: const TextStyle(
            fontSize: 12,
            fontWeight: FontWeight.w400,
          ),
        )
      ],
    );
  }

  Widget _buildExperiences(List<Experience>? experiences) {
    return _buildSection(
      title: AppLocalizations.of(context)!.experiences,
      items: experiences,
      itemBuilder: (experience) => PushableTile(
        title: experience.name,
        imageFit: BoxFit.fitHeight,
        imageHeight: double.infinity,
        imageLocation: AssetImageLocation(experience.icon),
      ),
      onAddMoreTapped: () => Navigator.of(context).pushMaterialRoute(
        name: 'itineraries/${widget.summary.area.isoCode}/experiences',
        builder: (_) => AreaExperiencePicker(area: widget.summary.area),
      ),
    );
  }

  Widget _buildHeader(Itinerary itinerary) {
    return SliverList.list(
      children: [
        Padding(
          padding: const EdgeInsets.all(16.0),
          child: Center(
            child: Text(
              AppLocalizations.of(context)!.itinerariesDescription,
              textAlign: TextAlign.center,
              style: Theme.of(context).textTheme.labelLarge,
            ),
          ),
        ),
        ItinerariesStatsTile(itinerary: itinerary),
      ],
    );
  }

  Widget _buildSection<T>({
    required String title,
    required List<T>? items,
    required Widget Function(T item) itemBuilder,
    required VoidCallback onAddMoreTapped,
  }) {
    return SliverStickyHeader(
      header: ListViewHeader(
        title: title,
      ),
      sliver: MultiSliver(
        children: [
          if (items != null && items.isNotEmpty)
            SliverGrid(
              delegate: _buildSliverChildDelegate<T>(items, itemBuilder),
              gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                crossAxisCount: 2,
                mainAxisSpacing: 16,
                crossAxisSpacing: 16,
              ),
            ),
          AddMoreSliver(onTapped: onAddMoreTapped),
        ],
      ),
    );
  }

  SliverChildBuilderDelegate _buildSliverChildDelegate<T>(
      List<T> items, Widget Function(T item) itemBuilder) {
    return SliverChildBuilderDelegate(
      childCount: items.length,
      (context, index) {
        final item = items[index];
        return itemBuilder(item);
      },
    );
  }

  void _onAddListItemsTapped(TodoListType type) async {
    final list = await DependencyInjector.todoListBloc.fetchAreaList(
      widget.summary.area,
    );
    if (list == null || !mounted) {
      return;
    }
    if (context.mounted) {
      Navigator.of(context).pushMaterialRoute(
        name: 'itineraries/lists/${widget.summary.area.isoCode}',
        builder: (_) => TodoListDetailsScreen(
          list: list,
          type: type,
          selections: const [Selection.want],
        ),
      );
    }
  }

  void shareItinerary(BuildContext context) {
    final itinerary = _itinerary;
    if (itinerary == null) {
      return;
    }

    final sharer = SharingService(
      context,
      content: ItinerarySharableContent(itinerary),
    );
    sharer.export();

    // DependencyInjector.itineraryBloc.share(
    //   itinerary: itinerary,
    //   localizations: AppLocalizations.of(context)!,
    //   listBloc: DependencyInjector.todoListBloc,
    // );
  }

  @override
  void dispose() {
    subscription?.cancel();
    super.dispose();
  }

  Widget _buildCities(Itinerary itinerary) {
    final bloc = DependencyInjector.cityBloc;
    return MultiSliver(
      children: [
        StreamBuilder<List<City>>(
          stream: DependencyInjector.cityBloc.listenToSelectedCities(
              area: itinerary.area, validSelections: [Selection.want]),
          builder: (context, snapshot) {
            final cities = snapshot.data ?? [];
            cities.sort();

            return SliverStickyHeader(
              header: ListViewHeader(
                title: AppLocalizations.of(context)!.cities,
              ),
              sliver: SliverList.builder(
                itemCount: cities.length,
                itemBuilder: (context, index) => SeparatedTile(
                  child: ListTile(
                    title: FutureBuilder<String>(
                        future: bloc.fullCityName(cities[index]),
                        builder: (context, snapshot) {
                          return Text(snapshot.data ?? '');
                        }),
                  ),
                ),
              ),
            );
          },
        ),
        AddMoreSliver(
          onTapped: () => Navigator.of(context).pushMaterialRoute(
              builder: (_) => CityPicker(
                    area: itinerary.area,
                    selectionTypes: const [Selection.want],
                  ),
              name: 'itinerary/${itinerary.area}/cities'),
        )
      ],
    );
  }
}

class ItineraryCarousel extends StatelessWidget {
  const ItineraryCarousel({
    super.key,
    required Itinerary? itinerary,
    required this.summary,
  }) : _itinerary = itinerary;

  final ItinerarySummary summary;
  final Itinerary? _itinerary;

  @override
  Widget build(BuildContext context) {
    final images = [
      Hero(
        tag: summary.area,
        child: CachedNetworkImage(
          imageUrl: summary.thumbnailUrl,
        ),
      ),
      if (_itinerary?.inspirations?.isNotEmpty ?? false)
        ..._itinerary!.inspirations!.map(
          (inspiration) => CachedNetworkImage(
            imageUrl: inspiration.imageUrl,
          ),
        )
    ];

    return DefaultTabController(
      length: images.length,
      child: Column(
        children: [
          SizedBox(height: 300, child: TabBarView(children: images)),
          Align(
            alignment: Alignment.bottomCenter,
            child: TabPageSelector(
              selectedColor: Theme.of(context).colorScheme.primary,
              color: Theme.of(context).colorScheme.primary.withAlpha(64),
            ),
          ),
        ],
      ),
    );
  }
}
