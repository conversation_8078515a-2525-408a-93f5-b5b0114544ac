import 'package:flutter/material.dart';
import 'package:flutter_map/flutter_map.dart';

import '../../dependency_injection/dependency_injector.dart';
import '../../generic_widgets/area_flag.dart';
import '../../generic_widgets/platform_aware/platform_app_bar.dart';
import '../../models/selection.dart';
import '../cities/city.dart';
import '../cities/map_annotation.dart';
import '../map/map_helper.dart';
import '../map/tiles/internal/coordinate_extension.dart';
import '../map/tiles/visited_tile_layer.dart';
import '../selection/selection_dialog.dart';
import '../todo_lists/components/todo_list_item_tile.dart';
import '../todo_lists/models/todo_list_item.dart';
import 'itinerary.dart';

class ItineraryMapScreen extends StatelessWidget with MapHelper {
  const ItineraryMapScreen({super.key, required this.itinerary});

  final Itinerary itinerary;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: PlatformAppBar(title: itinerary.area.name),
      body: FlutterMap(
        options: buildMapOptions(getBounds(itinerary.coordinates)),
        children: [
          const VisitedTileLayer(active: false),
          markerLayerBuilder(
            context: context,
            items: itinerary.displayableItems,
            markerBuilder: (context, item) {
              return Marker(
                point: item.coordinate.toLatLng(),
                child: MapAnnotation(
                  selection: Selection.clear,
                  onTapped: () async {
                    String? title;
                    Widget? centerContent;

                    if (item is City) {
                      title = await DependencyInjector.cityBloc.fullCityName(
                        item,
                      );
                      centerContent = _buildCityThumbnail(item);
                    } else if (item is TodoListItem) {
                      title = item.userFacingName();

                      if (item.thumbnailUrl != null) {
                        centerContent = _buildListItemThumbnail(item);
                      }
                    }

                    if (context.mounted == false) {
                      return;
                    }

                    showAdaptiveDialog(
                      context: context,
                      builder: (context) => SelectionDialog(
                        title: title ?? '',
                        centerContent: centerContent,
                        availableSelections: const [],
                        selection: Stream.value(Selection.clear),
                      ),
                    );
                  },
                ),
              );
            },
          ),
        ],
      ),
    );
  }

  Widget _buildCityThumbnail(City item) {
    return Padding(
      padding: const EdgeInsets.all(32.0),
      child: AreaFlag(
        size: 175,
        useCachedSize: false,
        area: DependencyInjector.areaBloc.areaByIsoCodeSync(
          item.geoAreaIsoCode,
        )!,
      ),
    );
  }

  Widget _buildListItemThumbnail(TodoListItem item) {
    return Padding(
      padding: const EdgeInsets.only(top: 16, bottom: 32, left: 32, right: 32),
      child: TodoListItemThumbnail(
        item: item,
        selection: Selection.clear,
        showActiveSelection: false,
      ),
    );
  }
}
