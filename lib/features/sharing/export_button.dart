import 'package:flutter/material.dart';

import '../../dependency_injection/dependency_injector.dart';
import '../../l10n/generated/app_localizations.dart';
import '../home/<USER>';
import '../home/<USER>';
import '../map/map_button.dart';
import '../map/map_button_properties.dart';
import '../map/visited_popup_menu.dart';

class ExportButton extends StatelessWidget with MapButtonProperties {
  const ExportButton({
    super.key,
    required this.onShareTapped,
  });

  final VoidCallback onShareTapped;

  @override
  Widget build(BuildContext context) {
    return FutureBuilder(
      future: DependencyInjector.featureFlags.posterEnabled(),
      initialData: true,
      builder: (context, snapshot) {
        if (snapshot.data == true) {
          return _buildExportMenu(context);
        }

        return MapButton(
          icon: Icons.adaptive.share,
          onTapped: (_) => onShareTapped(),
        );
      },
    );
  }

  Widget _buildExportMenu(BuildContext context) {
    return VisitedPopupMenu<_ExportType>(
      icon: Icons.adaptive.share,
      items: _ExportType.values,
      leftAligned: false,
      onSelected: _onSelected,
    );
  }

  void _onSelected(BuildContext context, _ExportType export) {
    switch (export) {
      case _ExportType.poster:
        const NavigateToTabNotification(tab: HomeTab.poster).dispatch(context);
        break;
      case _ExportType.share:
        onShareTapped();
        break;
    }
  }
}

enum _ExportType implements PopupMenuItemData {
  poster(Icons.print),
  share(Icons.map_outlined);

  const _ExportType(this.icon);

  @override
  String title(AppLocalizations localizations) {
    switch (this) {
      case _ExportType.poster:
        return localizations.orderPoster;
      case _ExportType.share:
        return localizations.shareMap;
    }
  }

  @override
  final IconData icon;
}
