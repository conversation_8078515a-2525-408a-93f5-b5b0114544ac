import 'dart:io';
import 'dart:ui';

import 'package:native_exif/native_exif.dart';
import '../../helpers/data_extensions.dart';

class SharableAsset {
  final double width;
  final Future<void> Function(Canvas canvas) drawContent;

  SharableAsset(this.width, this.drawContent);

  Future<File?> export() async {
    final size = Size.square(width);
    final recorder = PictureRecorder();
    final canvas = Canvas(
      recorder,
      Rect.fromLTWH(
        0,
        0,
        size.width,
        size.height,
      ),
    );

    await drawContent(canvas);
    return _writeImage(recorder, size);
  }

  Future<File?> _writeImage(PictureRecorder recorder, Size size) async {
    final file = await _rasterizePicture(recorder, size);

    if (file == null) {
      return null;
    }

    await _addMetadata(file);

    return file;
  }

  Future<File?> _rasterizePicture(PictureRecorder recorder, Size size) async {
    final picture = recorder.endRecording();
    final image = await picture.toImage(
      size.width.round(),
      size.height.round(),
    );

    final file = await image.saveTemporaryPng(
        filename: 'export-${DateTime.now().millisecondsSinceEpoch}.png');
    return file;
  }

  Future<void> _addMetadata(File file) async {
    final exif = await Exif.fromPath(file.path);
    await exif.writeAttributes(
        {'created-by': 'Visited', 'contact': 'https://www.visitedapp.com'});

    exif.close();
  }
}
