import 'package:email_validator/email_validator.dart';
import 'package:flutter/material.dart';

import '../../dependency_injection/dependency_injector.dart';
import '../../generic_widgets/alert.dart';
import '../../generic_widgets/platform_aware/platform_filled_button.dart';
import '../../generic_widgets/platform_aware/platform_sliver_app_bar.dart';
import '../../generic_widgets/responsive_padding.dart';
import '../../generic_widgets/sliver_bottom_safe_area.dart';
import '../../generic_widgets/spinner.dart';
import '../../helpers/margin.dart';
import '../../l10n/generated/app_localizations.dart';
import '../../models/geo_area.dart';
import '../poster_printing/components/visited_input_field.dart';
import '../poster_printing/enter_shipping_address_screen.dart';

class BrandAmbassadorScreen extends StatefulWidget {
  const BrandAmbassadorScreen({super.key});

  @override
  State<BrandAmbassadorScreen> createState() => _BrandAmbassadorScreenState();
}

class _BrandAmbassadorScreenState extends State<BrandAmbassadorScreen> {
  var loaded = false;
  final _nameController = TextEditingController();
  final _emailController = TextEditingController();
  GeoArea? _country;
  final _countryController = TextEditingController();

  late final List<SocialMediaInfluencer> influencers;

  final _otherController = TextEditingController();
  final _anythingElseController = TextEditingController();

  final instagramHandleController = TextEditingController();
  final instagramFollowerController = TextEditingController();
  final tiktokHandleController = TextEditingController();
  final tiktokFollowerController = TextEditingController();
  final youtubeHandleController = TextEditingController();
  final youtubeFollowerController = TextEditingController();
  final websiteHandleController = TextEditingController();
  final websiteFollowerController = TextEditingController();

  @override
  void initState() {
    super.initState();

    WidgetsBinding.instance.addPostFrameCallback((_) {
      final localizations = AppLocalizations.of(context)!;
      influencers = [
        SocialMediaInfluencer(
          network: localizations.instagram,
          handleController: instagramHandleController,
          followerController: instagramFollowerController,
        ),
        SocialMediaInfluencer(
          network: localizations.tiktok,
          handleController: tiktokHandleController,
          followerController: tiktokFollowerController,
        ),
        SocialMediaInfluencer(
          network: localizations.youtube,
          handleController: youtubeHandleController,
          followerController: youtubeFollowerController,
        ),
        SocialMediaInfluencer(
          network: localizations.website,
          handleController: websiteHandleController,
          followerController: websiteFollowerController,
        ),
      ];

      final user = DependencyInjector.sessionBloc.user;
      if (user != null) {
        setState(() {
          _emailController.text = user.email;
        });
      }

      DependencyInjector.areaSelectionBloc.currentCountryUserLivesIn().then((
        live,
      ) {
        setState(() {
          if (live != null) {
            _country = live;
            _countryController.text = live.name;
          }

          loaded = true;
        });
      });
    });
  }

  @override
  Widget build(BuildContext context) {
    final localizations = AppLocalizations.of(context)!;

    final subheadingTextStyle = Theme.of(context).textTheme.titleMedium
        ?.copyWith(
          fontWeight: FontWeight.w800,
        );
    return Scaffold(
      body: Scrollbar(
        child: CustomScrollView(
          keyboardDismissBehavior: ScrollViewKeyboardDismissBehavior.onDrag,
          slivers: [
            PlatformSliverAppBar(
              title: localizations.becomeABrandAmbassador,
            ),
            SliverToBoxAdapter(
              child: Image.asset(
                'assets/images/brand_ambassador.jpg',
                fit: BoxFit.fitWidth,
              ),
            ),
            if (loaded)
              ResponsiveSliverPadding(
                context: context,
                fillToEdgeOnPhone: false,
                sliver: SliverList.list(
                  children: [
                    Text(
                      localizations.joinBrandAmbassadorProgram,
                      textAlign: TextAlign.center,
                      style: Theme.of(context).textTheme.titleLarge,
                    ),
                    const SizedBox(height: Margin.small),
                    Text(
                      localizations.brandAmbassadorProgramDescription,
                    ),
                    const SizedBox(height: Margin.standard),
                    Text(
                      localizations.fillOutTheFormToGetStarted,
                      style: subheadingTextStyle,
                    ),
                    VisitedInputField(
                      controller: _nameController,
                      label: localizations.yourName,
                      autocompleteHints: [AutofillHints.name],
                    ),
                    VisitedInputField(
                      controller: _emailController,
                      label: localizations.email,
                      autocompleteHints: [AutofillHints.email],
                    ),
                    CountryInputField(
                      countryController: _countryController,
                      onCountrySelected: (country) {
                        setState(() {
                          _country = country;
                        });
                      },
                      validator: (String? value) {
                        return null;
                      },
                    ),
                    Padding(
                      padding: const EdgeInsets.symmetric(
                        vertical: Margin.standard,
                      ),
                      child: Text(
                        localizations.fillInWhereApplicable,
                        style: subheadingTextStyle,
                      ),
                    ),
                    const SizedBox(height: Margin.standard),
                    for (final influencer in influencers)
                      _buildInfluencerRow(influencer, localizations),
                    VisitedInputField(
                      controller: _otherController,
                      label: localizations.otherNetworks,
                    ),
                    const SizedBox(height: Margin.standard),
                    Text(
                      localizations.anythingElse,
                      style: subheadingTextStyle,
                    ),
                    TextFormField(
                      controller: _anythingElseController,
                      minLines: 6,
                      maxLines: 10,
                      decoration: VisitedInputField.buildDecoration(
                        context,
                        '',
                      ),
                    ),
                    Padding(
                      padding: const EdgeInsets.symmetric(
                        vertical: Margin.standard,
                      ),
                      child: PlatformFilledButton(
                        title: localizations.submit,
                        onTapped: () {
                          FocusManager.instance.primaryFocus?.unfocus();

                          final localizations = AppLocalizations.of(context)!;

                          if (_nameController.text.isEmpty) {
                            _showError(localizations.yourNameEmptyError);
                            return;
                          }

                          if (_emailController.text.isEmpty) {
                            _showError(localizations.emailEmptyError);
                            return;
                          }

                          if (EmailValidator.validate(
                                _emailController.text.trim(),
                              ) ==
                              false) {
                            _showError(localizations.enterValidEmail);
                            return;
                          }

                          if (_country == null) {
                            _showError(localizations.countryEmptyError);
                            return;
                          }

                          _submitForm(context);
                        },
                      ),
                    ),
                  ],
                ),
              ),
            const SliverBottomSafeArea(),
          ],
        ),
      ),
    );
  }

  Widget _buildInfluencerRow(
    SocialMediaInfluencer influencer,
    AppLocalizations localizations,
  ) {
    final inputDecoration = VisitedInputField.buildDecoration(context, '');

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisSize: MainAxisSize.min,
      children: [
        Text(influencer.network),
        VisitedInputField(
          controller: influencer.handleController,
          label: localizations.handle,
          autocompleteHints: [AutofillHints.username],
        ),
        DropdownMenu<FollowersRange>(
          width: double.infinity,
          inputDecorationTheme: InputDecorationTheme(
            border: inputDecoration.border,
            enabledBorder: inputDecoration.enabledBorder,
            focusedBorder: inputDecoration.focusedBorder,
            filled: true,
            fillColor: inputDecoration.fillColor,
            labelStyle: inputDecoration.labelStyle,
          ),
          controller: influencer.followerController,
          label: Text(localizations.followers),
          onSelected: (value) {
            setState(() {
              influencer.followers = value;
            });
          },
          dropdownMenuEntries: [
            for (final range in FollowersRange.values)
              DropdownMenuEntry(
                value: range,
                label: range.localizedName(localizations),
              ),
          ],
        ),
        const SizedBox(height: Margin.large),
      ],
    );
  }

  void _submitForm(BuildContext context) async {
    try {
      await SpinnerDialog.showDuringLongProcess(
        context,
        job: () {
          return DependencyInjector.sessionBloc.sendBrandAmbassadorForm(
            name: _nameController.text,
            email: _emailController.text,
            country: _country!,
            socialMedia: influencers
                .where((influencer) {
                  return influencer.handleController.text.isNotEmpty;
                })
                .map((influencer) {
                  return (
                    influencer.network,
                    influencer.handleController.text,
                    influencer.followers,
                  );
                })
                .toList(),
            other: _otherController.text,
            anythingElse: _anythingElseController.text,
          );
        },
      );
      if (!context.mounted) {
        return;
      }

      final localizations = AppLocalizations.of(context)!;
      await Alert(
        title: localizations.thankYou,
        message: localizations.formSubmitted,
      ).show(context);

      if (context.mounted) {
        Navigator.of(context).pop();
      }
    } catch (e) {
      _showError(e.toString());
    }
  }

  @override
  void dispose() {
    _countryController.dispose();
    _nameController.dispose();
    _emailController.dispose();
    _otherController.dispose();
    _anythingElseController.dispose();
    for (final influencer in influencers) {
      influencer.dispose();
    }
    super.dispose();
  }

  void _showError(String error) {
    if (!mounted) {
      return;
    }

    Alert(
      title: AppLocalizations.of(context)!.errorTitle,
      message: error,
    ).show(context);
  }
}

class SocialMediaInfluencer {
  SocialMediaInfluencer({
    required this.network,
    required this.handleController,
    required this.followerController,
  });

  final String network;
  final TextEditingController handleController;
  final TextEditingController followerController;
  FollowersRange? followers;

  void dispose() {
    handleController.dispose();
    followerController.dispose();
  }
}

enum FollowersRange {
  underOneThousand('< 1000'),
  oneThousandToTenThousand('1,000 - 10,000'),
  overTenThousand('10,000+');

  const FollowersRange(this.backendKey);

  final String backendKey;

  String localizedName(AppLocalizations localizations) {
    return switch (this) {
      FollowersRange.underOneThousand => localizations.underOneThousand,
      FollowersRange.oneThousandToTenThousand =>
        localizations.oneThousandToTenThousand,
      FollowersRange.overTenThousand => localizations.overTenThousand,
    };
  }
}
