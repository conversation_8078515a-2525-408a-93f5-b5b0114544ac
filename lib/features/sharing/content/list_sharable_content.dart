import 'package:collection/collection.dart';

import '../../../dependency_injection/dependency_injector.dart';
import '../../../generic_widgets/selectable_item.dart';
import '../../../l10n/generated/app_localizations.dart';
import '../../../models/selection.dart';
import '../../todo_lists/models/todo_list.dart';
import 'sharable_content.dart';

class TodoListSharableContent extends SharableContent {
  final TodoList list;

  TodoListSharableContent(this.list);

  @override
  Future<void> buildShareMessage(
      StringBuffer buffer, AppLocalizations localizations) async {
    final items =
        await DependencyInjector.todoListBloc.selectionsByList(list).first;

    buffer.writeln(localizations.numberOfItemsInTodoListShareMessage(
      items.length,
      list.name,
    ));
    buffer.writeln();

    final sortedBySelection = items.entries
        .groupListsBy((entry) => entry.value)
        .map((key, value) => MapEntry(key, value.map((e) => e.key).toList()));

    for (final selectionEntry in sortedBySelection.entries) {
      buffer.writeln(selectionEntry.key.localized(localizations));
      for (final item in selectionEntry.value) {
        buffer.writeln('- ${item.name}');
      }
      buffer.writeln();
    }
  }

  @override
  Future<double> get percentageComplete =>
      DependencyInjector.todoListBloc.percentComplete(list).first;

  @override
  String percentageLabel(AppLocalizations localizations) {
    return localizations.coverage;
  }

  @override
  Future<Map<SelectableItem, Selection>> get selections =>
      DependencyInjector.todoListBloc
          .selectionsByList(list)
          .first
          .then((value) {
        value.removeWhere((key, value) => value == Selection.clear);
        return value;
      });
}
