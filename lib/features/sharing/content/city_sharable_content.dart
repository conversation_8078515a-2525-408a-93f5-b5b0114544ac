import 'package:collection/collection.dart';

import '../../../dependency_injection/dependency_injector.dart';
import '../../../generic_widgets/selectable_item.dart';
import '../../../l10n/generated/app_localizations.dart';
import '../../../models/selection.dart';
import 'sharable_content.dart';

class AllCitiesSharableContent extends SharableContent {
  @override
  Future<double> get percentageComplete =>
      DependencyInjector.cityBloc.percentOfCitiesVisited.first;

  @override
  String percentageLabel(AppLocalizations localizations) => localizations.been;

  @override
  Future<Map<SelectableItem, Selection>> get selections =>
      DependencyInjector.cityBloc.currentSelections();

  @override
  Future<void> buildShareMessage(
      StringBuffer buffer, AppLocalizations localizations) async {
    final citySelections =
        await DependencyInjector.cityBloc.currentSelections();

    final areaBloc = DependencyInjector.areaBloc;
    final grouped = citySelections.entries.groupListsBy(
        (entry) => areaBloc.areaByIsoCodeSync(entry.key.geoAreaIsoCode)!);

    final numberOfCities = citySelections.entries
        .where((entry) =>
            entry.value == Selection.been ||
            entry.value == Selection.lived ||
            entry.value == Selection.live)
        .length;

    buffer.writeln(localizations.numberOfCitiesShareMessage(numberOfCities));

    for (final countryLevel in grouped.entries) {
      final area = countryLevel.key;
      final cityEntries = countryLevel.value;
      final sortedBySelection = cityEntries
          .groupListsBy((e) => e.value)
          .entries
          .sorted((a, b) => a.key.compareTo(b.key));

      buffer.writeln(area.name);
      buffer.writeln();

      for (final selectionEntry in sortedBySelection) {
        buffer.writeln(selectionEntry.key.localized(localizations));
        for (final cityEntry in selectionEntry.value) {
          buffer.writeln('- ${cityEntry.key.name}');
        }
        buffer.writeln();
      }
    }
  }
}
