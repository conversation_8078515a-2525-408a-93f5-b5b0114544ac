import 'package:collection/collection.dart';

import '../../../dependency_injection/dependency_injector.dart';
import '../../../generic_widgets/selectable_item.dart';
import '../../../l10n/generated/app_localizations.dart';
import '../../../models/geo_area.dart';
import '../../../models/geo_bounds.dart';
import '../../../models/selection.dart';
import '../../map/tiles/internal/geometry_repository.dart';
import '../../selection/area_selection_bloc.dart';
import 'sharable_content.dart';

abstract class _AbstractCountrySharableContent extends SharableContent {
  Future<Map<Selection, List<GeoArea>>> currentSortedSelections();

  Future<String> getSelectionList(
    AreaSelectionBloc bloc,
    AppLocalizations localizations,
  ) async {
    final selections = await currentSortedSelections();
    final buffer = StringBuffer();

    final sorted = selections.entries.sorted((a, b) => b.key.compareTo(a.key));

    for (final entry in sorted) {
      buffer.writeln(entry.key.localized(localizations));
      for (final area in entry.value) {
        buffer.writeln(' - ${area.name}');
      }
      buffer.writeln('');
    }

    return buffer.toString();
  }

  @override
  Future<void> buildShareMessage(
      StringBuffer buffer, AppLocalizations localizations) async {
    buffer.writeln(await getSelectionList(
      DependencyInjector.areaSelectionBloc,
      localizations,
    ));
  }
}

class AllCountriesSharableContent extends _AbstractCountrySharableContent {
  @override
  Future<double> get percentageComplete =>
      DependencyInjector.areaSelectionBloc.percentOfWorldSeen.first;

  @override
  String percentageLabel(AppLocalizations localizations) =>
      localizations.ofTheWorld;

  @override
  Future<Map<SelectableItem, Selection>> get selections =>
      DependencyInjector.areaSelectionBloc.currentSelections;

  @override
  Future<Map<Selection, List<GeoArea>>> currentSortedSelections() =>
      DependencyInjector.areaSelectionBloc.currentSortedCountrySelections();
}

class CountrySharableContent extends _AbstractCountrySharableContent {
  final GeoArea area;

  CountrySharableContent(this.area);

  @override
  Future<void> buildShareMessage(
      StringBuffer buffer, AppLocalizations localizations) async {
    final numberOfCountriesBeenTo = await DependencyInjector
        .areaSelectionBloc.numberOfCountriesBeenTo.first;
    buffer.writeln(localizations.numberOfCountriesShareMessage(
      numberOfCountriesBeenTo,
    ));
    buffer.writeln('');
    buffer.writeln(area.name);
    buffer.writeln();

    return super.buildShareMessage(buffer, localizations);
  }

  @override
  Future<double> get percentageComplete =>
      DependencyInjector.areaSelectionBloc.percentOfCountrySeen(area).first;

  @override
  String percentageLabel(AppLocalizations localizations) => area.name;

  @override
  Future<Map<SelectableItem, Selection>> get selections =>
      DependencyInjector.areaSelectionBloc.currentSelections.then((selections) {
        return {
          for (final entry in selections.entries)
            if (entry.key.parentId == area.id) entry.key: entry.value
        };
      });

  @override
  Future<Map<Selection, List<GeoArea>>> currentSortedSelections() =>
      DependencyInjector.areaSelectionBloc
          .currentSortedSubdivisionSelections(area);

  @override
  Future<(PolygonLookup, GeoBounds)> fetchGeometryAndImageBounds() async {
    final record =
        await DependencyInjector.geometryBloc.fetchPolygonsForArea(area);
    return (record.$1, record.$2 ?? area.renderingBounds);
  }

  @override
  String shareSubject(AppLocalizations localizations) => area.name;
}
