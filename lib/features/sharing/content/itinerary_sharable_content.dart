import '../../../dependency_injection/dependency_injector.dart';
import '../../../generic_widgets/selectable_item.dart';
import '../../../l10n/generated/app_localizations.dart';
import '../../../models/geo_bounds.dart';
import '../../../models/selection.dart';
import '../../itineraries/itinerary.dart';
import '../../map/tiles/internal/geometry_repository.dart';
import 'sharable_content.dart';

class ItinerarySharableContent extends SharableContent {
  final Itinerary itinerary;

  ItinerarySharableContent(this.itinerary);

  @override
  double get annotationSize => 10.0;

  @override
  bool get showCount => true;

  @override
  Future<double> get percentageComplete =>
      Future.value(itinerary.places?.length.toDouble() ?? 0.0);

  @override
  String percentageLabel(AppLocalizations localizations) =>
      localizations.places;

  @override
  Future<Map<SelectableItem, Selection>> get selections => Future.value({
        for (final area in itinerary.cities ?? []) area: Selection.want,
        for (final place in itinerary.places ?? []) place: Selection.want,
      });

  @override
  Future<(PolygonLookup, GeoBounds)> fetchGeometryAndImageBounds() async {
    final geometry = await DependencyInjector.geometryBloc
        .fetchPolygonsForArea(itinerary.area);
    return (geometry.$1, geometry.$2 ?? itinerary.area.renderingBounds);
  }

  @override
  Future<void> buildShareMessage(
      StringBuffer buffer, AppLocalizations localizations) async {
    buffer.writeln(itinerary.area.name);
    buffer.writeln();

    final experiences = itinerary.experiences;
    if (experiences != null && experiences.isNotEmpty) {
      buffer.writeln(localizations.experiences);
      for (final experience in experiences) {
        buffer.writeln('- ${experience.name}');
      }
      buffer.writeln();
    }

    final cities = itinerary.cities;
    if (cities != null && cities.isNotEmpty) {
      buffer.writeln(localizations.cities);
      for (final city in cities) {
        buffer.writeln('- ${city.name}');
      }
      buffer.writeln();
    }

    final places = itinerary.places;
    if (places != null && places.isNotEmpty) {
      buffer.writeln(localizations.places);
      for (final place in places) {
        buffer.writeln('- ${place.name}');
      }
      buffer.writeln();
    }

    final food = itinerary.food;
    if (food != null && food.isNotEmpty) {
      buffer.writeln(localizations.food);
      for (final item in food) {
        buffer.writeln('- ${item.name}');
      }
      buffer.writeln();
    }
  }
}
