import '../../../dependency_injection/dependency_injector.dart';
import '../../../generic_widgets/selectable_item.dart';
import '../../../l10n/generated/app_localizations.dart';
import '../../../models/geo_bounds.dart';
import '../../../models/selection.dart';
import '../../map/tiles/internal/geometry_repository.dart';

abstract class SharableContent {
  Future<double> get percentageComplete;
  String percentageLabel(AppLocalizations localizations);
  Future<Map<SelectableItem, Selection>> get selections;
  final annotationSize = 4.0;
  bool get showCount => false;

  /// Add share message to the buffer
  Future<void> buildShareMessage(
    StringBuffer buffer,
    AppLocalizations localizations,
  );

  Future<(PolygonLookup, GeoBounds)> fetchGeometryAndImageBounds() async {
    final geometry =
        await DependencyInjector.geometryBloc.fetchCountryPolygons();
    const imageBounds = GeoBounds.wholePlanet;
    return (geometry, imageBounds);
  }

  String shareSubject(AppLocalizations localizations) =>
      localizations.myTravelMap;
}
