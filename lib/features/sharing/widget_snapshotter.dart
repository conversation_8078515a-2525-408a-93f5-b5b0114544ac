import 'dart:ui' as ui;

import 'package:flutter/rendering.dart';
import 'package:flutter/widgets.dart';

class WidgetSnapshotter {
  final GlobalKey widgetKey;
  final double outputWidth;

  WidgetSnapshotter(this.widgetKey, this.outputWidth);

  Future<ui.Image?> rasterize() async {
    late final RenderRepaintBoundary boundary;
    try {
      boundary =
          widgetKey.currentContext?.findRenderObject() as RenderRepaintBoundary;
    } catch (e) {
      return null;
    }

    final pixelScale = outputWidth / boundary.size.width;
    return boundary.toImage(pixelRatio: pixelScale);
  }
}
