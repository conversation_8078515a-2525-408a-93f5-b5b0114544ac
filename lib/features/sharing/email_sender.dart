import 'dart:io';

import 'package:flutter/material.dart';
import 'package:open_mail_app_plus/open_mail_app_plus.dart';

import '../../dependency_injection/dependency_injector.dart';
import '../../generic_widgets/platform_aware/platform_text_button.dart';
import '../../generic_widgets/separated_tile.dart';
import '../../l10n/generated/app_localizations.dart';
import '../../models/selection.dart';
import '../../models/user.dart';
import '../../networking/environment.dart';
import '../selection/area_selection_bloc.dart';

class EmailSender {
  final EnvironmentData environment;
  final User? user;
  final AreaSelectionBloc selections;
  final AppLocalizations localizations;

  const EmailSender({
    required this.environment,
    required this.user,
    required this.selections,
    required this.localizations,
  });

  Future<bool> send({
    required BuildContext context,
    required String subject,
    String? customBody,
    String? customRecipient,
  }) async {
    final rootContext = Navigator.of(context, rootNavigator: true).context;

    final applications = await OpenMailAppPlus.getMailApps();

    if (applications.isEmpty) {
      return false;
    }

    final content = EmailContent(
      subject: subject,
      to: [customRecipient ?? localizations.supportEmailAddress],
      body: customBody ?? await _buildUserInfo(),
    );

    if (applications.length == 1) {
      final onlyApp = applications.first;
      return OpenMailAppPlus.composeNewEmailInSpecificMailApp(
        emailContent: content,
        mailApp: onlyApp,
      );
    }

    if (rootContext.mounted == false) {
      return false;
    }

    showModalBottomSheet(
      context: context,
      builder: (_) => PickEmailAppModal(
        mailApps: applications,
        emailContent: content,
      ),
      backgroundColor: Theme.of(context).cardColor,
      enableDrag: true,
      elevation: 0,
      showDragHandle: true,
    );

    return true;
  }

  Future<String> _buildUserInfo() async {
    final buffer = StringBuffer();

    buffer.writeln('');
    buffer.writeln('');
    buffer.writeln('version: ${environment.version}');
    buffer.writeln(
      'lang: ${DependencyInjector.settingsBloc.currentLanguage.localizationKey}',
    );

    if (user != null) {
      buffer.writeln('id: ${user!.email}');
    }

    final livedAreas = (await selections.fetchSelectedAreasByType(
      Selection.live,
    )).map((e) => '${e.id} - ${e.isoCode}');

    buffer.writeAll(livedAreas, '\n');
    buffer.writeln();
    buffer.writeln('device: ${Platform.operatingSystem}');

    return buffer.toString();
  }
}

class PickEmailAppModal extends StatelessWidget {
  const PickEmailAppModal({
    required this.mailApps,
    required this.emailContent,
    super.key,
  });

  final List<MailApp> mailApps;
  final EmailContent emailContent;

  @override
  Widget build(BuildContext context) {
    return SafeArea(
      child: ClipRRect(
        borderRadius: const BorderRadius.vertical(top: Radius.circular(16)),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Padding(
              padding: const EdgeInsets.only(bottom: 16),
              child: Text(
                AppLocalizations.of(context)!.pickEmailApp,
                style: Theme.of(context).textTheme.titleLarge,
              ),
            ),
            for (final mailApp in mailApps)
              _buildMailAppButton(mailApp, context),
            const SizedBox(height: 8),
            PlatformTextButton(
              title: AppLocalizations.of(context)!.cancel,
              textStyle: TextStyle(
                color: Theme.of(context).colorScheme.error,
                fontWeight: FontWeight.w900,
                fontSize: 18,
              ),
              onTapped: Navigator.of(context).pop,
            ),
            const SafeArea(
              top: false,
              child: SizedBox(
                height: 20,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildMailAppButton(MailApp mailApp, BuildContext context) {
    return SeparatedTile(
      child: GestureDetector(
        onTap: () async {
          final results =
              await OpenMailAppPlus.composeNewEmailInSpecificMailApp(
                emailContent: emailContent,
                mailApp: mailApp,
              );
          if (context.mounted) {
            Navigator.of(context).pop(results);
          }
        },
        child: SizedBox(
          width: double.infinity,
          child: Center(
            child: Padding(
              padding: const EdgeInsets.symmetric(vertical: 16.0),
              child: Text(
                mailApp.name,
                style: Theme.of(context).textTheme.bodyLarge,
              ),
            ),
          ),
        ),
      ),
    );
  }
}
