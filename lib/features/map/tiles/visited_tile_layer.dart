import 'package:flutter/material.dart';
import 'package:flutter_map/flutter_map.dart';

import 'internal/tile_file_locator.dart';
import 'internal/tile_provider.dart';

class VisitedTileLayer extends StatefulWidget {
  const VisitedTileLayer({
    super.key,
    this.reset,
    this.active = true,
  });

  final Stream<void>? reset;
  final bool active;

  @override
  State<VisitedTileLayer> createState() => _VisitedTileLayerState();
}

class _VisitedTileLayerState extends State<VisitedTileLayer> {
  String? tileTemplate;

  @override
  void initState() {
    super.initState();

    TileFileLocator()
        .getTemplate(showSelections: widget.active)
        .then((template) {
      setState(() {
        tileTemplate = template;
      });
    });
  }

  @override
  Widget build(BuildContext context) {
    return TileLayer(
      urlTemplate: tileTemplate,
      tileProvider: VisitedTileProvider(showSelections: widget.active),
      reset: widget.reset,
      keepBuffer: 0,
      panBuffer: 0,
    );
  }
}
