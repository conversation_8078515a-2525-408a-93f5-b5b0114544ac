import 'dart:async';
import 'dart:io';
import 'dart:ui' as ui;

import 'package:flutter/foundation.dart';
import 'package:flutter/widgets.dart';
import 'package:flutter_map/flutter_map.dart' hide Polygon;

import '../../../../dependency_injection/dependency_injector.dart';
import 'map_tile.dart';
import 'tile_file_locator.dart';

class VisitedTileProvider extends TileProvider {
  final bool showSelections;

  VisitedTileProvider({
    super.headers,
    this.showSelections = true,
  });

  @override
  ImageProvider getImage(TileCoordinates coordinates, TileLayer options) =>
      _CachedImageProvider(
        showSelections: showSelections,
        coordinates: coordinates,
      );
}

class _CachedImageProvider extends ImageProvider<_CachedImageProvider> {
  _CachedImageProvider({
    required this.coordinates,
    required this.showSelections,
  });

  final TileCoordinates coordinates;
  final bool showSelections;

  @override
  ImageStreamCompleter loadImage(
    _CachedImageProvider key,
    ImageDecoderCallback decode,
  ) {
    return _CachedAsyncImageStreamCompleter(key: key);
  }

  @override
  Future<_CachedImageProvider> obtainKey(ImageConfiguration configuration) {
    return SynchronousFuture<_CachedImageProvider>(this);
  }
}

class _CachedAsyncImageStreamCompleter extends ImageStreamCompleter {
  _CachedAsyncImageStreamCompleter({
    required _CachedImageProvider key,
  }) {
    _provideImage(key);
  }

  final renderer = DependencyInjector.tileRenderingService;
  final tileFileLocator = TileFileLocator();

  Future<void> _renderAndApplyTile(
    File file,
    MapTile tile,
    bool showSelections, {
    bool retry = true,
  }) async {
    await renderer.render(
      tile,
      showSelections: showSelections,
    );
    if (!file.existsSync()) {
      if (retry) {
        return _renderAndApplyTile(file, tile, showSelections, retry: false);
      }
      throw Exception('There was a problem rendering the tile');
    }
    final image = await _loadImageFromFile(file);

    if (image == null) {
      return;
    }

    try {
      setImage(ImageInfo(image: image));
    } catch (e) {
      debugPrint('Ignoring image set on disposed provider: $e');
    }
  }

  Future<void> _provideImage(
    _CachedImageProvider key,
  ) async {
    final tile = MapTile.fromTileCoordinate(key.coordinates);
    final file = await tileFileLocator.fileForTile(
      tile,
      showSelection: key.showSelections,
    );
    if (file.existsSync()) {
      return _loadPrerenderedTile(file, tile, key.showSelections);
    }

    return _renderAndApplyTile(file, tile, key.showSelections);
  }

  Future<ui.Image?> _loadImageFromFile(File file) async {
    try {
      final bytes = await file.readAsBytes();
      final codec = await ui.instantiateImageCodec(bytes);
      final frameInfo = await codec.getNextFrame();
      return frameInfo.image;
    } catch (e) {
      return null;
    }
  }

  Future<void> _loadPrerenderedTile(
      File file, MapTile tile, bool showSelections) async {
    try {
      final image = await _loadImageFromFile(file);
      if (image == null) {
        return _renderAndApplyTile(file, tile, showSelections);
      }

      setImage(ImageInfo(image: image));
    } catch (e) {
      return _renderAndApplyTile(file, tile, showSelections);
    }
  }
}
