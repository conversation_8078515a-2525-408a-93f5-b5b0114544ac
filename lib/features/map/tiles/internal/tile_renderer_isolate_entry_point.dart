import 'dart:isolate';

import 'render_tile_events.dart';
import 'tile_renderer.dart';

const eventResponseResultKey = 'result';
const eventResponseIdKey = 'id';
const eventResponseSucceededKey = 'succeeded';
const eventResponseErrorKey = 'error';

@pragma('vm:entry-point')
void tileRenderingIsolateEntryPoint(SendPort sendPort) {
  final renderer = TileRenderer();
  final receivePort = ReceivePort();
  sendPort.send(receivePort.sendPort);

  receivePort.listen(
    (message) async {
      final event = BackgroundRenderingEvent.fromMap(message);

      try {
        switch (event) {
          case RenderTileEvent():
            final data = await renderer.renderTile(event);
            sendPort.send({
              eventResponseSucceededKey: true,
              eventResponseIdKey: event.id,
              eventResponseResultKey: data,
            });
        }
      } catch (e) {
        sendPort.send({
          eventResponseSucceededKey: false,
          eventResponseIdKey: event.id,
          eventResponseErrorKey: e.toString(),
        });
      }
    },
  );
}
