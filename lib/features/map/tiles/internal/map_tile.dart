import 'package:flutter_map/flutter_map.dart';

class MapTile {
  const MapTile({
    required this.x,
    required this.y,
    required this.z,
  }) : assert(x < 1000 && y < 1000);

  factory MapTile.fromTileCoordinate(TileCoordinates coordinate) =>
      MapTile(x: coordinate.x, y: coordinate.y, z: coordinate.z);

  factory MapTile.fromId(int id) {
    var container = id;

    final z = container ~/ _zMultiplier;
    container -= z * _zMultiplier;

    final x = container ~/ _xMultiplier;
    container -= x * _xMultiplier;

    final y = container;

    return MapTile(x: x, y: y, z: z);
  }

  static const _zMultiplier = 1000000;
  static const _xMultiplier = 1000;

  int get id => (z * _zMultiplier) + (x * _xMultiplier) + y;
  final int x;
  final int y;
  final int z;

  MapTile get parent {
    if (z <= 0) {
      return this;
    }

    return MapTile(x: x ~/ 2, y: y ~/ 2, z: z - 1);
  }

  List<MapTile> get children {
    final nextX = x * 2;
    final nextY = y * 2;
    final nextZ = z + 1;
    return [
      MapTile(x: nextX, y: nextY, z: nextZ),
      MapTile(x: nextX, y: nextY + 1, z: nextZ),
      MapTile(x: nextX + 1, y: nextY, z: nextZ),
      MapTile(x: nextX + 1, y: nextY + 1, z: nextZ),
    ];
  }

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is MapTile &&
          runtimeType == other.runtimeType &&
          x == other.x &&
          y == other.y &&
          z == other.z;

  @override
  int get hashCode => x.hashCode ^ y.hashCode ^ z.hashCode;

  @override
  String toString() {
    return 'MapTile{x: $x, y: $y, z: $z}';
  }
}
