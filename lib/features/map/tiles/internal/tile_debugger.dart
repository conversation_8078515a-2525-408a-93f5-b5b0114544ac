import 'dart:io';

import 'package:flutter/material.dart';

import '../../../../dependency_injection/dependency_injector.dart';
import '../../../../generic_widgets/platform_aware/platform_filled_button.dart';
import '../../../../models/geo_area.dart';
import '../../../areas/area_tile.dart';
import 'map_tile.dart';
import 'tile_file_locator.dart';
import 'tile_repository.dart';

class TileDebugger extends StatefulWidget {
  const TileDebugger({super.key});

  @override
  State<TileDebugger> createState() => _TileDebuggerState();
}

class _TileDebuggerState extends State<TileDebugger> {
  final xController = TextEditingController(text: '0');
  final yController = TextEditingController(text: '0');
  final zController = TextEditingController(text: '1');

  MapTile? tile;
  late TileRepository _tileRepository;

  List<GeoArea>? _currentAreas;

  File? _renderedTile;

  final service = DependencyInjector.tileRenderingService;
  final _fileLocator = TileFileLocator();

  @override
  void initState() {
    super.initState();
    _tileRepository = TileRepository();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(),
      body: Column(children: [
        Row(
          children: [
            const SizedBox(width: 16),
            Expanded(
                child: TextField(
              controller: xController,
              decoration: const InputDecoration(label: Text('x')),
            )),
            const SizedBox(width: 16),
            Expanded(
                child: TextField(
              controller: yController,
              decoration: const InputDecoration(label: Text('y')),
            )),
            const SizedBox(width: 16),
            Expanded(
                child: TextField(
              controller: zController,
              decoration: const InputDecoration(label: Text('z')),
            )),
            const SizedBox(width: 16),
          ],
        ),
        const SizedBox(height: 16),
        Padding(
          padding: const EdgeInsets.all(8.0),
          child: PlatformFilledButton(
            title: 'Render',
            onTapped: () async {
              final tile = MapTile(
                x: int.parse(xController.text),
                y: int.parse(yController.text),
                z: int.parse(zController.text),
              );

              final areas = (await _tileRepository.findAreas(tile))
                  ?.map((a) => DependencyInjector.areaRepository
                      .fetchByIsoCodeSync(a.isoCode))
                  .nonNulls
                  .toList();

              await service.render(tile);
              final file = await _fileLocator.fileForTile(tile);
              setState(() {
                this.tile = tile;
                _renderedTile = file;
                _currentAreas = areas ?? [];
              });
            },
          ),
        ),
        if (_renderedTile != null)
          DecoratedBox(
            decoration: BoxDecoration(border: Border.all(width: 2)),
            child: Image.file(
              _renderedTile!,
              width: 256,
              height: 256,
            ),
          ),
        if (_currentAreas != null)
          Expanded(
            child: ListView.builder(
              itemCount: _currentAreas!.length,
              itemBuilder: (context, index) {
                return AreaTile(
                  area: _currentAreas![index],
                );
              },
            ),
          )
      ]),
    );
  }
}
