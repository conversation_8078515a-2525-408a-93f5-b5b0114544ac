import 'dart:async';
import 'dart:isolate';

import 'package:flutter_isolate/flutter_isolate.dart';
import 'package:rxdart/rxdart.dart';

import '../../../../dependency_injection/dependency_injector.dart';
import '../../../../logger.dart';
import '../../../../models/geo_area.dart';
import '../../../../models/palette.dart';
import '../../../../models/polygon.dart';
import '../../../../models/selection.dart';
import '../tile_constants.dart';
import 'map_tile.dart';
import 'render_tile_events.dart';
import 'tile_file_locator.dart';
import 'tile_renderer_isolate_entry_point.dart';

class TileRenderingService {
  Stream<bool> get refreshStream => _refreshController.stream;

  FlutterIsolate? _isolate;
  final ReceivePort _receivePort = ReceivePort();
  SendPort? _sendPort;
  final _initializationCompleter = Completer();

  final _eventQueue = <int, Completer>{};

  final _fileLocator = TileFileLocator();
  final _refreshController = BehaviorSubject<bool>();
  late final _tileRepo = DependencyInjector.tileRepository;
  late final _geometryRepo = DependencyInjector.geometryRepository;

  Palette? _currentPalette;

  Future<void> initialize() async {
    if (_sendPort != null) {
      return;
    }

    await _spinUpIsolate();

    // // Calculate the tiles, but don't wait for them
    // _tileRepo.calculate(TileConstants.maxTileDepth);

    refresh();

    final settings = DependencyInjector.settingsBloc;
    _currentPalette = settings.currentPalette;
    settings.palette.listen(_updatePalette);
  }

  Future<void> _spinUpIsolate() async {
    if (_isolate != null) {
      return;
    }

    _isolate = await FlutterIsolate.spawn(
      tileRenderingIsolateEntryPoint,
      _receivePort.sendPort,
    );

    _receivePort.listen(_onEventCompleted);
    return _initializationCompleter.future;
  }

  Future<bool> _sendEventToIsolate(BackgroundRenderingEvent event) async {
    final completer = Completer<bool>();
    _eventQueue[event.id] = completer;
    _sendPort?.send(event.toMap());
    return completer.future;
  }

  void _onEventCompleted(data) {
    if (data is SendPort) {
      _sendPort = data;
      _initializationCompleter.complete(true);
      return;
    }

    final success = data[eventResponseSucceededKey];
    final id = data[eventResponseIdKey];

    final completer = _eventQueue[id];
    if (completer == null) {
      return;
    }

    if (success == true) {
      final result = data[eventResponseResultKey];
      completer.complete(result);
    } else {
      final error = data[eventResponseErrorKey];
      completer.completeError(error);
    }

    _eventQueue.remove(id);
  }

  Future<void> _updatePalette(Palette palette) async {
    if (palette == _currentPalette) {
      return;
    }

    _currentPalette = palette;
    await clearAllTiles();
    refresh();
  }

  void renderAllTiles({required int minZoom, required int maxZoom}) {
    for (var z = minZoom; z <= maxZoom; z++) {
      final bounds = DependencyInjector.settingsBloc.currentSettings.projection
          .getProjectedBounds(z.toDouble());
      if (bounds == null) {
        return;
      }

      final size = bounds.size;
      final maxX = size.x ~/ TileConstants.tileSize;
      final maxY = size.y ~/ TileConstants.tileSize;

      for (var x = 0; x < maxX; x++) {
        for (var y = 0; y < maxY; y++) {
          _renderTileIfMissing(x, y, z);
        }
      }
    }
  }

  Future<void> _renderTileIfMissing(int x, int y, int z) async {
    final tile = MapTile(x: x, y: y, z: z);
    try {
      final file = await _fileLocator.fileForTile(tile);
      if (file.existsSync()) {
        return;
      }
      return render(tile);
    } catch (e) {
      // In case IO operations throw an exception, just render the tile
      return render(tile);
    }
  }

  /// Always renders a single tile, even it if already exists.
  Future<void> render(MapTile tile, {bool showSelections = true}) async {
    final areas = await _tileRepo.findAreas(tile);

    final polygonSelectionLookup = await _prepareGeometry(
      areas,
      tile,
      showSelections,
    );

    final palette = DependencyInjector.settingsBloc.currentPalette;

    final event = RenderTileEvent(
      tile: tile,
      geometryAndSelections: polygonSelectionLookup,
      palette: palette,
      showSelections: showSelections,
      crs: DependencyInjector.settingsBloc.currentSettings.projection,
    );
    final success = await _sendEventToIsolate(event);
    if (!success) {
      log('Failed to render tile ${tile.x}, ${tile.y}, ${tile.z}');
    }
  }

  Future<List<(List<Polygon>, Selection)>> _prepareGeometry(
    List<GeoArea>? areas,
    MapTile tile,
    bool showSelections,
  ) async {
    final polygonSelectionLookup = <(List<Polygon>, Selection)>[];

    if (areas != null) {
      final selections =
          (await DependencyInjector.areaSelectionService.fetchSelections()).map(
            (area, selection) => MapEntry(area.isoCode, selection),
          );

      for (final area in areas) {
        final polygons = await _geometryRepo.fetchGeometry(
          area,
          lowRes: tile.z <= TileConstants.lowResGeometryThreshold,
        );

        if (polygons == null) {
          continue;
        }

        final selection = showSelections
            ? selections[area.isoCode] ?? Selection.clear
            : Selection.clear;
        polygonSelectionLookup.add((polygons, selection));
      }
    }
    return polygonSelectionLookup;
  }

  Future<void> showRegions(bool selected) async {
    await clearAllTiles();
    refresh();
  }

  Future<bool> updateAreas(Iterable<GeoArea> areas) async {
    try {
      if (areas.isEmpty) {
        return true;
      }

      final affectedTiles = await _tileRepo.allTiles(areas);
      await _fileLocator.deleteTiles(affectedTiles);

      refresh();
      return true;
    } catch (e) {
      return false;
    }
  }

  /// Delete all of the rendered tiles in the local file cache
  Future<void> clearAllTiles() {
    return _fileLocator.clearAll();
  }

  void clearTilesWithAreas(List<GeoArea> areas) async {
    final tiles = await _tileRepo.allTiles(areas);
    await _fileLocator.deleteTiles(tiles);
    refresh();
  }

  void dispose() {
    for (final completer in _eventQueue.values) {
      completer.completeError(Exception('Render service disposed'));
    }
    _eventQueue.clear();
    _receivePort.close();
    _refreshController.close();
    _isolate?.kill();
    _isolate = null;
  }

  void refresh() {
    _refreshController.add(true);
  }
}
