import 'dart:math';

import 'package:collection/collection.dart';

import '../../../../dependency_injection/dependency_injector.dart';
import '../../../../models/geo_area.dart';
import '../../../../networking/asset_helper.dart';
import '../tile_constants.dart';
import 'map_tile.dart';

class TileRepository with GridSizeCalculable {
  static int minSubdivisionLevel(int zoomLevel) {
    if (zoomLevel < TileConstants.subdivisionLevelOneThreshold) {
      return 0;
    } else if (zoomLevel < TileConstants.subdivisionLevelTwoThreshold) {
      return 1;
    } else {
      return 2;
    }
  }

  late final _areaRepository = DependencyInjector.areaRepository;

  Map<String, List<GeoArea>>? _lookup;

  Future<Map<String, List<GeoArea>>> _fetchLookup() async {
    if (_lookup != null) {
      return _lookup!;
    }

    final crs = DependencyInjector.settingsBloc.currentSettings.projection;

    final rawLookup =
        await AssetHelper.loadCompressedJson(
              'assets/data/tiles_${crs.code}.bin',
            )
            as Map;

    if (_lookup != null) {
      return _lookup!;
    }

    final repo = DependencyInjector.areaRepository;
    final allAreasSet = await _areaRepository.fetchAll();

    if (_lookup != null) {
      return _lookup!;
    }

    final allAreas = {for (final area in allAreasSet) area.isoCode: area};
    _lookup = {};
    for (final entry in rawLookup.entries) {
      final tileId = entry.key;
      final List isoCodes = entry.value;
      final areas = <GeoArea>[];
      for (final isoCode in isoCodes) {
        var area = allAreas[isoCode];
        if (area != null) {
          areas.add(area);
        }

        area = await repo.fetchByIsoCode(isoCode);
        if (area != null) {
          areas.add(area);
        }
      }
      _lookup![tileId] = areas;
    }

    return _lookup!;
  }

  Future<List<GeoArea>?> findAreas(MapTile tile) async {
    if (tile.z < 0) {
      return (await _areaRepository.fetchAll()).toList();
    }

    final grid = gridSize(tile.z)!;
    final MapTile tileToUse;

    if (tile.x < 0) {
      final absoluteX = grid.x + tile.x;
      tileToUse = MapTile(x: absoluteX, y: tile.y, z: tile.z);
    } else if (tile.x >= grid.x) {
      final absoluteX = tile.x.abs() - grid.x;
      tileToUse = MapTile(x: absoluteX, y: tile.y, z: tile.z);
    } else {
      tileToUse = tile;
    }

    final lookup = await _fetchLookup();
    final areas = lookup[tileToUse.id.toString()];

    return _manageSubdivisions(tile, areas);
  }

  List<GeoArea>? _manageSubdivisions(MapTile tile, List<GeoArea>? areas) {
    if (areas == null) {
      return null;
    }

    if (tile.z < TileConstants.subdivisionLevelOneThreshold ||
        DependencyInjector.settingsBloc.currentSettings.showRegions == false) {
      return areas.where((area) => area.isCountry).toList();
    }

    final grouped = areas.groupListsBy(
      (area) => areas.firstWhereOrNull((parent) => area.parentId == parent.id),
    );

    final usable = <GeoArea>[];

    for (final entry in grouped.entries) {
      final parent = entry.key;
      final children = entry.value;

      // Automatically add the areas that don't support subdivisions
      if (parent == null) {
        for (final topLevel in children) {
          if (topLevel.hasSubdivisions == false) {
            usable.add(topLevel);
          }
        }
        continue;
      }

      // Only add the free regions if purchase is not unlocked
      if (DependencyInjector.iapBloc.hasUnlockedRegions == false) {
        if (TileConstants.freeParentSubdivisionIds.contains(parent.id)) {
          usable.addAll(children);
        } else {
          usable.add(parent);
        }

        continue;
      }

      // Add Level Two Subdivisions
      if (tile.z >= TileConstants.subdivisionLevelTwoThreshold &&
          parent.parentId != null) {
        usable.addAll(children);
        continue;
      }

      // Add Level One subdivisions, but only the ones that don't support level 2
      for (final child in children) {
        if (child.hasSubdivisions &&
            tile.z >= TileConstants.subdivisionLevelTwoThreshold) {
          continue;
        }

        usable.add(child);
      }
    }

    return usable;
  }

  Future<List<MapTile>> tiles(GeoArea area) async {
    var startingZoom = 1;

    final parentId = area.parentId;
    const ukCountryIds = [320, 321, 322, 323];
    if (parentId != null && ukCountryIds.contains(parentId)) {
      startingZoom = TileConstants.subdivisionLevelOneThreshold;
    }
    // if (area.parentId == null) {
    //   startingZoom = 1;
    // } else if (area.hasSubdivisions) {
    //   startingZoom = TileConstants.subdivisionLevelTwoThreshold;
    // } else {
    //   startingZoom = TileConstants.subdivisionLevelOneThreshold;
    // }

    final grid = gridSize(startingZoom);
    if (grid == null) {
      return [];
    }

    final tiles = [
      for (var x = 0; x < grid.x; x++)
        for (var y = 0; y < grid.y; y++) MapTile(x: x, y: y, z: startingZoom),
    ];

    return _recursivelySearchDownTileHierarchy(tiles, (areas) {
      final ids = areas.map((area) => area.id);
      return ids.contains(area.id) || ids.contains(area.parentId ?? -1);
    });
  }

  Future<List<MapTile>> allTiles(Iterable<GeoArea> areas) async {
    // TODO: Check if this is horribly inefficient
    final results = await Future.wait(areas.map((area) => tiles(area)));

    return results.expand((e) => e).toSet().toList();
  }

  Future<List<MapTile>> fetchTilesWithSubdivisions() async {
    final grid = gridSize(TileConstants.subdivisionLevelOneThreshold);
    if (grid == null) {
      return [];
    }

    final tiles = [
      for (var x = 0; x < grid.x; x++)
        for (var y = 0; y < grid.y; y++)
          MapTile(x: x, y: y, z: TileConstants.subdivisionLevelOneThreshold),
    ];

    return _recursivelySearchDownTileHierarchy(tiles, (areas) {
      for (final area in areas) {
        if (area.parentId != null) {
          return true;
        }
      }
      return false;
    });
  }

  Future<List<MapTile>> _recursivelySearchDownTileHierarchy(
    List<MapTile> tiles,
    bool Function(List<GeoArea> areas) filter,
  ) async {
    final found = <MapTile>[];
    for (final tile in tiles) {
      final areas = await findAreas(tile);
      if (areas == null) {
        continue;
      }

      if (!filter(areas)) {
        continue;
      }

      found.add(tile);

      if (tile.z == TileConstants.maxTileDepth) {
        continue;
      }

      final childTiles = await _recursivelySearchDownTileHierarchy(
        tile.children,
        filter,
      );
      if (childTiles.isNotEmpty) {
        found.addAll(childTiles);
      }
    }

    return found;
  }

  void clear() {
    _lookup = null;
  }
}

mixin GridSizeCalculable {
  Point<int>? gridSize(int zoom) {
    final crs = DependencyInjector.settingsBloc.currentSettings.projection;
    final bounds = crs.getProjectedBounds(zoom.toDouble());
    if (bounds == null) {
      return null;
    }

    final size = bounds.size;
    final maxX = size.x ~/ TileConstants.tileSize;
    final maxY = size.y ~/ TileConstants.tileSize;

    return Point(maxX, maxY);
  }
}
