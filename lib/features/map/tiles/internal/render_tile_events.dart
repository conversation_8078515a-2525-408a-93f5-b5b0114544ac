import 'dart:math';

import 'package:flutter_map/flutter_map.dart' hide Polygon;

import '../../../../models/palette.dart';
import '../../../../models/polygon.dart';
import '../../../../models/selection.dart';
import '../../../settings/settings.dart';
import 'map_tile.dart';

sealed class BackgroundRenderingEvent {
  factory BackgroundRenderingEvent.fromMap(Map map) {
    final type = map['type'];

    if (type == (RenderTileEvent).toString()) {
      return RenderTileEvent.fromMap(map);
    }

    throw Exception('Unknown rendering event for data $map');
  }

  Map toMap();
  int get id;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is BackgroundRenderingEvent &&
          runtimeType == other.runtimeType &&
          id == other.id;

  @override
  int get hashCode => id.hashCode;
}

class RenderTileEvent implements BackgroundRenderingEvent {
  @override
  final int id;
  final MapTile tile;
  final Palette palette;
  final bool showSelections;
  final Crs crs;

  /// Should contain every area that needs to be rendered, including clear;
  final List<(List<Polygon>, Selection)> geometryAndSelections;

  RenderTileEvent({
    required this.tile,
    required this.geometryAndSelections,
    required this.palette,
    required this.showSelections,
    required this.crs,
  }) : id = Random().nextInt(10000) + DateTime.now().millisecondsSinceEpoch;

  RenderTileEvent.fromMap(Map map)
      : id = map['id'],
        tile = MapTile(
          x: map['tile']['x'],
          y: map['tile']['y'],
          z: map['tile']['z'],
        ),
        geometryAndSelections = List.unmodifiable(
          map['geometry'].map(
            (element) => (
              element.first
                  .map<Polygon>((map) => Polygon.fromJson(map))
                  .toList(growable: false),
              Selection.fromBackendKey(element.last),
            ),
          ),
        ),
        palette = Palette.fromIsolate(map['palette']),
        showSelections = map['showSelections'],
        crs = CrsBuilder.fromCode(map['crs']);

  @override
  Map toMap() {
    return {
      'id': id,
      'type': runtimeType.toString(),
      'tile': {
        'x': tile.x,
        'y': tile.y,
        'z': tile.z,
      },
      'geometry': List.unmodifiable(
        geometryAndSelections.map(
          (record) {
            final (polygons, selection) = record;
            return List.unmodifiable([
              List.unmodifiable(polygons.map((polygon) => polygon.toJson())),
              selection.backendKey
            ]);
          },
        ),
      ),
      'palette': palette.toJson(),
      'showSelections': showSelections,
      'crs': crs.code,
    };
  }
}
