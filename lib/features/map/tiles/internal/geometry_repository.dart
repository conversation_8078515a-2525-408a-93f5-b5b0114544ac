import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';

import '../../../../dependency_injection/dependency_injector.dart';
import '../../../../helpers/pseudo_semaphore.dart';
import '../../../../models/geo_area.dart';
import '../../../../models/polygon.dart';
import '../../../../networking/asset_helper.dart';
import '../../../disputed_territories/disputed_territory.dart';

typedef PolygonLookup = Map<String, List<Polygon>>;

class GeometryRepository with WidgetsBindingObserver {
  GeometryRepository() {
    WidgetsBinding.instance.addObserver(this);
    load();
  }

  PolygonLookup? _lowResPolygons;
  PolygonLookup? _polygons;
  Map<String, PolygonLookup>? _disputedTerritoryAlternativeGeometry;
  Map<String, PolygonLookup>? _disputedTerritoryAlternativeGeometryLowRes;
  Map<String, PolygonLookup>? _subdivisions;
  final _disputedSwaps = <DisputedTerritory, DisputedTerritoryOption>{};

  Map<int, String>? _idToIsoLookup;

  final _loadingSemaphores = <String, PseudoSemaphore>{};

  Future<void> load() {
    return Future.wait([
      _loadHighResPolygons(),
      _loadSubdivisions(),
    ]);
  }

  Future<PolygonLookup> _loadHighResPolygons() async {
    _polygons ??= await _loadAssetWithSemaphore('geometry');
    return _polygons!;
  }

  Future<PolygonLookup> _loadLowResPolygons() async {
    _lowResPolygons ??= await _loadAssetWithSemaphore('geometry_low_res');
    return _lowResPolygons!;
  }

  Future<Map<String, PolygonLookup>> _loadSubdivisions() async {
    if (_subdivisions != null) {
      return _subdivisions!;
    }

    _subdivisions = await loadNestedPolygonsWithSemaphore('subdivisions');
    return _subdivisions!;
  }

  Future<Map<String, PolygonLookup>> _loadDisputedTerritories() async {
    if (_disputedTerritoryAlternativeGeometry != null) {
      return _disputedTerritoryAlternativeGeometry!;
    }

    _disputedTerritoryAlternativeGeometry =
        await loadNestedPolygonsWithSemaphore('disputed');
    return _disputedTerritoryAlternativeGeometry!;
  }

  Future<Map<String, PolygonLookup>> _loadDisputedTerritoriesLowRes() async {
    if (_disputedTerritoryAlternativeGeometryLowRes != null) {
      return _disputedTerritoryAlternativeGeometryLowRes!;
    }

    _disputedTerritoryAlternativeGeometryLowRes =
        await loadNestedPolygonsWithSemaphore('disputed_low_res');
    return _disputedTerritoryAlternativeGeometryLowRes!;
  }

  Future<void> preloadGeometry() {
    return Future.wait([
      _loadLowResPolygons(),
      _loadHighResPolygons(),
      _loadSubdivisions(),
    ]);
  }

  Future<PolygonLookup> allCountryPolygons({bool lowRes = false}) =>
      lowRes ? _loadLowResPolygons() : _loadHighResPolygons();

  Future<PolygonLookup?> allSubdivisionPolygons(GeoArea area) async {
    if (!area.hasSubdivisions) {
      return null;
    }
    final geometry = await _loadSubdivisions();
    return geometry[area.isoCode];
  }

  Future<PolygonLookup> fetchAreas(Iterable<GeoArea> areas) async {
    final subset = PolygonLookup();
    for (final area in areas) {
      final polygons = await fetchGeometry(area);
      if (polygons != null) {
        subset[area.isoCode] = polygons;
      }
    }

    return subset;
  }

  List<Polygon>? countryPolygonsSync(String isoCode) {
    return _polygons?[isoCode];
  }

  Future<List<Polygon>?> fetchGeometry(
    GeoArea area, {
    bool lowRes = false,
  }) async {
    if (area.parentId != null) {
      return _fetchSubdivisionGeometry(area);
    }

    if (lowRes) {
      return _fetchLowResGeometry(area.isoCode);
    }

    return _fetchGeometry(area.isoCode);
  }

  Future<List<Polygon>?> _fetchGeometry(String isoCode) async {
    final polygons = await _loadHighResPolygons();
    return polygons[isoCode];
  }

  Future<List<Polygon>?> _fetchLowResGeometry(String isoCode) async {
    final polygons = await _loadLowResPolygons();
    return polygons[isoCode];
  }

  Future<List<Polygon>?> _fetchSubdivisionGeometry(GeoArea area) async {
    final id = area.parentId;
    if (id == null) {
      return null;
    }

    var parentIsoCode = _idToIsoLookup?[id];
    if (parentIsoCode == null) {
      final parent = await DependencyInjector.areaRepository.fetchById(id);
      if (parent == null) {
        return null;
      }
      _idToIsoLookup ??= {};
      _idToIsoLookup?[id] = parent.isoCode;
      parentIsoCode = parent.isoCode;
    }

    final subdivisions = await _loadSubdivisions();
    final polygons = subdivisions[parentIsoCode];

    return polygons?[area.isoCode];
  }

  Future<PolygonLookup> _loadAssetWithSemaphore(String assetName) {
    var semaphore =
        _loadingSemaphores[assetName] as PseudoSemaphore<PolygonLookup>?;
    if (semaphore != null) {
      return semaphore.request();
    }

    semaphore = PseudoSemaphore<PolygonLookup>(expensiveOperation: () async {
      final polygons = await _loadPolygons('assets/data/$assetName.bin');
      _loadingSemaphores.remove(assetName);
      return polygons;
    });

    _loadingSemaphores[assetName] = semaphore;
    return semaphore.request();
  }

  Future<Map<String, PolygonLookup>> loadNestedPolygonsWithSemaphore(
      String filename) async {
    var loadingSemaphore = _loadingSemaphores[filename]
        as PseudoSemaphore<Map<String, PolygonLookup>>?;

    if (loadingSemaphore != null) {
      return loadingSemaphore.request();
    }

    loadingSemaphore = PseudoSemaphore<Map<String, PolygonLookup>>(
        expensiveOperation: () async {
      final buffer = await AssetHelper.loadBuffer('assets/data/$filename.bin');

      final nestedPolygons =
          await compute(_loadSubdivisionPolygonsInIsolate, buffer);

      _loadingSemaphores.remove(filename);
      return nestedPolygons;
    });

    _loadingSemaphores[filename] = loadingSemaphore;
    return loadingSemaphore.request();
  }

  Future<PolygonLookup> _loadPolygons(String assetName) async {
    final buffer = await AssetHelper.loadBuffer(assetName);
    final polygons = await compute(
      _loadPolygonsInIsolate,
      buffer,
      debugLabel: 'polygon_parsing',
    );
    return polygons;
  }

  Future<void> applyDisputedTerritoryAdjustments() async {
    final disputedBloc = DependencyInjector.disputedTerritoriesBloc;
    await disputedBloc.initialize();

    final selections = disputedBloc.currentSelections;
    if (selections == null) {
      return;
    }

    Map<String, PolygonLookup>? altGeometry;
    Map<String, PolygonLookup>? altGeometryLowRes;

    for (final entry in selections.entries) {
      final territory = entry.key;
      final option = entry.value;

      // Check if this change has already been applied
      if (_disputedSwaps[territory] == option) {
        continue;
      }

      final usingAltGeometry = _disputedSwaps.containsKey(territory);
      final requestedAltGeometry = !option.isDefault;

      final doNothing = !usingAltGeometry && !requestedAltGeometry;
      final restoreOriginalGeometry = usingAltGeometry && !requestedAltGeometry;
      final swapToAltGeometry = !usingAltGeometry && requestedAltGeometry;

      if (doNothing) {
        continue;
      }

      altGeometry ??= await _loadDisputedTerritories();
      altGeometryLowRes ??= await _loadDisputedTerritoriesLowRes();

      final lookup = altGeometry[territory.area.isoCode];
      final lookupLowRes = altGeometryLowRes[territory.area.isoCode] ?? lookup;

      if (lookup == null) {
        continue;
      }

      final buffer = PolygonLookup();
      final bufferLowRes = PolygonLookup();

      for (final entry in lookup.entries) {
        // Check if its a top level toggle first
        final isoCode = entry.key;
        final replacementGeometry = entry.value;
        var current = _polygons?[isoCode];

        final lowResCurrent = _lowResPolygons?[isoCode];
        final replacementLowRes = lookupLowRes?[isoCode];
        // Low Res only cares about top level geometry...
        if (lowResCurrent != null && replacementLowRes != null) {
          bufferLowRes[isoCode] = lowResCurrent;
          _lowResPolygons?[isoCode] = replacementLowRes;
        }

        // NOW SWAP
        if (current != null) {
          buffer[isoCode] = current;
          _polygons?[isoCode] = replacementGeometry;
          continue;
        }

        // Find the subdivision version
        if (current == null) {
          final areaBloc = DependencyInjector.areaBloc;
          final area = areaBloc.areaByIsoCodeSync(isoCode);

          if (area == null) {
            continue;
          }

          final parent = (await areaBloc.fetchParent(area));

          if (parent == null) {
            continue;
          }

          final subdivisionsLookup = _subdivisions?[parent.isoCode];
          current = subdivisionsLookup?[isoCode];

          // SWAP!
          if (current != null) {
            buffer[isoCode] = current;
            subdivisionsLookup?[isoCode] = replacementGeometry;
          }
        }
      }

      // Save the buffer
      _disputedTerritoryAlternativeGeometry?[territory.area.isoCode] = buffer;
      _disputedTerritoryAlternativeGeometryLowRes?[territory.area.isoCode] =
          bufferLowRes;

      if (restoreOriginalGeometry) {
        _disputedSwaps.remove(territory);
      } else if (swapToAltGeometry) {
        _disputedSwaps[territory] = option;
      }
    }
  }

  @override
  void didHaveMemoryPressure() {
    _lowResPolygons = null;
    _polygons = null;
    _subdivisions = null;
    _idToIsoLookup = null;
    _disputedTerritoryAlternativeGeometry = null;
    _disputedTerritoryAlternativeGeometryLowRes = null;
  }

  Future<PolygonLookup> allGeometry({required bool includeSubdivisions}) async {
    if (_polygons == null) {
      await _loadHighResPolygons();
    }

    if (includeSubdivisions && _subdivisions == null) {
      await _loadSubdivisions();
    }

    return {
      if (_polygons != null) ..._polygons!,
      if (includeSubdivisions && _subdivisions != null)
        for (final subdivision in _subdivisions!.values) ...subdivision,
    };
  }
}

/// Global functions that are executed in background isolates

PolygonLookup _loadPolygonsInIsolate(ByteData data) {
  final Map json = AssetHelper.decodeJsonFromCompressedData(data);
  return {
    for (final entry in json.entries)
      entry.key: entry.value.map<Polygon>((e) => Polygon.fromJson(e)).toList(),
  };
}

Map<String, PolygonLookup> _loadSubdivisionPolygonsInIsolate(ByteData data) {
  final Map json = AssetHelper.decodeJsonFromCompressedData(data);
  return {
    for (final entry in json.entries)
      entry.key: {
        for (final polygonEntry in entry.value.entries)
          polygonEntry.key: [
            for (final polygonJson in polygonEntry.value)
              Polygon.fromJson(polygonJson)
          ],
      }
  };
}
