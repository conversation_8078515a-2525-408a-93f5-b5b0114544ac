import 'dart:io';

import 'package:collection/collection.dart';
import 'package:path/path.dart' as p;
import 'package:path_provider/path_provider.dart';
import 'package:sentry_flutter/sentry_flutter.dart';

import 'map_tile.dart';

class TileFileLocator {
  static const filenameTemplate = '{x}_{y}_{z}.png';

  Future<String> getTemplate({bool showSelections = true}) async {
    final dir = await _getTileDirectory(
      showSelections: showSelections,
    );
    return _buildTemplate(dir);
  }

  Future<List<File>> filterRenderedTiles(List<MapTile> tiles) async {
    final dir = await _getTileDirectory();
    final template = _buildTemplate(dir);
    final potentiallyRendered = tiles.map((tile) => _buildFile(template, tile));
    final rendered = dir.listSync().whereType<File>();

    final actuallyRendered = potentiallyRendered
        .where((potential) =>
            rendered
                .firstWhereOrNull((actual) => actual.path == potential.path) !=
            null)
        .toList();

    return actuallyRendered;
  }

  Future<Directory> _getTileDirectory({bool showSelections = true}) async {
    final docs = await getApplicationDocumentsDirectory();
    final tileDir = showSelections ? 'map_tiles' : 'no_selections_tiles';

    final dir = Directory(p.join(docs.path, tileDir));
    if (dir.existsSync() == false) {
      dir.createSync(recursive: true);
    }

    return dir;
  }

  String _buildTemplate(Directory dir) {
    return p.join(dir.path, filenameTemplate);
  }

  Future<File> fileForTile(
    MapTile tile, {
    bool showSelection = true,
  }) async {
    final template = await getTemplate(showSelections: showSelection);
    return _buildFile(template, tile);
  }

  String _buildPath(String template, MapTile tile) {
    return template
        .replaceAll('{x}', tile.x.toString())
        .replaceAll('{y}', tile.y.toString())
        .replaceAll('{z}', tile.z.toString());
  }

  File _buildFile(String template, MapTile tile) {
    return File(_buildPath(template, tile));
  }

  Future<void> deleteTiles(List<MapTile> tiles) async {
    for (final tile in tiles) {
      final file = await fileForTile(tile);
      try {
        file.deleteSync();
      } on PathNotFoundException catch (_) {
        // Do nothing, file probably doesn't exist...
        // Purposely not checking if the file doesn't exist for performance
      } catch (e, stack) {
        Sentry.captureException(e, stackTrace: stack);
      }
    }
  }

  Future<void> clearAll() async {
    await Future.wait([
      _deleteDirectory(selections: true),
      _deleteDirectory(selections: false),
    ]);
  }

  Future<void> _deleteDirectory({required bool selections}) async {
    final directory = await _getTileDirectory(showSelections: selections);
    if (directory.existsSync()) {
      await directory.delete(recursive: true);
    }
  }
}
