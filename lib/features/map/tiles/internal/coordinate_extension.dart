import 'dart:ui';

import 'package:latlong2/latlong.dart';
import '../../../../models/coordinate.dart';

extension CoordinateExtension on Coordinate {
  LatLng toLatLng() {
    try {
      return LatLng(
        clampDouble(latitude.toDoubleIfNeeded(), -90, 90),
        clampDouble(longitude.toDoubleIfNeeded(), -180, 180),
      );
    } catch (e) {
      rethrow;
    }
  }

  Coordinate operator +(Coordinate other) {
    return Coordinate(
        latitude: latitude + other.latitude,
        longitude: longitude + other.longitude);
  }

  Coordinate operator -(Coordinate other) {
    return Coordinate(
        latitude: latitude - other.latitude,
        longitude: longitude - other.longitude);
  }
}

extension on num {
  double toDoubleIfNeeded() {
    if (this is double) {
      return this as double;
    }

    return toDouble();
  }
}
