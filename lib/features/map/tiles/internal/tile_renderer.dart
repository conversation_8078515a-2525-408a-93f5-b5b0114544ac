import 'dart:math';
import 'dart:ui';

import 'package:flutter/foundation.dart';
import 'package:flutter_map/flutter_map.dart' hide Polygon;

import '../../../../models/palette.dart';
import '../../../../models/polygon.dart';
import '../../../../models/selection.dart';
import '../tile_constants.dart';
import 'coordinate_extension.dart';
import 'map_tile.dart';
import 'render_tile_events.dart';
import 'tile_file_locator.dart';

class TileRenderer {
  Future<bool> renderTile(RenderTileEvent event) async {
    final tile = event.tile;

    final pictureRecorder = PictureRecorder();
    final canvas = Canvas(pictureRecorder);

    final crs = event.crs;

    await _performDrawing(
      canvas,
      tile,
      event.geometryAndSelections,
      event.palette,
      crs,
    );

    // if (kDebugMode) {
    //   _paintDebugInfo(canvas, tile);
    // }

    final picture = pictureRecorder.endRecording();
    final data = await _rasterizeDrawingToImageData(picture, tile);

    final fileLocator = TileFileLocator();
    final file = await fileLocator.fileForTile(
      tile,
      showSelection: event.showSelections,
    );
    file.writeAsBytesSync(data);
    return true;
  }

  // void _paintDebugInfo(Canvas canvas, MapTile tile) {
  //   canvas.drawRect(
  //       Offset.zero & Size.square(TileConstants.tileSize.toDouble()),
  //       Paint()
  //         ..color = const Color(0xFFFF0000)
  //         ..style = PaintingStyle.stroke
  //         ..strokeWidth = 1);
  //
  //   final span = TextSpan(
  //     text: '(${tile.x}, ${tile.y}, ${tile.z})',
  //     style: const TextStyle(
  //       color: Color(0xFF000000),
  //       fontSize: 10,
  //     ),
  //   );
  //   final painter = TextPainter(
  //     text: span,
  //     textDirection: TextDirection.ltr,
  //   );
  //   painter.layout(minWidth: 0, maxWidth: 256.0);
  //   painter.paint(canvas, const Offset(5, 5));
  // }

  Future<Uint8List> _rasterizeDrawingToImageData(
    Picture picture,
    MapTile tile,
  ) async {
    final image = await picture.toImage(
      TileConstants.tileSize,
      TileConstants.tileSize,
    );
    final bytes = await image.toByteData(format: ImageByteFormat.png);

    return bytes!.buffer.asUint8List(
      bytes.offsetInBytes,
      bytes.lengthInBytes,
    );
  }

  Future<void> _performDrawing(
    Canvas canvas,
    MapTile tile,
    List<(List<Polygon>, Selection)> geometryAndSelections,
    Palette palette,
    Crs crs,
  ) async {
    final tileSize = TileConstants.tileSize.toDouble();

    // FILL WITH WATER
    canvas.drawRect(
      Rect.fromLTWH(
        0,
        0,
        tileSize,
        tileSize,
      ),
      Paint()..color = palette.water,
    );

    final strokePaint = Paint()
      ..color = palette.border
      ..strokeWidth = 1.0
      ..style = PaintingStyle.stroke;

    final zoom = tile.z.toDouble();
    final tileOrigin = Point(tile.x * tileSize, tile.y * tileSize);

    if (geometryAndSelections.isEmpty) {
      return;
    }

    for (final record in geometryAndSelections) {
      final (polygons, selection) = record;
      final colour = palette.colorForSelection(selection);
      final fillPaint = Paint()..color = colour;

      for (final polygon in polygons) {
        final path = Path();

        final point =
            crs.latLngToPoint(polygon.coordinates.first.toLatLng(), zoom) -
                tileOrigin;
        path.moveTo(point.x, point.y);

        for (var i = 1; i < polygon.coordinates.length; i++) {
          final coordinate = polygon.coordinates[i].toLatLng();
          final point = crs.latLngToPoint(coordinate, zoom) - tileOrigin;
          path.lineTo(point.x, point.y);
        }
        path.close();

        canvas.drawPath(path, fillPaint);
        canvas.drawPath(path, strokePaint);
      }
    }
  }
}
