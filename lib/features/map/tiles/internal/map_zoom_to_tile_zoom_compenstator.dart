import 'package:flutter_map/flutter_map.dart';

mixin MapZoomToTileZoomCompensator {
  final minNativeZoom = 0.0;
  final maxNativeZoom = 19.0;

  /// Rounds the zoom to the nearest int and clamps it to the native zoom limits
  /// if there are any.
  int clampToTileZoom(MapCamera camera) => camera.zoom
      .round()
      .clamp(
        minNativeZoom,
        maxNativeZoom,
      )
      .toInt();
}
