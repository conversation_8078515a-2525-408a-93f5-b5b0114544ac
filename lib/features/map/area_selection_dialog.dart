import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';

import '../../dependency_injection/dependency_injector.dart';
import '../../generic_widgets/alert.dart';
import '../../generic_widgets/area_flag.dart';
import '../../generic_widgets/navigation_extensions.dart';
import '../../generic_widgets/platform_aware/platform_circle_button.dart';
import '../../generic_widgets/stadium_button.dart';
import '../../l10n/generated/app_localizations.dart';
import '../../models/geo_area.dart';
import '../../models/selection.dart';
import '../areas/replace_live_country_mixin.dart';
import '../areas/widgets/area_details_screen.dart';
import '../selection/selection_dialog.dart';
import '../sharing/content/country_sharable_content.dart';
import '../sharing/sharing_service.dart';
import 'selection_bar.dart';

class AreaSelectionDialog extends StatelessWidget
    with Presentable<void>, ReplaceLiveCountryMixin {
  final GeoArea area;

  const AreaSelectionDialog({super.key, required this.area});

  @override
  Widget build(BuildContext context) {
    return FutureBuilder<List<Selection>>(
      future: _findUsableSelections(context),
      builder: (context, snapshot) {
        final selections = snapshot.data ?? SelectionBar.defaultSelections;
        final bloc = DependencyInjector.areaSelectionBloc;
        return SelectionDialog(
          title: area.name,
          centerContent: _buildFlag(),
          availableSelections: selections,
          selection: bloc.listenToSelection(area),
          onChanged: (selection) async {
            if (selection == Selection.clear &&
                (await bloc.currentCountryUserLivesIn()) == area &&
                context.mounted) {
              await replaceLiveCountry(area: area, context: context);
              return;
            }

            bloc.select(area, selection);
          },
          trailing: area.hasDetails ? _buildDetailButtons(context) : null,
        );
      },
    );
  }

  Future<List<Selection>> _findUsableSelections(BuildContext context) async {
    final bloc = DependencyInjector.areaSelectionBloc;
    final live = await bloc.currentCountryUserLivesIn();
    final isLive = area == live;
    final selections = isLive
        ? [Selection.live, Selection.clear]
        : SelectionBar.defaultSelections;

    return selections;
  }

  Widget _buildDetailButtons(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Row(
        children: [
          if (DependencyInjector.sessionBloc.isAuthenticated && area.hasDetails)
            Expanded(
              child: StadiumButton(
                title: AppLocalizations.of(context)!.more,
                onTapped: () => Navigator.of(context).pushMaterialReplacement(
                  name: AreaDetailsScreen.routeName(area),
                  builder: (_) => AreaDetailsScreen(area: area),
                  fullscreen: true,
                ),
              ),
            )
          else
            const Expanded(child: SizedBox()),
          const SizedBox(width: 16),
          if (DependencyInjector.iapBloc.canAccessSubdivisions(area))
            PlatformCircleButton(
              onTapped: () => _shareAreaMap(context),
              fillColour: Theme.of(context).dividerColor,
              child: Icon(
                Theme.of(context).platform == TargetPlatform.iOS
                    ? CupertinoIcons.share
                    : Icons.share,
                color: Theme.of(context).textTheme.titleLarge?.color,
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildFlag() {
    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: AreaFlag(area: area, size: 175, useCachedSize: false),
    );
  }

  void _shareAreaMap(BuildContext context) async {
    final sharer = SharingService(
      context,
      content: CountrySharableContent(area),
    );
    sharer.export();
  }
}
