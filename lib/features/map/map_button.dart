import 'package:flutter/material.dart';

import '../../generic_widgets/platform_aware/platform_icon_button.dart';
import 'map_button_properties.dart';

class MapButton extends StatelessWidget with MapButtonProperties {
  const MapButton({
    super.key,
    required this.icon,
    required this.onTapped,
    this.childBuilder,
  });

  final IconData icon;
  final void Function(BuildContext context) onTapped;
  final Widget Function(BuildContext context, Widget icon)? childBuilder;
  static const margin = 4.0;

  @override
  Widget build(BuildContext context) {
    return SafeArea(
      child: PlatformIconButton(
        color: baseColour(context),
        onTapped: () => onTapped(context),
        icon: icon,
        childBuilder: childBuilder,
      ),
    );
  }
}
