import 'package:flutter/material.dart';

import '../../l10n/generated/app_localizations.dart';
import 'map_button_properties.dart';

class VisitedPopupMenu<T extends PopupMenuItemData> extends StatelessWidget
    with MapButtonProperties {
  const VisitedPopupMenu({
    super.key,
    required this.icon,
    required this.items,
    required this.onSelected,
    this.leftAligned = false,
  });

  final bool leftAligned;
  final IconData icon;
  final List<T> items;
  final void Function(BuildContext context, T item) onSelected;

  @override
  Widget build(BuildContext context) {
    return PopupMenuButton<T>(
      offset: Offset(20 * (leftAligned ? -1 : 1), 50),
      padding: EdgeInsets.zero,
      menuPadding: EdgeInsets.zero,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(18)),
      color: Theme.of(context).cardTheme.color?.withValues(alpha: 0.9),
      elevation: 3,
      itemBuilder: _buildItem,
      onSelected: (value) => onSelected(context, value),
      child: _buildIcon(context),
    );
  }

  List<PopupMenuEntry<T>> _buildItem(BuildContext context) => [
        for (int i = 0; i < items.length; i++) ...[
          _buildRow(context, items[i]),
          if (i < items.length - 1) const PopupMenuDivider(),
        ]
      ];

  Widget _buildIcon(BuildContext context) {
    return Icon(icon, color: Theme.of(context).textTheme.bodyMedium?.color);
  }

  PopupMenuItem<T> _buildRow(BuildContext context, T type) {
    final colour = Theme.of(context).textTheme.bodyMedium?.color;
    return PopupMenuItem(
      value: type,
      child: Row(
        children: [
          Icon(
            type.icon,
            color: colour,
          ),
          const SizedBox(width: 8),
          Text(
            type.title(AppLocalizations.of(context)!),
            style: TextStyle(
                color: colour, fontSize: 16, fontWeight: FontWeight.bold),
          ),
        ],
      ),
    );
  }
}

abstract class PopupMenuItemData {
  IconData get icon;
  String title(AppLocalizations localizations);
}
