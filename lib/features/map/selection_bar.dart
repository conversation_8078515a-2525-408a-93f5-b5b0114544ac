import 'package:flutter/material.dart';

import '../../models/selection.dart';
import 'selection_button.dart';

class SelectionBar extends StatelessWidget {
  final Stream<Selection> selection;
  final ValueChanged<Selection> onSelected;
  final List<Selection> selections;

  static const defaultSelections = [
    Selection.live,
    Selection.lived,
    Selection.been,
    Selection.want,
    Selection.clear,
  ];

  const SelectionBar({
    super.key,
    required this.selection,
    required this.onSelected,
    this.selections = defaultSelections,
  });

  @override
  Widget build(BuildContext context) {
    return StreamBuilder<Selection>(
      stream: selection,
      builder: (context, snapshot) {
        final values = [...selections];

        final currentSelection = snapshot.data ?? Selection.clear;

        if (currentSelection == Selection.clear) {
          values.remove(Selection.clear);
        }

        return SingleChildScrollView(
          scrollDirection: Axis.horizontal,
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: [
              for (final selection in values)
                _buildButton(selection, currentSelection, context)
            ],
          ),
        );
      },
    );
  }

  Widget _buildButton(
      Selection selection, Selection currentSelection, BuildContext context) {
    return SelectionButton(
      selection: selection,
      selected: currentSelection == selection,
      onTapped:
          currentSelection != selection ? () => onSelected(selection) : null,
    );
  }
}
