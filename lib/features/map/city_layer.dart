import 'dart:async';

import 'package:collection/collection.dart';
import 'package:flutter/material.dart';
import 'package:flutter_map/flutter_map.dart';

import '../../dependency_injection/dependency_injector.dart';
import '../../generic_widgets/spinner.dart';
import '../../models/selection.dart';
import '../cities/city.dart';
import 'city_marker_layer_builder.dart';

class CityLayer extends StatefulWidget {
  const CityLayer({
    super.key,
    required this.eventNotifier,
  });

  final ValueNotifier<MapEvent?> eventNotifier;

  @override
  State<CityLayer> createState() => _CityLayerState();
}

class _CityLayerState extends State<CityLayer> {
  int lastZoomLevel = 0;
  late final StreamSubscription citySubscription;
  var sortedCities = <List<MapEntry<City, Selection>>>[];
  var loading = true;

  @override
  void initState() {
    super.initState();
    _update();

    final bloc = DependencyInjector.cityBloc;
    citySubscription = bloc.selections.listen(_onCitiesUpdated);
    widget.eventNotifier.addListener(_update);
  }

  void _onCitiesUpdated(Map<City, Selection> event) {
    final sortedCities = event.entries
        .groupListsBy((entry) => entry.key.geoAreaIsoCode)
        .values
        .toList();
    setState(() {
      loading = false;
      this.sortedCities = sortedCities;
    });
  }

  @override
  void dispose() {
    widget.eventNotifier.removeListener(_update);
    citySubscription.cancel();
    super.dispose();
  }

  void _update() async {
    final camera = widget.eventNotifier.value?.camera;
    if (camera == null) {
      return;
    }
    final zoom = camera.zoom.truncate();
    if (zoom == lastZoomLevel) {
      return;
    }
    setState(() {
      lastZoomLevel = zoom;
    });
  }

  @override
  Widget build(BuildContext context) {
    if (loading) {
      return const Center(child: Spinner());
    }

    final allCities = sortedCities.expand((grouped) => grouped);

    return MarkerLayer(
      markers: [
        for (final entry in allCities)
          buildCityMarker(context, entry.key, entry.value),
      ],
    );
  }
}
