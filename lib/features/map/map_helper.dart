import 'package:flutter/material.dart';
import 'package:flutter_map/flutter_map.dart';
import 'package:latlong2/latlong.dart';

import '../../dependency_injection/dependency_injector.dart';
import '../../models/coordinate.dart';
import '../../models/geo_bounds.dart';
import 'tiles/internal/coordinate_extension.dart';
import 'tiles/tile_constants.dart';

mixin MapHelper {
  MapOptions buildMapOptions(LatLngBounds bounds) {
    return MapOptions(
      initialCenter: bounds.center,
      initialCameraFit: CameraFit.insideBounds(
        bounds: bounds,
        maxZoom: 3,
        minZoom: 1,
        padding: const EdgeInsets.all(32),
      ),
      crs: DependencyInjector.settingsBloc.currentSettings.projection,
      initialZoom: 0.6,
      cameraConstraint: CameraConstraint.contain(
        bounds: LatLngBounds(
          GeoBounds.wholePlanet.southWest.toLatLng(),
          GeoBounds.wholePlanet.northEast.toLatLng(),
        ),
      ),
      maxZoom: TileConstants.maxTileDepth.toDouble(),
      interactionOptions: const InteractionOptions(
        flags: InteractiveFlag.all & ~InteractiveFlag.rotate,
        rotationWinGestures: MultiFingerGesture.none,
      ),
    );
  }

  LatLngBounds getBounds(List<Coordinate> coordinates) {
    return Polygon(points: convertCoordinateList(coordinates)).boundingBox;
  }

  List<LatLng> convertCoordinateList(List<Coordinate> coordinates) {
    return [
      for (final coordinate in coordinates)
        if (!coordinate.isCenter) coordinate.toLatLng()
    ];
  }

  Widget markerLayerBuilder<T>({
    required List<T> items,
    required BuildContext context,
    required Marker Function(BuildContext context, T item) markerBuilder,
  }) {
    return MarkerLayer(
      markers: [
        for (final item in items) markerBuilder(context, item),
      ],
    );
  }
}
