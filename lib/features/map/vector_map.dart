import 'dart:async';
import 'dart:math';

import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';

import '../../dependency_injection/dependency_injector.dart';
import '../../generic_widgets/alert.dart';
import '../../generic_widgets/selectable_item.dart';
import '../../generic_widgets/spinner.dart';
import '../../models/color_extensions.dart';
import '../../models/coordinate.dart';
import '../../models/geo_area.dart';
import '../../models/geo_bounds.dart';
import '../../models/label.dart';
import '../../models/palette.dart';
import '../../models/selection.dart';
import 'coordinate_transformer.dart';
import 'geometry_painter.dart';
import 'map_screen.dart';
import 'projection.dart';
import 'tiles/internal/geometry_repository.dart';

const bool kShowCoordinate = false;

class VectorMap extends StatefulWidget {
  final GeoArea? area;
  final ValueChanged<GeoArea> onAreaTapped;
  final MapMode mode;

  const VectorMap({
    super.key,
    this.area,
    required this.onAreaTapped,
    this.mode = MapMode.countries,
  });

  @override
  State createState() => _VectorMapState();
}

class _VectorMapState extends State<VectorMap> {
  PolygonLookup? geometry;
  GeoBounds? customBounds;
  final mapController = TransformationController();
  var currentScale = 1.0;
  bool hasCentredCamera = false;
  var mapKey = UniqueKey();
  bool get showWholePlanet => widget.area == null;
  StreamSubscription? _changesSubscription;
  StreamSubscription? _paletteSubscription;
  StreamSubscription? _disputedTerritoriesSubscription;

  Map<GeoArea, Selection>? _selections;

  Size? _mapSize;

  double get maxScale => showWholePlanet ? 40.0 : 80.0;

  @override
  void initState() {
    super.initState();
    _loadGeometry();

    _changesSubscription ??=
        DependencyInjector.areaSelectionBloc.selections.listen(
      (selections) {
        _selections = selections;
        _refreshMap();
      },
    );

    _paletteSubscription ??= DependencyInjector.settingsBloc.palette.listen(
      (_) => _refreshMap(),
    );

    _disputedTerritoriesSubscription ??= DependencyInjector
        .disputedTerritoriesBloc.disputedTerritoriesChanged
        .listen(
      (_) {
        _loadGeometry();
      },
    );
  }

  void _loadGeometry() async {
    if (showWholePlanet) {
      _loadAllCountries();
    } else {
      _loadCountryGeometry();
    }
  }

  void _loadAllCountries() async {
    final countries =
        await DependencyInjector.geometryBloc.fetchCountryPolygons(
      lowRes: true,
    );
    if (mounted) {
      setState(() {
        geometry = countries;
        mapKey = UniqueKey();
      });
    }
  }

  void _loadCountryGeometry() async {
    final area = widget.area;
    if (area == null) {
      return _loadAllCountries();
    }

    final tuple =
        await DependencyInjector.geometryBloc.fetchPolygonsForArea(area);

    if (mounted) {
      setState(() {
        geometry = tuple.$1;
        customBounds = tuple.$2;
        mapKey = UniqueKey();
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    if (geometry == null) {
      return const Center(
        child: Spinner(),
      );
    }

    return _buildInteractiveViewer();
  }

  Widget _buildInteractiveViewer() {
    return LayoutBuilder(
      builder: (context, constraints) {
        final mapSize = _calculateMapSize(constraints);

        if (!hasCentredCamera) {
          _moveCameraToLiveCountry(mapSize, constraints.biggest);
          hasCentredCamera = true;
        }

        return InteractiveViewer(
          transformationController: mapController,
          maxScale: maxScale,
          minScale: 1.0,
          constrained: false,
          onInteractionUpdate: (detail) {
            if (detail.scale != currentScale && mounted) {
              setState(() => currentScale = detail.scale);
            }
          },
          child: GestureDetector(
              onTapUp: (details) =>
                  _onMapTapped(details.localPosition, mapSize),
              child: Stack(
                children: [
                  Positioned.fill(
                      child: DecoratedBox(
                    decoration: BoxDecoration(
                        color: DependencyInjector
                            .settingsBloc.currentPalette.water),
                  )),
                  _buildMapPainter(mapSize: mapSize),
                  ..._buildMapLabels(mapSize: mapSize),
                ],
              )),
        );
      },
    );
  }

  Iterable<Widget> _buildMapLabels({
    required Size mapSize,
  }) sync* {
    final palette = DependencyInjector.settingsBloc.currentPalette;
    for (final entry in geometry!.entries) {
      final area = DependencyInjector.areaBloc.areaByIsoCodeSync(entry.key);
      final labels = area?.labels;
      if (area == null || labels == null) {
        continue;
      }
      for (final label in labels) {
        if (_shouldRenderLabel(label)) {
          yield _buildMapLabel(
            area: area,
            label: label,
            mapSize: mapSize,
            palette: palette,
          );
        }
      }
    }
  }

  // Widget _buildCities({required Size mapSize}) {
  //   return StreamBuilder<Map<City, Selection>>(
  //     stream: DependencyInjector.cityBloc.selections,
  //     builder: (context, snapshot) {
  //       if (snapshot.hasData == false) {
  //         return const SizedBox();
  //       }
  //
  //       final selections = snapshot.requireData;
  //
  //       return Positioned.fill(
  //         child: CityAnnotations(
  //           cities: selections,
  //           bounds: bounds,
  //           mapController: mapController,
  //           mapSize: mapSize,
  //           maxZoom: maxScale.round(),
  //         ),
  //       );
  //     },
  //   );
  // }

  Size _calculateMapSize(BoxConstraints constraints) {
    if (_mapSize != null) {
      return _mapSize!;
    }

    final height = constraints.biggest.height;

    final area = widget.area;

    if (area == null) {
      return Size(height * 2, height);
    }

    final width = constraints.biggest.width;

    final size = area.bounds.calculateSize();

    Size renderSize;
    if (size.width > size.height) {
      renderSize = Size(height * size.aspectRatio, height);
    } else {
      renderSize = Size(width, width / size.aspectRatio);
    }

    _mapSize = renderSize;
    return renderSize;
  }

  bool _shouldRenderLabel(Label label) {
    final resolution =
        showWholePlanet ? label.resolution : max(0, label.resolution - 5);
    return mapController.value.getMaxScaleOnAxis() >= resolution * 1.5;
  }

  Widget _buildMapLabel({
    required GeoArea area,
    required Label label,
    required Size mapSize,
    required Palette palette,
  }) {
    var xModifier = 0.0;
    //TODO: Figure out how to remove this
    if (area.isoCode == 'GB') {
      xModifier = -9;
    }

    final transformer = CoordinateTransformer(
      bounds: bounds,
      size: mapSize,
      projection: EquirectangularProjection(mapSize.width),
    );

    final screenPosition = transformer.normalize(label.coordinate);
    final selection = _getSelection(area);

    final selectionColour = DependencyInjector.settingsBloc.currentPalette
        .colorForSelection(selection);
    final labelColour = selectionColour.legibleForegroundColor();

    final style = TextStyle(
      fontSize: labelFontSize,
      fontWeight: FontWeight.w700,
      color: labelColour,
    );

    const maxWidth = 100.0;
    const maxLines = 3;

    final painter = TextPainter(
      text: TextSpan(text: area.name, style: style),
      maxLines: maxLines,
      textDirection: TextDirection.ltr,
      // textScaleFactor: 1.0, MediaQuery.of(context).textScaler.scale,
      textWidthBasis: TextWidthBasis.longestLine,
    )..layout(maxWidth: maxWidth);

    final size = painter.size;

    return Positioned(
      top: screenPosition.y - size.height / 2,
      left: screenPosition.x - size.width / 2 + xModifier,
      child: Transform.scale(
        scale: 1 / mapController.value.getMaxScaleOnAxis(),
        child: ConstrainedBox(
          constraints: const BoxConstraints(maxWidth: maxWidth),
          child: Stack(
            children: [
              if (labelColour != palette.label)
                Text(
                  area.name,
                  textAlign: TextAlign.center,
                  style: TextStyle(
                    fontSize: labelFontSize,
                    fontWeight: FontWeight.bold,
                    foreground: Paint()
                      ..style = PaintingStyle.stroke
                      ..strokeWidth = 0.1
                      ..color = palette.label,
                  ),
                  maxLines: maxLines,
                ),
              Text(
                area.name,
                textAlign: TextAlign.center,
                style: style,
                maxLines: maxLines,
              ),
            ],
          ),
        ),
      ),
    );
  }

  Selection _getSelection(SelectableItem area) {
    var selection = Selection.clear;
    if (widget.mode == MapMode.countries && _selections != null) {
      selection = _selections![area] ?? Selection.clear;
    }
    return selection;
  }

  double get labelFontSize => 14;

  Widget _buildMapPainter({required Size mapSize}) {
    final palette = DependencyInjector.settingsBloc.currentPalette;

    final customPaint = CustomPaint(
      size: mapSize,
      isComplex: true,
      painter: GeometryPainter(
        key: mapKey,
        geometry: geometry!,
        palette: palette,
        bounds: bounds,
        projection: EquirectangularProjection(mapSize.width),
        selectionGetter: _getSelection,
      ),
    );

    return showWholePlanet
        ? RepaintBoundary(key: ValueKey(widget.mode), child: customPaint)
        : customPaint;
  }

  GeoBounds get bounds =>
      customBounds ?? widget.area?.bounds ?? GeoBounds.wholePlanet;

  void _onMapTapped(Offset position, Size mapSize) async {
    final point = Point(position.dx, position.dy);

    if (kDebugMode && kShowCoordinate) {
      final transformer = CoordinateTransformer(
          bounds: bounds,
          size: mapSize,
          projection: EquirectangularProjection(mapSize.width));
      final coordinate = transformer.denormalize(point);

      debugPrint(coordinate.toString());

      final alert = ConfirmDialog(
        title: 'Tapped!',
        message: coordinate.toString(),
        confirmText: 'Continue Geocoding?',
        cancelText: 'Close',
      );
      final results = await alert.show(context);
      if (results != true) {
        return;
      }
    }

    if (!mounted) {
      return;
    }

    final area = await DependencyInjector.geometryBloc.geocode(
      position: point,
      mapSize: mapSize,
      bounds: bounds,
      searchableAreas: geometry,
    );

    if (area == null) {
      return;
    }

    widget.onAreaTapped(area);
  }

  void _moveCameraToLiveCountry(Size mapSize, Size screenSize) async {
    final country = widget.area ??
        await DependencyInjector.areaSelectionBloc.currentCountryUserLivesIn();

    final coordinate =
        country != null ? _calculateInitialCoordinate(country) : bounds.centre;

    const scale = 1.5;
    mapSize *= scale;

    var cameraCentre = coordinate.renderablePoint(mapSize);
    cameraCentre = Point(
      max(
          0,
          min(cameraCentre.x - (screenSize.width / 2),
              mapSize.width - screenSize.width)),
      max(
        0,
        min(cameraCentre.y - (screenSize.height / 2), screenSize.height / 2),
      ),
    );

    mapController.value = Matrix4.identity()
      ..translate(-cameraCentre.x, -cameraCentre.y)
      ..scale(scale);
  }

  Coordinate _calculateInitialCoordinate(GeoArea area) {
    return area.renderingBounds.centre;
  }

  void _refreshMap() {
    if (!hasCentredCamera) {
      return;
    }

    if (mounted) {
      setState(() => mapKey = UniqueKey());
    }
  }

  @override
  void dispose() {
    mapController.dispose();
    _changesSubscription?.cancel();
    _paletteSubscription?.cancel();
    _disputedTerritoriesSubscription?.cancel();
    super.dispose();
  }
}
