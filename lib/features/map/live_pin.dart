// import 'dart:async';
//
// import 'package:flutter/material.dart';
// import 'package:rxdart/rxdart.dart';
//
// import '../../dependency_injection/dependency_injector.dart';
// import '../../models/geo_bounds.dart';
// import '../../models/selection.dart';
// import '../cities/city.dart';
// import '../cities/map_annotation.dart';
// import '../cities/live_city_dialog.dart';
// import 'map_annotation.dart';
//
// class LivePin extends StatefulWidget {
//   const LivePin({
//     super.key,
//     required this.bounds,
//     required this.mapController,
//     required this.mapSize,
//   });
//
//   final Size mapSize;
//   final GeoBounds bounds;
//   final TransformationController mapController;
//
//   @override
//   State createState() => _LivePinState();
// }
//
// class _LivePinState extends State<LivePin> {
//   City? _city;
//   late StreamSubscription<City?> _subscription;
//
//   @override
//   void initState() {
//     super.initState();
//     _subscription = _cityStream(context).listen((city) {
//       setState(
//         () => _city = city,
//       );
//     });
//   }
//
//   @override
//   Widget build(BuildContext context) {
//     final city = _city;
//     if (city == null) {
//       return const SizedBox();
//     }
//
//     return MapAnnotation(
//       selection: Selection.live,
//       coordinate: city.coordinate,
//       mapController: widget.mapController,
//       mapSize: widget.mapSize,
//       bounds: widget.bounds,
//       builder: (context) {
//         return MapAnnotation(
//           selection: Selection.live,
//           city: city,
//           onTapped: () => LiveCityDialog(city: city).show(context),
//         );
//       },
//     );
//   }
//
//   Stream<City?> _cityStream(BuildContext context) {
//     return Rx.combineLatest2<bool, City?, City?>(
//         DependencyInjector.settingsBloc.showLivedPin,
//         DependencyInjector.cityBloc.livedCity, (show, city) {
//       if (!show) {
//         return null;
//       }
//
//       return city;
//     });
//   }
//
//   @override
//   void dispose() {
//     _subscription.cancel();
//     super.dispose();
//   }
// }
