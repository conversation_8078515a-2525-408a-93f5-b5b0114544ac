import 'package:flutter/material.dart';
import 'package:flutter_map/flutter_map.dart';

import '../../models/selection.dart';
import '../cities/city.dart';
import '../cities/city_selection_dialog.dart';
import '../cities/live_city_dialog.dart';
import '../cities/map_annotation.dart';
import 'tiles/internal/coordinate_extension.dart';

Marker buildCityMarker(
  BuildContext context,
  City city,
  Selection? selection,
) {
  return Marker(
    point: city.coordinate.toLatLng(),
    alignment: Alignment.center,
    child: MapAnnotation(
      selection: selection!,
      onTapped: () async {
        await (selection == Selection.live
            ? LiveCityDialog(city: city).show(context)
            : CitySelectionDialog(city: city).show(context));
      },
    ),
  );
}
