import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';

import '../../dependency_injection/dependency_injector.dart';
import '../../generic_widgets/platform_aware/platform_circle_button.dart';
import '../../l10n/generated/app_localizations.dart';
import '../../models/color_extensions.dart';
import '../../models/selection.dart';

class SelectionButton extends StatelessWidget {
  const SelectionButton({
    super.key,
    required this.selection,
    required this.selected,
    this.onTapped,
  });

  final Selection selection;
  final bool selected;
  final VoidCallback? onTapped;

  @override
  Widget build(BuildContext context) {
    final palette = DependencyInjector.settingsBloc.currentPalette;
    final fillColour = selected
        ? palette.colorForSelection(selection)
        : Theme.of(context).dividerColor;

    return Padding(
      padding: const EdgeInsets.all(4.0),
      child: Column(
        children: [
          PlatformCircleButton(
            onTapped: onTapped,
            fillColour: fillColour,
            disabledColour: palette.colorForSelection(selection),
            child: Padding(
              padding: const EdgeInsets.all(8.0),
              child: SvgPicture.asset(
                selection.svgPath,
                colorFilter: ColorFilter.mode(
                  fillColour.legibleForegroundColor(),
                  BlendMode.srcIn,
                ),
              ),
            ),
          ),
          Padding(
            padding: const EdgeInsets.symmetric(vertical: 8.0),
            child: Text(selection.localized(AppLocalizations.of(context)!)),
          ),
        ],
      ),
    );
  }
}
