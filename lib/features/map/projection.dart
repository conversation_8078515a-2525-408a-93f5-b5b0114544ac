import 'dart:math';
import 'dart:ui';

import '../../models/coordinate.dart';

abstract class Projection {
  final double width;
  final double aspectRatio = 1.0;

  Size get size => Size(width, width * aspectRatio);

  const Projection(this.width);

  Point<double> toScreenPosition(Coordinate coordinate);
  // Coordinate toCoordinate(Point<double> screenPosition);
}

class EquirectangularProjection extends Projection {
  const EquirectangularProjection(super.width);

  @override
  double get aspectRatio => 0.5;

  @override
  Point<double> toScreenPosition(Coordinate coordinate) {
    final size = this.size;

    final relative = coordinate.screenPoint;
    return Point(relative.x * size.width, relative.y * size.height);
  }
}

class MillerProjection extends Projection {
  const MillerProjection(super.width);

  @override
  double get aspectRatio => 0.81; //0.75469387755;

  @override
  Point<double> toScreenPosition(Coordinate coordinate) {
    final size = this.size;

    final long = toRadian(coordinate.longitude);
    final lat = toRadian(coordinate.latitude);

    var x = long;
    var y = 1.25 * log(tan(0.25 * pi + 0.4 * lat));

    x = (size.width / 2) + (size.width / (2 * pi)) * x;
    y = (size.height / 2) - (size.height / (2 * 2.303412543)) * y;

    y += 140;

    return Point(x, y);
  }

  double toRadian(num value) {
    return value * pi / 180;
  }
}

// public final double W = 6343;
// public final double H = 4767 - 34;
//
// protected Point toMillerXY(double lon, double lat)
// {
//   double x, y;
//
//   lon = Utils.degToRad(lon);
//   lat = Utils.degToRad(lat);
//
//   x = lon - CENTRAL_MERIDIAN_OFFSET;
//   y = 1.25 * Math.log( Math.tan( 0.25 * Math.PI + 0.4 * lat ) );
//
//   x = ( W / 2 ) + ( W / (2 * Math.PI) ) * x;
//   y = ( H / 2 ) - ( H / ( 2 * 2.303412543 ) ) * y;
//
//   y += 34;
//
//   return new Point(x, y);
// }
