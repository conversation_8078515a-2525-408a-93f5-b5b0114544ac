import 'package:flutter/material.dart';

import '../../dependency_injection/dependency_injector.dart';
import '../../generic_widgets/selectable_item.dart';
import '../../models/geo_area.dart';
import '../../models/geo_bounds.dart';
import '../../models/palette.dart';
import '../../models/polygon.dart';
import '../../models/selection.dart';
import 'coordinate_transformer.dart';
import 'polygon_transformer.dart';
import 'projection.dart';
import 'tiles/internal/geometry_repository.dart';

typedef SelectionGetter = Selection Function(SelectableItem area);

class GeometryPainter extends CustomPainter {
  const GeometryPainter({
    this.key,
    required this.geometry,
    required this.selectionGetter,
    required this.palette,
    this.bounds = GeoBounds.wholePlanet,
    this.borderWidth = 0.1,
    required this.projection,
    this.shouldRenderArea,
    this.annotations,
    this.renderWater = true,
    this.renderFill = true,
    this.renderBorder = true,
  });

  final Key? key;
  final PolygonLookup geometry;
  final Palette palette;
  final GeoBounds bounds;
  final double borderWidth;
  final SelectionGetter selectionGetter;
  final Projection projection;
  final bool Function(String area)? shouldRenderArea;
  final bool renderWater;
  final bool renderBorder;
  final bool renderFill;
  final Map<MapDisplayable, Selection>? annotations;

  @override
  void paint(Canvas canvas, Size size) {
    if (renderWater) {
      canvas.drawColor(
        palette.water,
        BlendMode.src,
      );
    }

    final countryPaint = Paint();

    final strokePaint = Paint()
      ..color = palette.border
      ..strokeWidth = borderWidth
      ..strokeJoin = StrokeJoin.round
      ..style = PaintingStyle.stroke;

    for (final entry in geometry.entries) {
      if (shouldRenderArea?.call(entry.key) == false) {
        continue;
      }

      final area = DependencyInjector.areaBloc.areaByIsoCodeSync(entry.key);
      if (area != null) {
        countryPaint.color = _colorForArea(area);
      }

      _drawPolygonsWithShapes(
        size,
        entry,
        canvas,
        countryPaint,
        strokePaint,
      );
    }

    if (annotations != null) {
      final transformer = CoordinateTransformer(
        bounds: bounds,
        size: size,
        projection: projection,
      );

      for (final annotation in annotations!.entries) {
        final coordinate = transformer.normalize(annotation.key.coordinate);
        canvas.drawCircle(Offset(coordinate.x, coordinate.y), 1,
            Paint()..color = palette.colorForSelection(annotation.value));
      }
    }
  }

  void _drawPolygonsWithShapes(
    Size size,
    MapEntry<String, List<Polygon>> entry,
    Canvas canvas,
    Paint countryPaint,
    Paint strokePaint,
  ) {
    final paths = pathsFromPolygons(size, entry.value);
    for (final path in paths) {
      canvas.drawPath(path, countryPaint);
      canvas.drawPath(path, strokePaint);
      if (renderFill) {
        canvas.drawPath(path, countryPaint);
      }

      if (renderBorder) {
        canvas.drawPath(path, strokePaint);
      }
    }
  }

  Color _colorForArea(GeoArea area) {
    final selection = selectionGetter(area);
    return palette.colorForSelection(selection);
  }

  Iterable<Path> pathsFromPolygons(Size size, List<Polygon> polygons) {
    return polygons.map<Path>((e) {
      final renderer = PolygonTransformer(
        polygon: e,
        size: size,
        bounds: bounds,
        projection: projection,
      );
      return renderer.normalize();
    });
  }

  @override
  bool shouldRepaint(covariant GeometryPainter oldDelegate) {
    return key != oldDelegate.key;
  }
}
