import 'dart:math';
import 'dart:ui';

import 'projection.dart';
import '../../models/coordinate.dart';
import '../../models/geo_bounds.dart';

class CoordinateTransformer {
  final GeoBounds bounds;
  final Size size;
  final Projection projection;

  CoordinateTransformer({
    required this.bounds,
    required this.size,
    required this.projection,
  });

  Point<double> normalize(Coordinate coordinate) {
    final min = projection.toScreenPosition(bounds.northWest);
    final max = projection.toScreenPosition(bounds.southEast);

    final relative = projection.toScreenPosition(coordinate);

    final normalizedX = _normalize(relative.x, min.x, max.x);
    final normalizedY = _normalize(relative.y, min.y, max.y);

    return Point(normalizedX * size.width, normalizedY * size.height);
  }

  Coordinate denormalize(Point<num> point) {
    final min = bounds.northWest.screenPoint;
    final max = bounds.southEast.screenPoint;

    final normalizedX = point.x / size.width;
    final normalizedY = point.y / size.height;

    final relativeX = _denormalize(normalizedX, min.x, max.x);
    final relativeY = _denormalize(normalizedY, min.y, max.y);

    return Coordinate.fromRelativePosition(Point(relativeX, relativeY));
  }

  double _normalize(num value, num min, num max) {
    return (value - min) / (max - min);
  }

  double _denormalize(double value, num min, num max) {
    return lerpDouble(min, max, value)!;
  }
}
