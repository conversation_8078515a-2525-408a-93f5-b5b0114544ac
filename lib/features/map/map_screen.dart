import 'package:debounce_throttle/debounce_throttle.dart';
import 'package:flutter/material.dart' hide SearchBar;

import '../../dependency_injection/dependency_injector.dart';
import '../../generic_widgets/alert.dart';
import '../../generic_widgets/area_flag.dart';
import '../../generic_widgets/navigation_extensions.dart';
import '../../generic_widgets/platform_aware/platform_app_bar.dart';
import '../../generic_widgets/search_bar.dart';
import '../../generic_widgets/selectable_item.dart';
import '../../generic_widgets/streaming_selectable_tile.dart';
import '../../generic_widgets/visited_toggle_bar.dart';
import '../../helpers/margin.dart';
import '../../helpers/string_extensions.dart';
import '../../l10n/generated/app_localizations.dart';
import '../../models/geo_area.dart';
import '../../models/selection.dart';
import '../areas/area_list_picker.dart';
import '../areas/area_tile.dart';
import '../areas/my_country_selections_screen.dart';
import '../cities/city.dart';
import '../cities/city_picker.dart';
import '../cities/city_selection_dialog.dart';
import '../cities/city_selections_screen.dart';
import '../cities/free_cities_remaining_counter.dart';
import '../cities/missing_cities_tile.dart';
import '../cities/paywall_dialog.dart';
import '../in_app_purchase/iap_product.dart';
import '../settings/customize_screen.dart';
import '../sharing/content/city_sharable_content.dart';
import '../sharing/content/country_sharable_content.dart';
import '../sharing/content/sharable_content.dart';
import '../sharing/export_button.dart';
import '../sharing/sharing_service.dart';
import 'area_selection_dialog.dart';
import 'legend.dart';
import 'map_button.dart';
import 'map_screen_tutorial.dart';
import 'tiles/tiled_map.dart';
import 'vector_map.dart';
import 'visited_popup_menu.dart';

enum MapMode { countries, cities, list }

class MapScreen extends StatefulWidget {
  /// Leave the area null if you want to render the whole world
  const MapScreen({super.key, this.area, this.useLegacy = false});

  static String routeName(GeoArea? area) => 'map/${area?.isoCode ?? 'world'}';

  final GeoArea? area;
  final bool useLegacy;

  @override
  State createState() => _MapScreenState();
}

class _MapScreenState extends State<MapScreen> {
  var mode = MapMode.countries;

  final _searchController = TextEditingController();
  final _debouncer = Debouncer<String>(
    const Duration(milliseconds: 200),
    initialValue: '',
  );

  List<SelectableItem>? _searchResults;

  @override
  void initState() {
    super.initState();
    _showTutorial();

    _searchController.addListener(() {
      if (mode == MapMode.cities) {
        _debouncer.setValue(_searchController.text);
      }
    });

    _debouncer.values.listen((query) async {
      if (query.isEmpty) {
        setState(() {
          _searchResults = null;
        });
        return;
      }

      final cityBloc = DependencyInjector.cityBloc;

      final remaining = await cityBloc.freeSelectionsRemaining().first;
      if (remaining == 0) {
        _showCitiesPaywall();
        return;
      }

      final results = await cityBloc.search(query);
      if (mounted) {
        setState(() {
          _searchResults = results;
        });
      }
    });
  }

  void _showTutorial() async {
    final tutorial = MapScreenTutorial();
    tutorial.showOnlyOnce(
      context: context,
      hasShownKey: 'com.visited.didShowMapTutorial',
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: (widget.area != null)
          ? PlatformAppBar(title: widget.area!.name)
          : null,
      backgroundColor: DependencyInjector.settingsBloc.currentPalette.water,
      body: _buildContent(),
    );
  }

  Widget _buildContent() {
    return Stack(
      alignment: Alignment.center,
      children: [
        if (mode == MapMode.list)
          AreaListPicker(area: widget.area, showAppBar: false)
        else
          widget.useLegacy
              ? VectorMap(area: widget.area, onAreaTapped: _onAreaTapped)
              : TiledMap(mode: mode, onAreaTapped: _onAreaTapped),
        _buildTopBar(context),
        _buildControls(),
        if (mode == MapMode.cities)
          const Align(
            alignment: Alignment.bottomRight,
            child: SafeArea(
              child: Padding(
                padding: EdgeInsets.all(16.0),
                child: FreeCitiesRemainingCounter(),
              ),
            ),
          ),
        if (_searchResults != null) ..._buildSearchResults(),
      ],
    );
  }

  List<Widget> _buildSearchResults() {
    return [
      Positioned.fill(
        top: 70,
        child: SafeArea(
          child: GestureDetector(
            behavior: HitTestBehavior.opaque,
            onTap: _clearSearch,
            child: const SizedBox(),
          ),
        ),
      ),
      Positioned.fill(
        top: 60,
        child: SafeArea(
          child: Padding(
            padding: const EdgeInsets.all(16.0),
            child: CustomScrollView(
              slivers: [
                SliverList.builder(
                  itemCount: _searchResults!.length,
                  itemBuilder: (context, index) {
                    final item = _searchResults![index];
                    if (item is City) {
                      final area = DependencyInjector.areaBloc
                          .areaByIsoCodeSync(item.geoAreaIsoCode);
                      return SelectableTile(
                        leading: area != null ? AreaFlag(area: area) : null,
                        item: item,
                        currentSelection: Selection.clear,
                        nameBuilder: DependencyInjector.cityBloc.fullCityName(
                          item,
                        ),
                        onTapped: () {
                          CitySelectionDialog(
                            city: item,
                            availableSelections: [
                              if (DependencyInjector.iapBloc.canSelectLived)
                                Selection.lived,
                              Selection.been,
                              Selection.want,
                              Selection.clear,
                            ],
                          ).show(context);
                          _clearSearch();
                          // _onAreaTapped(item);
                        },
                      );
                    }

                    if (item is! GeoArea) {
                      return const SizedBox();
                    }

                    return AreaTile(
                      area: item,
                      onTapped: () {
                        _clearSearch();
                        _onAreaTapped(item);
                      },
                    );
                  },
                ),
                if (mode == MapMode.cities)
                  MissingCitiesTile(
                    backgroundColor: Theme.of(context).scaffoldBackgroundColor,
                  ),
              ],
            ),
          ),
        ),
      ),
    ];
  }

  Widget _buildControls() {
    return Positioned(
      bottom: Margin.standard,
      left: Margin.standard,
      right: Margin.standard,
      child: SafeArea(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            DependencyInjector.sessionBloc.isAuthenticated
                ? _buildCountriesCityToggle()
                : const SizedBox(),
            _buildLegend(context),
          ],
        ),
      ),
    );
  }

  Widget _buildLegend(BuildContext context) {
    return StreamBuilder<bool>(
      stream: DependencyInjector.settingsBloc.showLegend,
      initialData: true,
      builder: (context, snapshot) {
        if (!(snapshot.data ?? false)) {
          return const SizedBox();
        }

        return const Legend();
      },
    );
  }

  void _onAreaTapped(GeoArea area) async {
    if (mode == MapMode.countries) {
      final dialog = AreaSelectionDialog(area: area);
      dialog.show(context, barrierDismissible: true);
      return;
    }

    if (mode == MapMode.cities) {
      final topLevelArea = area.parentId == null
          ? area
          : await DependencyInjector.areaBloc.fetchParent(area) ?? area;

      if (mounted) {
        Navigator.of(context).pushMaterialRoute(
          name: CityPicker.routeName(topLevelArea),
          fullscreen: true,
          builder: (_) => CityPicker(area: topLevelArea),
        );
      }
    }
  }

  Widget _buildCountriesCityToggle() {
    final localizations = AppLocalizations.of(context)!;
    return VisitedToggleBar<MapMode>(
      items: _isViewingWholePlanet
          ? [
              ToggleItem(
                label: localizations.countries.capitalized,
                value: MapMode.countries,
              ),
              ToggleItem(label: localizations.cities, value: MapMode.cities),
            ]
          : [
              ToggleItem(
                label: localizations.map.capitalized,
                value: MapMode.countries,
              ),
              ToggleItem(label: localizations.list, value: MapMode.list),
            ],
      selected: mode,
      onSelected: (value) async {
        _clearSearch();
        if (value == MapMode.cities) {
          final limitCities = await DependencyInjector.featureFlags
              .limitCitySelections();
          if (!DependencyInjector.iapBloc.hasUnlockedCities && !limitCities) {
            _showCitiesPaywall();
            return;
          }

          _showCitiesInstructionsDialog();
        }

        setState(() {
          mode = value;
          DependencyInjector.tileRenderingService.refresh();
        });
      },
    );
  }

  void _showCitiesInstructionsDialog() async {
    final cityBloc = DependencyInjector.cityBloc;
    final hasShown = await cityBloc.hasShowedInstructions();
    if (hasShown) {
      return;
    }

    cityBloc.saveInstructionsHaveBeenShown();

    if (!mounted) {
      return;
    }

    Alert(
      title: AppLocalizations.of(context)!.cities,
      message: AppLocalizations.of(context)!.citiesInstructions,
    ).show(context, barrierDismissible: true);
  }

  void _showCitiesPaywall() async {
    final purchased = await PaywallDialog(
      feature: IAPFeature.unlockCities,
    ).show(context);
    if (purchased == true) {
      _showCitiesInstructionsDialog();
      setState(() {
        mode = MapMode.cities;
      });
    }
  }

  Widget _buildShareButton(BuildContext context) {
    return Semantics(
      button: true,
      label: AppLocalizations.of(context)!.shareMap,
      child: ExportButton(onShareTapped: () => _share(context)),
    );
  }

  Widget _buildPopupMenuButton(BuildContext context) {
    return Semantics(
      button: true,
      label: AppLocalizations.of(context)!.more,
      child: VisitedPopupMenu<MapSideMenuOption>(
        icon: Icons.menu,
        leftAligned: true,
        items: MapSideMenuOption.values,
        onSelected: _onMenuOptionSelected,
      ),
    );
  }

  bool get _isViewingWholePlanet => widget.area == null;

  void _onMenuOptionSelected(BuildContext context, MapSideMenuOption option) {
    switch (option) {
      case MapSideMenuOption.list:
        _showList(context);
        break;
      case MapSideMenuOption.customize:
        _pushCustomized(context);
        break;
      case MapSideMenuOption.help:
        showAdaptiveDialog(
          context: context,
          builder: (_) => MapScreenTutorial(),
        );
        break;
    }
  }

  void _showList(BuildContext context) {
    final showCities = mode == MapMode.cities;
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (_) => showCities
            ? const CitySelectionsScreen()
            : const MyCountrySelectionsScreen(),
        fullscreenDialog: true,
        settings: RouteSettings(
          name: showCities
              ? CitySelectionsScreen.routeName
              : AreaListPicker.routeName,
        ),
      ),
    );
  }

  void _share(BuildContext context) {
    SharableContent content;
    if (mode == MapMode.cities) {
      content = AllCitiesSharableContent();
    } else if (widget.area != null) {
      content = CountrySharableContent(widget.area!);
    } else {
      content = AllCountriesSharableContent();
    }

    final sharer = SharingService(context, content: content);
    sharer.export();
  }

  void _pushCustomized(BuildContext context) {
    Navigator.of(context).pushMaterialRoute(
      name: CustomizeScreen.routeName,
      fullscreen: true,
      builder: (_) => const CustomizeScreen(),
    );
  }

  Widget _buildTopBar(BuildContext context) {
    final theme = Theme.of(context);
    return Positioned(
      top: MapButton.margin,
      left: MapButton.margin,
      right: MapButton.margin,
      child: SafeArea(
        child: Card(
          color: theme.cardTheme.color?.withValues(alpha: 0.8),
          elevation: 3,
          child: Padding(
            padding: const EdgeInsets.all(Margin.standard),
            child: Row(
              spacing: Margin.standard,
              children: [
                _buildPopupMenuButton(context),
                _buildSearchBar(theme),
                _buildShareButton(context),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildSearchBar(ThemeData theme) {
    return Expanded(
      child: mode == MapMode.countries
          ? _buildCountrySearchBar(theme)
          : SearchBar(controller: _searchController, onCleared: _clearSearch),
    );
  }

  Widget _buildCountrySearchBar(ThemeData theme) {
    return StreamBuilder<bool>(
      stream: DependencyInjector.iapBloc.status.map(
        (event) => event.hasUnlockedRegions,
      ),
      builder: (context, snapshot) {
        final showRegions = snapshot.data ?? false;
        final bloc = DependencyInjector.areaBloc;
        final areas = showRegions
            ? bloc.allAreasSync()
            : bloc.allCountriesSync();
        return SearchBar(
          controller: _searchController,
          items: areas,
          onCleared: _clearSearch,
          onQuery: (results) {
            setState(() {
              _searchResults = results?.isNotEmpty ?? false ? results : null;
            });
          },
        );
      },
    );
  }

  void _clearSearch() {
    _searchController.clear();
    FocusManager.instance.primaryFocus?.unfocus();

    if (_searchResults != null) {
      setState(() {
        _searchResults = null;
      });
    }
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }
}

enum MapSideMenuOption implements PopupMenuItemData {
  list,
  customize,
  help;

  @override
  IconData get icon {
    return switch (this) {
      MapSideMenuOption.list => Icons.list,
      MapSideMenuOption.customize => Icons.palette_outlined,
      MapSideMenuOption.help => Icons.help,
    };
  }

  @override
  String title(AppLocalizations localizations) {
    switch (this) {
      case MapSideMenuOption.list:
        return localizations.list;
      case MapSideMenuOption.customize:
        return localizations.customize;
      case MapSideMenuOption.help:
        return localizations.help;
    }
  }
}
