import 'package:flutter/material.dart';
import 'package:flutter_map/flutter_map.dart';
import 'package:latlong2/latlong.dart';

import '../../dependency_injection/dependency_injector.dart';
import '../../models/coordinate.dart';
import '../../models/selection.dart';
import 'map_helper.dart';
import 'tiles/internal/coordinate_extension.dart';
import 'tiles/visited_tile_layer.dart';

class SliverAnnotatedMap extends StatelessWidget with MapHelper {
  const SliverAnnotatedMap({
    super.key,
    required this.coordinates,
    this.onTapped,
  });

  final VoidCallback? onTapped;
  final List<Coordinate> coordinates;

  @override
  Widget build(BuildContext context) {
    final polygon = Polygon(
        points: [for (final coordinate in coordinates) coordinate.toLatLng()]);

    final bounds = polygon.boundingBox;

    final mapOptions = buildMapOptions(bounds);

    return SliverToBoxAdapter(
      child: GestureDetector(
        behavior: HitTestBehavior.opaque,
        onTap: onTapped,
        child: Sized<PERSON>ox(
          height: 300,
          child: IgnorePointer(
            child: FlutterMap(
              options: mapOptions,
              children: [
                const VisitedTileLayer(active: false),
                MarkerLayer(
                  markers: [
                    for (final latlng in polygon.points) _buildMarker(latlng)
                  ],
                )
              ],
            ),
          ),
        ),
      ),
    );
  }

  Marker _buildMarker(LatLng latlng) {
    return Marker(
      width: 4,
      height: 4,
      point: latlng,
      child: Container(
        width: 4,
        height: 4,
        decoration: BoxDecoration(
          shape: BoxShape.circle,
          color: DependencyInjector.settingsBloc.currentPalette
              .colorForSelection(Selection.been),
        ),
      ),
    );
  }
}
