enum OnboardingCard {
  countryMap('assets/onboarding/1. Map.jpg'),
  list('assets/onboarding/2. Travel Lists.jpg'),
  stats('assets/onboarding/3. Travel Stats.jpg'),
  cityMapEurope('assets/onboarding/4. City Map.jpg'),
  inspiration('assets/onboarding/5. Travel Inspirations.jpg'),
  poster('assets/onboarding/P.4._Poster.jpg'),
  countryList('assets/onboarding/P.5. Travel List By Country.jpg'),
  cityMapAmerica('assets/onboarding/P1. City Map.jpg'),
  premiumCityMap('assets/onboarding/P6.1. City Map.jpg'),
  premiumItineraries('assets/onboarding/P6.2. Itineraries.jpg'),
  premiumRegions('assets/onboarding/P6.3. Regions.jpg'),
  premiumPoster('assets/onboarding/P6.4. Poster.jpg'),
  premiumCountryList('assets/onboarding/P6.5. Travel List By Country.jpg');

  const OnboardingCard(this.assetPath);

  final String assetPath;
}

enum OnboardingStyle {
  none,
  appImages(cards: OnboardingStyle._standardCards),
  appImagesWithText(
    cards: OnboardingStyle._standardCards,
    labels: [
      'Map Your Travels',
      'Check Off Famous Places',
      'Get Personal Travel Stats',
      'Map cities visited',
      'Get Inspired',
    ],
  ),
  appImagesWithAutoScroll(cards: OnboardingStyle._standardCards),
  appImagesWithPremiumUpsellAndText(
    cards: [
      OnboardingCard.countryMap,
      OnboardingCard.cityMapAmerica,
      OnboardingCard.premiumItineraries,
      OnboardingCard.list,
      OnboardingCard.poster,
    ],
    labels: [
      'Map Cities Visited',
      'Map by Regions',
      'Plan Future Travels',
      'Order A Printed Poster',
      'Travel List By Country',
    ],
  ),
  appImagesWithPremiumUpsell(
    cards: [
      OnboardingCard.premiumCityMap,
      OnboardingCard.premiumCityMap,
      OnboardingCard.list,
      OnboardingCard.premiumItineraries,
      OnboardingCard.stats,
    ],
  );

  const OnboardingStyle({this.cards, this.labels});

  final List<OnboardingCard>? cards;
  final List<String>? labels;

  static const _standardCards = [
    OnboardingCard.countryMap,
    OnboardingCard.list,
    OnboardingCard.stats,
    OnboardingCard.cityMapEurope,
    OnboardingCard.inspiration,
  ];
}
