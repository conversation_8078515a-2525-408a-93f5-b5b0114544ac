import 'package:email_validator/email_validator.dart';
import 'package:firebase_analytics/firebase_analytics.dart';
import 'package:flutter/material.dart';

import '../../generic_widgets/alert.dart';
import '../../generic_widgets/frosted_glass_card.dart';
import '../../generic_widgets/platform_aware/platform_filled_button.dart';
import '../../generic_widgets/platform_aware/platform_icon_button.dart';
import '../../l10n/generated/app_localizations.dart';
import 'login_screen.dart';

class LoginForm extends StatefulWidget {
  final VoidCallback? onCancelTapped;
  final ValueChanged<String> onEmailSubmitted;
  final FocusNode? focus;
  final bool forceSignup;
  final String title;
  final LoginMode mode;

  const LoginForm({
    super.key,
    required this.onEmailSubmitted,
    this.onCancelTapped,
    this.focus,
    this.forceSignup = false,
    required this.title,
    required this.mode,
  });

  @override
  State createState() => _LoginFormState();
}

class _LoginFormState extends State<LoginForm> {
  final _controller = TextEditingController();

  @override
  void initState() {
    super.initState();

    _controller.addListener(() {
      setState(() {});
    });
  }

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 4),
      child: FrostedGlassCard(
        child: Padding(
          padding: EdgeInsets.only(
              left: 2.0,
              right: 2.0,
              top: widget.onCancelTapped == null ? 2 : 0),
          child: Stack(
            children: [
              Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const SizedBox(height: 16),
                  Padding(
                    padding: const EdgeInsets.only(bottom: 16.0),
                    child: Text(
                      widget.title,
                      style: Theme.of(context)
                          .textTheme
                          .titleLarge
                          ?.copyWith(color: kLoginDarkTextColour),
                    ),
                  ),
                  _buildEmailField(),
                  Padding(
                    padding: const EdgeInsets.only(top: 10.0, bottom: 4),
                    child: _buildSubmitButton(),
                  ),
                ],
              ),
              if (widget.onCancelTapped != null) _buildBackButton(),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildBackButton() {
    return Align(
      alignment: Alignment.topRight,
      child: PlatformIconButton(
        icon: Icons.close,
        padding: EdgeInsets.zero,
        onTapped: () {
          FocusManager.instance.primaryFocus?.unfocus();
          FirebaseAnalytics.instance
              .logEvent(name: '${widget.mode.name}Cancelled');
          widget.onCancelTapped!.call();
        },
        iconColour: Colors.black,
      ),
    );
  }

  Widget _buildSubmitButton() {
    return PlatformFilledButton(
      key: ValueKey(enabled),
      title: AppLocalizations.of(context)!.submit,
      color: LoginScreen.confirmButtonColour,
      textColor: Colors.black,
      disabledTextColor: Colors.black45,
      onTapped: _onLoginTappedBuilder(),
    );
  }

  bool get enabled => _controller.text.isNotEmpty;

  VoidCallback? _onLoginTappedBuilder() {
    return enabled ? _onLoginTapped : null;
  }

  void _onLoginTapped() async {
    final email = _controller.text;

    if (email.isEmpty) {
      return;
    }

    if (!EmailValidator.validate(email)) {
      final localizations = AppLocalizations.of(context)!;
      Alert(
        title: localizations.errorTitle,
        message: localizations.enterValidEmail,
      ).show(context);
      return;
    }

    FirebaseAnalytics.instance.logEvent(name: '${widget.mode}SubmitTapped');
    widget.onEmailSubmitted(email);
  }

  Widget _buildEmailField() {
    return Container(
      decoration: BoxDecoration(
          color: Colors.white70, borderRadius: BorderRadius.circular(10)),
      child: Padding(
        padding: const EdgeInsets.all(2.0),
        child: Row(
          children: [
            Expanded(
              child: Transform.translate(
                offset: const Offset(0, -6),
                child: Theme(
                  data: ThemeData(brightness: Brightness.light),
                  child: Builder(builder: (context) {
                    return TextField(
                      controller: _controller,
                      focusNode: widget.focus,
                      autocorrect: false,
                      keyboardType: TextInputType.emailAddress,
                      onTap: () async {
                        await Future.delayed(const Duration(milliseconds: 300));
                        if (context.mounted) {
                          Scrollable.ensureVisible(context,
                              alignment: -1,
                              duration: const Duration(milliseconds: 250));
                        }
                      },
                      onSubmitted: (_) => _onLoginTapped(),
                      decoration: InputDecoration(
                        contentPadding: const EdgeInsets.all(8),
                        border: InputBorder.none,
                        labelText: AppLocalizations.of(context)!.enterYourEmail,
                        alignLabelWithHint: true,
                        labelStyle:
                            const TextStyle(color: kLoginDarkTextColour),
                        floatingLabelBehavior: FloatingLabelBehavior.auto,
                      ),
                    );
                  }),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }
}
