import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

import '../../dependency_injection/dependency_injector.dart';
import '../../generic_widgets/navigation_extensions.dart';
import '../../generic_widgets/spinner.dart';
import '../../models/device.dart';
import 'login_background.dart';
import 'login_screen.dart';

class LoadingScreen extends StatefulWidget {
  const LoadingScreen({super.key});

  @override
  State createState() => _LoadingScreenState();
}

class _LoadingScreenState extends State<LoadingScreen> {
  @override
  Widget build(BuildContext context) {
    return const Stack(
      children: [
        LoginBackground(),
        Center(child: Spinner()),
      ],
    );
  }

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.scheduleFrameCallback((_) {
      SystemChrome.setPreferredOrientations(Device.isPhone(context)
          ? [
              DeviceOrientation.portraitUp,
              DeviceOrientation.portraitDown,
            ]
          : DeviceOrientation.values);
      startSession();
    });
  }

  void startSession() async {
    try {
      final renderingService = DependencyInjector.tileRenderingService;
      await renderingService.initialize();

      final session = DependencyInjector.sessionBloc;
      await DependencyInjector.areaBloc.initialize();
      await session.start();
      if (!mounted) {
        return;
      }

      final geoRepo = DependencyInjector.geometryRepository;

      await geoRepo.load();
      await geoRepo.applyDisputedTerritoryAdjustments();

      final citiesBloc = DependencyInjector.cityBloc;
      await citiesBloc.initialize();
    } catch (e) {
      if (mounted) {
        Navigator.of(context).pushMaterialReplacement(
          builder: (_) => const LoginScreen(),
          name: '/login',
        );
      }
    }
  }
}
