import 'dart:async';

import 'package:firebase_analytics/firebase_analytics.dart';
import 'package:rxdart/rxdart.dart';

import '../../caching/resettable_behaviour_subject.dart';
import '../../caching/storage.dart';
import '../../dependency_injection/dependency_injector.dart';
import '../../generic_widgets/bloc.dart';
import '../../models/geo_area.dart';
import '../../models/user.dart';
import '../areas/area_bloc.dart';
import '../cities/city_bloc.dart';
import '../in_app_purchase/iap_bloc.dart';
import '../privacy_agreement/privacy_agreement.dart';
import '../selection/area_selection_bloc.dart';
import '../sharing/brand_ambassador_screen.dart';
import 'legacy_user_fetcher.dart';
import 'session_service.dart';

enum SessionStatus {
  pending,
  needPrivacyAgreement,
  firstTime,
  requiresLiveCity,
  loggedIn,
  noUser,
}

class SessionBloc implements Bloc {
  SessionBloc({
    required this.iapBloc,
    required this.selectionBloc,
    required this.areaBloc,
    required this.cityBloc,
    required this.allBlocs,
  }) : _service = SessionService() {
    DependencyInjector.client.sessionRefresher = _refreshSession;
  }

  static const _kSavedUserKey = 'email';
  final _sessionStatusController = BehaviorSubject<SessionStatus>();
  final SessionService _service;
  final AreaSelectionBloc selectionBloc;
  final CityBloc cityBloc;
  final IAPBloc iapBloc;
  final AreaBloc areaBloc;
  Storage get storage => DependencyInjector.sharedPrefsStorage;
  final Set<Bloc> allBlocs;

  Stream<SessionStatus> get status => _sessionStatusController.stream;

  bool get isFirstTimeUsingTheApp => _isFirstTimeUsingTheApp;
  var _isFirstTimeUsingTheApp = false;

  final _userController = ResettableBehaviorSubject<User?>();

  User? get user => _userController.valueOrNull;
  Stream<User?> get userStream => _userController.stream;

  bool get isAuthenticated => _service.isAuthenticated;
  bool get isInUnauthenticatedSession =>
      _service.isAuthenticated == false && (user?.authenticated ?? false);

  Future<void> login(String email, {PrivacyAgreement? privacyAgreement}) async {
    final user = await _service.login(email);
    return _doPostAuthenticationActions(
      user: user,
      privacyAgreement: privacyAgreement,
    );
  }

  Future<bool> checkIfEmailIsAvailable(String email) {
    return _service.isAvailable(email);
  }

  Future<void> createAccount({
    required PrivacyAgreement privacyAgreement,
  }) async {
    try {
      final user = this.user;
      if (user == null) {
        throw Exception(
          'You cannot create an account without first starting the onboarding process',
        );
      }

      if (user.authenticated) {
        throw Exception(
          'User is already authenticated.  No need to create an account',
        );
      }

      final results = await _service.signup(user.email);
      return _doPostAuthenticationActions(
        user: results,
        privacyAgreement: privacyAgreement,
      );
    } catch (e) {
      //TODO: Handle Errors
      rethrow;
    }
  }

  Future<void> start() async {
    if (user != null) {
      return;
    }

    final savedEmail = await _loadSavedEmail();
    if (savedEmail == null) {
      _sessionStatusController.add(SessionStatus.noUser);
      return;
    }

    try {
      return login(savedEmail);
    } catch (_) {
      logout();
    }
  }

  Future<String?> _loadSavedEmail() async {
    final email = await storage.get(_kSavedUserKey);
    if (email != null) {
      return email;
    }

    const fetcher = LegacyUserFetcher();
    return fetcher.get();
  }

  Future<void> _refreshSession() async {
    final savedEmail = await storage.get(_kSavedUserKey);
    if (savedEmail == null) {
      logout();
      return;
    }

    // Log in again to get a new session token,
    // but don't do any of the post login tasks
    await _service.login(savedEmail);
  }

  void beginOnboarding(String email) {
    final user = User(email: email, authenticated: false);
    _userController.add(user);
    _sessionStatusController.add(SessionStatus.needPrivacyAgreement);
  }

  void cancelOnboarding() {
    _userController.add(null);
    _sessionStatusController.add(SessionStatus.noUser);
  }

  void beginUnauthenticatedSession() {
    _userController.add(
      const User(
        email: '<EMAIL>',
        authenticated: false,
      ),
    );
    _sessionStatusController.add(SessionStatus.firstTime);
  }

  Future<void> _doPostAuthenticationActions({
    required User user,
    PrivacyAgreement? privacyAgreement,
  }) async {
    _userController.add(user);
    await DependencyInjector.tileRenderingService.clearAllTiles();
    DependencyInjector.tileRenderingService.renderAllTiles(
      minZoom: 1,
      maxZoom: 2,
    );

    await storage.put(_kSavedUserKey, user.email);
    await iapBloc.initialize();

    if (privacyAgreement != null) {
      await submitPrivacyAgreement(privacyAgreement);
    }

    await refreshStatus();

    _isFirstTimeUsingTheApp =
        _sessionStatusController.valueOrNull != SessionStatus.loggedIn;
  }

  Future<PrivacyAgreement> fetchPrivacyAgreement() =>
      _service.fetchPrivacyAgreement();

  Future<void> submitPrivacyAgreement(PrivacyAgreement agreement) =>
      _service.submitPrivacyAgreement(agreement);

  void logout() async {
    DependencyInjector.tileRenderingService.clearAllTiles();
    FirebaseAnalytics.instance.logEvent(name: 'log_out');
    await storage.delete(_kSavedUserKey);
    await const LegacyUserFetcher().delete();
    _userController.add(null);

    for (final bloc in allBlocs) {
      bloc.clear();
    }

    DependencyInjector.client.clearCache();

    await _service.logout();
    refreshStatus();
  }

  Future<void> refreshStatus() async {
    final status = await _getCurrentStatus();
    _sessionStatusController.add(status);
  }

  void toggleNewsletterSubscription() async {
    final updatedUser = await _service.toggleNewsletterSubscription();
    _userController.add(updatedUser);
  }

  Future<SessionStatus> _getCurrentStatus() async {
    if (user == null) {
      return SessionStatus.noUser;
    }

    if (await _requiresLiveCity()) {
      return SessionStatus.firstTime;
    }

    return SessionStatus.loggedIn;
  }

  Future<bool> _requiresLiveCity() async {
    final homeCity = await cityBloc.fetchLivedCity();
    return homeCity == null;
  }

  Future<void> deleteAccount() async {
    return _service.deleteAccount().whenComplete(() {
      FirebaseAnalytics.instance.logEvent(name: 'account_deleted');
      logout();
    });
  }

  @override
  void clear() {
    // No-op
  }

  @override
  void dispose() {
    _userController.close();
    _sessionStatusController.close();
  }

  Future<void> sendBrandAmbassadorForm({
    required String name,
    required String email,
    required GeoArea country,
    required List<(String, String, FollowersRange?)> socialMedia,
    required String other,
    required String anythingElse,
  }) {
    return _service.sendBrandAmbassadorForm(
      name: name,
      email: email,
      country: country,
      socialMedia: socialMedia,
      other: other,
      anythingElse: anythingElse,
    );
  }
}
