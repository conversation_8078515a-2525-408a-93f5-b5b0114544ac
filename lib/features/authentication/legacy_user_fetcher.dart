import 'dart:io';

import 'package:flutter/services.dart';

class LegacyUserFetcher {
  const LegacyUserFetcher();

  final _channel = const MethodChannel('com.highheels.visited/legacyuser');

  Future<String?> get() async {
    if (Platform.isIOS == false) {
      return null;
    }

    try {
      final legacyEmail = await _channel.invokeMethod<String>('getUser');
      return legacyEmail;
    } catch (_) {
      return null;
    }
  }

  Future<void> delete() async {
    if (Platform.isIOS == false) {
      return;
    }

    return _channel.invokeMethod('deleteUser');
  }
}
