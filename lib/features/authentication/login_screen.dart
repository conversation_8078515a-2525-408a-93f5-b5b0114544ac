import 'dart:async';
import 'dart:io';

import 'package:firebase_analytics/firebase_analytics.dart';
import 'package:flutter/material.dart';
import 'package:smooth_page_indicator/smooth_page_indicator.dart';

import '../../dependency_injection/dependency_injector.dart';
import '../../generic_widgets/alert.dart';
import '../../generic_widgets/legal_links.dart';
import '../../generic_widgets/navigation_extensions.dart';
import '../../generic_widgets/platform_aware/platform_filled_button.dart';
import '../../generic_widgets/responsive_padding.dart';
import '../../generic_widgets/spinner.dart';
import '../../helpers/margin.dart';
import '../../l10n/generated/app_localizations.dart';
import '../../models/palette.dart';
import '../../networking/network_client.dart';
import '../../state_safety.dart';
import '../tutorial/tutorial_screen.dart';
import 'keyboard_aware_scaffold.dart';
import 'login_background.dart';
import 'login_form.dart';
import 'onboarding_cards.dart';
import 'session_bloc.dart';
import 'visited_logo.dart';

enum LoginMode { normal, login, signUp }

const kLoginDarkTextColour = Color(0xFF161617);
const kLoginLightButtonColour = Color(0xFFE6E6F5);

class LoginScreen extends StatefulWidget {
  const LoginScreen({super.key});

  @override
  State createState() => _LoginScreenState();

  static final confirmButtonColour =
      Palette.standard.lived; // Color(0xFFFFE74C); //Color(0xFFDC810F);
}

class _LoginScreenState extends State<LoginScreen> {
  var showLoginForm = false;
  var authenticationRequired = true;
  var mode = LoginMode.normal;
  var onboardingStyle = OnboardingStyle.none;

  final _pageController = PageController();
  final _formFocus = FocusNode();

  Timer? _autoPageTurner;

  @override
  void initState() {
    super.initState();

    DependencyInjector.featureFlags.onboardingStyle().then((style) {
      if (!mounted) {
        return;
      }

      if (style == onboardingStyle) {
        return;
      }

      setState(() {
        onboardingStyle = style;
        if (style == OnboardingStyle.appImagesWithAutoScroll) {
          _configureAutoPageTurner();
        }
      });
    });

    DependencyInjector.featureFlags.accountRequiredOnAppLaunch().then((
      authRequired,
    ) {
      if (!authRequired) {
        setState(() {
          authenticationRequired = authRequired;
        });
      }

      _formFocus.addListener(() {
        setState(() {});
      });
    });
  }

  void _configureAutoPageTurner() {
    _autoPageTurner ??= Timer.periodic(const Duration(seconds: 9), _flipPage);
  }

  void _flipPage(Timer timer) {
    if (!mounted) {
      return;
    }

    var nextPage = (_pageController.page?.toInt() ?? 0) + 1;
    final count = onboardingStyle.cards?.length ?? 0;
    if (nextPage >= count) {
      nextPage = 0;
    }
    _pageController.animateToPage(
      nextPage,
      duration: const Duration(milliseconds: 780),
      curve: Curves.easeInOut,
    );
  }

  @override
  void dispose() {
    super.dispose();
    _formFocus.dispose();
    _pageController.dispose();
    _autoPageTurner?.cancel();
  }

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        const LoginBackground(),
        KeyboardAwareScaffold(body: _buildKeyboardAwareContent(context)),
        _buildLegal(context),
      ],
    );
  }

  Widget _buildKeyboardAwareContent(BuildContext context) {
    return SingleChildScrollView(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          if (onboardingStyle == OnboardingStyle.none)
            _buildTopPadding(context),
          _buildLogo(context),
          if (onboardingStyle != OnboardingStyle.none)
            AnimatedSwitcher(
              duration: const Duration(milliseconds: 350),
              switchInCurve: Curves.easeIn,
              switchOutCurve: Curves.easeOut,
              child: mode == LoginMode.normal
                  ? _buildOnboardingCards(context)
                  : SizedBox(height: MediaQuery.of(context).size.height * 0.05),
            ),
          authenticationRequired
              ? _buildAuthRequiredForm()
              : _buildLoginOptionalForm(),
        ],
      ),
    );
  }

  Widget _buildOnboardingCards(BuildContext context) {
    return SizedBox(
      height: MediaQuery.of(context).size.height * 0.4,
      child: Column(
        spacing: Margin.small,
        children: [
          Expanded(
            child: PageView.builder(
              controller: _pageController,
              itemCount: onboardingStyle.cards?.length ?? 0,
              itemBuilder: (context, index) {
                return Center(
                  child: DecoratedBox(
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(Margin.small),
                      boxShadow: const [BoxShadow(offset: Offset(0, 5))],
                    ),
                    child: ClipRRect(
                      borderRadius: BorderRadius.circular(Margin.small),
                      child: Stack(
                        children: [
                          Image.asset(onboardingStyle.cards![index].assetPath),
                          if (onboardingStyle.labels != null)
                            Positioned(
                              bottom: 0,
                              left: 0,
                              right: 0,
                              child: Container(
                                color: VisitedGradientBackground.darkColour
                                    .withAlpha(180),
                                padding: const EdgeInsets.all(Margin.small),
                                child: Text(
                                  onboardingStyle.labels![index],
                                  textAlign: TextAlign.center,
                                  style: Theme.of(context).textTheme.titleLarge
                                      ?.copyWith(
                                        color: Colors.white,
                                        fontWeight: FontWeight.w800,
                                      ),
                                ),
                              ),
                            ),
                        ],
                      ),
                    ),
                  ),
                );
              },
            ),
          ),
          SmoothPageIndicator(
            controller: _pageController,
            count: onboardingStyle.cards?.length ?? 0,
            effect: const ScaleEffect(
              activeDotColor: Colors.white,
              dotColor: Colors.white24,
              dotHeight: 10,
              dotWidth: 10,
            ),
            onDotClicked: (index) {
              _pageController.animateToPage(
                index,
                duration: const Duration(milliseconds: 350),
                curve: Curves.easeInOut,
              );
            },
          ),
        ],
      ),
    );
  }

  AnimatedPadding _buildLogo(BuildContext context) {
    return AnimatedPadding(
      duration: const Duration(milliseconds: 350),
      padding: EdgeInsets.all(
        _formFocus.hasFocus
            ? 0
            : MediaQuery.of(context).size.height *
                  (onboardingStyle == OnboardingStyle.none ? 0.02 : 0.005),
      ),
      curve: Curves.easeInOut,
      child: const VisitedLogo(),
    );
  }

  AnimatedContainer _buildTopPadding(BuildContext context) {
    return AnimatedContainer(
      height: _formFocus.hasFocus
          ? 0
          : MediaQuery.of(context).size.height * 0.05,
      duration: const Duration(milliseconds: 350),
      curve: Curves.easeInOut,
    );
  }

  Widget _buildAuthRequiredForm() {
    return ResponsivePadding(
      context: context,
      fillToEdgeOnPhone: true,
      child: _buildFormSwitcher(
        child: (() {
          if (mode == LoginMode.normal) {
            return _buildNormalLoginState();
          }

          final localization = AppLocalizations.of(context)!;

          return LoginForm(
            key: ValueKey(mode),
            focus: _formFocus,
            title: mode == LoginMode.login
                ? localization.login
                : localization.signup,
            onEmailSubmitted: _onEmailSubmitted,
            mode: mode,
            onCancelTapped: () {
              setState(() {
                mode = LoginMode.normal;
              });
            },
          );
        })(),
      ),
    );
  }

  Widget _buildNormalLoginState() {
    final localizations = AppLocalizations.of(context)!;
    return SizedBox(
      key: const ValueKey(LoginMode.login),
      width: double.infinity,
      child: Padding(
        padding: const EdgeInsets.all(Margin.small),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            SizedBox(
              height:
                  MediaQuery.of(context).size.height *
                  (onboardingStyle == OnboardingStyle.none ? 0.16 : 0.05),
            ),
            PlatformFilledButton(
              title: localizations.signup,
              color: LoginScreen.confirmButtonColour,
              textColor: kLoginDarkTextColour,
              onTapped: () {
                FirebaseAnalytics.instance.logEvent(name: 'signupTapped');
                setState(() => mode = LoginMode.signUp);
              },
            ),
            Padding(
              padding: const EdgeInsets.only(left: 24, right: 24, top: 24),
              child: PlatformFilledButton(
                title: localizations.login,
                fontSize: 16,
                onTapped: () {
                  FirebaseAnalytics.instance.logEvent(name: 'loginTapped');
                  setState(() => mode = LoginMode.login);
                },
                color: kLoginLightButtonColour,
                textColor: kLoginDarkTextColour,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildLoginOptionalForm() {
    return _buildFormSwitcher(
      child: showLoginForm
          ? LoginForm(
              title: AppLocalizations.of(context)!.signup,
              onCancelTapped: _onLoginFormClosed,
              onEmailSubmitted: _onEmailSubmitted,
              mode: mode,
            )
          : _buildGetStarted(),
    );
  }

  Widget _buildFormSwitcher({required Widget child}) {
    return Align(
      alignment: const Alignment(0, -0.5),
      child: AnimatedSwitcher(
        duration: const Duration(milliseconds: 350),
        switchInCurve: Curves.bounceIn,
        transitionBuilder: (child, anim) => FadeTransition(
          opacity: anim,
          child: ScaleTransition(
            scale: Tween(begin: 0.5, end: 1.0).animate(anim),
            child: SlideTransition(
              position: Tween(
                begin: const Offset(0, 1),
                end: Offset.zero,
              ).animate(anim),
              child: child,
            ),
          ),
        ),
        child: child,
      ),
    );
  }

  void _onLoginFormClosed() => setState(() => showLoginForm = false);

  void _onEmailSubmitted(String email) async {
    email = email.trim();

    const spinner = SpinnerDialog();
    spinner.show(context);
    final localizations = AppLocalizations.of(context)!;

    try {
      final bloc = DependencyInjector.sessionBloc;
      if (mode == LoginMode.login) {
        await _handleLogin(bloc, email, localizations);
      }

      if (mode == LoginMode.signUp) {
        await _handleSignUp(bloc, email, localizations);
      }

      maybeNavigator?.pop();
    } catch (e) {
      if (mounted) {
        await Alert(
          title: AppLocalizations.of(context)!.errorTitle,
          message: e.toString(),
        ).show(context);
        maybeNavigator?.pop();
      }
    }
  }

  Future<void> _handleSignUp(
    SessionBloc bloc,
    String email,
    AppLocalizations localizations,
  ) async {
    final available = await bloc.checkIfEmailIsAvailable(email);
    if (available) {
      bloc.beginOnboarding(email);
    } else {
      if (mounted) {
        await Alert(
          title: localizations.errorTitle,
          message: localizations.emailNotAvailable,
        ).show(context);
      }
    }
  }

  Future<void> _handleLogin(
    SessionBloc bloc,
    String email,
    AppLocalizations localizations,
  ) async {
    try {
      await bloc.login(email);
    } on NetworkException catch (e) {
      if (e.statusCode == HttpStatus.notFound ||
          e.statusCode == HttpStatus.unauthorized) {
        return _showAccountNotFoundErrorMessage(localizations, email, bloc);
      }

      final message = e.errorJson;
      return _showGenericError(localizations, message?['error']);
    } catch (e) {
      return _showGenericError(localizations, e);
    }
  }

  Future<void> _showGenericError(AppLocalizations localizations, Object e) {
    return Alert(
      title: localizations.errorTitle,
      message: e.toString(),
    ).show(context);
  }

  Future<void> _showAccountNotFoundErrorMessage(
    AppLocalizations localizations,
    String email,
    SessionBloc bloc,
  ) async {
    final dialog = ConfirmDialog(
      title: localizations.errorTitle,
      message: localizations.loginEmailNotFoundError(email),
      confirmText: localizations.signup,
      cancelText: localizations.cancel,
      confirmDestructive: true,
    );

    final signup = await dialog.show(context);
    if (signup == true) {
      return _handleSignUp(bloc, email, localizations);
    } else {
      return Future.value();
    }
  }

  Widget _buildLegal(BuildContext context) {
    return Positioned(
      bottom: 0,
      left: 0,
      right: 0,
      child: SafeArea(
        child: LegalLinks(
          leading: Localizations.localeOf(context).languageCode == 'en'
              ? _buildMoreInfoButton()
              : null,
          showDropShadows: true,
        ),
      ),
    );
  }

  Widget _buildMoreInfoButton() {
    return UnderlinedButton(
      title: AppLocalizations.of(context)!.moreInfo,
      color: kLoginLightButtonColour,
      addDropShadow: true,
      onTapped: () {
        FirebaseAnalytics.instance.logEvent(name: 'loginMoreInfoTapped');
        Navigator.of(context).pushMaterialRoute(
          name: TutorialScreen.routeName,
          fullscreen: true,
          builder: (context) => const TutorialScreen(),
        );
      },
    );
  }

  Widget _buildGetStarted() {
    return ResponsivePadding(
      context: context,
      fillToEdgeOnPhone: false,
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.center,
        mainAxisSize: MainAxisSize.min,
        children: [
          PlatformFilledButton(
            title: AppLocalizations.of(context)!.getStarted,
            onTapped:
                DependencyInjector.sessionBloc.beginUnauthenticatedSession,
          ),
          const SizedBox(height: 8),
          const Padding(padding: EdgeInsets.all(16), child: Divider()),
          PlatformFilledButton(
            title: AppLocalizations.of(context)!.login,
            color: Colors.white70,
            textColor: Theme.of(context).primaryColor, // colorScheme.primary,
            onTapped: () {
              FirebaseAnalytics.instance.logEvent(name: 'loginTapped');
              setState(() {
                showLoginForm = true;
              });
            },
          ),
        ],
      ),
    );
  }
}
