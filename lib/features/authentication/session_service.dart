import 'dart:io';

import '../../dependency_injection/dependency_injector.dart';
import '../../models/geo_area.dart';
import '../../models/user.dart';
import '../../networking/client.dart';
import '../../networking/user_not_authenticated_exception.dart';
import '../privacy_agreement/privacy_agreement.dart';
import '../sharing/brand_ambassador_screen.dart';

class SessionService {
  late final _client = DependencyInjector.client;
  late final _database = DependencyInjector.database;

  bool get isAuthenticated => _client.authenticated;

  Future<User> login(String email) async {
    try {
      final response = await _client.post(
        'auth/login',
        body: _authenticationBody(email),
        retry: false,
      );
      return _handleAuthenticationResponse(response);
    } catch (e) {
      rethrow;
    }
  }

  Future<User> signup(String email) async {
    final response = await _client.post(
      'auth/signup',
      body: _authenticationBody(email),
      retry: false,
    );
    return _handleAuthenticationResponse(response);
  }

  JsonMap _authenticationBody(String email) {
    final env = _client.environment;
    return {
      'email': _sanitizedEmail(email),
      'platform': {
        'os': Platform.isIOS ? 'iOS' : 'Android',
        'version': env.version,
      },
    };
  }

  User _handleAuthenticationResponse(Map response) {
    _storeAuthenticationToken(response);
    final json = response['user'];
    return User.fromJson(json);
  }

  String _sanitizedEmail(String email) => email.toLowerCase().trim();

  void _storeAuthenticationToken(Map response) {
    final token = response['token'];
    _client.setHeader(key: kAuthorizationHeader, value: token);
  }

  Future<void> logout() async {
    _client.customHeaders.remove(kAuthorizationHeader);
    return _database.clearUserData();
  }

  Future<PrivacyAgreement> fetchPrivacyAgreement() async {
    if (!_client.authenticated) {
      throw const UserNotAuthenticatedException();
    }

    final json = await _client.get('casl', cacheable: false);
    final agreement = PrivacyAgreement.fromJson(json);
    return agreement;
  }

  Future<void> submitPrivacyAgreement(PrivacyAgreement agreement) {
    final json = {
      'optIn': agreement.optedIn ?? false ? 'YES' : 'NO',
      'terms': agreement.agreedToTerms ?? false ? 'YES' : 'NO',
    };

    return _client.post('casl', body: json);
  }

  Future<bool> isAvailable(String email) async {
    final results = await _client.post(
      'signup/available',
      body: {'email': email},
    );
    return results['available'] ?? false;
  }

  Future<void> deleteAccount() {
    return _client.delete('users/me');
  }

  Future<User> toggleNewsletterSubscription() async {
    final json = await _client.post('auth/toggleUnsubscribed');
    return User.fromJson(json);
  }

  Future<void> sendBrandAmbassadorForm({
    required String name,
    required String email,
    required GeoArea country,
    required List<(String, String, FollowersRange?)> socialMedia,
    required String other,
    required String anythingElse,
  }) async {
    final json = {
      'name': name,
      'email': email,
      'country': country.isoCode,
      'socialMedia': [
        for (final e in socialMedia)
          {
            'platform': e.$1,
            'handle': e.$2,
            'followers': e.$3?.backendKey,
          },
      ],
      'other': other,
      'anythingElse': anythingElse,
    };

    try {
      await _client.post('contactUs/becomeBrandAmbassador', body: json);
    } catch (e) {
      rethrow;
    }
  }
}
