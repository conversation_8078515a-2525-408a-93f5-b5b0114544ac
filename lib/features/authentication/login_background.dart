import 'dart:ui';

import 'package:flutter/material.dart';

class LoginBackground extends StatefulWidget {
  const LoginBackground({super.key});

  @override
  State createState() => _LoginBackgroundState();
}

class _LoginBackgroundState extends State<LoginBackground>
    with SingleTickerProviderStateMixin {
  late final controller = AnimationController(
    vsync: this,
    duration: const Duration(minutes: 2),
  );

  @override
  void initState() {
    super.initState();

    controller.repeat(reverse: false);
  }

  @override
  void dispose() {
    controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [const VisitedGradientBackground(), _buildAnimatedWorld()],
    );
  }

  Widget _buildAnimatedWorld() {
    return AnimatedBuilder(
      animation: controller,
      builder: (context, child) {
        return Positioned.fill(
          child: Opacity(
            opacity: 0.1,
            child: Image.asset(
              'assets/images/world_map.png',
              fit: BoxFit.fitHeight,
              alignment: Alignment(
                lerpDouble(-1.4, 1.5, controller.value) ?? 0,
                0,
              ),
            ),
          ),
        );
      },
    );
  }
}

class VisitedGradientBackground extends StatelessWidget {
  const VisitedGradientBackground({super.key});

  static const lightColour = Color(0xFF0B2B65);
  static const darkColour = Color(0xFF05122B);

  @override
  Widget build(BuildContext context) {
    return Container(
      width: double.infinity,
      height: double.infinity,
      decoration: const BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topCenter,
          end: Alignment.bottomLeft,
          colors: [Color(0xFF0B2B65), Color(0xFF05122B)],
        ),
      ),
    );
  }
}
