import 'package:flutter/material.dart';

import '../../../dependency_injection/dependency_injector.dart';
import '../../../models/selection.dart';
import '../../areas/custom_area_picker.dart';
import '../models/experience.dart';

class ExperienceAreaPicker extends StatelessWidget {
  static String routeName(Experience experience) =>
      'experiences/${experience.id}/areas';

  final Experience experience;

  const ExperienceAreaPicker({
    super.key,
    required this.experience,
  });

  @override
  Widget build(BuildContext context) {
    final experienceBloc = DependencyInjector.experienceBloc;

    return CustomAreaPicker(
      titleFetcher: () async {
        // Fetch Dynamically to always get the correct localized title
        final experiences = await experienceBloc.fetchExperiences();
        return experiences.firstWhere((e) => e.id == experience.id).name;
      },
      areaFetcher: () {

        return experienceBloc.fetchAreas(experience);
      },
      availableSelections: const [Selection.been, Selection.want],
      selectionStream: (area) =>
          experienceBloc.areaSelection(area, experience),
      onSelected: (area, selection) => experienceBloc.select(
        experience: experience,
        area: area,
        selection: selection,
      ),
    );
  }
}
