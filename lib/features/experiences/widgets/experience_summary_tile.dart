import 'package:flutter/material.dart';

import '../../../generic_widgets/navigation_extensions.dart';
import '../../../generic_widgets/pushable_tile.dart';
import '../../dashboard/graphs/percent_graph.dart';
import '../models/experience_selections.dart';
import 'experience_area_picker.dart';
import 'experience_details_screen.dart';

class ExperienceSummaryTile extends StatelessWidget {
  const ExperienceSummaryTile({
    super.key,
    required this.summary,
  });

  final ExperienceSelections summary;

  @override
  Widget build(BuildContext context) {
    return PushableTile(
      title: summary.experience.name,
      imageLocation: AssetImageLocation(summary.experience.icon),
      onTapped: () => _onTapped(context),
      trailing: _buildCompletionStats(context, summary),
    );
  }

  Widget _buildCompletionStats(
      BuildContext context, ExperienceSelections summary) {
    if (summary.percentage == 0.0) {
      return const SizedBox(
        height: 30,
        width: 30,
      );
    }

    return SizedBox(
      height: 30,
      width: 30,
      child: PercentGraph(
        percentage: summary.percentage,
        lineWidth: 2,
        child: _buildAmount(context, summary.percentage),
      ),
    );
  }

  Widget? _buildAmount(BuildContext context, double percentage) {
    if (percentage <= 0.0 || percentage.isNaN || percentage.isInfinite) {
      return null;
    }

    return Padding(
      padding: const EdgeInsets.all(2.0),
      child: Center(
        child: FittedBox(
          fit: BoxFit.scaleDown,
          child: Text(
            '${(percentage * 100).round()}%',
            style: Theme.of(context).textTheme.titleMedium,
          ),
        ),
      ),
    );
  }

  void _onTapped(BuildContext context) {
    final experience = summary.experience;
    late final String routeName;
    late final WidgetBuilder builder;
    late final bool fullScreen;

    if (summary.hasSelections) {
      routeName = ExperienceDetailsScreen.routeName(experience);
      builder = (_) => ExperienceDetailsScreen(experience: experience);
      fullScreen = false;
    } else {
      routeName = ExperienceAreaPicker.routeName(experience);
      builder = (_) => ExperienceAreaPicker(experience: experience);
      fullScreen = true;
    }

    Navigator.of(context).pushMaterialRoute(
      name: routeName,
      builder: builder,
      fullscreen: fullScreen,
    );
  }
}
