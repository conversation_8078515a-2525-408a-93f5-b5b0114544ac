import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';

import '../../../l10n/generated/app_localizations.dart';

class ExperiencesExplanation extends StatelessWidget {
  const ExperiencesExplanation({super.key});

  @override
  Widget build(BuildContext context) {
    final localizations = AppLocalizations.of(context)!;
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        SvgPicture.asset(
          'assets/images/i-experiences-filled.svg',
          height: 120,
          colorFilter: ColorFilter.mode(
              Theme.of(context).colorScheme.primary, BlendMode.srcIn),
        ),
        Text(
          localizations.experiences,
          style: Theme.of(context).textTheme.titleLarge,
        ),
        Text(
          localizations.experiencesInstructions,
          style: Theme.of(context).textTheme.bodyLarge,
        ),
      ],
    );
  }
}
