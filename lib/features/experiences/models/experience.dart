class Experience implements Comparable<Experience> {
  final int id;
  final String name;
  final String _iconName;
  final String? eTag;
  String get icon => 'assets/images/$_iconName.jpg';

  Experience.fromJson(Map json)
      : id = json['id'],
        name = json['name'],
        eTag = json['etag'],
        _iconName = json['file'].toString().toLowerCase();

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is Experience &&
          runtimeType == other.runtimeType &&
          id == other.id &&
          name == other.name;

  @override
  int get hashCode => id.hashCode ^ name.hashCode;

  @override
  String toString() {
    return 'Experience{name: $name}';
  }

  @override
  int compareTo(Experience other) => name.compareTo(other.name);
}
