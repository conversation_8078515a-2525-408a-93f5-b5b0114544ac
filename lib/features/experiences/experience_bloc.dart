import 'dart:async';

import 'package:collection/collection.dart';

import '../../caching/resettable_behaviour_subject.dart';
import '../../dependency_injection/dependency_injector.dart';
import '../../generic_widgets/bloc.dart';
import '../../models/geo_area.dart';
import '../../models/selection.dart';
import '../areas/area_bloc.dart';
import '../books/book_link.dart';
import 'experience_service.dart';
import 'models/experience.dart';
import 'models/experience_selections.dart';

abstract class OnExperiencesSelectedDelegate {
  void onExperienceSelected(
    Experience experiences,
    GeoArea area,
    Selection selection,
  );
}

class ExperienceBloc implements Bloc {
  ExperienceBloc({
    required AreaBloc areaBloc,
    this.delegate,
  }) : _service = ExperienceService(areaBloc) {
    _selectionsController.onListen = _fetchSelections;

    _languageSubscription ??=
        DependencyInjector.settingsBloc.language.listen((_) {
      _service.repo.clear();
      _selectionsController.reset();

      if (DependencyInjector.sessionBloc.isAuthenticated) {
        _fetchSelections();
      }
    });
  }

  StreamSubscription? _languageSubscription;

  final OnExperiencesSelectedDelegate? delegate;
  final ExperienceService _service;

  final _selectionsController =
      ResettableBehaviorSubject<Set<ExperienceSelections>>();

  Stream<List<ExperienceSelections>> get preferredExperiences =>
      _selectionsController.map((summaries) =>
          summaries.where((summary) => summary.preferred).toList()..sort());

  Stream<bool> isPreferred(Experience experience) {
    return _selectionsController.map((summaries) {
      return summaries
          .where((summary) =>
              summary.experience == experience && summary.preferred)
          .isNotEmpty;
    });
  }

  Stream<ExperienceSelections> experienceSelections(Experience experience) =>
      _selectionsController.map(
        (selections) => selections.firstWhere(
          (e) => e.experience == experience,
          orElse: () => ExperienceSelections(experience: experience),
        ),
      );

  Stream<Selection> areaSelection(GeoArea area, Experience experience) {
    return experienceSelections(experience).map(
      (experienceSelections) => experienceSelections.selection(area),
    );
  }

  Future<Map<GeoArea, Selection>> currentSelections(
      Experience experience) async {
    var allSelections = _selectionsController.valueOrNull;
    if (allSelections == null) {
      await _fetchSelections();
      allSelections = _selectionsController.valueOrNull;
      if (allSelections == null) {
        return {};
      }
    }

    final selections = allSelections.firstWhereOrNull(
      (selection) => selection.experience == experience,
    );

    if (selections == null) {
      return {};
    }

    return {
      for (final area in selections.been) area: Selection.been,
      for (final area in selections.want) area: Selection.want,
    };
  }

  Future<List<Experience>> fetchExperiences() async {
    final experiences = await _service.fetchExperiences();
    return experiences.toList()..sort();
  }

  Future<List<GeoArea>> fetchAreas(Experience experience) {
    return _service.fetchAreas(experience);
  }

  Future<List<Experience>> unselectedExperiencesForArea(GeoArea area) async {
    final experiences = [...await _service.fetchExperiencesForArea(area)];
    final selected = await _service.fetchSelectionsForArea(area);

    experiences.removeWhere((experience) {
      return (selected.want?.contains(experience) ?? false) ||
          (selected.been?.contains(experience) ?? false);
    });

    return experiences;
  }

  Future<void> _fetchSelections() async {
    if (_selectionsController.hasValue) {
      return;
    }

    final summaries = await _service.fetchSelections();
    _selectionsController.add(summaries);
  }

  void togglePreferred(Experience experience) async {
    final updated = await _service.togglePreferred(experience);
    _selectionsController.add(updated);
  }

  void select({
    required Experience experience,
    required GeoArea area,
    required Selection selection,
  }) async {
    if (selection == Selection.live) {
      return;
    }

    final updates = await _service.select(experience, area, selection);
    if (updates != null) {
      _selectionsController.add(updates);
    }

    delegate?.onExperienceSelected(experience, area, selection);
  }

  Future<BookLink?> fetchBook(Experience experience) {
    return DependencyInjector.bookService.fetchExperienceBookLink(experience);
  }

  @override
  void clear() {
    _selectionsController.reset();
    _service.clear();
  }

  @override
  void dispose() {
    _languageSubscription?.cancel();
    _selectionsController.close();
  }
}
