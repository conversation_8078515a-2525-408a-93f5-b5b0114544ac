import 'dart:async';

import '../../caching/resettable_behaviour_subject.dart';
import '../../dependency_injection/dependency_injector.dart';
import '../../models/geo_area.dart';
import '../../models/rank.dart';
import '../../models/selection.dart';
import '../../networking/client.dart';
import '../areas/area_repository.dart';
import 'area_selection_repository.dart';
import 'selection_update_response.dart';

class AreaSelectionService {
  AreaRepository get areaRepository => DependencyInjector.areaRepository;
  AreaSelectionRepository get _selectionRepository =>
      DependencyInjector.areaSelectionRepository;

  late final client = DependencyInjector.client;
  late final _rankController = ResettableBehaviorSubject<Rank>()
    ..onListen = _refreshRank;

  Stream<Rank> get listenToRank => _rankController.stream;
  Rank get rank => _rankController.value;

  var _hasRestoredFromBackend = false;
  bool get hasRestoredFromBackend => _hasRestoredFromBackend;

  Future<Map<GeoArea, Selection>> fetchCachedSelections() {
    return _selectionRepository.selections;
  }

  Future<Map<GeoArea, Selection>> fetchSelections() async {
    if (client.authenticated) {
      await _restoreSelectionsFromBacked();
    }

    return _selectionRepository.selections;
  }

  Future<void> _refreshRank() async {
    final response = await client.get('rank');
    final rank = Rank.fromJson(response);
    _rankController.add(rank);
  }

  Future<Map<GeoArea, Selection>> select({
    required GeoArea area,
    required Selection selection,
    bool syncBackend = true,
  }) async {
    // If selection hasn't changed just stop
    if (await (_selectionRepository.fetchSelection(area)) == selection) {
      return {area: selection};
    }

    if (syncBackend && client.authenticated) {
      _updateSelectionsOnBackend(area, selection);
    }

    final localChanges = <GeoArea, Selection>{};

    if (selection == Selection.live) {
      await _demotePreviousLiveAreas(localChanges, area);
    }

    localChanges[area] = selection;

    if (client.authenticated) {
      _selectionRepository.updateSelection(
        area: area,
        selection: selection,
        synced: false,
      );
    }

    _updateSubdivisions(area, selection, localChanges);

    return localChanges;
  }

  Future<Map<GeoArea, Selection>> selectMany(
    Map<GeoArea, Selection> selections, {
    bool waitForBackend = false,
  }) async {
    final allChanges = <GeoArea, Selection>{};
    for (final entry in selections.entries) {
      final changes = await select(
        area: entry.key,
        selection: entry.value,
        syncBackend: false,
      );
      allChanges.addAll(changes);
    }

    if (client.authenticated) {
      if (waitForBackend) {
        await _updateManyOnBackend(selections);
      } else {
        _updateManyOnBackend(selections);
      }
    }

    return allChanges;
  }

  Future<void> _demotePreviousLiveAreas(
    Map<GeoArea, Selection> localChanges,
    GeoArea updatedLiveSelection,
  ) async {
    final livedAreas = await _selectionRepository.fetchAreasByType({
      Selection.live,
    });
    for (final previousLive in livedAreas) {
      if (await _isRelated(updatedLiveSelection, previousLive)) {
        continue;
      }

      localChanges[previousLive] = Selection.been;
      if (client.authenticated) {
        _selectionRepository.updateSelection(
          area: previousLive,
          selection: Selection.been,
          synced: false,
        );
      }
    }
  }

  Future<bool> _isRelated(GeoArea area, GeoArea other) async {
    if (area == other) {
      return true;
    }

    final parentId = area.parentId;

    // Check up the tree first; its faster
    if (parentId != null) {
      final related = await _checkIfAreasAreaRelatedUpwards(area, other);
      if (related) {
        return true;
      }
    }

    // Otherwise check down the tree for a relationship
    if (area.hasSubdivisions) {
      return _checkIfAreasAreRelatedDownwards(area, other);
    }

    return false;
  }

  Future<bool> _checkIfAreasAreaRelatedUpwards(
    GeoArea area,
    GeoArea other,
  ) async {
    if (area.parentId == other.id) {
      return true;
    }

    final parent = await areaRepository.fetchParent(area);
    if (parent == null) {
      return false;
    }

    return _checkIfAreasAreaRelatedUpwards(parent, other);
  }

  Future<bool> _checkIfAreasAreRelatedDownwards(
    GeoArea area,
    GeoArea other,
  ) async {
    final children = await areaRepository.fetchSubdivisions(area);

    if (children.contains(other)) {
      return true;
    }

    final childrenWithSubdivisions = children.where(
      (element) => element.hasSubdivisions,
    );
    if (childrenWithSubdivisions.isEmpty) {
      return false;
    }

    for (final child in childrenWithSubdivisions) {
      final related = await _checkIfAreasAreRelatedDownwards(child, other);
      if (related) {
        return true;
      }
    }

    return false;
  }

  Future<void> _updateSelectionsOnBackend(GeoArea area, Selection selection) {
    final json = {
      'areaIsoKey': area.isoCode,
      'selectionType': selection.backendKey,
    };
    return _updateSelectableModel(json);
  }

  Future<void> _updateManyOnBackend(Map<GeoArea, Selection> areas) async {
    final json = {
      'selectionList': areas.entries
          .map(
            (e) => {
              'areaIsoKey': e.key.isoCode,
              'selectionType': e.value.backendKey,
            },
          )
          .toList(),
    };
    final response = await client.post('selectMultiple', body: json);
    final updates = SelectionUpdateResponse.fromJson(response);
    return importSelections(updates);
  }

  Future<void> _updateSelectableModel(JsonMap payload) async {
    final response = await client.post('select', body: payload);
    final updates = SelectionUpdateResponse.fromJson(response);
    return importSelections(updates);
  }

  Future<void> _restoreSelectionsFromBacked() async {
    if (_hasRestoredFromBackend) {
      return;
    }

    final json = await client.get('select', cacheable: false);
    final selectionResponse = SelectionUpdateResponse.fromJson(json);
    await importSelections(selectionResponse);
    _hasRestoredFromBackend = true;
  }

  Future<void> importSelections(SelectionUpdateResponse response) async {
    _rankController.add(response.rank);
    final selections = <GeoArea, Selection>{};
    await Future.wait([
      _parseAreas(
        isoCodes: response.liveIsoCodes,
        selection: Selection.live,
        storage: selections,
      ),
      _parseAreas(
        isoCodes: response.beenIsoCodes,
        selection: Selection.been,
        storage: selections,
      ),
      _parseAreas(
        isoCodes: response.wantIsoCode,
        selection: Selection.want,
        storage: selections,
      ),
      _parseAreas(
        isoCodes: response.livedIsoCodes,
        selection: Selection.lived,
        storage: selections,
      ),
    ]);

    await _selectionRepository.acceptServerUpdates(selections);
    _hasRestoredFromBackend = true;
  }

  Future<void> _parseAreas({
    required Iterable<String> isoCodes,
    required Selection selection,
    required Map<GeoArea, Selection> storage,
  }) async {
    final selections = await Stream.fromIterable(
      isoCodes,
    ).asyncMap((code) => areaRepository.fetchByIsoCode(code)).toSet();

    for (final area in selections.whereType<GeoArea>()) {
      storage[area] = selection;
    }
  }

  // Recursive function to walk through the subdivision tree
  void _updateSubdivisions(
    GeoArea area,
    Selection selection,
    Map<GeoArea, Selection> localChanges,
  ) async {
    if (!area.hasSubdivisions) {
      return;
    }

    final subdivisions = await areaRepository.fetchSubdivisions(area);
    for (final subdivision in subdivisions) {
      final existingSelection = await _selectionRepository.fetchSelection(
        subdivision,
      );
      if (existingSelection < selection) {
        localChanges[subdivision] = selection;
        if (client.authenticated) {
          _selectionRepository.updateSelection(
            area: area,
            selection: selection,
            synced: false,
          );
        }
      }
      _updateSubdivisions(subdivision, selection, localChanges);
    }
  }

  Future<Selection> selection(GeoArea area) =>
      _selectionRepository.fetchSelection(area);

  Future<List<GeoArea>> fetchTopVisitedCountries(GeoArea area) async {
    final cached = _selectionRepository.topPlaces(area);
    if (cached != null) {
      return cached;
    }

    final path = 'areas/${area.isoCode}/topPlaces';
    final results = await client.get(path);
    final areas =
        results
            ?.map<GeoArea>((e) => GeoArea.fromJson(e))
            .toList(growable: false) ??
        [];

    _selectionRepository.cacheTopPlaces(area, areas);

    return areas;
  }

  Future<String?> fetchNotes(GeoArea area) async {
    final path = 'areas/${area.isoCode}/notes';
    try {
      final request = await client.get(path, cacheable: false);
      return request['notes'];
    } catch (_) {
      return null;
    }
  }

  Future<void> updateNotes(GeoArea area, String text) async {
    final path = 'areas/${area.isoCode}/notes';
    await client.post(path, body: {'notes': text});
  }

  Future<void> updateBeenCounter({
    required GeoArea area,
    required int count,
  }) async {
    await client.post('select/counts', body: {area.isoCode: count});
  }

  Future<Map<GeoArea, int>> fetchBeenCounters() async {
    Map results;
    try {
      results = await client.get('select/counts', cacheable: false);
    } catch (e) {
      return {};
    }

    final Map? counts = results['counts'];

    // Nothing has been updated on the backend, but we know the user has at least
    // selected 'been' so consider this a value of one.
    if (counts == null) {
      return {};
    }

    final parsed = <GeoArea, int>{};

    for (final entry in counts.entries) {
      final iso = entry.key;
      final count = entry.value;
      final area = await areaRepository.fetchByIsoCode(iso);
      if (area == null) {
        continue;
      }

      parsed[area] = count;
    }

    return parsed;
  }

  void clear() {
    _rankController.reset();
    _hasRestoredFromBackend = false;
    _selectionRepository.clear();
  }
}
