import 'package:drift/drift.dart';
import '../../caching/database.dart';
import '../../dependency_injection/dependency_injector.dart';
import '../../models/geo_area.dart';
import '../../models/selection.dart';

part 'area_selection_database.g.dart';

@DriftAccessor(tables: [
  Areas,
  Continents,
  AreaSelections,
])
class AreaSelectionDatabase extends DatabaseAccessor<VisitedDatabase>
    with _$AreaSelectionDatabaseMixin {
  AreaSelectionDatabase(super.attachedDatabase);

  Future<Map<GeoArea, Selection>> fetchSelections() async {
    final results = await _buildAllSelectionsQuery().get();
    return _mapSelectionQueryResult(results);
  }

  Stream<Map<GeoArea, Selection>> listenToSelections() {
    return _buildAllSelectionsQuery().watch().map(_mapSelectionQueryResult);
  }

  Future<Selection> fetchSelection(GeoArea area) async {
    final results = await _buildFetchSelectionQuery(area).getSingleOrNull();
    return _extractSelectionResult(results);
  }

  Future<Selection> fetchByIsoCode(String isoCode) async {
    final query = select(areas).join(
      [
        leftOuterJoin(areaSelections, areaSelections.areaId.equalsExp(areas.id))
      ],
    )..where(areas.isoCode.equals(isoCode));

    try {
      final results = await query
          .map((row) => row.readTable(areaSelections))
          .getSingleOrNull();

      return _extractSelectionResult(results);
    } catch (e) {
      return Selection.clear;
    }
  }

  Stream<Selection> listenToSelection(GeoArea area) {
    return _buildFetchSelectionQuery(area)
        .watchSingleOrNull()
        .map(_extractSelectionResult);
  }

  Future<bool> hasSelection(Selection selection) async {
    final count = countAll(
      filter: areaSelections.type.equals(selection.type),
    );

    final result = await (selectOnly(areaSelections)..addColumns([count]))
        .getSingleOrNull();

    if (result == null) {
      return false;
    }

    return (result.read(count) ?? 0) > 0;
  }

  Future<Set<GeoArea>> fetchAreasByType(Set<Selection> selections) async {
    final results = await _buildFetchAllBySelectionTypeQuery(selections).get();
    return _extractAreasFromSelectionsByTypeQuery(results);
  }

  Stream<Set<GeoArea>> listenToAreasByType(Set<Selection> selections) {
    return _buildFetchAllBySelectionTypeQuery(selections)
        .watch()
        .map(_extractAreasFromSelectionsByTypeQuery);
  }

  Set<GeoArea> _extractAreasFromSelectionsByTypeQuery(
      List<TypedResult> results) {
    final areaRepo = DependencyInjector.areaRepository;
    return results
        .map((e) => areaRepo.geoAreaFromDto(e.readTable(areas)))
        .toSet();
  }

  JoinedSelectStatement<HasResultSet, dynamic>
      _buildFetchAllBySelectionTypeQuery(Set<Selection> selections) {
    return (select(areaSelections)
          ..where((tbl) {
            final first = selections.first;
            selections.remove(first);
            var expression = tbl.type.equals(first.type);

            if (selections.isNotEmpty) {
              for (final selection in selections) {
                expression |= tbl.type.equals(selection.type);
              }
            }

            return expression;
          }))
        .join(
            [leftOuterJoin(areas, areas.id.equalsExp(areaSelections.areaId))]);
  }

  Future<void> saveAll(Set<AreaSelectionDto> dtos) {
    return batch((batch) {
      batch.insertAllOnConflictUpdate(areaSelections, dtos);
    });
  }

  Future<void> save(AreaSelectionDto dto) {
    return into(areaSelections).insertOnConflictUpdate(dto);
  }

  JoinedSelectStatement<HasResultSet, dynamic> _buildAllSelectionsQuery() {
    return (select(areaSelections)
          ..where((tbl) => tbl.type.isNotValue(Selection.clear.type)))
        .join(
      [
        leftOuterJoin(areas, areas.id.equalsExp(areaSelections.areaId)),
      ],
    );
  }

  Map<GeoArea, Selection> _mapSelectionQueryResult(List<TypedResult> results) {
    final areaRepo = DependencyInjector.areaRepository;
    return Map<GeoArea, Selection>.fromIterable(
      results,
      key: (row) {
        final dto = (row as TypedResult).readTable(areas);
        return areaRepo.geoAreaFromDto(dto);
      },
      value: (row) {
        final areaSelection = (row as TypedResult).readTable(areaSelections);
        return Selection(areaSelection.type);
      },
    );
  }

  SimpleSelectStatement<$AreaSelectionsTable, AreaSelectionDto>
      _buildFetchSelectionQuery(GeoArea area) {
    return (select(areaSelections)..where((tbl) => tbl.areaId.equals(area.id)));
  }

  Selection _extractSelectionResult(AreaSelectionDto? results) {
    if (results == null) {
      return Selection.clear;
    }

    return Selection(results.type);
  }

  Future<Map<GeoArea, Selection>> fetchSelectionsByParent(
      GeoArea parent) async {
    final subdivisions = await (select(areas)
          ..where((tbl) => tbl.parentId.equals(parent.id)))
        .get();
    final subdivisionIds = subdivisions.map((e) => e.id);
    final subdivisionSelections = await (select(areaSelections)
          ..where((tbl) => tbl.areaId.isIn(subdivisionIds)))
        .get();

    final areaRepo = DependencyInjector.areaRepository;
    final selections = {
      for (final selection in subdivisionSelections)
        areaRepo.geoAreaFromDto(
                subdivisions.firstWhere((dto) => dto.id == selection.areaId)):
            Selection(selection.type)
    };

    return selections;
  }
}
