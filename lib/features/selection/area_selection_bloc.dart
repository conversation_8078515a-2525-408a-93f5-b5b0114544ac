import 'dart:async';
import 'dart:math';

import 'package:collection/collection.dart';
import 'package:flutter/cupertino.dart';
import 'package:rxdart/rxdart.dart';

import '../../caching/resettable_behaviour_subject.dart';
import '../../dependency_injection/dependency_injector.dart';
import '../../generic_widgets/bloc.dart';
import '../../helpers/pseudo_semaphore.dart';
import '../../models/continent.dart';
import '../../models/geo_area.dart';
import '../../models/rank.dart';
import '../../models/selection.dart';
import '../areas/area_bloc.dart';
import '../disputed_territories/disputed_territories_service.dart';
import '../notes/notes_repository.dart';
import '../settings/settings_bloc.dart';
import 'area_selection_service.dart';
import 'selection_update_response.dart';

class AreaSelectionBloc implements Bloc {
  AreaSelectionBloc({
    required AreaBloc areaBloc,
    required SettingsBloc settingsBloc,
  }) : _settingsBloc = settingsBloc,
       _areaBloc = areaBloc;

  late final AreaSelectionService _service =
      DependencyInjector.areaSelectionService;
  late final _repo = DependencyInjector.areaSelectionRepository;

  final DisputedTerritoriesService _disputedTerritoriesService =
      DependencyInjector.disputedTerritoriesService;
  final AreaBloc _areaBloc;
  final SettingsBloc _settingsBloc;

  final _notesRepo = NotesRepository();

  late final _beenCounterController =
      ResettableBehaviorSubject<Map<GeoArea, int>>()
        ..onListen = _loadBeenCounters;

  Stream<Map<GeoArea, int>> get beenCounter => _beenCounterController.stream;

  VoidCallback? onLiveUpdated;
  Stream<Map<GeoArea, Selection>> get selections => _repo.listenToSelections;
  Future<Map<GeoArea, Selection>> get currentSelections => _repo.selections;
  bool _loaded = false;
  bool get loaded => _loaded;
  PseudoSemaphore<void>? _fetchSelectionsSemaphore;

  Future<bool> hasSelections(Selection selection) async {
    return _repo.hasSelection(selection);
  }

  Stream<Selection> listenToSelection(GeoArea area) {
    return _repo.listenToSelection(area);
  }

  Future<Selection> fetchSelection(GeoArea area) async {
    return _repo.fetchSelection(area);
  }

  Stream<int?> numberOfSelectionsRemaining() {
    final flags = DependencyInjector.featureFlags;

    final maxAmountSelectable = Stream.fromFuture(() async {
      final limitSelections = await flags.limitCountrySelections();
      if (!limitSelections) {
        return null;
      }

      return flags.maxFreeCountries();
    }());

    return Rx.combineLatest3(
      _repo.listenToSelections,
      DependencyInjector.iapBloc.status,
      maxAmountSelectable,
      (selections, purchases, maxFreeCountrySelections) {
        if (maxFreeCountrySelections == null) {
          return null;
        }

        if (purchases.purchasedPro) {
          return null;
        }

        final totalCountrySelections = selections.entries.where((entry) {
          if (!entry.key.isCountry) {
            return false;
          }

          if (entry.value == Selection.live) {
            return false;
          }
          return true;
        }).length;

        return maxFreeCountrySelections - totalCountrySelections;
      },
    );
  }

  Future<void> load() async {
    if (_loaded) {
      return;
    }

    if (_fetchSelectionsSemaphore != null) {
      return _fetchSelectionsSemaphore?.request();
    }

    _fetchSelectionsSemaphore = PseudoSemaphore<void>(
      expensiveOperation: () async {
        // Check if there are locally saved selections and consider that "loaded"
        await _repo.hasSelection(Selection.live);
        _loaded = true;

        // Now pull the latest from the server in a non-blocking manner
        final cached = await _repo.selections;
        final updated = await _service.fetchSelections();

        final differences = <GeoArea>[];
        updated.forEach((area, selection) {
          if (cached[area] != selection) {
            differences.add(area);
          }
        });

        if (differences.isNotEmpty) {
          DependencyInjector.tileRenderingService.updateAreas(differences);
        }

        _handleSelectionUpdates(updated, triggerRender: false);
        _fetchSelectionsSemaphore = null;
      },
    );

    return _fetchSelectionsSemaphore!.request();
  }

  void select(
    GeoArea area,
    Selection selection, {
    bool syncBackend = true,
    bool allowLiveDemotion = false,
  }) async {
    // Don't allow live to be demoted;
    final currentSelection = await fetchSelection(area);
    if (!allowLiveDemotion && currentSelection == Selection.live) {
      return;
    }

    final updates = await _service.select(
      area: area,
      selection: selection,
      syncBackend: syncBackend,
    );

    _handleSelectionUpdates(updates, selection: selection, triggerRender: true);
  }

  Future<void> batchSelect(
    Map<GeoArea, Selection> selections, {
    bool waitForBackend = false,
  }) async {
    final updates = await _service.selectMany(
      selections,
      waitForBackend: waitForBackend,
    );
    _handleSelectionUpdates(updates, triggerRender: true);
  }

  void _handleSelectionUpdates(
    Map<GeoArea, Selection> updates, {
    Selection? selection,
    required bool triggerRender,
  }) async {
    await _repo.acceptServerUpdates(updates, replace: false);

    if (triggerRender) {
      DependencyInjector.tileRenderingService.updateAreas(updates.keys);
    }

    if (selection == Selection.live) {
      onLiveUpdated?.call();
    }
  }

  Future<void> acceptUpdates(SelectionUpdateResponse updates) async {
    await _service.importSelections(updates);
  }

  Stream<double> percentOfTravelGoalCompleted() {
    return Rx.combineLatest2<int, int, double>(
      numberOfCountriesBeenTo,
      numberOfCountriesWantToVisit,
      (been, want) {
        final total = want + been;
        if (total == 0) {
          return 0;
        }

        return been / total;
      },
    );
  }

  Stream<int> get numberOfCountriesWantToVisit {
    return Rx.combineLatest2<Set<GeoArea>, Set<GeoArea>, int>(
      _repo.listenToAreasByType({Selection.want}),
      _allCountriesRespectingUserPreferences,
      _countValidCountries,
    );
  }

  Stream<int> get numberOfCountriesBeenTo {
    return Rx.combineLatest2<Set<GeoArea>, Set<GeoArea>, int>(
      _repo.listenToAreasByType({
        Selection.been,
        Selection.live,
        Selection.lived,
      }),
      _allCountriesRespectingUserPreferences,
      _countValidCountries,
    );
  }

  int _countValidCountries(Set<GeoArea> selections, Set<GeoArea> countries) {
    final selectedCountries = selections.intersection(countries);
    return selectedCountries.length;
  }

  Stream<double> get percentOfWorldSeen {
    return Rx.combineLatest2<int, Set<GeoArea>, double>(
      numberOfCountriesBeenTo,
      _allCountriesRespectingUserPreferences,
      (beenCount, countries) => beenCount / countries.length,
    );
  }

  Stream<Set<GeoArea>> get _allCountriesRespectingUserPreferences {
    return _settingsBloc.settings.asyncMap((settings) async {
      final countries = (await _service.areaRepository.fetchAll()).where((
        area,
      ) {
        if (settings.onlySovereign) {
          return area.sovereign;
        }

        return area.isCountry;
      }).toList();

      if (settings.countUkCountries) {
        final repo = _service.areaRepository;
        final uk = await repo.fetchByIsoCode('GB');
        final ukCountries = await repo.fetchSubdivisions(uk!);
        countries.remove(uk);
        countries.addAll(ukCountries);
      }

      final disputedAdjustments = await _disputedTerritoriesService
          .topLevelDisputedPreferences();

      if (disputedAdjustments.remove.isNotEmpty) {
        final removeIsoCodes = disputedAdjustments.remove.map((e) => e.isoCode);
        countries.removeWhere((area) => removeIsoCodes.contains(area.isoCode));
      }

      if (disputedAdjustments.add.isNotEmpty) {
        for (final toAdd in disputedAdjustments.add) {
          final area = await _areaBloc.areaByIsoCode(toAdd.isoCode);
          countries.add(area!);
        }
      }

      return countries.toSet();
    });
  }

  Stream<Rank> get rank => _service.listenToRank;

  late final _topVisitedCountriesController =
      ResettableBehaviorSubject<(GeoArea, List<GeoArea>)>()
        ..onListen = _startLoadingTopVisitedCountries;

  Stream<(GeoArea, List<GeoArea>)>
  get topVisitedCountriesFromCurrentLivedCountry =>
      _topVisitedCountriesController.stream;

  StreamSubscription? _topVisitedCountriesSubscription;
  StreamSubscription? _languageSubscription;

  void _startLoadingTopVisitedCountries() {
    _topVisitedCountriesSubscription ??= _repo
        .listenToAreasByType({Selection.live})
        .listen((live) => _refreshTopVisitedCountries());

    _languageSubscription ??= DependencyInjector.settingsBloc.language.listen(
      (_) => _refreshTopVisitedCountries(),
    );
  }

  void _refreshTopVisitedCountries() async {
    final live = await currentCountryUserLivesIn();

    if (live == null) {
      return;
    }

    final topPlaces = await _service.fetchTopVisitedCountries(live);
    _topVisitedCountriesController.add((live, topPlaces));
  }

  Future<Set<GeoArea>> fetchSelectedAreasByType(Selection selection) {
    return _repo.fetchAreasByType({selection});
  }

  Future<GeoArea?> currentCountryUserLivesIn() async {
    final live = await _repo.fetchAreasByType({Selection.live});
    return live.firstWhereOrNull((element) => element.isCountry);
  }

  Stream<double> percentOfCountrySeen(GeoArea area) {
    assert(area.hasSubdivisions);
    //TODO: Maybe optimize this?
    return _repo.listenToSelections.asyncMap((selections) async {
      final subdivisions = await _areaBloc.fetchSubdivisions(area);

      final selectedSubdivisions = selections.keys.where((subdivision) {
        if (!subdivisions.contains(subdivision)) {
          return false;
        }

        final selection = selections[subdivision];
        return selection?.shouldCountTowardBeen ?? false;
      });

      return selectedSubdivisions.length / subdivisions.length;
    });
  }

  Stream<Map<Selection, List<GeoArea>>> sortedContinentSelections(
    Continent continent,
  ) {
    return Rx.combineLatest2(
      selections,
      DependencyInjector.areaBloc.listenToCountriesInContinent(continent),
      _doCurrentSortedAreaSelections,
    );
  }

  Stream<(int, int)> percentOfContinentSeen(Continent continent) {
    return Rx.combineLatest2(
      selections,
      DependencyInjector.areaBloc.listenToCountriesInContinent(continent),
      (selections, areas) {
        var total = 0;
        for (final area in areas) {
          final selection = selections[area];
          if (selection?.shouldCountTowardBeen ?? false) {
            total++;
          }
        }

        return (total, areas.length);
      },
    );
  }

  Stream<(int, int)> fractionOfCountrySeen(GeoArea area) {
    assert(area.hasSubdivisions);
    //TODO: Maybe optimize this?
    return _repo.listenToSelections.asyncMap((selections) async {
      final subdivisions = await _areaBloc.fetchSubdivisions(area);

      final selectedSubdivisions = selections.keys.where((subdivision) {
        if (!subdivisions.contains(subdivision)) {
          return false;
        }

        final selection = selections[subdivision];
        return selection?.shouldCountTowardBeen ?? false;
      });

      return (selectedSubdivisions.length, subdivisions.length);
    });
  }

  Future<String?> fetchNotes(GeoArea area) async {
    final note = await _notesRepo.get(area);
    if (note != null) {
      return note;
    }

    final fetchedNote = await _service.fetchNotes(area);
    if (fetchedNote != null) {
      _notesRepo.update(area, fetchedNote);
    }

    return fetchedNote;
  }

  Future<void> updateNotes(GeoArea area, String text) {
    _notesRepo.update(area, text);
    return _service.updateNotes(area, text);
  }

  Future<int> beenCount(GeoArea area) async {
    await _loadBeenCounters();
    return _beenCounterController.valueOrNull?[area] ?? 1;
  }

  void updateBeenCounter({required GeoArea area, required int count}) {
    final counters = _beenCounterController.valueOrNull ?? {};
    counters[area] = count;
    _beenCounterController.add(counters);

    _service.updateBeenCounter(area: area, count: count);
  }

  Stream<List<GeoArea>> listenToMostFrequentlyVisitedCountries() {
    return _beenCounterController.map((event) {
      const length = 5;

      final counters =
          event.entries.where((entry) => entry.key.isCountry).toList()
            ..sort((a, b) => b.value.compareTo(a.value));
      final areas = counters
          .sublist(0, min(length, counters.length))
          .map<GeoArea>((e) => e.key)
          .toList();

      return areas;
    });
  }

  Stream<Map<Selection, List<GeoArea>>> sortedCountrySelections() {
    return selections.asyncMap(_doCurrentSortedCountrySelections);
  }

  Future<Map<Selection, List<GeoArea>>> currentSortedCountrySelections() async {
    final selections = await currentSelections;
    return _doCurrentSortedCountrySelections(selections);
  }

  Future<Map<Selection, List<GeoArea>>> currentSortedSubdivisionSelections(
    GeoArea parent,
  ) async {
    final subdivisions = await _areaBloc.fetchSubdivisions(parent);
    final selections = await _repo.fetchSubdivisionsSelections(parent);
    return _doCurrentSortedAreaSelections(selections, subdivisions);
  }

  Future<Map<Selection, List<GeoArea>>> _doCurrentSortedCountrySelections(
    Map<GeoArea, Selection> selections,
  ) async {
    final countries = await _areaBloc.allCountries();
    return _doCurrentSortedAreaSelections(selections, countries);
  }

  Map<Selection, List<GeoArea>> _doCurrentSortedAreaSelections(
    Map<GeoArea, Selection> selections,
    List<GeoArea> whitelist,
  ) {
    final live = <GeoArea>[];
    final lived = <GeoArea>[];
    final been = <GeoArea>[];
    final want = <GeoArea>[];
    for (final country in whitelist) {
      final selection = selections[country];

      if (selection == Selection.live) {
        live.add(country);
      } else if (selection == Selection.lived) {
        lived.add(country);
      } else if (selection == Selection.been) {
        been.add(country);
      } else if (selection == Selection.want) {
        want.add(country);
      }
    }

    final output = <Selection, List<GeoArea>>{};

    _addToOutput(output: output, selection: Selection.live, areas: live);
    _addToOutput(output: output, selection: Selection.lived, areas: lived);
    _addToOutput(output: output, selection: Selection.been, areas: been);
    _addToOutput(output: output, selection: Selection.want, areas: want);

    return output;
  }

  void _alphabetize(List<GeoArea> areas) {
    if (areas.length <= 1) {
      return;
    }

    areas.sort((a, b) => a.name.compareTo(b.name));
  }

  void _addToOutput({
    required Map<Selection, List<GeoArea>> output,
    required Selection selection,
    required List<GeoArea> areas,
  }) {
    if (areas.isEmpty) {
      return;
    }

    _alphabetize(areas);
    output[selection] = areas;
  }

  Stream<List<GeoArea>> listenToCountriesByType(Selection selection) {
    return _repo
        .listenToAreasByType({selection})
        .map(
          (selections) =>
              selections.where((area) => area.isCountry).toList()..sort(),
        );
  }

  Future<void> _loadBeenCounters() async {
    if (_beenCounterController.hasValue) {
      return;
    }

    final counters = await _service.fetchBeenCounters();
    _beenCounterController.add(counters);
  }

  @override
  void clear() {
    _beenCounterController.reset();
    _topVisitedCountriesController.reset();
    _notesRepo.clear();
    _service.clear();
    _loaded = false;
  }

  @override
  void dispose() {
    _topVisitedCountriesSubscription?.cancel();
    _languageSubscription?.cancel();
    _beenCounterController.close();
  }
}
