import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';

import '../../dependency_injection/dependency_injector.dart';
import '../../generic_widgets/frosted_glass_dialog.dart';
import '../../generic_widgets/platform_aware/platform_icon_button.dart';
import '../../models/selection.dart';
import '../in_app_purchase/buy_visited_pro_soft_sell.dart';
import '../map/selection_bar.dart';

class SelectionDialog extends StatelessWidget {
  const SelectionDialog({
    super.key,
    required this.title,
    required this.selection,
    this.onChanged,
    this.centerContent,
    this.trailing,
    this.availableSelections = SelectionBar.defaultSelections,
  });

  final Widget? centerContent;
  final String title;
  final Stream<Selection> selection;
  final List<Selection> availableSelections;
  final Future<void> Function(Selection selection)? onChanged;
  final Widget? trailing;

  @override
  Widget build(BuildContext context) {
    return FrostedGlassDialog(child: _buildContent(context));
  }

  Widget _buildContent(BuildContext context) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        _buildClose(context),
        _buildTitle(context),
        if (centerContent != null) centerContent!,
        if (availableSelections.isNotEmpty)
          SelectionBar(
            selection: selection,
            selections: availableSelections,
            onSelected: (selection) {
              if (selection == Selection.lived) {
                _checkIfLivedCanBeSelected(context);
                return;
              }
              _applySelection(context, selection);
            },
          ),
        if (trailing != null) ...[
          Divider(
            height: 12,
            color: Theme.of(context).brightness == Brightness.dark
                ? Colors.grey
                : null,
          ),
          trailing!,
        ],
      ],
    );
  }

  void _applySelection(BuildContext context, Selection selection) async {
    await onChanged?.call(selection);
    if (context.mounted) {
      Navigator.of(context).pop();
    }
  }

  void _checkIfLivedCanBeSelected(BuildContext context) async {
    if (DependencyInjector.iapBloc.canSelectLived) {
      _applySelection(context, Selection.lived);
      return;
    }

    final purchased = await BuyVisitedProSoftSell.present(context);
    if (context.mounted && purchased == true) {
      _applySelection(context, Selection.lived);
    }
  }

  Widget _buildClose(BuildContext context) {
    return Align(
      alignment: Alignment.centerRight,
      child: Padding(
        padding: const EdgeInsets.only(top: 4.0, right: 8.0),
        child: PlatformIconButton(
          icon: CupertinoIcons.xmark,
          onTapped: Navigator.of(context).pop,
        ),
      ),
    );
  }

  Widget _buildTitle(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16.0),
      child: Text(
        title,
        textAlign: TextAlign.center,
        style: Theme.of(
          context,
        ).textTheme.titleLarge?.copyWith(fontWeight: FontWeight.w800),
      ),
    );
  }
}
