import '../../caching/database.dart';
import '../../dependency_injection/dependency_injector.dart';
import '../../models/geo_area.dart';
import '../../models/selection.dart';
import 'area_selection_database.dart';

class AreaSelectionRepository {
  AreaSelectionRepository(this._database) {
    DependencyInjector.settingsBloc.language.listen((_) {
      _topPlaces.clear();
    });
  }

  final VisitedDatabase _database;
  late final _selectionDb = AreaSelectionDatabase(_database);
  final _topPlaces = <GeoArea, List<GeoArea>>{};

  Future<Map<GeoArea, Selection>> get selections =>
      _selectionDb.fetchSelections();

  Stream<Map<GeoArea, Selection>> get listenToSelections =>
      _selectionDb.listenToSelections();

  final _cache = <String, Selection>{};

  Selection? selectionSync(GeoArea area) => _cache[area.isoCode];

  Future<Selection> fetchSelection(GeoArea area) async =>
      _cache[area.isoCode] ??= await _selectionDb.fetchSelection(area);

  Stream<Selection> listenToSelection(GeoArea area) =>
      _selectionDb.listenToSelection(area);

  Future<bool> hasSelection(Selection selection) =>
      _selectionDb.hasSelection(selection);

  Future<void> acceptServerUpdates(
    Map<GeoArea, Selection> updates, {
    bool replace = true,
  }) async {
    for (final update in updates.entries) {
      _cache[update.key.isoCode] = update.value;
    }

    if (replace) {
      await _database.clearSelections();
    }

    final dtos = <AreaSelectionDto>{};

    updates.forEach(
      (area, selection) => dtos.add(
        AreaSelectionDto(
          areaId: area.id,
          isoCode: area.isoCode,
          type: selection.type,
          synced: true,
        ),
      ),
    );

    return _selectionDb.saveAll(dtos);
  }

  void updateSelection({
    required GeoArea area,
    required Selection selection,
    required bool synced,
  }) {
    final dto = AreaSelectionDto(
      areaId: area.id,
      isoCode: area.isoCode,
      type: selection.type,
      synced: synced,
    );
    _selectionDb.save(dto);
  }

  Future<Set<GeoArea>> fetchAreasByType(Set<Selection> selections) =>
      _selectionDb.fetchAreasByType(selections);

  Stream<Set<GeoArea>> listenToAreasByType(Set<Selection> selections) =>
      _selectionDb.listenToAreasByType(selections);

  Future<Map<GeoArea, Selection>> fetchSubdivisionsSelections(GeoArea parent) {
    return _selectionDb.fetchSelectionsByParent(parent);
  }

  List<GeoArea>? topPlaces(GeoArea area) {
    return _topPlaces[area];
  }

  void cacheTopPlaces(GeoArea area, List<GeoArea> places) =>
      _topPlaces[area] = places;

  void clear() {
    _topPlaces.clear();
    _cache.clear();
    _database.clearUserData();
  }
}
