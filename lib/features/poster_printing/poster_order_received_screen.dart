import 'package:flutter/material.dart';
import 'package:flutter_markdown/flutter_markdown.dart';

import '../../bridges/environment_fetcher.dart';
import '../../dependency_injection/dependency_injector.dart';
import '../../generic_widgets/platform_aware/platform_app_bar.dart';
import '../../generic_widgets/responsive_padding.dart';
import '../../generic_widgets/slide_and_fade_in.dart';
import '../../l10n/generated/app_localizations.dart';
import '../sharing/email_sender.dart';
import 'components/map_poster_preview.dart';
import 'components/poster_bottom_button.dart';

class PosterOrderReceivedScreen extends StatelessWidget {
  const PosterOrderReceivedScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final localizations = AppLocalizations.of(context)!;
    final theme = Theme.of(context).textTheme;
    return Scaffold(
      appBar: PlatformAppBar(
        title: localizations.posterOrderReceivedTitle,
      ),
      body: ResponsivePadding(
        fillToEdgeOnPhone: false,
        context: context,
        child: Column(
          children: [
            _buildSuccessMessage(localizations, theme, context),
            PosterBottomButton(
              title: localizations.continueText,
              onTapped: Navigator.of(context).pop,
            )
          ],
        ),
      ),
    );
  }

  Widget _buildSuccessMessage(
      AppLocalizations localizations, TextTheme theme, BuildContext context) {
    return Expanded(
      child: ListView(
        children: [
          const SizedBox(height: 32),
          Text(
            localizations.posterOrderReceivedSubtitle,
            textAlign: TextAlign.center,
            style: theme.headlineSmall?.copyWith(fontWeight: FontWeight.w800),
          ),
          const SlideAndFadeIn(
              duration: Duration(milliseconds: 700),
              offset: Offset(0, 10),
              curve: Curves.bounceIn,
              child: MapPosterPreview()),
          MarkdownBody(
            data: localizations.posterOrderReceivedInstructionsMarkdown,
            onTapLink: (text, href, title) {
              EmailSender(
                environment: Environment.of(context)!,
                user: DependencyInjector.sessionBloc.user,
                localizations: localizations,
                selections: DependencyInjector.areaSelectionBloc,
              ).send(
                context: context,
                subject: localizations.posterOrderReceivedEmailSubject,
              );
            },
            styleSheet:
                MarkdownStyleSheet.fromTheme(Theme.of(context)).copyWith(
                    p: const TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.w500,
                    ),
                    pPadding: const EdgeInsets.all(10)),
            // textAlign: TextAlign.center,
            // style: theme.headline6,
          ),
        ],
      ),
    );
  }
}
