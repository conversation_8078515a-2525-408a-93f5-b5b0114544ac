import 'package:flutter/material.dart';
import 'package:flutter_stripe/flutter_stripe.dart' hide Card;

import '../../dependency_injection/dependency_injector.dart';
import '../../generic_widgets/alert.dart';
import '../../generic_widgets/device_aware_bottom_padding.dart';
import '../../generic_widgets/navigation_extensions.dart';
import '../../generic_widgets/platform_aware/platform_app_bar.dart';
import '../../generic_widgets/platform_aware/platform_filled_button.dart';
import '../../generic_widgets/responsive_padding.dart';
import '../../generic_widgets/spinner.dart';
import '../../helpers/margin.dart';
import '../../l10n/generated/app_localizations.dart';
import 'components/coupon_row.dart';
import 'components/map_poster_preview.dart';
import 'models/price.dart';
import 'poster_order_received_screen.dart';
import 'poster_printing_bloc.dart';

class PosterReviewOrderScreen extends StatefulWidget {
  const PosterReviewOrderScreen({super.key});

  @override
  State<PosterReviewOrderScreen> createState() =>
      _PosterReviewOrderScreenState();
}

class _PosterReviewOrderScreenState extends State<PosterReviewOrderScreen> {
  final couponFocus = FocusNode();

  @override
  void initState() {
    super.initState();
    couponFocus.addListener(() {
      setState(() {});
    });
  }

  @override
  void dispose() {
    couponFocus.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final bloc = DependencyInjector.posterPrintingBloc;
    return Stack(
      children: [
        _buildScreen(context, bloc),
        _buildLoadingSpinner(bloc),
      ],
    );
  }

  Widget _buildLoadingSpinner(PosterPrintingBloc bloc) {
    return StreamBuilder<bool>(
      stream: bloc.busy,
      builder: (context, snapshot) {
        if (snapshot.data != true) {
          return const SizedBox();
        }

        return Positioned.fill(
          child: Container(
            width: double.infinity,
            height: double.infinity,
            color: Colors.black38,
            child: const Center(
              child: Spinner(),
            ),
          ),
        );
      },
    );
  }

  Widget _buildScreen(BuildContext context, PosterPrintingBloc bloc) {
    final localizations = AppLocalizations.of(context)!;
    return Scaffold(
      appBar: PlatformAppBar(
        title: localizations.posterReviewOrderTitle,
      ),
      body: Column(
        children: [
          Expanded(
            child: ResponsivePadding(
              context: context,
              fillToEdgeOnPhone: false,
              child: _buildOrderDetails(bloc, localizations),
            ),
          ),
          if (!couponFocus.hasFocus) ...[
            ResponsivePadding(
              context: context,
              fillToEdgeOnPhone: false,
              child: PlatformFilledButton(
                title: localizations.buyNow,
                onTapped: () => _onBuyTapped(context, bloc),
              ),
            ),
            const SizedBox(height: Margin.standard),
            DeviceAwareBottomPadding(
              child: Text(
                localizations.secureCheckoutDisclaimer,
                style: Theme.of(context).textTheme.bodySmall,
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildOrderDetails(
    PosterPrintingBloc bloc,
    AppLocalizations localizations,
  ) {
    return CustomScrollView(
      slivers: [
        _buildPrimaryLineItems(bloc, localizations),
        _buildTotals(bloc, localizations),
      ],
    );
  }

  Widget _buildTotals(PosterPrintingBloc bloc, AppLocalizations localizations) {
    return StreamBuilder<Price?>(
      stream: bloc.tax,
      builder: (context, snapshot) {
        final hasTax = snapshot.data != null;

        return SliverList.list(
          children: [
            if (hasTax) ...[
              _buildListeningLineItem(
                title: localizations.subtotal,
                price: bloc.subtotal,
              ),
              _buildListeningLineItem(
                title: localizations.tax,
                price: bloc.tax,
              ),
              const Divider(),
            ],
            _buildListeningLineItem(
              title: localizations.total,
              price: bloc.total,
            ),
          ],
        );
      },
    );
  }

  Widget _buildPrimaryLineItems(
    PosterPrintingBloc bloc,
    AppLocalizations localizations,
  ) {
    return SliverList.list(
      children: [
        const MapPosterPreview(),
        CouponInputRow(focus: couponFocus),
        _buildListeningLineItem(
          title: localizations.posterProductName,
          price: bloc.price,
        ),
        StreamBuilder<Price?>(
          stream: bloc.discount,
          builder: (context, snapshot) {
            final discount = snapshot.data;
            if (discount == null) {
              return const SizedBox();
            }

            return _buildLineItem(
              title: AppLocalizations.of(context)!.discount,
              price: discount,
              isDiscount: true,
            );
          },
        ),
        _buildListeningLineItem(
          title: localizations.shipping,
          price: bloc.shipping,
        ),
        const Divider(),
      ],
    );
  }

  Widget _buildListeningLineItem({
    required String title,
    required Stream<Price?> price,
  }) {
    return StreamBuilder(
      stream: price,
      builder: (context, snapshot) {
        final price = snapshot.data;
        return _buildLineItem(
          title: title,
          price: price,
        );
      },
    );
  }

  Widget _buildLineItem({
    required String title,
    Price? price,
    bool isDiscount = false,
  }) {
    return ListTile(
      title: Text(title),
      textColor: isDiscount ? Colors.green : null,
      trailing: price == null
          ? const SizedBox()
          : Text(isDiscount ? '-${price.formatted}' : price.formatted),
    );
  }

  void _onBuyTapped(BuildContext context, PosterPrintingBloc bloc) async {
    final nav = Navigator.of(context);
    final localizations = AppLocalizations.of(context)!;
    try {
      await bloc.buy();
      nav.pushMaterialAndRemoveUntil(
        builder: (_) => const PosterOrderReceivedScreen(),
        name: 'poster_order_received',
        predicate: (route) => route.isFirst,
      );
    } on StripeException catch (e) {
      if (e.error.code == FailureCode.Canceled) {
        return;
      }

      if (context.mounted) {
        Alert(
          title: localizations.errorTitle,
          message: e.error.localizedMessage ?? e.error.message,
        ).show(context);
      }
    } catch (e) {
      if (context.mounted) {
        Alert(
          title: localizations.errorTitle,
          message: e.toString(),
        ).show(context);
      }
    } finally {
      bloc.removeCoupon();
    }
  }
}
