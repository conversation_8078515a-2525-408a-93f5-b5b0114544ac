import 'package:address/address.dart';
import 'package:email_validator/email_validator.dart';
import 'package:flutter/material.dart';

import '../../dependency_injection/dependency_injector.dart';
import '../../generic_widgets/area_flag.dart';
import '../../generic_widgets/device_aware_bottom_padding.dart';
import '../../generic_widgets/navigation_extensions.dart';
import '../../generic_widgets/platform_aware/platform_app_bar.dart';
import '../../generic_widgets/platform_aware/platform_filled_button.dart';
import '../../generic_widgets/responsive_padding.dart';
import '../../generic_widgets/separated_tile.dart';
import '../../helpers/margin.dart';
import '../../l10n/generated/app_localizations.dart';
import '../../models/geo_area.dart';
import 'components/map_poster_preview.dart';
import 'components/visited_input_field.dart';
import 'models/price.dart';
import 'models/shipping_address.dart';
import 'poster_review_order_screen.dart';

class EnterShippingAddressScreen extends StatefulWidget {
  const EnterShippingAddressScreen({super.key});

  @override
  State<EnterShippingAddressScreen> createState() =>
      _EnterShippingAddressScreenState();
}

class _EnterShippingAddressScreenState
    extends State<EnterShippingAddressScreen> {
  GeoArea? _currentCountry;

  final formKey = GlobalKey<FormState>();

  final nameController = TextEditingController();
  final emailController = TextEditingController();
  final street1Controller = TextEditingController();
  final street2Controller = TextEditingController();
  final cityController = TextEditingController();
  final stateController = TextEditingController();
  final countryController = TextEditingController();
  final postalController = TextEditingController();

  final nameFocus = FocusNode();
  final emailFocus = FocusNode();
  final street1Focus = FocusNode();
  final street2Focus = FocusNode();
  final cityFocus = FocusNode();
  final stateFocus = FocusNode();
  final countryFocus = FocusNode();
  final postalFocus = FocusNode();

  late final allFocuses = [
    nameFocus,
    emailFocus,
    street1Focus,
    street2Focus,
    cityFocus,
    stateFocus,
    countryFocus,
    postalFocus,
  ];

  bool _isAnyFocusActive = false;

  @override
  void initState() {
    super.initState();

    emailController.text = DependencyInjector.sessionBloc.user?.email ?? '';

    DependencyInjector.areaSelectionBloc.currentCountryUserLivesIn().then((
      live,
    ) {
      if (live != null) {
        setState(() {
          _currentCountry = live;
          countryController.text = live.name;
        });
      }
    });

    for (final focus in allFocuses) {
      focus.addListener(_onFocusChanged);
    }
  }

  void _onFocusChanged() {
    for (final focus in allFocuses) {
      if (focus.hasFocus && !_isAnyFocusActive) {
        setState(() {
          _isAnyFocusActive = true;
        });
        return;
      }
    }

    if (_isAnyFocusActive) {
      setState(() {
        _isAnyFocusActive = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    final localizations = AppLocalizations.of(context)!;
    return Scaffold(
      appBar: PlatformAppBar(title: localizations.enterShippingAddress),
      body: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const SizedBox(height: Margin.small),
          _buildMapPreview(context),
          _buildAddressForm(context),
          if (!_isAnyFocusActive) ...[
            _buildReviewOrderButton(context),
            Center(
              child: DeviceAwareBottomPadding(
                child: Text(
                  localizations.posterNoRefunds,
                  textAlign: TextAlign.center,
                ),
              ),
            ),
            const SizedBox(height: Margin.small),
          ],
        ],
      ),
    );
  }

  Widget _buildMapPreview(BuildContext context) {
    return ResponsivePadding(
      fillToEdgeOnPhone: false,
      context: context,
      child: Row(
        spacing: Margin.standard,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Expanded(
            flex: 2,
            child: SizedBox(
              height: 180,
              child: MapPosterPreview(includeResponsivePadding: false),
            ),
          ),
          Expanded(
            flex: 1,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(AppLocalizations.of(context)!.posterProductName),
                StreamBuilder<Price>(
                  stream: DependencyInjector.posterPrintingBloc.total,
                  builder: (context, snapshot) {
                    final price = snapshot.data;
                    if (price == null) {
                      return const SizedBox();
                    }
                    return Text(
                      price.formatted,
                      style: const TextStyle(fontWeight: FontWeight.bold),
                    );
                  },
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildReviewOrderButton(BuildContext context) {
    return ResponsivePadding(
      context: context,
      fillToEdgeOnPhone: false,
      child: PlatformFilledButton(
        title: AppLocalizations.of(context)!.posterReviewOrder,
        onTapped: () => _onReviewOrderTapped(context),
      ),
    );
  }

  Widget _buildAddressForm(BuildContext context) {
    final addressFormatter = AddressFormatter(
      Localizations.localeOf(context).languageCode,
    );
    final fields = addressFormatter.formatForm(
      _currentCountry?.isoCode ??
          Localizations.localeOf(context).countryCode ??
          'US',
    );

    final localizations = AppLocalizations.of(context)!;

    return Form(
      key: formKey,
      child: Expanded(
        child: ListView(
          padding: ResponsivePaddingShared.responsivePadding(
            context,
            fillToEdgeOnPhone: false,
          ),
          children: [
            const SizedBox(height: 16),
            VisitedInputField(
              controller: emailController,
              label: localizations.email,
              focusNode: emailFocus,
              autocompleteHints: [AutofillHints.email],
              validator: (text) => _emailValidator(text, localizations),
            ),
            for (final field in fields)
              if (field.type == AddressFormField.fullName)
                VisitedInputField(
                  controller: nameController,
                  focusNode: nameFocus,
                  label: localizations.fullName,
                  autocompleteHints: [AutofillHints.name],
                  validator: (text) => _contentRequiredValidator(
                    text,
                    errorMessage: localizations.fullNameEmptyError,
                  ),
                )
              else if (field.type == AddressFormField.addressLine1)
                VisitedInputField(
                  controller: street1Controller,
                  focusNode: street1Focus,
                  label: field.label,
                  autocompleteHints: [
                    AutofillHints.streetAddressLevel1,
                    AutofillHints.streetAddressLine1,
                  ],
                  validator: (text) => _contentRequiredValidator(
                    text,
                    errorMessage: localizations.streetAddressEmptyError,
                  ),
                )
              else if (field.type == AddressFormField.addressLine2)
                VisitedInputField(
                  controller: street2Controller,
                  focusNode: street2Focus,
                  label: field.label,
                  autocompleteHints: [
                    AutofillHints.streetAddressLevel2,
                    AutofillHints.streetAddressLine2,
                  ],
                ) // Optional Field
              else if (field.type == AddressFormField.city)
                VisitedInputField(
                  controller: cityController,
                  focusNode: cityFocus,
                  autocompleteHints: [AutofillHints.addressCity],
                  label: field.label,
                  validator: (text) => _contentRequiredValidator(
                    text,
                    errorMessage: localizations.cityEmptyError,
                  ),
                )
              else if (field.type == AddressFormField.zone) ...[
                VisitedInputField(
                  controller: stateController,
                  focusNode: stateFocus,
                  autocompleteHints: [AutofillHints.addressState],
                  label: field.label,
                  validator: (text) => _contentRequiredValidator(
                    text,
                    errorMessage: localizations.fieldEmptyError(field.label),
                  ),
                ),
                _buildCountryPicker(context),
              ] else if (field.type == AddressFormField.postalCode)
                VisitedInputField(
                  controller: postalController,
                  focusNode: postalFocus,
                  autocompleteHints: [AutofillHints.postalCode],
                  label: field.label,
                  validator: (text) => _contentRequiredValidator(
                    text,
                    errorMessage: localizations.fieldEmptyError(field.label),
                  ),
                ),
          ],
        ),
      ),
    );
  }

  String? _emailValidator(String? text, AppLocalizations localizations) {
    final error = _contentRequiredValidator(
      text,
      errorMessage: localizations.emailEmptyError,
    );
    if (error != null) {
      return error;
    }

    final valid = EmailValidator.validate(text?.trim() ?? '');
    if (!valid) {
      return localizations.enterValidEmail;
    }

    return null;
  }

  String? _contentRequiredValidator(
    String? input, {
    required String errorMessage,
  }) {
    if (input?.trim().isEmpty ?? true) {
      return errorMessage;
    }

    return null;
  }

  Widget _buildCountryPicker(BuildContext context) {
    return CountryInputField(
      countryController: countryController,
      onCountrySelected: (GeoArea area) {
        _currentCountry = area;
        Future.delayed(
          const Duration(milliseconds: 100),
        ).then((_) => setState(() {}));
      },
      validator: (text) => _currentCountry == null
          ? AppLocalizations.of(context)!.countryEmptyError
          : null,
    );
  }

  void _onReviewOrderTapped(BuildContext context) async {
    FocusManager.instance.primaryFocus?.unfocus();
    if (formKey.currentState?.validate() == false) {
      return;
    }

    final address = ShippingAddress(
      name: nameController.text,
      email: emailController.text,
      addressLine1: street1Controller.text,
      addressLine2: street2Controller.text,
      city: cityController.text,
      state: stateController.text,
      country: _currentCountry!,
      postalCode: postalController.text,
    );

    final nav = Navigator.of(context);
    await DependencyInjector.posterPrintingBloc.setShippingAddress(address);
    nav.pushMaterialRoute(
      name: 'poster_review_order',
      builder: (_) => const PosterReviewOrderScreen(),
    );
  }

  @override
  void dispose() {
    nameController.dispose();
    emailController.dispose();
    street1Controller.dispose();
    street2Controller.dispose();
    cityController.dispose();
    stateController.dispose();
    countryController.dispose();
    postalController.dispose();

    for (final focus in allFocuses) {
      focus.dispose();
    }

    super.dispose();
  }
}

class CountryInputField extends StatelessWidget {
  const CountryInputField({
    super.key,
    required this.countryController,
    required this.onCountrySelected,
    required this.validator,
  });

  final TextEditingController countryController;
  final void Function(GeoArea area) onCountrySelected;
  final String? Function(String? text) validator;

  @override
  Widget build(BuildContext context) {
    final localizations = AppLocalizations.of(context)!;
    return Autocomplete<GeoArea>(
      initialValue: countryController.value,
      optionsBuilder: _fetchAutoCompleteOptions,
      onSelected: (area) {
        countryController.text = area.name;
        FocusManager.instance.primaryFocus?.unfocus();

        onCountrySelected(area);
      },
      optionsViewBuilder: _buildCountryAutocompleteOptions,
      fieldViewBuilder: (_, controller, focusNode, onFieldSubmitted) {
        return VisitedInputField(
          controller: controller,
          label: localizations.country,
          autocompleteHints: [AutofillHints.countryName],
          focusNode: focusNode,
          onFieldSubmitted: onFieldSubmitted,
          validator: validator,
        );
      },
    );
  }

  Future<Iterable<GeoArea>> _fetchAutoCompleteOptions(
    TextEditingValue value,
  ) async => (await DependencyInjector.areaBloc.allCountries()).where(
    (element) =>
        element.name.toLowerCase().contains(value.text.toLowerCase().trim()),
  );

  Widget _buildCountryAutocompleteOptions(
    BuildContext context,
    AutocompleteOnSelected<GeoArea> onSelected,
    Iterable<GeoArea> options,
  ) {
    final list = options.toList();
    return Align(
      alignment: Alignment.topLeft,
      child: LayoutBuilder(
        builder: (context, constraints) {
          return ConstrainedBox(
            constraints: BoxConstraints(
              maxHeight: 200,
              maxWidth: constraints.maxWidth - 30,
            ),
            child: Material(
              elevation: 7,
              child: ListView.builder(
                itemCount: list.length,
                shrinkWrap: true,
                padding: EdgeInsets.zero,
                itemBuilder: (context, index) {
                  final area = list[index];
                  return SeparatedTile(
                    child: ListTile(
                      leading: AreaFlag(area: area),
                      title: Text(area.name),
                      onTap: () => onSelected(area),
                    ),
                  );
                },
              ),
            ),
          );
        },
      ),
    );
  }
}
