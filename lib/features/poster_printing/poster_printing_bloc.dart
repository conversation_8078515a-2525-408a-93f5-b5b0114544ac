import 'dart:async';

import 'package:flutter/material.dart';
import 'package:flutter_stripe/flutter_stripe.dart';
import 'package:rxdart/rxdart.dart';

import '../../dependency_injection/dependency_injector.dart';
import '../../generic_widgets/bloc.dart';
import '../../models/palette.dart';
import '../../models/selection.dart';
import '../areas/area_bloc.dart';
import '../areas/geometry_bloc.dart';
import '../disputed_territories/disputed_territories_bloc.dart';
import '../selection/area_selection_bloc.dart';
import '../settings/settings_bloc.dart';
import 'models/coupon.dart';
import 'models/currency.dart';
import 'models/poster.dart';
import 'models/poster_type.dart';
import 'models/price.dart';
import 'models/shipping.dart';
import 'models/shipping_address.dart';
import 'poster_printing_service.dart';

typedef PosterConfig = (List<Selection>, Palette, PosterType);

class PosterPrintingBloc implements Bloc {
  PosterPrintingBloc({
    required this.disputedTerritoryBloc,
    required this.areaBloc,
    required this.settingsBloc,
    required GeometryBloc geometryBloc,
    required AreaSelectionBloc selectionBloc,
  }) : _service = PosterPrintingService() {
    _paletteSubscription = settingsBloc.palette.listen(_onMapPaletteChanged);
    _customPaletteSubscription = settingsBloc.customPalette.listen(
      _onCustomPaletteChange,
    );
  }

  Stream<List<Selection>> get printableSelections =>
      _printableSelectionsController.stream;

  Stream<PosterType> get posterType => _typeController.stream;
  PosterType get currentPosterType => _typeController.value;

  Stream<Coupon?> get coupon => _couponController.stream;
  Stream<Price> get price => _priceController.stream;
  Stream<Shipping> get shipping => _shippingController.stream;

  Stream<bool> get busy => _busyController.stream;

  Stream<Price> get subtotal => Rx.combineLatest3(
    price,
    discount,
    shipping,
    (Price price, Price? discount, Shipping shipping) {
      if (discount != null) {
        return price - discount + shipping;
      }

      return price + shipping;
    },
  );

  Stream<Price?> get discount => _couponController.map(
    (coupon) {
      if (coupon == null) {
        return null;
      }

      return coupon.discount;
    },
  );

  double roundTo2Decimals(double value) {
    return (value * 100).roundToDouble() / 100;
  }

  Stream<PosterConfig> get posterConfig => Rx.combineLatest3(
    printableSelections,
    palette,
    posterType,
    (
      selectionsToRender,
      palette,
      type,
    ) {
      return (
        selectionsToRender,
        palette,
        type,
      );
    },
  );

  Stream<Palette> get palette => _paletteController.stream;
  Stream<Palette> get customPalette => _customPaletteController.stream;

  bool _hasBrokenConnectionWithMapPalette = false;
  bool _hadBrokenConnectionWithCustomPalette = false;

  StreamSubscription? _paletteSubscription;
  StreamSubscription? _customPaletteSubscription;
  void _onMapPaletteChanged(Palette palette) {
    if (_hasBrokenConnectionWithMapPalette) {
      return;
    }

    _paletteController.add(palette);
  }

  void updatePosterType(PosterType type) {
    _typeController.valueOrNull?.dispose();
    _typeController.add(type);
    _printableSelectionsController.add(type.availableSelections);
  }

  void _onCustomPaletteChange(Palette palette) {
    if (_hadBrokenConnectionWithCustomPalette) {
      return;
    }

    _customPaletteController.add(palette);
  }

  void useCustomSelections(bool useCustomSelections) {
    final current = _typeController.value;
    if (useCustomSelections != current.isUsingCustomSelections) {
      final updated = current.toggle();
      updatePosterType(updated);
    }
  }

  void updatePalette(Palette palette) {
    _paletteSubscription?.cancel();
    _paletteSubscription = null;
    _hasBrokenConnectionWithMapPalette = true;
    _paletteController.add(palette);
  }

  void updateCustomPalette(Palette palette) {
    _customPaletteSubscription?.cancel();
    _customPaletteSubscription = null;
    _hadBrokenConnectionWithCustomPalette = true;
    _customPaletteController.add(palette);
    updatePalette(palette);
  }

  Stream<Price?> get tax => subtotal.map(
    (subtotal) {
      if (subtotal.tax == null) {
        return null;
      }

      return Price(
        id: 'tax',
        currency: subtotal.currency,
        unitDecimal: subtotal.tax!,
        unitAmount: (subtotal.tax! * 100).round(),
        tax: subtotal.tax,
      );
    },
  );

  Stream<Price> get total =>
      Rx.combineLatest2(subtotal, tax, (Price subtotal, Price? tax) {
        if (tax == null) {
          return subtotal;
        }

        return subtotal + tax;
      });

  List<Selection> get currentPrintableSelections =>
      _printableSelectionsController.value;

  Poster? _poster;
  ShippingAddress? _shippingAddress;

  final PosterPrintingService _service;
  final DisputedTerritoriesBloc disputedTerritoryBloc;
  final AreaBloc areaBloc;
  final SettingsBloc settingsBloc;

  late final _printableSelectionsController = BehaviorSubject.seeded(
    _typeController.value.availableSelections,
  )..onListen = _checkIfLivedShouldBeAdded;

  final _priceController = BehaviorSubject<Price>();
  final _couponController = BehaviorSubject<Coupon?>();
  final _shippingController = BehaviorSubject<Shipping>();
  final _busyController = BehaviorSubject.seeded(false);
  late final _typeController = BehaviorSubject<PosterType>.seeded(
    CountryPosterType(),
  );

  late final _paletteController = BehaviorSubject.seeded(
    settingsBloc.currentPalette,
  );
  late final _customPaletteController = BehaviorSubject.seeded(
    settingsBloc.currentCustomPalette,
  );

  List<Price> get availablePrices {
    final poster = _poster;
    if (poster == null) {
      throw Exception('Prices are unavailable.  Fetch poster first.');
    }

    final prices = <Price>[];
    prices.add(poster.price);

    // Only allow Canadian if that currency is the primary
    // because we have to add tax.
    if (poster.price.currency == Currency.cad) {
      return prices;
    }

    final usdPrice = poster.usdPrice;
    if (poster.price.currency != Currency.usd && usdPrice != null) {
      prices.add(usdPrice);
    }

    final eurPrice = poster.eurPrice;
    if (poster.price.currency != Currency.eur && eurPrice != null) {
      prices.add(eurPrice);
    }

    return prices;
  }

  void toggleSelection(Selection selection) {
    final selections = [..._printableSelectionsController.value];
    selections.contains(selection)
        ? selections.remove(selection)
        : selections.add(selection);

    selections.sort();
    _printableSelectionsController.add(selections.reversed.toList());
  }

  void selectPrice(Price? price) {
    if (price == null) {
      return;
    }

    _priceController.add(price);
  }

  Future<Poster> fetchPoster() async {
    if (_poster != null) {
      return _poster!;
    }

    final poster = await _service.fetchPoster();

    _priceController.add(poster.price);
    _shippingController.add(poster.shipping.national);

    _poster = poster;
    return poster;
  }

  Future<void> buy() async {
    final poster = _poster;
    if (poster == null) {
      return;
    }

    final address = _shippingAddress;
    if (address == null) {
      throw Exception('Cannot buy the poster without setting an address');
    }

    try {
      _busyController.add(true);

      final payment = await _service.buyPoster(
        poster: poster,
        coupon: _couponController.valueOrNull,
        price: _priceController.value,
        shipping: _shippingController.value,
        shippingAddress: address,
        selections: _printableSelectionsController.value,
        disputedTerritories: disputedTerritoryBloc.currentSelections,
        palette: _paletteController.value,
        type: _typeController.value,
      );

      final stripe = Stripe.instance;

      await stripe.initPaymentSheet(
        paymentSheetParameters: _buildPaymentSheet(payment, address),
      );

      await Future.delayed(const Duration(milliseconds: 100));
      await stripe.presentPaymentSheet();

      debugPrint('Payment confirmed!');
    } catch (e) {
      debugPrint(e.toString());
      rethrow;
    } finally {
      _busyController.add(false);
    }
  }

  SetupPaymentSheetParameters _buildPaymentSheet(
    PaymentRequest payment,
    ShippingAddress address,
  ) {
    return SetupPaymentSheetParameters(
      paymentIntentClientSecret: payment.paymentIntent,
      merchantDisplayName: 'Visited',
      customerId: payment.customerId,
      customerEphemeralKeySecret: payment.ephemeralKey,
      applePay: _buildApplePayConfiguration(),
      googlePay: const PaymentSheetGooglePay(
        merchantCountryCode: 'CA',
      ),
      style: ThemeMode.system,
      billingDetails: BillingDetails(
        name: address.name,
        email: address.email,
        address: Address(
          city: address.city,
          country: address.country.isoCode,
          line1: address.addressLine1,
          line2: address.addressLine2,
          state: address.state,
          postalCode: address.postalCode,
        ),
      ),
    );
  }

  PaymentSheetApplePay _buildApplePayConfiguration() {
    return const PaymentSheetApplePay(merchantCountryCode: 'CA');
  }

  Future<void> setShippingAddress(ShippingAddress address) async {
    _shippingAddress = address;

    final poster = _poster;
    if (poster == null) {
      throw StateError(
        'You must have a poster product before trying to set a shipping address',
      );
    }

    final selectedCurrency = _priceController.value.currency;
    List<Shipping> shippingOptions;

    final usdShipping = poster.usdShipping;
    final eurShipping = poster.eurShipping;
    if (selectedCurrency == Currency.usd && usdShipping != null) {
      shippingOptions = usdShipping;
    } else if (selectedCurrency == Currency.eur && eurShipping != null) {
      shippingOptions = eurShipping;
    } else {
      shippingOptions = poster.shipping;
    }

    final geoArea = await areaBloc.areaByIsoCode(selectedCurrency.isoCode);

    final shipping = (geoArea != address.country)
        ? shippingOptions.international ?? shippingOptions.first
        : shippingOptions.national;

    _shippingController.add(shipping);
  }

  @override
  void clear() {
    _printableSelectionsController.add([
      Selection.live,
      Selection.been,
      Selection.want,
    ]);
  }

  @override
  void dispose() {
    _busyController.close();
    _printableSelectionsController.close();
    _priceController.close();
    _shippingController.close();
    _paletteController.close();
    _paletteSubscription?.cancel();
    _customPaletteController.close();
    _customPaletteSubscription?.cancel();
    _typeController.close();
    _couponController.close();
  }

  void _checkIfLivedShouldBeAdded() {
    if (!DependencyInjector.iapBloc.canSelectLived) {
      return;
    }

    final selections = _printableSelectionsController.value;
    if (selections.contains(Selection.lived)) {
      return;
    }

    _printableSelectionsController.add(
      [Selection.lived, ...selections],
    );
  }

  Future<Coupon> applyCoupon(String couponCode) async {
    try {
      final coupon = await _service.validateCouponCode(
        couponCode.toUpperCase(),
        _priceController.value,
      );
      _couponController.add(coupon);
      return coupon;
    } catch (e) {
      _couponController.add(null);
      rethrow;
    }
  }

  void removeCoupon() {
    _couponController.add(null);
  }
}
