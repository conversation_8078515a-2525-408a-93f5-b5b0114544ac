import '../../dependency_injection/dependency_injector.dart';
import '../../models/palette.dart';
import '../../models/selection.dart';
import '../disputed_territories/disputed_territory.dart';
import 'models/coupon.dart';
import 'models/poster.dart';
import 'models/poster_type.dart';
import 'models/price.dart';
import 'models/shipping.dart';
import 'models/shipping_address.dart';

class PosterPrintingService {
  late final client = DependencyInjector.client;

  Future<Poster> fetchPoster() async {
    final result = await client.get('stripe/productDetails');
    return Poster.fromJson(result);
  }

  Future<PaymentRequest> buyPoster({
    required Poster poster,
    required Price price,
    required ShippingAddress shippingAddress,
    required Shipping shipping,
    required List<Selection> selections,
    Map<DisputedTerritory, DisputedTerritoryOption>? disputedTerritories,
    required Palette palette,
    required PosterType type,
    Coupon? coupon,
  }) async {
    final exportedSelections = await type.selectionJson();

    final postBody = {
      'payment': {
        'clientSdkVersion': '2020-08-27', //TODO: Remove hardcoding
        'productId': poster.id,
        'priceId': price.id,
        'shippingId': shipping.id,
        'receiptEmail': shippingAddress.email,
        if (coupon != null) 'couponId': coupon.id,
      },
      'poster': {
        'mapType': type.backendMapType,
        'shippingAddress': shippingAddress.toJson(),
        'selectionTypes': selections.map((e) => e.backendKey).toList(),
        'selections': exportedSelections,
        'palette': palette.toJson(eightBitColourArrays: true),
        if (disputedTerritories != null)
          'disputedTerritoryPreferences': _buildDisputedTerritoryJson(
            disputedTerritories,
          ),
      },
    };

    final response = await client.post(
      'stripe/payment',
      body: postBody,
    );

    return PaymentRequest.fromJson(response);
  }

  Map _buildDisputedTerritoryJson(
    Map<DisputedTerritory, DisputedTerritoryOption> disputedSelections,
  ) {
    return Map.fromEntries(
      (disputedSelections).entries
          .where((entry) => !entry.value.isDefault)
          .map(
            (entry) =>
                MapEntry(entry.key.area.isoCode, entry.value.area.isoCode),
          ),
    );
  }

  Future<Coupon> validateCouponCode(
    String couponCode,
    Price selectedPrice,
  ) async {
    final response = await client.post(
      'stripe/coupon',
      body: {
        'couponCode': couponCode,
        'priceId': selectedPrice.id,
      },
    );
    return Coupon.fromJson(response);
  }
}

class PaymentRequest {
  final String paymentIntent;
  final String ephemeralKey;
  final String customerId;

  PaymentRequest.fromJson(Map json)
    : paymentIntent = json['paymentIntent'],
      ephemeralKey = json['ephemeralKey'],
      customerId = json['customerId'];
}
