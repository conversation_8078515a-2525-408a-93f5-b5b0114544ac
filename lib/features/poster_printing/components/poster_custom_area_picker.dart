import 'package:flutter/material.dart';

import '../../../dependency_injection/dependency_injector.dart';
import '../../../helpers/string_extensions.dart';
import '../../../l10n/generated/app_localizations.dart';
import '../../../models/geo_area.dart';
import '../../../models/selection.dart';
import '../../areas/custom_area_picker.dart';
import '../models/poster_type.dart';

class PosterCustomAreaPicker extends StatelessWidget {
  const PosterCustomAreaPicker({
    super.key,
    required this.poster,
  });

  final PosterType<GeoArea, String> poster;

  @override
  Widget build(BuildContext context) {
    return CustomAreaPicker(
      titleFetcher: () async => switch (poster) {
        CountryPosterType() =>
          AppLocalizations.of(context)!.countries.capitalized,
        ExperiencePosterType(experience: final experience) =>
          experience?.name ?? AppLocalizations.of(context)!.experiences,
      },
      areaFetcher: _fetchAreas,
      selectionStream: (GeoArea area) => poster.selections.map(
        (areas) => areas[area] ?? Selection.clear,
      ),
      availableSelections: poster.availableSelections,
      onSelected: (GeoArea area, Selection selection) =>
          poster.selectForCustomPoster(area, selection),
    );
  }

  Future<List<GeoArea>> _fetchAreas() {
    return switch (poster) {
      CountryPosterType() => DependencyInjector.areaBloc.allCountries(),
      ExperiencePosterType(experience: final experience) =>
        DependencyInjector.experienceBloc.fetchAreas(experience!),
    };
  }
}
