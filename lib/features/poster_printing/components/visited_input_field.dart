import 'package:flutter/material.dart';

class VisitedInputField extends StatelessWidget {
  final TextEditingController controller;
  final String label;
  final List<String>? autocompleteHints;
  final FormFieldValidator<String>? validator;
  final FocusNode? focusNode;
  final VoidCallback? onFieldSubmitted;

  const VisitedInputField({
    super.key,
    required this.controller,
    required this.label,
    this.autocompleteHints,
    this.validator,
    this.focusNode,
    this.onFieldSubmitted,
  });

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: TextFormField(
        controller: controller,
        focusNode: focusNode,
        validator: validator,
        autofillHints: autocompleteHints,
        onFieldSubmitted: (_) => onFieldSubmitted?.call(),
        style: TextStyle(
          fontSize: 16,
          fontWeight: FontWeight.w600,
          color: Theme.of(context).primaryColor,
        ),
        decoration: buildDecoration(context, label),
      ),
    );
  }

  static InputDecoration buildDecoration(BuildContext context, String label) {
    return InputDecoration(
      labelText: label,
      fillColor: Theme.of(context).brightness == Brightness.light
          ? Colors.white70
          : Colors.black54,
      filled: true,
      labelStyle: const TextStyle(
        fontSize: 16,
        fontWeight: FontWeight.w600,
      ),
      border: const OutlineInputBorder(),
      enabledBorder: OutlineInputBorder(
        borderSide: BorderSide(
          color: Theme.of(context).primaryColor.withAlpha(32),
        ),
      ),
    );
  }
}
