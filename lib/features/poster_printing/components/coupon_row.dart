import 'package:flutter/material.dart';

import '../../../dependency_injection/dependency_injector.dart';
import '../../../generic_widgets/alert.dart';
import '../../../generic_widgets/platform_aware/platform_filled_button.dart';
import '../../../generic_widgets/spinner.dart';
import '../../../helpers/margin.dart';
import '../../../l10n/generated/app_localizations.dart';
import 'visited_input_field.dart';

class CouponInputRow extends StatefulWidget {
  const CouponInputRow({
    super.key,
    required this.focus,
  });

  final FocusNode focus;

  @override
  State<CouponInputRow> createState() => _CouponInputRowState();
}

class _CouponInputRowState extends State<CouponInputRow> {
  final _couponCodeController = TextEditingController();
  final formKey = GlobalKey<FormState>();
  String? errorMessage;

  @override
  Widget build(BuildContext context) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(8.0),
        child: Form(
          key: formKey,
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Expanded(
                flex: 4,
                child: VisitedInputField(
                  controller: _couponCodeController,
                  focusNode: widget.focus,
                  label: AppLocalizations.of(context)!.couponCode,
                  validator: (String? value) {
                    if (errorMessage != null) {
                      return errorMessage;
                    }

                    return null;
                  },
                ),
              ),
              const SizedBox(width: Margin.small),
              Expanded(
                flex: 1,
                child: Padding(
                  padding: const EdgeInsets.only(top: 12.0),
                  child: PlatformFilledButton(
                    fontSize: 14,
                    title: AppLocalizations.of(context)!.apply,
                    onTapped: _onSubmit,
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _onSubmit() async {
    final localizations = AppLocalizations.of(context)!;

    widget.focus.unfocus();

    final couponCode = _couponCodeController.text.trim();

    if (couponCode.isEmpty) {
      _setError(localizations.noCouponCode);
      return;
    }

    _clearError();

    final printingBloc = DependencyInjector.posterPrintingBloc;
    try {
      SpinnerDialog.present(context);
      _clearError();
      final coupon = await printingBloc.applyCoupon(couponCode);

      if (mounted) {
        Alert(
          title: localizations.couponApplied,
          message: coupon.description(localizations),
        ).show(context);
      }
    } catch (e) {
      _setError(localizations.invalidCouponCode);
    } finally {
      SpinnerDialog.dismiss();
    }
  }

  void _setError(String? message) {
    if (!mounted) {
      return;
    }

    setState(() {
      errorMessage = message;
    });
    formKey.currentState?.validate();
  }

  void _clearError() {
    if (errorMessage == null) {
      return;
    }

    _setError(null);
  }

  @override
  void dispose() {
    _couponCodeController.dispose();
    super.dispose();
  }
}
