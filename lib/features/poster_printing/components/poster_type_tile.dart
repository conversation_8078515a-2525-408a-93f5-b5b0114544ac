import 'package:flutter/material.dart';

import '../../../dependency_injection/dependency_injector.dart';
import '../../../generic_widgets/animated_checkmark.dart';
import '../../../generic_widgets/navigation_extensions.dart';
import '../../../generic_widgets/separated_tile.dart';
import '../../../helpers/string_extensions.dart';
import '../../../l10n/generated/app_localizations.dart';
import '../../experiences/models/experience.dart';
import '../../experiences/widgets/preferred_experiences_screen.dart';
import '../../todo_lists/components/todo_list_by_topic_view.dart';
import '../../todo_lists/models/todo_list.dart';
import '../models/poster_type.dart';

class PosterTypeTile extends StatelessWidget {
  const PosterTypeTile({
    super.key,
    required this.type,
    required this.selected,
    required this.useCustom,
  });

  final bool useCustom;
  final PosterType type;
  final PosterType selected;

  @override
  Widget build(BuildContext context) {
    return SeparatedTile(
      child: ListTile(
        title: Text(
          type.localizedName(AppLocalizations.of(context)!).capitalized,
        ),
        subtitle: _buildSubtitle(),
        trailing: AnimatedCheckmark(
          checked: type.runtimeType == selected.runtimeType,
        ),
        onTap: () => _onTapped(context),
      ),
    );
  }

  void _onTapped(BuildContext context) async {
    var typeToSubmit = type;

    switch (type) {
      case CountryPosterType():
        typeToSubmit =
            useCustom ? CountryPosterType.custom() : CountryPosterType();

      case CityPosterType():
        typeToSubmit = useCustom ? CityPosterType.custom() : CityPosterType();

      case TodoListPosterType():
        final list = await Navigator.of(context).pushMaterialRoute<TodoList>(
            name: 'poster_list_picker',
            fullscreen: true,
            builder: (context) => const ListPicker());

        if (list == null) {
          return;
        }

        typeToSubmit = useCustom
            ? TodoListPosterType.custom(list)
            : TodoListPosterType(list: list);

      case ExperiencePosterType():
        final experience = await Navigator.of(context)
            .pushMaterialRoute<Experience>(
                name: 'poster_experience_picker',
                fullscreen: true,
                builder: (context) => const ExperiencePicker());

        if (experience == null) {
          return;
        }

        typeToSubmit = useCustom
            ? ExperiencePosterType.custom(experience)
            : ExperiencePosterType(experience: experience);
    }

    DependencyInjector.posterPrintingBloc.updatePosterType(typeToSubmit);
  }

  Widget? _buildSubtitle() {
    if (type.runtimeType != selected.runtimeType) {
      return null;
    }

    final subtitle = selected.subtitle;
    if (subtitle == null) {
      return null;
    }

    return Text(subtitle);
  }
}
