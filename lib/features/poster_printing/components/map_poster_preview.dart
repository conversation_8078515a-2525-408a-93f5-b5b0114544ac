import 'package:flutter/material.dart';

import '../../../dependency_injection/dependency_injector.dart';
import '../../../generic_widgets/responsive_padding.dart';
import '../../../generic_widgets/selectable_item.dart';
import '../../../generic_widgets/spinner.dart';
import '../../../helpers/margin.dart';
import '../../../l10n/generated/app_localizations.dart';
import '../../../models/palette.dart';
import '../../../models/selection.dart';
import '../../map/geometry_painter.dart';
import '../../map/projection.dart';
import '../../map/tiles/internal/geometry_repository.dart';
import '../poster_printing_bloc.dart';

class MapPosterPreview extends StatelessWidget {
  const MapPosterPreview({super.key, this.includeResponsivePadding = true});

  final bool includeResponsivePadding;

  static const border = 12.0;

  static const posterDpi = 32.0;
  static const posterSize = Size(20 * posterDpi, 16 * posterDpi);
  static final imageRect = Rect.fromLTWH(
    0,
    0,
    posterSize.width,
    posterSize.height,
  );

  @override
  Widget build(BuildContext context) {
    if (!includeResponsivePadding) {
      return _buildPoster();
    }

    return ResponsivePadding(
      context: context,
      fillToEdgeOnPhone: false,
      child: Padding(
        padding: const EdgeInsets.all(Margin.standard),
        child: _buildPoster(),
      ),
    );
  }

  Widget _buildPoster() {
    return LayoutBuilder(
      builder: (context, constraints) {
        return SizedBox(
          width: constraints.maxWidth,
          height: constraints.maxWidth / posterSize.aspectRatio,
          child: Stack(
            children: [
              Positioned.fill(child: _buildPosterPaper()),
              _buildPosterBorder(border),
              _buildDataListener(
                context,
                mapBuilder: (context, geometry) {
                  return Stack(
                    children: [
                      Padding(
                        padding: const EdgeInsets.all(border),
                        child: _paintGeometry(
                          context: context,
                          geometry: geometry,
                          constraints: constraints,
                        ),
                      ),
                      const MapPosterLegend(border: border),
                    ],
                  );
                },
              ),
              _buildPosterLogo(border, constraints),
            ],
          ),
        );
      },
    );
  }

  Widget _buildDataListener(
    BuildContext context, {
    required Widget Function(BuildContext context, PolygonLookup geometry)
    mapBuilder,
  }) {
    return FutureBuilder<PolygonLookup>(
      future: DependencyInjector.geometryBloc.fetchCountryPolygons(),
      builder: (context, snapshot) {
        final geometry = snapshot.data;
        if (geometry == null) {
          return const SizedBox();
        }

        return mapBuilder(context, geometry);
      },
    );
  }

  Widget _buildPosterLogo(double border, BoxConstraints constraints) {
    return MediaQuery.withNoTextScaling(
      child: Positioned(
        bottom: border + 2,
        left: border + 2,
        child: Column(
          children: [
            Image.asset(
              'assets/images/visited_logo.png',
              width: constraints.maxWidth * 0.1,
              height: constraints.maxWidth * 0.1,
              fit: BoxFit.fitWidth,
            ),
            const Text(
              'VisitedApp.com',
              style: TextStyle(
                color: Color(0xFF065CB9),
                fontWeight: FontWeight.bold,
                fontSize: 6,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPosterBorder(double border) {
    return Positioned(
      top: border,
      bottom: border,
      right: border + 2,
      left: border + 2,
      child: Container(
        decoration: BoxDecoration(border: Border.all(color: Colors.black)),
      ),
    );
  }

  Widget _buildPosterPaper() {
    return Container(
      decoration: const BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(blurRadius: 7, color: Colors.black38, offset: Offset(0, 4)),
        ],
      ),
    );
  }

  Future<PolygonLookup> _fetchData() {
    return DependencyInjector.geometryBloc.fetchCountryPolygons(lowRes: true);
  }

  Widget _paintGeometry({
    required BuildContext context,
    required PolygonLookup geometry,
    required BoxConstraints constraints,
  }) {
    return FutureBuilder<PolygonLookup>(
      future: _fetchData(),
      builder: (context, snapshot) {
        if (snapshot.hasData == false) {
          return const Center(child: Spinner());
        }

        final geometry = snapshot.requireData;

        return StreamBuilder<PosterConfig>(
          stream: DependencyInjector.posterPrintingBloc.posterConfig,
          builder: (context, snapshot) {
            final data = snapshot.data;

            if (data == null) {
              return const Center(child: Spinner());
            }

            final (selectionsToRender, palette, type) = data;

            return StreamBuilder<Map<SelectableItem, Selection>>(
              key: UniqueKey(),
              stream: type.selections,
              builder: (context, snapshot) {
                final selections = snapshot.data ?? {};

                return ClipRect(
                  child: SizedBox.fromSize(
                    size: constraints.biggest,
                    child: CustomPaint(
                      size: constraints.biggest,
                      isComplex: true,
                      painter: GeometryPainter(
                        geometry: geometry,
                        selectionGetter: (area) {
                          if (type.isAnnotatedMap) {
                            return Selection.clear;
                          }
                          final selection = selections[area];

                          return selectionsToRender.contains(selection)
                              ? selection!
                              : Selection.clear;
                        },
                        palette: palette,
                        projection: MillerProjection(constraints.biggest.width),
                        annotations: type.isAnnotatedMap
                            ? selections.cast<MapDisplayable, Selection>()
                            : null,
                      ),
                    ),
                  ),
                );
              },
            );
          },
        );
      },
    );
  }
}

class MapPosterLegend extends StatelessWidget {
  const MapPosterLegend({super.key, required this.border});

  final double border;

  @override
  Widget build(BuildContext context) {
    return MediaQuery.withNoTextScaling(
      child: StreamBuilder<Palette>(
        stream: DependencyInjector.posterPrintingBloc.palette,
        initialData: Palette.standard,
        builder: (context, snapshot) {
          final palette = snapshot.data ?? Palette.standard;

          return FutureBuilder<Selection>(
            future: _fetchAntarcticaSelection(context),
            builder: (context, snapshot) {
              final selection = snapshot.data;
              if (selection == null) {
                return const SizedBox();
              }
              return _buildLegendContent(border, palette, context, selection);
            },
          );
        },
      ),
    );
  }

  Future<Selection> _fetchAntarcticaSelection(BuildContext context) async {
    final areaBloc = DependencyInjector.areaBloc;
    final selectionBloc = DependencyInjector.areaSelectionBloc;
    final area = await areaBloc.areaByIsoCode('AQ');

    if (area == null) {
      return Selection.clear;
    }

    return selectionBloc.fetchSelection(area);
  }

  Widget _buildLegendContent(
    double border,
    Palette palette,
    BuildContext context,
    Selection antarcticaSelection,
  ) {
    final bloc = DependencyInjector.posterPrintingBloc;

    return Positioned(
      bottom: border + 5,
      right: border * 2.5,
      child: StreamBuilder<List<Selection>>(
        stream: bloc.printableSelections,
        builder: (context, snapshot) {
          final selections = snapshot.data ?? bloc.currentPrintableSelections;

          final labelSelection = selections.contains(antarcticaSelection)
              ? antarcticaSelection
              : Selection.clear;

          final textColour = palette.labelColorForSelection(labelSelection);

          return Row(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            crossAxisAlignment: CrossAxisAlignment.end,
            children: [
              for (final selection in selections)
                Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 2),
                  child: Row(
                    children: [
                      Container(
                        width: 10,
                        height: 10,
                        decoration: BoxDecoration(
                          border: Border.all(color: palette.border, width: 0.5),
                          color: palette.colorForSelection(selection),
                        ),
                      ),
                      const SizedBox(width: 3),
                      Text(
                        selection.localized(AppLocalizations.of(context)!),
                        style: TextStyle(
                          fontSize: 6,
                          fontWeight: FontWeight.bold,
                          color: textColour,
                        ),
                      ),
                    ],
                  ),
                ),
            ],
          );
        },
      ),
    );
  }
}
