import 'package:flutter/material.dart';

import '../../../dependency_injection/dependency_injector.dart';
import '../../../generic_widgets/area_flag.dart';
import '../../../generic_widgets/navigation_extensions.dart';
import '../../../generic_widgets/pinned_selection_toggle.dart';
import '../../../generic_widgets/platform_aware/platform_sliver_app_bar.dart';
import '../../../generic_widgets/responsive_padding.dart';
import '../../../generic_widgets/sliver_sticky_search_bar.dart';
import '../../../generic_widgets/streaming_selectable_tile.dart';
import '../../../l10n/generated/app_localizations.dart';
import '../../../models/geo_area.dart';
import '../../../models/selection.dart';
import '../../areas/area_tile.dart';
import '../../cities/city.dart';
import '../../cities/city_picker.dart';
import '../../cities/city_searchable_mixin.dart';
import '../models/poster_type.dart';

class PosterCustomCityPicker extends StatefulWidget {
  const PosterCustomCityPicker({super.key, required this.poster});

  final CityPosterType poster;

  @override
  State<PosterCustomCityPicker> createState() => _PosterCustomCityPickerState();
}

class _PosterCustomCityPickerState extends State<PosterCustomCityPicker>
    with CitySearchableMixin {
  late Selection currentSelection = widget.poster.availableSelections.first;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Scrollbar(
        child: CustomScrollView(
          slivers: [
            PlatformSliverAppBar(title: AppLocalizations.of(context)!.cities),
            PinnedSelectionToggle(
              active: currentSelection,
              selections: widget.poster.availableSelections,
              expand: true,
              onChanged: (value) {
                setState(() {
                  currentSelection = value;
                });
              },
            ),
            ResponsiveSliverPadding(
              context: context,
              fillToEdgeOnPhone: true,
              sliver: SliverStickySearchBar(
                searchController: searchController,
                onCleared: () {
                  setState(() {
                    cities = null;
                    countrySearchResults = countrySearcher?.items;
                  });
                },
              ),
            ),
            ...buildSlivers(context),
          ],
        ),
      ),
    );
  }

  @override
  Widget buildCityTile(BuildContext context, City city) {
    return StreamBuilder<Selection>(
      stream: widget.poster.selections.map(
        (selections) => selections[city] ?? Selection.clear,
      ),
      initialData: Selection.clear,
      builder: (context, snapshot) {
        final area = DependencyInjector.areaBloc.areaByIsoCodeSync(
          city.geoAreaIsoCode,
        );

        return SelectableTile(
          leading: area != null ? AreaFlag(area: area) : null,
          item: city,
          currentSelection: snapshot.data ?? Selection.clear,
          nameBuilder: DependencyInjector.cityBloc.fullCityName(city),
          onTapped: () {
            widget.poster.selectForCustomPoster(city, currentSelection);
          },
        );
      },
    );
  }

  @override
  Widget buildCountryTile(BuildContext context, GeoArea country) {
    return AreaTile(
      area: country,
      onTapped: () {
        Navigator.of(context).pushMaterialRoute(
          name: 'poster_custom_city_selection_${country.isoCode}',
          builder: (context) => CityPicker(
            area: country,
            selectionTypes: widget.poster.availableSelections,
            initialSelection: currentSelection,
            onSelectedOverride: widget.poster.selectForCustomPoster,
            fetchSelectionsOverride: Future.value({
              for (final entry
                  in widget.poster.currentCustomSelections?.entries ??
                      <City, Selection>{}.entries)
                if (entry.key.geoAreaIsoCode == country.isoCode)
                  entry.key: entry.value,
            }),
          ),
        );
      },
    );
  }

  @override
  void onNoCityFound() {}
}
