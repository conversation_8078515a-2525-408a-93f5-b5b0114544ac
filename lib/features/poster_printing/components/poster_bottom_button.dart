import 'package:flutter/material.dart';
import '../../../generic_widgets/device_aware_bottom_padding.dart';
import '../../../generic_widgets/platform_aware/platform_filled_button.dart';
import '../../../generic_widgets/responsive_padding.dart';

class PosterBottomButton extends StatelessWidget {
  const PosterBottomButton({
    super.key,
    required this.title,
    required this.onTapped,
  });

  final String title;
  final VoidCallback onTapped;

  @override
  Widget build(BuildContext context) {
    return DeviceAwareBottomPadding(
      child: ResponsivePadding(
        context: context,
        fillToEdgeOnPhone: false,
        child: PlatformFilledButton(
          title: title,
          onTapped: onTapped,
        ),
      ),
    );
  }
}
