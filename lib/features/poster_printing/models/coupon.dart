import '../../../l10n/generated/app_localizations.dart';
import 'price.dart';

class Coupon {
  Coupon.fromJson(Map json)
    : id = json['id'],
      name = json['name'],
      code = json['code'],
      percentOff = json['percentOff'],
      amountOff = json['amountOff'],
      discount = Price.fromJson(json['discount']);

  final String id;
  final String name;
  final String code;
  final double? percentOff;
  final double? amountOff;
  final Price discount;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is Coupon &&
          runtimeType == other.runtimeType &&
          id == other.id &&
          name == other.name &&
          code == other.code &&
          percentOff == other.percentOff &&
          amountOff == other.amountOff;

  @override
  int get hashCode => Object.hash(id, name, code, percentOff, amountOff);

  @override
  String toString() {
    return 'Coupon{name: $name, code: $code}';
  }

  String description(AppLocalizations localizations) {
    return switch (percentOff) {
      final percentOff when percentOff != null =>
        localizations.discountPercentage(percentOff),
      final amountOff when amountOff != null => localizations.discountAmount(
        amountOff,
      ),
      _ => throw UnimplementedError('Unsupported coupon type'),
    };
  }
}
