import 'package:collection/collection.dart';

import '../../../caching/resettable_behaviour_subject.dart';
import '../../../dependency_injection/dependency_injector.dart';
import '../../../generic_widgets/selectable_item.dart';
import '../../../l10n/generated/app_localizations.dart';
import '../../../models/geo_area.dart';
import '../../../models/selection.dart';
import '../../cities/city.dart';
import '../../experiences/models/experience.dart';
import '../../todo_lists/models/todo_list.dart';
import '../../todo_lists/models/todo_list_item.dart';

sealed class PosterType<T extends SelectableItem, E> {
  PosterType() : _customSelectionsController = null;
  PosterType.custom()
    : _customSelectionsController =
          ResettableBehaviorSubject<Map<T, Selection>>() {
    _loadCustomSelections();
  }

  String localizedName(AppLocalizations localizations);

  @override
  bool operator ==(Object other) => runtimeType == other.runtimeType;

  bool get isAnnotatedMap;

  @override
  int get hashCode => 0;

  String? get subtitle;

  List<Selection> get availableSelections;

  Stream<Map<T, Selection>> get selections {
    final controller = _customSelectionsController;
    if (controller != null) {
      return controller.stream;
    }

    return _userSelectionsStream;
  }

  Map<T, Selection>? get currentCustomSelections =>
      _customSelectionsController?.valueOrNull;

  Stream<Map<T, Selection>> get _userSelectionsStream;

  String get backendMapType;

  final ResettableBehaviorSubject<Map<T, Selection>>?
  _customSelectionsController;

  void selectForCustomPoster(T item, Selection selection) {
    final controller = _customSelectionsController;
    if (controller == null) {
      return;
    }

    final selections = {...controller.valueOrNull ?? {}};

    if (selections[item] == selection) {
      selections.remove(item);
    } else {
      selections[item] = selection;
    }

    controller.add(selections);
    _saveCustomSelections();
  }

  bool get isUsingCustomSelections => _customSelectionsController != null;

  PosterType<T, E> toggle();

  void reset() {
    _customSelectionsController?.reset();
    _deleteCustomSelections();
  }

  Future<void> _saveCustomSelections() async {
    final data = _customSelectionsController?.valueOrNull;
    if (data == null) {
      return;
    }

    final storage = DependencyInjector.sharedPrefsStorage;
    final persistableJson = data.entries.groupFoldBy<String, List<E>>(
      (entry) => entry.value.backendKey,
      (output, entry) {
        final list = output ?? <E>[];
        list.add(_persistableJson(entry.key));
        return list;
      },
    );

    await storage.putJson(_storageKey, persistableJson);
  }

  Future<void> _loadCustomSelections() async {
    if (_customSelectionsController?.hasValue ?? false) {
      return;
    }

    final storage = DependencyInjector.sharedPrefsStorage;
    final json = await storage.getJson(_storageKey);
    if (json == null || json is! Map) {
      return;
    }

    final restored = <T, Selection>{};

    for (final MapEntry(:key as String, :value as List) in json.entries) {
      final selection = Selection.fromBackendKey(key);
      for (final json in value) {
        final parsed = await _parsePersistableJson(json);
        if (parsed != null) {
          restored[parsed] = selection;
        }
      }
    }

    _customSelectionsController?.add(restored);
  }

  Future<void> _deleteCustomSelections() {
    final storage = DependencyInjector.sharedPrefsStorage;
    return storage.delete(_storageKey);
  }

  String get _storageKey;
  E _persistableJson(T element);
  Future<T?> _parsePersistableJson(E json);

  Future<Map<String, Object>> selectionJson() async {
    final selections =
        this.currentCustomSelections ?? await this.selections.first;
    final grouped = selections.entries.groupListsBy(
      (e) => e.value,
    );

    return grouped.map(
      (key, value) {
        return MapEntry(
          key.backendKey,
          value
              .map(
                (entry) => _elementToJson(entry.key),
              )
              .toList(),
        );
      },
    );
  }

  Object _elementToJson(T element);

  void dispose() {
    _customSelectionsController?.close();
  }

  static final List<PosterType> values = [
    CountryPosterType(),
    CityPosterType(),
    TodoListPosterType(),
    ExperiencePosterType(),
  ];

  @override
  String toString() {
    return isUsingCustomSelections
        ? 'Custom $runtimeType'
        : runtimeType.toString();
  }
}

class CountryPosterType extends PosterType<GeoArea, String> {
  CountryPosterType() : super();
  CountryPosterType.custom() : super.custom();

  @override
  String localizedName(AppLocalizations localizations) =>
      localizations.countries;

  @override
  bool get isAnnotatedMap => false;

  @override
  String? get subtitle => null;

  @override
  Stream<Map<GeoArea, Selection>> get _userSelectionsStream =>
      DependencyInjector.areaSelectionBloc.selections;

  @override
  String get backendMapType => 'WORLD';

  @override
  Object _elementToJson(GeoArea element) {
    return element.isoCode;
  }

  @override
  Future<GeoArea?> _parsePersistableJson(String json) async {
    return DependencyInjector.areaBloc.areaByIsoCodeSync(json);
  }

  @override
  String _persistableJson(GeoArea element) {
    return element.isoCode;
  }

  @override
  String get _storageKey => 'com.visited.poster.custom.areas';

  @override
  PosterType<GeoArea, String> toggle() {
    return isUsingCustomSelections
        ? CountryPosterType()
        : CountryPosterType.custom();
  }

  @override
  List<Selection> get availableSelections => [
    Selection.live,
    if (DependencyInjector.iapBloc.canSelectLived) Selection.lived,
    Selection.been,
    Selection.want,
  ];

  @override
  void selectForCustomPoster(GeoArea item, Selection selection) {
    if (selection == Selection.live) {
      final currentLive = (_customSelectionsController?.valueOrNull ?? {})
          .entries
          .where((entry) => entry.value == Selection.live)
          .map((entry) => entry.key);

      for (final item in currentLive) {
        selectForCustomPoster(item, Selection.clear);
      }
    }

    super.selectForCustomPoster(item, selection);
  }
}

class CityPosterType extends PosterType<City, int> {
  CityPosterType() : super();
  CityPosterType.custom() : super.custom();

  @override
  String localizedName(AppLocalizations localizations) => localizations.cities;

  @override
  bool get isAnnotatedMap => true;

  @override
  String? get subtitle => null;

  @override
  Stream<Map<City, Selection>> get _userSelectionsStream =>
      DependencyInjector.cityBloc.selections;

  @override
  String get backendMapType => 'ANNOTATIONS';

  @override
  Object _elementToJson(City element) {
    return {
      'name': element.name,
      'lat': element.coordinate.latitude,
      'long': element.coordinate.longitude,
    };
  }

  @override
  Future<City?> _parsePersistableJson(int json) async {
    return DependencyInjector.cityBloc.fetchCity(json);
  }

  @override
  int _persistableJson(City element) {
    return element.id;
  }

  @override
  String get _storageKey => 'com.visited.poster.custom.cities';

  @override
  PosterType<City, int> toggle() {
    return isUsingCustomSelections ? CityPosterType() : CityPosterType.custom();
  }

  @override
  List<Selection> get availableSelections => [
    Selection.live,
    if (DependencyInjector.iapBloc.canSelectLived) Selection.lived,
    Selection.been,
    Selection.want,
  ];

  @override
  void selectForCustomPoster(City item, Selection selection) {
    if (selection == Selection.live) {
      final currentLive = (_customSelectionsController?.valueOrNull ?? {})
          .entries
          .where((entry) => entry.value == Selection.live)
          .map((entry) => entry.key);

      for (final item in currentLive) {
        selectForCustomPoster(item, Selection.clear);
      }
    }

    super.selectForCustomPoster(item, selection);
  }
}

class TodoListPosterType extends PosterType<TodoListItem, int> {
  TodoListPosterType({this.list}) : super();
  TodoListPosterType.custom(this.list) : assert(list != null), super.custom();

  final TodoList? list;

  @override
  String localizedName(AppLocalizations localizations) => localizations.lists;

  @override
  bool get isAnnotatedMap => true;

  @override
  String? get subtitle => list?.name;

  @override
  Stream<Map<TodoListItem, Selection>> get _userSelectionsStream {
    if (list == null) {
      return const Stream.empty();
    }
    return DependencyInjector.todoListBloc.selectionsByList(list!);
  }

  @override
  String get backendMapType => 'ANNOTATIONS';

  @override
  Object _elementToJson(TodoListItem element) {
    return {
      'name': element.name,
      'lat': element.coordinate.latitude,
      'long': element.coordinate.longitude,
    };
  }

  @override
  Future<TodoListItem?> _parsePersistableJson(int json) async {
    final items = await DependencyInjector.todoListBloc.fetchItems(list!);
    return items.firstWhereOrNull((item) => item.id == json);
  }

  @override
  int _persistableJson(TodoListItem element) {
    return element.id;
  }

  @override
  String get _storageKey => 'com.visited.poster.custom.todoList.${list!.id}';

  @override
  PosterType<TodoListItem, int> toggle() {
    return isUsingCustomSelections
        ? TodoListPosterType(list: list)
        : TodoListPosterType.custom(list);
  }

  @override
  final availableSelections = [Selection.been, Selection.want];
}

class ExperiencePosterType extends PosterType<GeoArea, String> {
  ExperiencePosterType({this.experience}) : super();
  ExperiencePosterType.custom([this.experience]) : super.custom();

  final Experience? experience;

  @override
  String localizedName(AppLocalizations localizations) =>
      localizations.experiences;

  @override
  bool get isAnnotatedMap => false;

  @override
  String? get subtitle => experience?.name;

  @override
  Stream<Map<GeoArea, Selection>> get _userSelectionsStream {
    if (experience == null) {
      return const Stream.empty();
    }
    return DependencyInjector.experienceBloc
        .experienceSelections(experience!)
        .map((event) {
          return {
            for (final area in event.been) area: Selection.been,
            for (final area in event.want) area: Selection.want,
          };
        });
  }

  @override
  String get backendMapType => 'WORLD';

  @override
  Object _elementToJson(GeoArea element) {
    return element.isoCode;
  }

  @override
  Future<GeoArea?> _parsePersistableJson(String json) {
    return DependencyInjector.areaBloc.areaByIsoCode(json);
  }

  @override
  String _persistableJson(GeoArea element) {
    return element.isoCode;
  }

  @override
  String get _storageKey =>
      'com.visited.poster.custom.experiences.${experience!.id}';

  @override
  PosterType<GeoArea, String> toggle() {
    return isUsingCustomSelections
        ? ExperiencePosterType(experience: experience)
        : ExperiencePosterType.custom(experience);
  }

  @override
  final availableSelections = [Selection.been, Selection.want];
}
