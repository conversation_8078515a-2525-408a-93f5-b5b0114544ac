enum Currency {
  usd,
  cad,
  eur,
  gbp,
  cny,
  krw,
  jpy,
  aud,
  nzd;

  static Currency fromCurrencyCode(String key) => Currency.values.firstWhere(
        (element) => element.name == key.toLowerCase(),
        orElse: () => Currency.usd,
      );
}

extension CurrencySymbols on Currency {
  String get symbol {
    switch (this) {
      case Currency.usd:
        return r'$';

      case Currency.cad:
        return r'$';

      case Currency.eur:
        return '€';

      case Currency.gbp:
        return '£';

      case Currency.cny:
        return '¥';

      case Currency.krw:
        return '₩';

      case Currency.jpy:
        return '¥';

      case Currency.aud:
        return r'$';

      case Currency.nzd:
        return r'$';
    }
  }

  String get asset {
    switch (this) {
      case Currency.usd:
        return 'assets/flags/flag_us.png';

      case Currency.cad:
        return 'assets/flags/flag_ca.png';

      case Currency.eur:
        return 'assets/flags/flag_eu.png';

      case Currency.gbp:
        return 'assets/flags/flag_gb.png';

      case Currency.cny:
        return 'assets/flags/flag_cn.png';

      case Currency.krw:
        return 'assets/flags/flag_kr.png';

      case Currency.jpy:
        return 'assets/flags/flag_jp.png';

      case Currency.aud:
        return 'assets/flags/flag_au.png';

      case Currency.nzd:
        return 'assets/flags/flag_nz.png';
    }
  }

  String get isoCode {
    switch (this) {
      case Currency.usd:
        return 'US';
      case Currency.cad:
        return 'CA';
      case Currency.eur:
        return '';
      case Currency.gbp:
        return 'GB';
      case Currency.cny:
        return 'CN';
      case Currency.krw:
        return 'KR';
      case Currency.jpy:
        return 'JP';
      case Currency.aud:
        return 'AU';
      case Currency.nzd:
        return 'NZ';
    }
  }
}
