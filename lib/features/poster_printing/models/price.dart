import 'currency.dart';

class Price {
  final String id;
  final Currency currency;
  final int unitAmount;
  final double unitDecimal;
  final double? tax;

  const Price({
    required this.id,
    required this.currency,
    required this.unitDecimal,
    required this.unitAmount,
    required this.tax,
  });

  Price.fromJson(Map json)
    : id = json['id'],
      currency = Currency.fromCurrencyCode(json['currencyCode']),
      unitAmount = json['unitAmount'],
      unitDecimal = json['unitAmountDecimal'],
      tax = json['tax']?.toDouble();

  String get formatted => '${currency.symbol}${unitDecimal.toStringAsFixed(2)}';

  Price operator +(Price other) {
    assert(currency == other.currency);
    return Price(
      id: id + other.id,
      currency: currency,
      unitDecimal: unitDecimal + other.unitDecimal,
      unitAmount: unitAmount + other.unitAmount,
      tax: (tax ?? 0) + (other.tax ?? 0),
    );
  }

  Price operator -(Price other) {
    assert(currency == other.currency);
    return Price(
      id: id + other.id,
      currency: currency,
      unitDecimal: unitDecimal - other.unitDecimal,
      unitAmount: unitAmount - other.unitAmount,
      tax: (tax ?? 0) - (other.tax ?? 0),
    );
  }
}
