import 'package:collection/collection.dart';
import 'price.dart';

class Shipping extends Price {
  final bool international;

  Shipping.fromJson(super.json)
      : international = json['internationalShipping'],
        super.fromJson();
}

extension ShippingSelection on List<Shipping> {
  Shipping get national =>
      firstWhere((e) => !e.international, orElse: () => first);
  Shipping? get international => firstWhereOrNull((e) => e.international);
}
