import 'price.dart';
import 'shipping.dart';

class Poster {
  final String id;
  final String name;
  final String image;
  final Price price;
  final Price? usdPrice;
  final Price? eurPrice;
  List<Shipping> shipping;
  List<Shipping>? usdShipping;
  List<Shipping>? eurShipping;

  Poster.fromJson(Map json)
      : id = json['id'],
        name = json['name'],
        image = json['imageUrl'],
        price = Price.fromJson(json['price']),
        usdPrice = _parseOptionalPrice(json, 'usdAlternativePrice'),
        eurPrice = _parseOptionalPrice(json, 'eurAlternativePrice'),
        shipping = _parseShipping(json, 'shippingOptions'),
        usdShipping =
            _parseOptionalShipping(json, 'usdAlternativeShippingOptions'),
        eurShipping =
            _parseOptionalShipping(json, 'eurAlternativePriceShippingOptions');

  static Price? _parseOptionalPrice(Map json, String key) {
    final priceJson = json[key];
    if (priceJson == null) {
      return null;
    }

    return Price.fromJson(priceJson);
  }

  static List<Shipping>? _parseOptionalShipping(Map json, String key) {
    if (json[key] == null) {
      return null;
    }

    return _parseShipping(json, key);
  }

  static List<Shipping> _parseShipping(Map json, String key) => json[key]
      .map<Shipping>((e) => Shipping.fromJson(e))
      .toList(growable: false);
}
