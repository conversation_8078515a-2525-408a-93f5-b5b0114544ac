import 'dart:io';

import 'package:flutter/material.dart';

import '../../dependency_injection/dependency_injector.dart';
import '../../generic_widgets/navigation_extensions.dart';
import '../../generic_widgets/platform_aware/platform_app_bar.dart';
import '../../generic_widgets/platform_aware/platform_filled_button.dart';
import '../../generic_widgets/responsive_padding.dart';
import '../../generic_widgets/spinner.dart';
import '../../helpers/margin.dart';
import '../../l10n/generated/app_localizations.dart';
import 'components/map_poster_preview.dart';
import 'models/poster.dart';
import 'models/shipping.dart';
import 'poster_customize_screen.dart';

class OrderPosterLandingScreen extends StatelessWidget {
  const OrderPosterLandingScreen({super.key});

  static const routeName = 'printing_landing_page';

  @override
  Widget build(BuildContext context) {
    final localizations = AppLocalizations.of(context)!;
    return Scaffold(
      appBar: PlatformAppBar(
        title: localizations.posterLandingAppBar,
      ),
      body: SafeArea(
        child: FutureBuilder<Poster>(
          future: DependencyInjector.posterPrintingBloc.fetchPoster(),
          builder: (context, snapshot) {
            if (snapshot.hasError) {
              return _buildError(localizations, context, snapshot.error);
            }

            final poster = snapshot.data;
            if (poster == null) {
              return const Center(
                child: Spinner(),
              );
            }

            return Column(
              children: [
                _buildPosterDetails(context, poster, localizations),
                _buildRemotePosterMessage(context)
              ],
            );
          },
        ),
      ),
    );
  }

  FutureBuilder<String?> _buildRemotePosterMessage(BuildContext context) {
    return FutureBuilder<String?>(
      future: DependencyInjector.featureFlags
          .posterMessage(Localizations.localeOf(context).languageCode),
      builder: (context, snapshot) {
        final message = snapshot.data;
        if (message == null || message.isEmpty) {
          return const SizedBox();
        }

        return ResponsivePadding(
          context: context,
          child: Container(
            margin: const EdgeInsets.symmetric(horizontal: 32, vertical: 8),
            padding: const EdgeInsets.all(8),
            color: Theme.of(context).colorScheme.primary,
            child: Text(
              message,
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: Colors.white,
                    fontWeight: FontWeight.w600,
                  ),
              textAlign: TextAlign.center,
            ),
          ),
        );
      },
    );
  }

  Widget _buildPrice(
      AppLocalizations localizations, Poster poster, BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(
        vertical: 8.0,
        horizontal: 16.0,
      ),
      child: DecoratedBox(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(16),
          color: Theme.of(context).colorScheme.primary,
          boxShadow: [
            const BoxShadow(blurRadius: 4.0, offset: Offset(0, 2)),
          ],
        ),
        child: Padding(
          padding: const EdgeInsets.all(8.0),
          child: Text(
            localizations.posterPricePlusShipping(
                poster.price.formatted, poster.shipping.national.formatted),
            textAlign: TextAlign.center,
            style: Theme.of(context)
                .textTheme
                .titleLarge
                ?.copyWith(color: Colors.white),
          ),
        ),
      ),
    );
  }

  Widget _buildCustomizeButtons(BuildContext context, Poster poster) {
    return ResponsivePadding(
      context: context,
      fillToEdgeOnPhone: false,
      child: Row(
        children: [
          Expanded(
            child: PlatformFilledButton(
                trailing: const Icon(
                  Icons.favorite_border,
                  color: Colors.white,
                ),
                title: AppLocalizations.of(context)!.posterForMe,
                onTapped: () =>
                    _navigateToCustomizeScreen(context, poster, false)),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: PlatformFilledButton(
                title: AppLocalizations.of(context)!.posterSendGift,
                color: Colors.orangeAccent,
                textColor: Colors.black,
                trailing: const Icon(
                  Icons.redeem_outlined,
                  color: Colors.black,
                ),
                onTapped: () =>
                    _navigateToCustomizeScreen(context, poster, true)),
          )
        ],
      ),
    );
  }

  void _navigateToCustomizeScreen(
      BuildContext context, Poster poster, bool customSelections) {
    DependencyInjector.posterPrintingBloc.useCustomSelections(customSelections);
    Navigator.of(context).pushMaterialRoute(
      name: customSelections ? 'poster_customize_gift' : 'poster_customize',
      builder: (context) => PosterCustomizeScreen(
        poster: poster,
        useCustomSelections: customSelections,
      ),
    );
  }

  Widget _buildPosterDetails(
    BuildContext context,
    Poster poster,
    AppLocalizations localizations,
  ) {
    return Expanded(
      child: Scrollbar(
        child: CustomScrollView(slivers: [
          ResponsiveSliverPadding(
              context: context,
              fillToEdgeOnPhone: false,
              sliver: SliverList.list(children: [
                const MapPosterPreview(),
                Text(
                  localizations.posterLandingSubHeading,
                  style: Theme.of(context).textTheme.titleLarge,
                ),
                Text(
                  localizations.posterLandingSubDescription,
                  style: Theme.of(context).textTheme.bodyMedium,
                ),
                const SizedBox(height: 16.0),
              ])),
          SliverAppBar(
            pinned: true,
            title: _buildCustomizeButtons(context, poster),
          ),
          ResponsiveSliverPadding(
            context: context,
            fillToEdgeOnPhone: false,
            sliver: SliverList.list(children: [
              const SizedBox(height: 16.0),
              Text(
                localizations.posterLandingPromoBullet1,
                style: Theme.of(context).textTheme.bodyMedium,
              ),
              const SizedBox(height: Margin.small),
              Text(localizations.posterLandingPromoBullet1),
              const SizedBox(height: Margin.small),
              Text(localizations.posterLandingPromoBullet2),
              const SizedBox(height: Margin.small),
              Text(localizations.posterLandingPromoBullet3),
              Padding(
                padding: const EdgeInsets.all(Margin.standard),
                child: Stack(
                  children: [
                    Image.asset('assets/poster/poster_1.jpg'),
                    Positioned(
                        bottom: 0,
                        left: 0,
                        right: 0,
                        child: _buildPrice(localizations, poster, context)),
                  ],
                ),
              ),
              Text(localizations.posterLandingHowItWorks,
                  style: Theme.of(context).textTheme.titleLarge),
              Text(localizations.posterLandingHowItWorksStep1),
              const SizedBox(height: Margin.small),
              Text(localizations.posterLandingHowItWorksStep2),
              const SizedBox(height: Margin.small),
              Text(Platform.isIOS
                  ? localizations.posterLandingHowItWorksStep3_iOS
                  : localizations.posterLandingHowItWorksStep3_android),
              const SizedBox(height: Margin.small),
              Text(localizations.posterLandingHowItWorksStep4),
              Padding(
                padding: const EdgeInsets.all(Margin.standard),
                child: Stack(
                  children: [
                    Image.asset('assets/poster/poster_2.jpg'),
                  ],
                ),
              ),
              Text(
                localizations.posterLandingCustomerReviewsHeader,
                style: Theme.of(context).textTheme.titleLarge,
              ),
              const SizedBox(height: Margin.standard),
              Text(localizations.posterLandingCustomerReview1),
              const Divider(
                indent: Margin.large,
                endIndent: Margin.large,
                height: Margin.large,
              ),
              Text(localizations.posterLandingCustomerReview2),
              const Divider(
                indent: Margin.large,
                endIndent: Margin.large,
                height: Margin.large,
              ),
              Text(localizations.posterLandingCustomerReview3),
              const Divider(
                indent: Margin.large,
                endIndent: Margin.large,
                height: Margin.large,
              ),
              Text(localizations.posterLandingCustomerReview4),
              Padding(
                padding: const EdgeInsets.all(Margin.standard),
                child: Image.asset('assets/poster/poster_3.jpg'),
              ),
              Text(
                localizations.posterLandingSpecifications,
                style: Theme.of(context).textTheme.titleLarge,
              ),
              Text(localizations.posterLandingSpecification1),
              const SizedBox(height: Margin.small),
              Text(localizations.posterLandingSpecification2),
              const SizedBox(height: Margin.small),
              Text(localizations.posterLandingSpecification3),
              const SizedBox(height: Margin.small),
              Text(localizations.posterLandingSpecification4),
              Padding(
                padding: const EdgeInsets.all(Margin.standard),
                child: Image.asset('assets/poster/poster_4.jpg'),
              ),
              Text(localizations.posterLandingShippingHeader,
                  style: Theme.of(context).textTheme.titleLarge),
              Text(localizations.posterLandingShipping1),
              const SizedBox(height: Margin.small),
              Text(localizations.posterLandingShipping2),
              const SizedBox(height: Margin.small),
              Text(localizations.posterLandingShipping3),
              const SizedBox(height: Margin.standard),
              Text(
                localizations.posterLandingCancellationHeader,
                style: Theme.of(context).textTheme.titleMedium,
              ),
              Text(localizations.posterLandingCancellationBody),
            ]),
          ),
        ]),
      ),
    );
  }

  Widget _buildError(
    AppLocalizations localizations,
    BuildContext context,
    Object? error,
  ) {
    return Center(
        child: Padding(
      padding: const EdgeInsets.all(32),
      child: Column(
        children: [
          Text(
            localizations.errorTitle,
            style: Theme.of(context).textTheme.titleLarge,
          ),
          Text(localizations.posterNotAvailableError),
          const SizedBox(height: 16),
          Text(
            error.toString(),
            style: Theme.of(context).textTheme.bodySmall,
          )
        ],
      ),
    ));
  }
}
