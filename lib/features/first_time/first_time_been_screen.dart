import 'package:flutter/material.dart';

import '../../dependency_injection/dependency_injector.dart';
import '../../generic_widgets/responsive_padding.dart';
import '../../helpers/searcher.dart';
import '../../l10n/generated/app_localizations.dart';
import '../../models/geo_area.dart';
import '../../models/palette.dart';
import '../../models/selection.dart';
import 'first_time_list_tile.dart';
import 'first_time_scaffold.dart';

class FirstTimeBeenScreen extends StatefulWidget {
  static const routeName = 'firstTime/been';

  const FirstTimeBeenScreen({super.key});

  @override
  State<FirstTimeBeenScreen> createState() => _FirstTimeBeenScreenState();
}

class _FirstTimeBeenScreenState extends State<FirstTimeBeenScreen> {
  final searchController = TextEditingController();
  var countries = <GeoArea>[];
  List<GeoArea>? searchResults;
  List<GeoArea> get areasToDisplay => searchResults ?? countries;
  late final Searcher<GeoArea> searcher;
  final selected = <GeoArea>{};

  @override
  void initState() {
    super.initState();

    searchController.addListener(() {
      if (searchController.text.trim().isEmpty && searchResults != null) {
        setState(() => searchResults = null);
        return;
      }
      searcher.search(searchController.text);
    });

    WidgetsBinding.instance.addPostFrameCallback((_) async {
      final areaBloc = DependencyInjector.areaBloc;
      final liveCountry = await DependencyInjector.areaSelectionBloc
          .currentCountryUserLivesIn();
      final areas = await areaBloc.allCountries();
      areas.remove(liveCountry);

      searcher = Searcher(areas, (results) {
        setState(() => searchResults = results);
      });

      setState(() => countries = areas);
    });
  }

  void _onContinue() async {
    final session = DependencyInjector.sessionBloc;
    await DependencyInjector.areaSelectionBloc.batchSelect(
      {for (final area in selected) area: Selection.been},
    );
    session.refreshStatus();
  }

  @override
  Widget build(BuildContext context) {
    final localizations = AppLocalizations.of(context)!;
    return FirstTimeScaffold(
      searchController: searchController,
      title: localizations.whereHaveYouBeen,
      tooltip: localizations.firstTimeBeenTutorial,
      backgroundImageIndex: 7,
      showContinue: true,
      onContinue: _onContinue,
      slivers: [
        _buildCountryList(context),
      ],
    );
  }

  Widget _buildCountryList(BuildContext context) {
    return ResponsiveSliverPadding(
      context: context,
      fillToEdgeOnPhone: false,
      sliver: SliverList(
        delegate: SliverChildBuilderDelegate(childCount: areasToDisplay.length,
            (context, index) {
          final area = areasToDisplay[index];
          return FirstTimeListTile(
            selected: selected.contains(area),
            selectionColor: Palette.standard.been,
            area: area,
            title: area.name,
            dense: false,
            onTapped: () {
              setState(() {
                selected.contains(area)
                    ? selected.remove(area)
                    : selected.add(area);
              });
            },
          );
        }),
      ),
    );
  }

  @override
  void dispose() {
    searchController.dispose();
    super.dispose();
  }
}
