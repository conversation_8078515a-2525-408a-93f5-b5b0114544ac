import 'package:flutter/material.dart';

import '../../dependency_injection/dependency_injector.dart';
import '../../generic_widgets/navigation_extensions.dart';
import '../../generic_widgets/slide_and_fade_in.dart';
import '../../l10n/generated/app_localizations.dart';
import '../../models/geo_area.dart';
import '../../models/palette.dart';
import '../cities/city.dart';
import '../cities/city_searchable_mixin.dart';
import 'first_time_city_mixin.dart';
import 'first_time_city_picker.dart';
import 'first_time_list_tile.dart';
import 'first_time_scaffold.dart';

class FirstTimeCitySearchScreen extends StatefulWidget {
  static const routeName = 'firstTime/cites';

  const FirstTimeCitySearchScreen({super.key});

  @override
  State createState() => _FirstTimeCitySearchScreenState();
}

class _FirstTimeCitySearchScreenState extends State<FirstTimeCitySearchScreen>
    with FirstTimeCityMixin, CitySearchableMixin {
  @override
  Widget build(BuildContext context) {
    final localizations = AppLocalizations.of(context)!;
    return FirstTimeScaffold(
      searchController: searchController,
      title: localizations.whereDoYouLive,
      tooltip: localizations.firstTimeLiveTutorial,
      searchHint: localizations.cityEmptyError,
      showContinue: selectedCity != null,
      onContinue: onContinueTapped,
      footer: selectedCity != null
          ? SlideAndFadeIn(
              key: ValueKey(selectedCity),
              duration: const Duration(milliseconds: 600),
              offset: const Offset(0, -0.5),
              child: Container(
                decoration: const BoxDecoration(
                  boxShadow: [BoxShadow(offset: Offset.zero, blurRadius: 2)],
                ),
                padding: const EdgeInsets.symmetric(vertical: 8),
                child: FirstTimeCityTile(
                  city: selectedCity!,
                  selected: true,
                  onTapped: () {},
                ),
              ),
            )
          : null,
      slivers: buildSlivers(context),
    );
  }

  @override
  void onNoCityFound() {
    setState(() {
      selectedCity = null;
    });
  }

  @override
  Widget buildCityTile(BuildContext context, City city) {
    return FirstTimeCityTile(
      city: city,
      selected: selectedCity == city,
      onTapped: () => setState(() => selectedCity = city),
    );
  }

  @override
  Widget buildCountryTile(BuildContext context, GeoArea country) {
    return FirstTimeListTile(
      title: country.name,
      selected: false,
      onTapped: () => Navigator.of(context).pushMaterialRoute(
        name: FirstTimeCityPicker.routeName,
        builder: (_) => FirstTimeCityPicker(country: country),
      ),
      area: country,
      selectionColor: DependencyInjector.settingsBloc.currentPalette.live,
    );
  }
}

class FirstTimeCityTile extends StatelessWidget {
  const FirstTimeCityTile({
    super.key,
    required this.city,
    required this.selected,
    required this.onTapped,
  });

  final City city;
  final bool selected;
  final VoidCallback onTapped;

  @override
  Widget build(BuildContext context) {
    return FutureBuilder<(GeoArea?, String)>(
      future: _fetchAreaAndCityLabel(context),
      builder: (context, snapshot) {
        final (area, title) = snapshot.data ?? (null, '');
        if (area == null) {
          return const SizedBox();
        }

        return FirstTimeListTile(
          selected: selected,
          onTapped: onTapped,
          area: area,
          title: title,
          subtitle: area.name,
          selectionColor: Palette.standard.live,
        );
      },
    );
  }

  Future<(GeoArea?, String)> _fetchAreaAndCityLabel(
    BuildContext context,
  ) async {
    final areaBloc = DependencyInjector.areaBloc;
    final cityBloc = DependencyInjector.cityBloc;

    final area = await areaBloc.areaByIsoCode(city.geoAreaIsoCode);
    final title = await cityBloc.fullCityName(city);

    return (area, title);
  }
}
