import 'package:flutter/material.dart';

import '../../dependency_injection/dependency_injector.dart';
import '../../generic_widgets/navigation_extensions.dart';
import '../../generic_widgets/spinner.dart';
import '../../models/selection.dart';
import '../cities/city.dart';
import 'first_time_been_screen.dart';

mixin FirstTimeCityMixin<T extends StatefulWidget> on State<T> {
  City? selectedCity;

  void onContinueTapped() async {
    final city = selectedCity;
    if (city == null) {
      return;
    }

    final nav = Navigator.of(context);
    final cityBloc = DependencyInjector.cityBloc;
    final currentRoute = ModalRoute.of(context);

    await SpinnerDialog.showDuringLongProcess(
      context,
      job: () => cityBloc.select(city, Selection.live),
    );

    if (currentRoute is MaterialPageRoute && currentRoute.fullscreenDialog) {
      nav.pop();
    }

    nav.pushMaterialRoute(
      name: FirstTimeBeenScreen.routeName,
      builder: (_) => const FirstTimeBeenScreen(),
    );
  }
}
