import 'package:flutter/material.dart';

import '../../generic_widgets/navigation_extensions.dart';
import '../../generic_widgets/stadium_button.dart';
import '../../l10n/generated/app_localizations.dart';
import '../authentication/login_background.dart';
import 'first_time_city_search_screen.dart';

class FirstTimeSoftStart extends StatelessWidget {
  const FirstTimeSoftStart({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Stack(
        alignment: Alignment.center,
        children: [
          const LoginBackground(),
          _buildContent(context),
        ],
      ),
    );
  }

  Widget _buildContent(BuildContext context) {
    final localizations = AppLocalizations.of(context)!;
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        Image.asset('assets/images/visited_logo.png'),
        Text(
          localizations.welcomeTitle,
          style: Theme.of(context).textTheme.headlineMedium!.copyWith(
              color: Colors.white,
              fontWeight: FontWeight.w700,
              shadows: const [BoxShadow(blurRadius: 10)]),
        ),
        Text(
          localizations.welcomeSubtitle,
          style: Theme.of(context).textTheme.headlineSmall!.copyWith(
              color: Colors.white,
              fontWeight: FontWeight.w600,
              shadows: const [BoxShadow(blurRadius: 10)]),
          textAlign: TextAlign.center,
        ),
        _buildContinueButton(context)
      ],
    );
  }

  Widget _buildContinueButton(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Padding(
        padding: const EdgeInsets.all(8.0),
        child: StadiumButton(
          title: AppLocalizations.of(context)!.getStarted,
          onTapped: () {
            Navigator.of(context).pushMaterialReplacement(
              name: FirstTimeCitySearchScreen.routeName,
              builder: (_) => const FirstTimeCitySearchScreen(),
            );
          },
        ),
      ),
    );
  }
}
