import 'package:flutter/material.dart';

import '../../generic_widgets/responsive_padding.dart';
import '../../helpers/searcher.dart';
import '../../l10n/generated/app_localizations.dart';
import '../../models/geo_area.dart';
import '../cities/city.dart';
import '../cities/city_listenable.dart';
import 'first_time_city_mixin.dart';
import 'first_time_city_search_screen.dart';
import 'first_time_scaffold.dart';
import 'missing_cities_tile.dart';

class FirstTimeCityPicker extends StatefulWidget {
  const FirstTimeCityPicker({super.key, required this.country});

  static const routeName = 'firstTime/liveCountry';

  final GeoArea country;

  @override
  State<FirstTimeCityPicker> createState() => _FirstTimeCityPickerState();
}

class _FirstTimeCityPickerState extends State<FirstTimeCityPicker>
    with CityListenable, FirstTimeCityMixin {
  final _searchController = TextEditingController();
  List<City>? _searchResults;
  Searcher<City>? _searcher;

  @override
  void initState() {
    super.initState();
    loadCities(widget.country);

    _searchController.addListener(() {
      if (_searcher == null) {
        _setupSearcher();
      }

      final query = _searchController.text;

      _searcher?.search(query);
    });
  }

  @override
  Widget build(BuildContext context) {
    return FirstTimeScaffold(
      searchController: _searchController,
      title: widget.country.name,
      tooltip: AppLocalizations.of(context)!.firstTimeLiveTutorial,
      showContinue: selectedCity != null,
      onContinue: onContinueTapped,
      slivers: [
        if (error != null)
          buildError(widget.country)
        else if (!loaded)
          buildLoading()
        else ...[
          _buildCities(),
          const MissingCitiesSliverTile(),
        ],
      ],
    );
  }

  Widget _buildCities() {
    final selectableCities = _searchResults ?? cities;
    return ResponsiveSliverPadding(
      context: context,
      fillToEdgeOnPhone: false,
      sliver: SliverList.builder(
        itemCount: selectableCities?.length ?? 0,
        itemBuilder: (context, index) {
          final city = selectableCities?[index];
          if (city == null) {
            return const SizedBox();
          }

          return FirstTimeCityTile(
            city: city,
            selected: selectedCity == city,
            onTapped: () => setState(() => selectedCity = city),
          );
        },
      ),
    );
  }

  void _setupSearcher() {
    _searcher ??= Searcher(score: 0.3, cities ?? [], (results) {
      setState(() {
        _searchResults = results == null || results.isEmpty ? null : results;
      });
    });
  }

  @override
  void dispose() {
    cancelCitySubscription();
    _searchController.dispose();
    super.dispose();
  }
}
