import 'package:flutter/material.dart';

import '../../generic_widgets/missing_item_sliver_tile.dart';
import '../../l10n/generated/app_localizations.dart';

class MissingCitiesSliverTile extends StatelessWidget {
  const MissingCitiesSliverTile({
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    final localizations = AppLocalizations.of(context)!;
    return SliverPadding(
      padding: const EdgeInsets.only(top: 16),
      sliver: DecoratedSliver(
        decoration: const BoxDecoration(color: Color(0xE6FFFFFF)),
        sliver: MissingItemSliverTile(
          title: localizations.missingAirports,
          emailTitle: localizations.missingCitiesEmailTitle,
          style: const TextStyle(color: Colors.black),
        ),
      ),
    );
  }
}
