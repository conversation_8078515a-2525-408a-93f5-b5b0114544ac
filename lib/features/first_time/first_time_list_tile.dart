import 'package:flutter/material.dart';

import '../../generic_widgets/animated_checkmark.dart';
import '../../generic_widgets/area_flag.dart';
import '../../generic_widgets/separated_tile.dart';
import '../../models/color_extensions.dart';
import '../../models/geo_area.dart';
import 'first_time_scaffold.dart';

class FirstTimeListTile extends StatelessWidget {
  const FirstTimeListTile({
    super.key,
    required this.selected,
    required this.onTapped,
    required this.area,
    required this.title,
    required this.selectionColor,
    this.dense = true,
    this.subtitle,
  });

  final bool selected;
  final VoidCallback onTapped;
  final GeoArea area;
  final String title;
  final String? subtitle;
  final bool dense;
  final Color selectionColor;

  @override
  Widget build(BuildContext context) {
    const baseColour = FirstTimeScaffold.tileBackgroundColour;

    final backgroundColour = selected
        ? Color.alphaBlend(
            selectionColor.withValues(alpha: 0.8),
            baseColour,
          )
        : baseColour;

    final textColour = backgroundColour.legibleForegroundColor();

    return SeparatedTile(
      color: backgroundColour,
      child: ListTile(
        selected: selected,
        onTap: onTapped,
        leading: AreaFlag(area: area),
        dense: true,
        title: Text(title,
            style: TextStyle(
              fontWeight: FontWeight.w600,
              fontSize: 16,
              color: textColour,
            )),
        subtitle: subtitle != null
            ? Text(
                subtitle!,
                style: TextStyle(color: textColour),
              )
            : null,
        trailing: AnimatedCheckmark(
          checked: selected,
          color: textColour,
        ),
      ),
    );
  }
}
