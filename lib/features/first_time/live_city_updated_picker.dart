import 'package:flutter/material.dart';

import '../../dependency_injection/dependency_injector.dart';
import '../../generic_widgets/responsive_padding.dart';
import '../../generic_widgets/spinner.dart';
import '../../helpers/searcher.dart';
import '../../l10n/generated/app_localizations.dart';
import '../../models/geo_area.dart';
import '../../models/palette.dart';
import '../../models/selection.dart';
import '../cities/city.dart';
import 'first_time_list_tile.dart';
import 'first_time_scaffold.dart';
import 'missing_cities_tile.dart';

class LiveCityUpdatedPicker extends StatefulWidget {
  static const routeName = 'live_city_updated_picker';
  const LiveCityUpdatedPicker({
    super.key,
    required this.area,
  });

  final GeoArea area;

  @override
  State createState() => _LiveCityUpdatedPickerState();
}

class _LiveCityUpdatedPickerState extends State<LiveCityUpdatedPicker> {
  final searchController = TextEditingController();
  var cities = <City>[];
  List<City>? searchResults;
  List<City> get cityToDisplay => searchResults ?? cities;
  Searcher<City>? searcher;

  bool loaded = false;

  City? selectedCity;

  @override
  void initState() {
    super.initState();

    searchController.addListener(() {
      if (searchController.text.trim().isEmpty && searchResults != null) {
        setState(() => searchResults = null);
        return;
      }
      searcher?.search(searchController.text);
    });

    WidgetsBinding.instance.addPostFrameCallback((_) async {
      cities = await DependencyInjector.cityBloc.fetchCities(widget.area);
      searcher = Searcher(cities, (results) {
        setState(() => searchResults = results);
      });

      setState(() {
        loaded = true;
      });
    });
  }

  @override
  Widget build(BuildContext context) {
    final localizations = AppLocalizations.of(context)!;
    return PopScope(
      canPop: false,
      child: FirstTimeScaffold(
        searchController: searchController,
        title: localizations.whereDoYouLive,
        tooltip: localizations.firstTimeLiveTutorial,
        backgroundImageIndex: 6,
        showContinue: selectedCity != null,
        hideBackButton: true,
        onContinue: () async {
          final city = selectedCity;
          if (city == null) {
            return;
          }
          final nav = Navigator.of(context);
          await DependencyInjector.cityBloc.select(
            city,
            Selection.live,
          );

          nav.pop();
        },
        slivers: [
          if (loaded)
            _buildCities(context)
          else
            const SliverFillRemaining(
              child: Center(
                child: Spinner(),
              ),
            ),
          if (cities.isNotEmpty) const MissingCitiesSliverTile(),
        ],
      ),
    );
  }

  Widget _buildCities(BuildContext context) {
    return ResponsiveSliverPadding(
      context: context,
      fillToEdgeOnPhone: false,
      sliver: SliverList(
        delegate: SliverChildBuilderDelegate(
          childCount: cityToDisplay.length,
          (context, index) {
            final city = cityToDisplay[index];
            final selected = selectedCity == city;

            return FutureBuilder<String>(
                future: DependencyInjector.cityBloc.fullCityName(city),
                builder: (context, snapshot) {
                  return FirstTimeListTile(
                    selected: selected,
                    selectionColor: Palette.standard.live,
                    area: widget.area,
                    title: snapshot.data ?? city.name,
                    onTapped: () => setState(() => selectedCity = city),
                  );
                });
          },
        ),
      ),
    );
  }
}
