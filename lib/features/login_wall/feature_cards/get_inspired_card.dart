import 'dart:math';

import 'package:flutter/material.dart';

import '../../../generic_widgets/frosted_glass_card.dart';
import '../../../l10n/generated/app_localizations.dart';
import 'card_text.dart';

class GetInspiredCard extends StatelessWidget {
  const GetInspiredCard({
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: FrostedGlassCard(
        child: SizedBox(
          width: double.infinity,
          child: Column(
            children: [
              CardText(AppLocalizations.of(context)!.inspirationPromotion),
              _buildImages(),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildImages() {
    return Expanded(
      child: Stack(
        children: [
          Align(
            alignment: const Alignment(-0.4, 0),
            child: _buildCard(
              angle: -0.05,
              assetPath: 'assets/images/login_bg_2.jpg',
            ),
          ),
          Align(
            alignment: const Alignment(0.4, 0),
            child: _buildCard(
              angle: 0.06,
              assetPath: 'assets/images/login_bg_4.jpg',
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildCard({
    required String assetPath,
    required double angle,
  }) {
    return Transform.rotate(
      angle: pi * angle,
      child: Card(
        elevation: 6,
        child: Padding(
          padding: const EdgeInsets.all(8.0),
          child: Image.asset(assetPath),
        ),
      ),
    );
  }
}
