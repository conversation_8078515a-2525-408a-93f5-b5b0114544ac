import 'package:flutter/material.dart';

class CardText extends StatelessWidget {
  final String text;

  const CardText(this.text, {super.key});

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8.0),
      child: Text(
        text,
        style: const TextStyle(
            fontWeight: FontWeight.w800,
            fontSize: 20,
            color: Colors.black,
            shadows: [
              Shadow(
                blurRadius: 6,
                offset: Offset(0, 2),
                color: Colors.black26,
              )
            ]),
        textAlign: TextAlign.center,
      ),
    );
  }
}
