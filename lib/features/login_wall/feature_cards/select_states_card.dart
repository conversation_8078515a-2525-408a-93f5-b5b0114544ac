import 'package:flutter/material.dart';

import '../../../generic_widgets/frosted_glass_card.dart';
import '../../../l10n/generated/app_localizations.dart';
import 'card_text.dart';

class SelectStatesCard extends StatelessWidget {
  const SelectStatesCard({
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: FrostedGlassCard(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            CardText(AppLocalizations.of(context)!.selectRegionsPromotion),
            FittedBox(
              child: Row(
                children: [
                  Image.asset('assets/flags/flag_us.png'),
                  const SizedBox(width: 16),
                  Image.asset('assets/flags/flag_ca.png'),
                ],
              ),
            ),
            const SizedBox(height: 10),
            FittedBox(
              child: Row(
                children: [
                  Image.asset('assets/flags/flag_gb.png'),
                  const SizedBox(width: 16),
                  Image.asset('assets/flags/flag_au.png'),
                ],
              ),
            )
          ],
        ),
      ),
    );
  }
}
