import 'package:flutter/material.dart';

import '../../../l10n/generated/app_localizations.dart';
import '../../dashboard/travel_goal_graph.dart';

import '../../../generic_widgets/frosted_glass_card.dart';
import 'card_text.dart';

class SaveStatsCard extends StatelessWidget {
  const SaveStatsCard({
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: FrostedGlassCard(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            CardText(AppLocalizations.of(context)!.saveStatsPromotion),
            const Flexible(
              child: FittedBox(
                fit: BoxFit.scaleDown,
                child: TravelGoalGraph(
                  forcedPercent: 0.38,
                  forcedRemainder: 15,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
