import 'package:flutter/material.dart';

import '../../../generic_widgets/frosted_glass_card.dart';
import '../../../l10n/generated/app_localizations.dart';
import 'card_text.dart';

class TrackExperiencesCard extends StatelessWidget {
  const TrackExperiencesCard({
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: FrostedGlassCard(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            CardText(AppLocalizations.of(context)!.experiencesPromotion),
            FittedBox(
              child: Padding(
                padding: const EdgeInsets.all(8.0),
                child: Row(
                  crossAxisAlignment: CrossAxisAlignment.center,
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Image.asset('assets/images/Beach.png'),
                    Image.asset('assets/images/Hiking.png'),
                    Image.asset('assets/images/History.png'),
                    Image.asset('assets/images/spas.png'),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
