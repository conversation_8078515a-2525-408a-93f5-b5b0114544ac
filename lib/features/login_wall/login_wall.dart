import 'dart:async';

import 'package:flutter/material.dart';

import '../../dependency_injection/dependency_injector.dart';
import '../../generic_widgets/alert.dart';
import '../../generic_widgets/spinner.dart';
import '../../generic_widgets/stadium_button.dart';
import '../../generic_widgets/stroked_text.dart';
import '../../l10n/generated/app_localizations.dart';
import '../../state_safety.dart';
import '../authentication/keyboard_aware_scaffold.dart';
import '../authentication/login_background.dart';
import '../authentication/login_form.dart';
import '../authentication/login_screen.dart';
import '../privacy_agreement/privacy_agreement.dart';
import '../privacy_agreement/privacy_dialog.dart';
import 'feature_cards/get_inspired_card.dart';
import 'feature_cards/save_stats_card.dart';
import 'feature_cards/select_states_card.dart';

class LoginWall extends StatefulWidget {
  static const routeName = 'login/unauthenticated';

  const LoginWall({super.key});

  @override
  State createState() => _LoginWallState();
}

class _LoginWallState extends State<LoginWall> {
  bool keyboardOpen = false;
  final _controller = PageController();
  final focus = FocusNode();

  late Timer autoPageTurner;

  @override
  void initState() {
    super.initState();
    focus.addListener(() {
      setState(() {
        keyboardOpen = focus.hasFocus;
        if (focus.hasFocus) {
          autoPageTurner.cancel();
        } else {
          _configureAutoPageTurner();
        }
      });
    });

    _configureAutoPageTurner();
  }

  void _configureAutoPageTurner() {
    autoPageTurner = Timer.periodic(const Duration(seconds: 9), _flipPage);
  }

  void _flipPage(Timer timer) {
    final page = _controller.page?.toInt() ?? 0;
    final nextPage = page == 3 ? 0 : page + 1;
    _controller.animateToPage(
      nextPage,
      duration: const Duration(milliseconds: 780),
      curve: Curves.easeInOut,
    );
  }

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        const LoginBackground(),
        KeyboardAwareScaffold(
          body: Column(
            children: [
              const SizedBox(
                height: 10,
              ),
              if (!keyboardOpen && !isCompact) _buildPromotionalCards(context),
              if (shouldShowSubtitle) _buildSubtitle(context),
              Flexible(
                flex: 4,
                child: LoginForm(
                  title: AppLocalizations.of(context)!.signup,
                  focus: focus,
                  onEmailSubmitted: _onEmailSubmitted,
                  mode: LoginMode.signUp,
                ),
              ),
              Padding(
                padding: const EdgeInsets.only(top: 8),
                child: StadiumButton(
                  title: AppLocalizations.of(context)!.cancel,
                  onTapped: _onCancelTapped,
                ),
              )
            ],
          ),
        ),
      ],
    );
  }

  bool get isCompact => MediaQuery.of(context).size.height <= 570;

  bool get shouldShowSubtitle {
    if (!isCompact) {
      return true;
    }

    return !keyboardOpen;
  }

  Widget _buildPromotionalCards(BuildContext context) {
    return Expanded(
      flex: 5,
      child: PageView(
        controller: _controller,
        children: const [
          SaveStatsCard(),
          GetInspiredCard(),
          SelectStatesCard(),
        ],
      ),
    );
  }

  Padding _buildSubtitle(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8.0),
      child: StrokedText(
        AppLocalizations.of(context)!.loginWallSubtitle,
        style: const TextStyle(
          fontWeight: FontWeight.w800,
          fontSize: 20,
          color: Colors.white,
        ),
        strokeColour: Colors.black,
        strokeWidth: 2,
        maxLines: 4,
        textAlign: TextAlign.center,
      ),
    );
  }

  void _onCancelTapped() async {
    final localizations = AppLocalizations.of(context)!;
    final nav = Navigator.maybeOf(context);
    final dialog = ConfirmDialog(
      title: localizations.areYouSure,
      message: localizations.loseAllSelectionsWarning,
      confirmText: localizations.createAccount,
      cancelText: localizations.continueWithoutAccount,
      cancelDestructive: true,
    );

    final confirmed = await dialog.show(context);

    if (confirmed == true) {
      return;
    }

    nav?.pop();
  }

  void _onEmailSubmitted(String email) async {
    final dialog = PrivacyDialog(
      agreement: PrivacyAgreement(),
    );
    final signedAgreement = await dialog.show(context);

    if (!mounted) {
      return;
    }

    const spinner = SpinnerDialog();
    spinner.show(context);
    try {
      await DependencyInjector.sessionBloc.login(
        email,
        privacyAgreement: signedAgreement,
      );
      maybeNavigator?.pop();
    } catch (e) {
      if (mounted) {
        await Alert(
          title: AppLocalizations.of(context)!.errorTitle,
          message: e.toString(),
        ).show(context);
        maybeNavigator?.pop();
      }
    }
  }

  @override
  void dispose() {
    _controller.dispose();
    focus.dispose();
    autoPageTurner.cancel();
    super.dispose();
  }
}
