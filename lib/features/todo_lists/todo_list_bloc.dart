import 'dart:async';

import 'package:collection/collection.dart';
import 'package:rxdart/rxdart.dart';

import '../../caching/resettable_behaviour_subject.dart';
import '../../dependency_injection/dependency_injector.dart';
import '../../generic_widgets/bloc.dart';
import '../../models/geo_area.dart';
import '../../models/selection.dart';
import '../books/book_link.dart';
import '../in_app_purchase/iap_bloc.dart';
import '../in_app_purchase/iap_product.dart';
import '../in_app_purchase/iap_purchase_required_exception.dart';
import 'models/preferred_todo_list.dart';
import 'models/todo_area_list.dart';
import 'models/todo_list.dart';
import 'models/todo_list_item.dart';
import 'models/todo_list_selection_summary.dart';
import 'models/todo_list_tag.dart';
import 'models/todo_list_type.dart';
import 'models/todo_list_view_model.dart';
import 'models/top_one_thousand_list.dart';
import 'preferred_list_repository.dart';
import 'todo_list_service.dart';
import 'todo_list_toggle_sheet.dart';

abstract class OnListUpdatedDelegate {
  void onListItemUpdated(TodoListItem item, TodoList list, Selection selection);
}

class TodoListBloc implements Bloc {
  static const kSponsoredTag = 'SPONSORED_CATEGORY';
  static const kNoHeadersTag = 'NO_STICKY_HEADERS';
  static const kNationParksTagId = 18;

  TodoListBloc({required IAPBloc iap, required OnListUpdatedDelegate delegate})
    : _iap = iap,
      _delegate = delegate,
      _service = TodoListService() {
    _languageSubscription ??= DependencyInjector.settingsBloc.language.listen((
      _,
    ) async {
      _service.clearCache();

      if (!DependencyInjector.sessionBloc.isAuthenticated) {
        return;
      }

      await _fetchLists();

      // Force refresh of area lists by notifying listeners
      _refreshAreaLists();
    });
  }

  StreamSubscription? _languageSubscription;

  // Force refresh of area lists
  void _refreshAreaLists() {
    // This will force the listByCountry stream to emit a new value
    _iap.status.first.then((status) {
      if (status.hasUnlockedItineraries) {
        _service.fetchAreaLists().then((lists) {
          // No need to do anything with the result, just trigger the fetch
        });
      }
    });
  }

  final OnListUpdatedDelegate _delegate;
  final IAPBloc _iap;
  final TodoListService _service;
  final _preferredListRepository = PreferredListRepository();

  late final _topOneThousandController =
      ResettableBehaviorSubject<TopOneThousandViewModel>()
        ..onListen = _fetchTopOneThousand;

  Stream<TopOneThousandViewModel> get topOneThousand =>
      _topOneThousandController.stream;

  final _allListSelections =
      BehaviorSubject<Map<TodoList, TodoListSelections>>();

  // We need to track this separately since area lists can only add a subset of the selection data
  // Area Lists are considered only a "view," they are not a source of truth
  final _fetchedSelectionsGuard = <TodoList>{};

  late final _listsController = BehaviorSubject<Set<PreferredTodoList>>()
    ..onListen = _fetchLists;

  final _sortOrderController = BehaviorSubject<SortOrder>.seeded(
    SortOrder.popularity,
  );

  final _modeController = BehaviorSubject.seeded(TodoListMode.topics);

  Stream<SortOrder> get sortOrder => _sortOrderController.stream;
  Stream<TodoListMode> get mode => _modeController.stream;

  Stream<TodoListSelections> rawSelection(TodoList list) =>
      _allListSelections.map((event) => event[list] ?? TodoListSelections());

  SortOrder get currentSortOrder => _sortOrderController.value;

  final _bookLinks = <TodoList, BookLink>{};

  Stream<Map<TodoList, TodoListSelections>> get selections =>
      _allListSelections.stream;

  BookLink? getBook(TodoList list) => _bookLinks[list];

  void setSortOrder(SortOrder value) => _sortOrderController.add(value);

  Stream<List<PreferredTodoList>> get listPreferences =>
      _listsController.stream.map((event) => event.sorted());

  Stream<List<TodoAreaList>> get listByCountry {
    return _iap.status.asyncMap((status) {
      if (!status.hasUnlockedItineraries) {
        throw IAPPurchaseRequiredException(IAPFeature.unlockItineraries);
      }

      return _service.fetchAreaLists();
    });
  }

  Stream<List<TodoListViewModel>> get listsByTopic {
    return _listsController.stream.asyncMap(_buildLists);
  }

  Future<List<TodoListViewModel>> _buildLists(
    Set<PreferredTodoList> preferredLists,
  ) async {
    final sponsored = preferredLists.where(
      (element) => element.list.isSponsored,
    );

    final lists = preferredLists
        .whereNot((element) => element.list.isSponsored)
        .where((element) => element.enabled)
        .sorted();

    final tags = await _service.fetchTags();

    final flags = DependencyInjector.featureFlags;
    if (await flags.organizeListByTag() == false) {
      return _buildFlattenedLists(lists, sponsored, tags);
    }

    return _buildListsOrganizedByCategory(sponsored, lists, tags);
  }

  List<TodoListViewModel> _buildListsOrganizedByCategory(
    Iterable<PreferredTodoList> sponsored,
    List<PreferredTodoList> lists,
    List<TodoListTag> tags,
  ) {
    final structured = [
      if (sponsored.isNotEmpty)
        TodoListTileFlatListViewModel(
          lists: sponsored.map((e) => e.list).toList(),
        ),
      for (final tag in tags)
        if (tag.listIds.isNotEmpty)
          TodoListTileCategoryViewModel(
            tag: tag,
            mode: NestedTodoTileRenderMode.dropdown,
            lists: tag.listIds
                .map(
                  (id) => lists.firstWhereOrNull(
                    (element) => element.list.id == id,
                  ),
                )
                .nonNulls
                .sorted()
                .map((e) => e.list)
                .toList(),
          ),
    ];

    structured.removeWhere((e) => e.lists.isEmpty);
    return structured;
  }

  List<TodoListViewModel> _buildFlattenedLists(
    List<PreferredTodoList> lists,
    Iterable<PreferredTodoList> sponsored,
    List<TodoListTag> tags,
  ) {
    TodoListTileCategoryViewModel? nationParkViewModel;
    final nationParkTag = tags.firstWhereOrNull(
      (tag) => tag.id == kNationParksTagId,
    );
    if (nationParkTag != null) {
      final nationParkLists = lists
          .where(
            (preferred) => nationParkTag.listIds.contains(preferred.list.id),
          )
          .map((e) => e.list)
          .toList();
      lists.removeWhere((element) => nationParkLists.contains(element.list));

      nationParkViewModel = TodoListTileCategoryViewModel(
        tag: nationParkTag,
        lists: nationParkLists,
        mode: NestedTodoTileRenderMode.pushToScreen,
      );
    }

    return [
      TodoListTileFlatListViewModel(
        lists: [
          ...sponsored.map((e) => e.list),
          ...lists.sorted().map((e) => e.list),
        ],
      ),
      if (nationParkViewModel?.lists.isNotEmpty ?? false) nationParkViewModel!,
    ];
  }

  Set<TodoListSelectionSummary>? _selectionsSummaries;
  Set<TodoListSelectionSummary>? _areaSelectionsSummaries;

  Future<List<TodoList>> fetchLists() => _service.fetchLists();

  Future<List<TodoListItem>> fetchItems(
    TodoList list, {
    TodoListType? filter,
  }) async {
    final details = await (list is TodoAreaList
        ? _service.fetchAreaListDetails(list, filter: filter)
        : _service.fetchDetails(list));
    if (details.bookLink != null) {
      _bookLinks[list] = details.bookLink!;
    }

    populateSelections(list);

    return details.items;
  }

  Future<void> populateSelections(TodoList list) async {
    if (list is TodoAreaList) {
      return _populateAreaListSelections(list);
    }

    if (_fetchedSelectionsGuard.contains(list)) {
      return;
    }

    final listSelections = await _service.fetchSelections(list);
    final selections = _allListSelections.valueOrNull ?? {};
    selections[list] = listSelections;
    _allListSelections.add(selections);
    _fetchedSelectionsGuard.add(list);
  }

  Future<void> _populateAreaListSelections(TodoAreaList areaList) async {
    if (_fetchedSelectionsGuard.contains(areaList)) {
      return;
    }

    final selections = await _service.fetchAreaSelections(areaList);

    if (selections.isEmpty) {
      return;
    }

    final existingSelections = _allListSelections.valueOrNull ?? {};

    for (final entry in selections.entries) {
      final list = entry.key;
      final selections = entry.value;

      final existingListSelections = existingSelections[list] ?? {};
      for (final selectionTypeEntry in selections.entries) {
        final selectionType = selectionTypeEntry.key;
        final existingSelectionIds =
            existingListSelections[selectionType] ?? {};
        existingSelectionIds.addAll(selectionTypeEntry.value);
        existingListSelections[selectionType] = existingSelectionIds;
      }
      existingSelections[list] = existingListSelections;
    }

    _allListSelections.add(existingSelections);
    _fetchedSelectionsGuard.add(areaList);
  }

  Stream<Selection> selection(TodoList list, TodoListItem item) {
    if (item is TodoAreaListItem) {
      list = item.parentList;
    }
    return _allListSelections.map(
      (selections) => _findSelection(selections, list, item),
    );
  }

  Selection currentSelection(TodoList list, TodoListItem item) {
    if (item is TodoAreaListItem) {
      list = item.parentList;
    }
    final selections = _allListSelections.valueOrNull;
    if (selections == null) {
      return Selection.clear;
    }

    return _findSelection(selections, list, item);
  }

  Stream<Map<TodoListItem, Selection>> selectionsByList(TodoList list) {
    if (list is TopOneThousandList) {
      return _topOneThousandController.stream.map((event) {
        final selections = <TodoListItem, Selection>{};
        for (final record in event.items) {
          final (item, selection) = record;
          if (selection == null) {
            continue;
          }

          selections[item] = selection;
        }
        return selections;
      });
    }

    return _allListSelections.asyncMap((event) async {
      final selections = event[list];
      if (selections == null) {
        return {};
      }

      final items = await fetchItems(list);

      final formattedSelections = <TodoListItem, Selection>{};

      for (final entry in selections.entries) {
        final selection = entry.key;
        final ids = entry.value;
        for (final id in ids) {
          final item = items.firstWhere((item) => item.id == id);
          formattedSelections[item] = selection;
        }
      }

      return formattedSelections;
    });
  }

  Selection _findSelection(
    Map<TodoList, TodoListSelections> selections,
    TodoList list,
    TodoListItem item,
  ) {
    final listSelections = selections[list];
    if (listSelections == null) {
      return Selection.clear;
    }

    for (final entry in listSelections.entries) {
      if (entry.value.contains(item.id)) {
        return entry.key;
      }
    }

    return Selection.clear;
  }

  Selection getSelection(TodoList list, TodoListItem item) {
    if (item is TodoAreaListItem) {
      list = item.parentList;
    }

    final selections = _allListSelections.valueOrNull ?? {};
    return _findSelection(selections, list, item);
  }

  Stream<double> percentComplete(TodoList list) async* {
    if (list is TopOneThousandList) {
      yield* _topOneThousandController.stream.map(
        (event) => list.percentageComplete,
      );
      return;
    }

    final currentSelected = _allListSelections.valueOrNull?[list];

    if (currentSelected != null) {
      yield (currentSelected[Selection.been]?.length ?? 0) / list.itemCount;
    } else {
      final summaries = await (list is TodoAreaList
          ? fetchAreaSummaries()
          : fetchSummaries());
      final summary = summaries.firstWhereOrNull(
        (element) => element.listId == list.id,
      );
      yield (summary?.selected ?? 0) / list.itemCount;
    }

    yield* amountSelected(
      list,
      Selection.been,
    ).map((amount) => amount / list.itemCount);
  }

  Stream<double> goalCompleted(TodoList list) {
    if (list is TopOneThousandList) {
      return _topOneThousandController.stream.map((event) {
        final totalWant = event.items
            .where((item) => item.$2 == Selection.want)
            .length;

        final totalBeen = event.items
            .where(
              (item) =>
                  item.$2 == Selection.been ||
                  item.$2 == Selection.live ||
                  item.$2 == Selection.lived,
            )
            .length;
        return _goalCompleted(totalBeen, totalWant);
      });
    }

    if (list is TodoAreaList) {
      return Rx.combineLatest2(
        _amountSelectedForAreaList(list, selectionType: Selection.been),
        _amountSelectedForAreaList(list, selectionType: Selection.want),
        (beenCount, wantCount) {
          return _goalCompleted(beenCount, wantCount);
        },
      );
    }

    return _allListSelections.map((event) {
      final selections = event[list];
      if (selections == null) {
        return 0.0;
      }

      final wantCount = selections[Selection.want]?.length ?? 0;
      final beenCount = selections[Selection.been]?.length ?? 0;

      return _goalCompleted(beenCount, wantCount);
    });
  }

  double _goalCompleted(int beenCount, int wantCount) {
    final denominator = wantCount + beenCount;

    if (denominator == 0) {
      return 0.0;
    }

    return beenCount / denominator;
  }

  Stream<int> amountSelected(TodoList list, Selection selection) {
    if (list is TodoAreaList) {
      return _amountSelectedForAreaList(list, selectionType: selection);
    }

    return _allListSelections.map(
      (event) => event[list]?[selection]?.length ?? 0,
    );
  }

  Stream<int> _amountSelectedForAreaList(
    TodoAreaList list, {
    required Selection selectionType,
  }) => _allListSelections.asyncMap((event) async {
    final allSelectedIds = event.values
        .expand<int>((element) => element[selectionType] ?? {})
        .toSet();
    final details = await _service.fetchAreaListDetails(list);
    final itemIds = details.items.map((e) => e.id).toSet();
    return allSelectedIds.intersection(itemIds).length;
  });

  Stream<bool> canShare(TodoList list) =>
      amountSelected(list, Selection.been).map((amount) => amount > 1);

  Stream<(double, int)> percentAndAmountComplete(TodoList list) =>
      percentComplete(list).map(
        (percent) =>
            (percent, _allListSelections.valueOrNull?[list]?.length ?? 0),
      );

  int currentSelectedAmount(TodoList list) =>
      _allListSelections.valueOrNull?[list]?.length ?? 0;

  Stream<double> percentTopSelected(TodoList list) {
    if (list is TopOneThousandList) {
      return _topOneThousandController.stream.map(
        (event) =>
            event.items
                .take(100)
                .where(
                  (item) =>
                      item.$2 == Selection.been ||
                      item.$2 == Selection.live ||
                      item.$2 == Selection.lived,
                )
                .length /
            100,
      );
    }

    if (list is TodoAreaList) {
      return _percentTopTenSelectedForAreaList(list);
    }

    return _allListSelections.asyncMap((event) async {
      final selections = event[list];
      if (selections == null) {
        return 0;
      }

      final items = await fetchItems(list);
      final topTen = items.length < 10 ? items : items.getRange(0, 10);
      final topTenIds = topTen.map((e) => e.id).toSet();
      final selected =
          selections[Selection.been]?.intersection(topTenIds).length ?? 0;
      return selected / topTenIds.length;
    });
  }

  Stream<double> _percentTopTenSelectedForAreaList(TodoAreaList list) {
    return _allListSelections.asyncMap((event) async {
      if (event.isEmpty) {
        return 0;
      }

      final items = await fetchItems(list);
      final topTen = items.length < 10 ? items : items.getRange(0, 10);

      var selected = 0;

      for (final item in topTen) {
        final listId = item.listIds?.first;
        if (listId == null) {
          continue;
        }

        final list = event.keys.firstWhereOrNull((list) => list.id == listId);

        if (list == null) {
          continue;
        }

        final selectedBeenIds = event[list]?[Selection.been];

        if (selectedBeenIds?.contains(item.id) ?? false) {
          selected++;
        }
      }

      return selected / topTen.length;
    });
  }

  Future<Set<TodoListSelectionSummary>> fetchSummaries() async {
    if (_selectionsSummaries != null) {
      return _selectionsSummaries!;
    }

    _selectionsSummaries = await _service.fetchSelectionSummaries();
    return _selectionsSummaries!;
  }

  Future<Set<TodoListSelectionSummary>> fetchAreaSummaries() async {
    if (_areaSelectionsSummaries != null) {
      return _areaSelectionsSummaries!;
    }

    _areaSelectionsSummaries = await _service.fetchAreaSelectionSummaries();
    return _areaSelectionsSummaries!;
  }

  void select(TodoList list, TodoListItem item, Selection selection) {
    Selection? topOneThousandSelection;
    if (list is TopOneThousandList) {
      topOneThousandSelection = _topOneThousandController.valueOrNull?.items
          .firstWhereOrNull((e) => e.$1.id == item.id)
          ?.$2;
    }

    if (item is TodoAreaListItem) {
      list = item.parentList;
    }

    final selections = _allListSelections.valueOrNull?[list] ?? {};
    final itemId = item.id;

    final existingEntry = selections.entries.firstWhereOrNull(
      (entry) => entry.value.contains(itemId),
    );
    final existingSelections = existingEntry?.value;

    final existingSelection = topOneThousandSelection ?? existingEntry?.key;

    // Unselect if the user is sending the same selection type
    if (selection == Selection.clear || selection == existingSelection) {
      existingSelections?.remove(itemId);
      _updateSelection(list, selections);
      _service.unselect(list, item);
      _delegate.onListItemUpdated(item, list, Selection.clear);
      _updateTopOneThousandList(item, list, Selection.clear);
      return;
    }

    final newSelections = selections[selection] ?? {};
    existingSelections?.remove(itemId);
    newSelections.add(itemId);
    selections[selection] = newSelections;
    _updateSelection(list, selections);

    _service.select(list: list, item: item, selection: selection);

    _updateTopOneThousandList(item, list, selection);
    _delegate.onListItemUpdated(item, list, selection);
  }

  void _updateTopOneThousandList(
    TodoListItem item,
    TodoList list,
    Selection? selection,
  ) {
    if (selection == Selection.clear) {
      selection = null;
    }

    final currentTopItems = _topOneThousandController.valueOrNull?.items;
    if (currentTopItems == null) {
      return;
    }

    final index = currentTopItems.indexWhere((e) => e.$1.id == item.id);
    if (index == -1) {
      return;
    }

    final updatedItem = TodoAreaListItem(item: item, parentList: list);

    currentTopItems[index] = (updatedItem, selection);

    _topOneThousandController.add(
      TopOneThousandViewModel(items: currentTopItems),
    );
  }

  void reorderListPreference(int oldIndex, int newIndex) {
    final preferences = _listsController.value.sorted();
    if (oldIndex < newIndex) {
      newIndex -= 1;
    }

    final preference = preferences.removeAt(oldIndex);
    preferences.insert(newIndex, preference);

    final publishable = preferences
        .mapIndexed(
          (index, element) => PreferredTodoList(
            list: element.list,
            ordinal: index,
            enabled: element.enabled,
          ),
        )
        .toSet();

    _listsController.add(publishable);
    _preferredListRepository.save(publishable);
  }

  void toggleVisibility(PreferredTodoList preference) {
    final updated = preference.copyWith(enabled: !preference.enabled);
    final preferences = {..._listsController.value};
    preferences.remove(preference);
    preferences.add(updated);
    _listsController.add(preferences);
    _preferredListRepository.save(preferences);
  }

  void _updateSelection(TodoList list, TodoListSelections selections) {
    final allSelections = _allListSelections.valueOrNull ?? {};
    allSelections[list] = selections;
    _allListSelections.add(allSelections);
  }

  Future<void> _fetchLists() async {
    final lists = await fetchLists();
    final preferences = await _preferredListRepository.retrieve(lists);
    _listsController.add(preferences);
  }

  void resetPreferredLists() async {
    await _preferredListRepository.reset();
    _fetchLists();
  }

  void setMode(TodoListMode mode) {
    if (_modeController.valueOrNull == mode) {
      return;
    }

    _modeController.add(mode);
  }

  @override
  void clear() {
    resetPreferredLists();
    _selectionsSummaries = null;
    _areaSelectionsSummaries = null;
    _allListSelections.add({});
    _fetchedSelectionsGuard.clear();
    _sortOrderController.add(SortOrder.popularity);
    _modeController.add(TodoListMode.topics);
  }

  Future<TodoAreaList?> fetchAreaList(GeoArea area) async {
    final lists = await listByCountry.first;
    final list = lists.firstWhereOrNull((element) => element.area == area);
    return list;
  }

  void _fetchTopOneThousand() async {
    if (_topOneThousandController.hasValue) {
      return;
    }

    final items = await _service.fetchTopOneThousandItems();
    _topOneThousandController.add(TopOneThousandViewModel(items: items));
  }

  @override
  void dispose() {
    _languageSubscription?.cancel();
    _allListSelections.close();
    _listsController.close();
    _sortOrderController.close();
    _modeController.close();
  }
}

enum NestedTodoTileRenderMode { dropdown, pushToScreen }

enum TodoListMode { topics, countries }
