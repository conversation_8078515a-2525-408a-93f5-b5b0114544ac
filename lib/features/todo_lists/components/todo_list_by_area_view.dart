import 'dart:async';

import 'package:flutter/material.dart';
import 'package:sliver_tools/sliver_tools.dart';

import '../../../dependency_injection/dependency_injector.dart';
import '../../../generic_widgets/navigation_extensions.dart';
import '../../../generic_widgets/sliver_sticky_search_bar.dart';
import '../../../generic_widgets/spinner.dart';
import '../../in_app_purchase/iap_purchase_required_exception.dart';
import '../models/todo_list.dart';
import '../todo_list_details_screen.dart';
import 'todo_list_tile.dart';

class TodoListByCountryView extends StatefulWidget {
  const TodoListByCountryView({super.key});

  @override
  State<TodoListByCountryView> createState() => _TodoListByCountryViewState();
}

class _TodoListByCountryViewState extends State<TodoListByCountryView> {
  List<TodoList>? areaLists;
  List<TodoList>? searchResults;
  Object? error;
  StreamSubscription? subscription;
  StreamSubscription? _languageSubscription;

  @override
  void initState() {
    super.initState();
    subscription ??= DependencyInjector.todoListBloc.listByCountry.listen(
      (event) {
        setState(() {
          areaLists = event;
          error = null;
        });
      },
      onError: (error) {
        setState(() {
          this.error = error;
        });
      },
    );

    // Listen for language changes
    _languageSubscription ??= DependencyInjector.settingsBloc.language.listen((
      _,
    ) {
      // Reset state and force refresh when language changes
      setState(() {
        areaLists = null;
        searchResults = null;
        error = null;
      });

      // Re-subscribe to the listByCountry stream to get fresh data
      subscription?.cancel();
      subscription = DependencyInjector.todoListBloc.listByCountry.listen(
        (event) {
          setState(() {
            areaLists = event;
            error = null;
          });
        },
        onError: (error) {
          setState(() {
            this.error = error;
          });
        },
      );
    });
  }

  @override
  Widget build(BuildContext context) {
    if (error != null) {
      return SliverFillRemaining(child: _buildError(context, error!));
    }

    final lists = searchResults ?? areaLists;
    if (lists == null) {
      return const SliverFillRemaining(child: Center(child: Spinner()));
    }

    return MultiSliver(
      children: [
        SliverStickySearchBar<TodoList>(
          items: areaLists!,
          onQuery: (List<TodoList>? results) {
            setState(() {
              searchResults = results;
            });
          },
        ),
        SliverList(
          delegate: SliverChildBuilderDelegate(childCount: lists.length, (
            context,
            index,
          ) {
            final list = lists[index];
            return TodoListTile(
              list: list,
              onTapped: () {
                Navigator.of(context).pushMaterialRoute(
                  name: 'lists/area/${list.id}',
                  builder: (context) => TodoListDetailsScreen(list: list),
                );
              },
            );
          }),
        ),
      ],
    );
  }

  Widget _buildError(BuildContext context, Object error) {
    if (error is IAPPurchaseRequiredException) {
      return const Center(child: Text('You must purchase itineraries first'));
    }

    return Center(child: Text(error.toString()));
  }

  @override
  void dispose() {
    subscription?.cancel();
    _languageSubscription?.cancel();
    super.dispose();
  }
}
