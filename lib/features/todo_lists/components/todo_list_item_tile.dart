import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';

import '../../../dependency_injection/dependency_injector.dart';
import '../../../generic_widgets/area_flag.dart';
import '../../../models/color_extensions.dart';
import '../../../models/geo_area.dart';
import '../../../models/selection.dart';
import '../models/todo_list.dart';
import '../models/todo_list_item.dart';

class TodoListItemTile extends StatelessWidget {
  const TodoListItemTile({
    super.key,
    required this.item,
    required this.activeSelection,
    this.list,
    this.onTapped,
    this.showActiveSelection = true,
    this.forceFlagToBeRendered = false,
  });

  final bool showActiveSelection;
  final TodoListItem item;
  final TodoList? list;
  final VoidCallback? onTapped;
  final Stream<Selection> activeSelection;
  final bool forceFlagToBeRendered;

  @override
  Widget build(BuildContext context) {
    final requireList = list;

    if (requireList == null) {
      return _buildAnimatedContent(context, Selection.want);
    }

    return StreamBuilder<Selection>(
      stream: activeSelection,
      initialData: Selection.clear,
      builder: (context, snapshot) {
        return GestureDetector(
          onTap: onTapped,
          behavior: HitTestBehavior.opaque,
          child: _buildAnimatedContent(context, snapshot.requireData),
        );
      },
    );
  }

  Widget _buildAnimatedContent(BuildContext context, Selection selection) {
    return AnimatedContainer(
      duration: const Duration(milliseconds: 240),
      decoration: _buildDecoration(context, selection),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(8),
        child: Column(
          children: [
            TodoListItemThumbnail(
              item: item,
              selection: selection,
              showActiveSelection: showActiveSelection,
              forceFlagToBeRendered: forceFlagToBeRendered,
            ),
            _buildTitle(context, selection),
          ],
        ),
      ),
    );
  }

  Decoration _buildDecoration(BuildContext context, Selection selection) {
    final theme = Theme.of(context);
    final selected = showActiveSelection && selection.active;
    final colour = selected
        ? selection.getColor(context)
        : theme.secondaryHeaderColor;
    return BoxDecoration(
      color: colour,
      borderRadius: BorderRadius.circular(8),
      border: selected ? Border.all(color: colour, width: 3) : null,
      boxShadow: [
        if (selected)
          BoxShadow(
            offset: const Offset(0, 2),
            blurRadius: 8,
            color: theme.shadowColor.withValues(alpha: 0.5),
          ),
      ],
    );
  }

  Widget _buildTitle(BuildContext context, Selection selection) {
    return Expanded(
      child: Center(
        child: Padding(
          padding: const EdgeInsets.all(4.0),
          child: Text(
            item.userFacingName(),
            textAlign: TextAlign.center,
            style: Theme.of(context).textTheme.labelLarge?.copyWith(
              fontSize: 13,
              color: selection.active
                  ? selection.getColor(context).legibleForegroundColor()
                  : null,
            ),
          ),
        ),
      ),
    );
  }
}

extension SelectionColor on Selection {
  Color getColor(BuildContext context) =>
      DependencyInjector.settingsBloc.currentPalette.colorForSelection(this);

  bool get active => this != Selection.clear;
}

class TodoListItemThumbnail extends StatelessWidget {
  const TodoListItemThumbnail({
    super.key,
    required this.item,
    required this.selection,
    required this.showActiveSelection,
    this.forceFlagToBeRendered = false,
  });

  final TodoListItem item;
  final Selection selection;
  final bool showActiveSelection;
  final bool forceFlagToBeRendered;

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        _buildThumbnail(item.thumbnailUrl, selection.active),
        if (showActiveSelection && selection.active)
          _buildCheckmark(context, selection),
        if (_shouldBuildFlag) _buildFlag(context),
      ],
    );
  }

  Widget _buildThumbnail(String? thumbnailUrl, bool selected) {
    if (thumbnailUrl != null) {
      return CachedNetworkImage(
        imageUrl: thumbnailUrl,
        errorWidget: (context, _, _) => _buildMissingImage(selected),
      );
    }

    return _buildMissingImage(selected);
  }

  Widget _buildMissingImage(bool selected) {
    return SizedBox(
      height: 100,
      child: Center(
        child: Icon(
          Icons.image_not_supported_outlined,
          size: 50,
          color: selected ? Colors.blue[400] : Colors.grey,
        ),
      ),
    );
  }

  Widget _buildCheckmark(BuildContext context, Selection selection) {
    final background = selection.getColor(context);
    final foreground = background.legibleForegroundColor();
    return Positioned(
      bottom: 4,
      right: 4,
      child: Container(
        decoration: BoxDecoration(
          shape: BoxShape.circle,
          color: background.withValues(alpha: 0.9),
        ),
        child: Padding(
          padding: const EdgeInsets.all(4.0),
          child: Icon(Icons.check, color: foreground, size: 18),
        ),
      ),
    );
  }

  Widget _buildFlag(BuildContext context) {
    final iso = item.isoCode;
    if (iso == null) {
      return const SizedBox();
    }

    return FutureBuilder<GeoArea?>(
      future: DependencyInjector.areaBloc.areaByIsoCode(iso),
      builder: (context, snapshot) {
        final country = snapshot.data;
        if (country == null) {
          return const SizedBox();
        }

        return Positioned(
          top: 8,
          left: 8,
          child: Tooltip(
            message: country.name,
            preferBelow: false,
            child: AreaFlag(area: country, size: 30),
          ),
        );
      },
    );
  }

  bool get _shouldBuildFlag =>
      (forceFlagToBeRendered || item is! TodoAreaListItem) &&
      item.isoCode != null;
}
