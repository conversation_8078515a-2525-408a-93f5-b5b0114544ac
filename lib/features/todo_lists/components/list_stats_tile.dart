import 'package:flutter/material.dart';

import '../../../dependency_injection/dependency_injector.dart';
import '../../../helpers/string_extensions.dart';
import '../../../l10n/generated/app_localizations.dart';
import '../../dashboard/graphs/graph_theme.dart';
import '../../dashboard/graphs/listening_percent_graph.dart';
import '../models/todo_list.dart';
import '../models/top_one_thousand_list.dart';

class ListStatsTile extends StatelessWidget with GraphTheme {
  const ListStatsTile({super.key, required this.list});

  final TodoList list;

  @override
  Widget build(BuildContext context) {
    final bloc = DependencyInjector.todoListBloc;
    final localizations = AppLocalizations.of(context)!;
    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          Expanded(
            child: ListeningPercentGraph(
              percentage: bloc.percentComplete(list),
              labelStream: bloc.selectionsByList(list).map<String>((event) {
                var beenCount = 0;
                for (final selection in event.values) {
                  if (selection.shouldCountTowardBeen) {
                    beenCount++;
                  }
                }
                return '$beenCount ${localizations.places}';
              }),
            ),
          ),
          buildVerticalDivider(),
          Expanded(
            child: ListeningPercentGraph(
              percentage: bloc.goalCompleted(list),
              label: localizations.goal,
            ),
          ),
          buildVerticalDivider(),
          Expanded(
            child: ListeningPercentGraph(
              percentage: bloc.percentTopSelected(list),
              label:
                  '${localizations.top.capitalized} ${list is TopOneThousandList ? 100 : 10}',
            ),
          ),
        ],
      ),
    );
  }
}
