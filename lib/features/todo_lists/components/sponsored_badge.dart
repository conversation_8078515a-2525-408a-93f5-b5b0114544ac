import 'package:flutter/material.dart';

import '../../../l10n/generated/app_localizations.dart';

class SponsoredBadge extends StatelessWidget {
  const SponsoredBadge({
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: const Color(0xFF1BA24C),
        borderRadius: BorderRadius.circular(4),
      ),
      padding: const EdgeInsets.all(8),
      child: Text(
        AppLocalizations.of(context)!.sponsored,
        style: const TextStyle(
            color: Colors.white, fontSize: 10, fontWeight: FontWeight.w700),
      ),
    );
  }
}
