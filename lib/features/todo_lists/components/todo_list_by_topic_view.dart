import 'dart:async';

import 'package:flutter/material.dart';
import 'package:flutter_sticky_header/flutter_sticky_header.dart';
import 'package:sliver_tools/sliver_tools.dart';

import '../../../dependency_injection/dependency_injector.dart';
import '../../../generic_widgets/navigation_extensions.dart';
import '../../../generic_widgets/platform_aware/platform_sliver_app_bar.dart';
import '../../../generic_widgets/pushable_tile.dart';
import '../../../generic_widgets/responsive_padding.dart';
import '../../../generic_widgets/sliver_bottom_safe_area.dart';
import '../../../generic_widgets/sliver_sticky_search_bar.dart';
import '../../../generic_widgets/spinner.dart';
import '../../../l10n/generated/app_localizations.dart';
import '../../ads/ad_manager.dart';
import '../models/todo_list.dart';
import '../models/todo_list_view_model.dart';
import '../todo_list_bloc.dart';
import '../todo_list_category_screen.dart';
import '../todo_list_details_screen.dart';
import 'collapsible_header.dart';
import 'todo_list_tile.dart';

class ListPicker extends StatelessWidget {
  const ListPicker({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: CustomScrollView(
        slivers: [
          PlatformSliverAppBar(title: AppLocalizations.of(context)!.list),
          TodoListsByTopicView(onTapped: Navigator.of(context).pop),
          const SliverBottomSafeArea(),
        ],
      ),
    );
  }
}

class TodoListsByTopicView extends StatefulWidget {
  const TodoListsByTopicView({super.key, this.onTapped});

  final void Function(TodoList list)? onTapped;

  @override
  State<TodoListsByTopicView> createState() => _TodoListsByTopicViewState();
}

class _TodoListsByTopicViewState extends State<TodoListsByTopicView> {
  List<TodoListViewModel>? _viewModels;
  List<TodoList>? _searchResults;

  Object? _error;
  StreamSubscription? _listSubscription;
  StreamSubscription? _languageSubscription;
  bool _showAds = false;
  StreamSubscription? _adsSubscription;

  @override
  void initState() {
    super.initState();
    _adsSubscription ??= DependencyInjector.iapBloc.status.listen((value) {
      setState(() {
        _showAds = !value.hasRemovedAds;
      });
    });

    _listSubscription ??= DependencyInjector.todoListBloc.listsByTopic.listen(
      _onListsReceived,
      onError: (e) => setState(() {
        _viewModels = null;
        _error = e;
      }),
    );

    // Listen for language changes
    _languageSubscription ??= DependencyInjector.settingsBloc.language.listen((
      _,
    ) {
      // Reset state and force refresh when language changes
      setState(() {
        _viewModels = null;
        _searchResults = null;
        _error = null;
      });

      // Re-subscribe to the listsByTopic stream to get fresh data
      _listSubscription?.cancel();
      _listSubscription = DependencyInjector.todoListBloc.listsByTopic.listen(
        _onListsReceived,
        onError: (e) => setState(() {
          _viewModels = null;
          _error = e;
        }),
      );
    });
  }

  @override
  Widget build(BuildContext context) {
    if (_error != null) {
      return _buildError();
    } else if (_viewModels == null) {
      return _buildLoading();
    } else {
      return MultiSliver(
        children: [
          _buildSearchBar(context),
          if (_searchResults != null)
            _buildListsWithoutStickyHeader(_searchResults!)
          else
            StreamBuilder<TopOneThousandViewModel>(
              stream: DependencyInjector.todoListBloc.topOneThousand,
              builder: (context, snapshot) {
                if (snapshot.hasData) {
                  return _buildListsWithoutStickyHeader(snapshot.data!.lists);
                }

                return const SliverToBoxAdapter(child: SizedBox());
              },
            ),
          for (final viewModel in _viewModels!) _buildCategory(viewModel),
        ],
      );
    }
  }

  Widget _buildLoading() {
    return const SliverFillRemaining(child: Center(child: Spinner()));
  }

  Widget _buildError() {
    return SliverFillRemaining(child: Center(child: Text(_error!.toString())));
  }

  Widget _buildSearchBar(BuildContext context) {
    return FutureBuilder<bool>(
      future: DependencyInjector.featureFlags.enableSearchInTodoListScreen(),
      initialData: false,
      builder: (context, snapshot) {
        if (snapshot.data != true) {
          return const SliverToBoxAdapter(child: SizedBox());
        }

        return SliverStickySearchBar<TodoList>(
          items: _viewModels!
              .expand((element) => element.lists)
              .toSet()
              .toList(),
          onQuery: (List<TodoList>? value) {
            setState(() {
              _searchResults = value;
            });
          },
        );
      },
    );
  }

  void _onListsReceived(List<TodoListViewModel> viewModels) {
    setState(() {
      _error = null;
      _viewModels = [
        for (final viewModel in viewModels)
          if (viewModel is TodoListTileCategoryViewModel &&
              viewModel.mode == NestedTodoTileRenderMode.dropdown)
            CollapsibleListCategoryViewModel(
              name: viewModel.tag.name,
              lists: viewModel.lists,
            )
          else
            viewModel,
      ];
    });
  }

  Widget _buildCategory(TodoListViewModel viewModel) {
    return switch (viewModel) {
      final TodoListTileFlatListViewModel flat =>
        _buildListsWithoutStickyHeader(flat.lists),
      final CollapsibleListCategoryViewModel collaspible =>
        _buildCollapsibleCategory(collaspible),
      final TodoListTileCategoryViewModel category => _buildCategoryTile(
        category,
      ),

      // TODO might not need this
      final TopOneThousandViewModel topOneThousand =>
        _buildListsWithoutStickyHeader(topOneThousand.lists),
    };
  }

  Widget _buildCategoryTile(TodoListTileCategoryViewModel category) {
    return SliverToBoxAdapter(
      child: PushableTile(
        title: category.tag.name,
        imageLocation: NetworkImageLocation(category.lists.last.thumbnailUrl),
        onTapped: () => Navigator.of(context).pushMaterialRoute(
          name: '/lists/category/${category.tag.id}',
          builder: (context) => TodoListCategoryScreen(viewModel: category),
        ),
      ),
    );
  }

  Widget _buildCollapsibleCategory(CollapsibleListCategoryViewModel viewModel) {
    return SliverStickyHeader(
      header: CollapsibleHeader(viewModel: viewModel),
      sliver: ResponsiveSliverPadding(
        context: context,
        fillToEdgeOnPhone: false,
        sliver: SliverAnimatedList(
          key: viewModel.listKey,
          initialItemCount: viewModel.collapsed ? 0 : viewModel.lists.length,
          itemBuilder:
              (BuildContext context, int index, Animation<double> animation) {
                final list = viewModel.lists[index];

                return SizeTransition(
                  sizeFactor: animation,
                  child: _buildListTile(list, context),
                );
              },
        ),
      ),
    );
  }

  Widget _buildListsWithoutStickyHeader(List<TodoList> lists) {
    return ResponsiveSliverPadding(
      context: context,
      fillToEdgeOnPhone: true,
      sliver: SliverList.builder(
        itemCount: lists.length,
        itemBuilder: (context, index) {
          final list = lists[index];
          if (index != 0 && index % 10 == 0) {
            return Column(
              children: [
                _buildListTile(list, context),
                if (_showAds) AdView(location: AdLocation.listScreen),
              ],
            );
          }

          return _buildListTile(list, context);
        },
      ),
    );
  }

  Widget _buildListTile(TodoList list, BuildContext context) {
    return TodoListTile(
      list: list,
      onTapped: () {
        if (widget.onTapped != null) {
          widget.onTapped!(list);
          return;
        }
        Navigator.of(context).pushMaterialRoute(
          name: TodoListDetailsScreen.routeName(list),
          builder: (_) => TodoListDetailsScreen(list: list),
          maintainState: false,
        );
      },
    );
  }

  @override
  void dispose() {
    _listSubscription?.cancel();
    _languageSubscription?.cancel();
    _adsSubscription?.cancel();
    super.dispose();
  }
}
