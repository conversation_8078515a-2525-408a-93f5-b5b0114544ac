import 'package:flutter/material.dart';

import '../../../generic_widgets/separated_tile.dart';
import '../models/todo_list_view_model.dart';

class CollapsibleHeader extends StatefulWidget {
  const CollapsibleHeader({
    super.key,
    required this.viewModel,
  });

  final CollapsibleListCategoryViewModel viewModel;

  @override
  State<CollapsibleHeader> createState() => _CollapsibleHeaderState();
}

class _CollapsibleHeaderState extends State<CollapsibleHeader> {
  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: _toggle,
      child: SeparatedTile(
        child: AnimatedContainer(
          duration: const Duration(milliseconds: 400),
          decoration: _buildDecoration(context),
          padding: const EdgeInsets.all(16),
          child: _buildContent(context),
        ),
      ),
    );
  }

  Widget _buildContent(BuildContext context) {
    return Row(
      children: [
        Expanded(
          child: Text(
            widget.viewModel.name,
            style: Theme.of(context)
                .textTheme
                .titleLarge
                ?.copyWith(fontWeight: FontWeight.w700, fontSize: 15),
          ),
        ),
        ExpandIcon(
          expandedColor: Theme.of(context).primaryColor,
          isExpanded: !widget.viewModel.collapsed,
          onPressed: (_) => _toggle(),
        )
      ],
    );
  }

  Decoration _buildDecoration(BuildContext context) {
    return BoxDecoration(
        color: Theme.of(context).secondaryHeaderColor,
        boxShadow: !widget.viewModel.collapsed
            ? [
                BoxShadow(
                  offset: const Offset(0, 4),
                  blurRadius: 8,
                  color: Theme.of(context)
                      .unselectedWidgetColor
                      .withValues(alpha: 0.2),
                )
              ]
            : null);
  }

  void _toggle() {
    setState(() {
      widget.viewModel.toggle();
    });
  }
}
