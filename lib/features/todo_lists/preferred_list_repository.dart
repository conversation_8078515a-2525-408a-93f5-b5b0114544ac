import 'package:collection/collection.dart';

import '../../caching/storage.dart';
import '../../dependency_injection/dependency_injector.dart';
import 'models/preferred_todo_list.dart';
import 'models/todo_list.dart';

class PreferredListRepository {
  static const _key = 'com.visited.lists.preferred';

  Storage get _storage => DependencyInjector.sharedPrefsStorage;

  Future<void> save(Set<PreferredTodoList> preferences) {
    final json = <String, dynamic>{
      for (final preference in preferences)
        //<PERSON><PERSON>'s json encoder required keys to be Strings
        preference.list.id.toString(): <String, dynamic>{
          'ordinal': preference.ordinal,
          'enabled': preference.enabled,
        },
    };

    return _storage.putJson(_key, json);
  }

  Future<Set<PreferredTodoList>> retrieve(List<TodoList> lists) async {
    final saved = await _storage.getJson(_key) as Map?;
    if (saved == null) {
      return lists
          .mapIndexed(
            (ordinal, list) => PreferredTodoList(
              list: list,
              ordinal: ordinal,
            ),
          )
          .toSet();
    }

    var unsavedListOrdinal = saved.length;

    return lists.map((list) {
      final preference = saved[list.id.toString()];
      if (preference == null) {
        return PreferredTodoList(list: list, ordinal: unsavedListOrdinal++);
      }

      return PreferredTodoList(
        list: list,
        ordinal: preference['ordinal'],
        enabled: preference['enabled'],
      );
    }).toSet();
  }

  Future<void> reset() => _storage.delete(_key);
}
