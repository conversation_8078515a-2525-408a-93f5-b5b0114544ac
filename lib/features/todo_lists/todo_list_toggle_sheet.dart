import 'package:flutter/material.dart';

import '../../dependency_injection/dependency_injector.dart';
import '../../generic_widgets/platform_aware/platform_icon_button.dart';
import '../../generic_widgets/separated_tile.dart';
import '../../l10n/generated/app_localizations.dart';
import 'models/todo_area_list.dart';
import 'models/todo_list.dart';

enum SortOrder {
  popularity,
  alphabetical,
  country,
  selected;

  String localizedName(AppLocalizations localizations) {
    switch (this) {
      case SortOrder.country:
        return localizations.country;
      case SortOrder.alphabetical:
        return localizations.alphabetical;
      case SortOrder.popularity:
        return localizations.popularity;
      case SortOrder.selected:
        return localizations.selection;
    }
  }
}

class TodoListFilterSheet extends StatelessWidget {
  const TodoListFilterSheet({
    super.key,
    required this.list,
  });

  final TodoList list;

  List<SortOrder> get selectableOrders {
    if (list is TodoAreaList) {
      return [
        SortOrder.popularity,
        SortOrder.alphabetical,
        SortOrder.selected,
      ];
    }

    return SortOrder.values;
  }

  @override
  Widget build(BuildContext context) {
    final sortOrder = DependencyInjector.todoListBloc.currentSortOrder;

    return Stack(
      children: [
        SafeArea(
          child: Padding(
            padding: const EdgeInsets.symmetric(vertical: 20.0),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                _buildTitle(context),
                const SizedBox(height: 20),
                for (final order in selectableOrders)
                  _buildSortTile(order, context, sortOrder),
              ],
            ),
          ),
        ),
        _buildCloseButton(context)
      ],
    );
  }

  Widget _buildCloseButton(BuildContext context) {
    return PlatformIconButton(
      onTapped: Navigator.of(context).pop,
      icon: Icons.close,
      size: 20,
    );
  }

  Widget _buildSortTile(
    SortOrder order,
    BuildContext context,
    SortOrder sortOrder,
  ) {
    return SeparatedTile(
      child: ListTile(
        title: Text(
          order.localizedName(AppLocalizations.of(context)!),
          style: sortOrder == order
              ? const TextStyle(fontWeight: FontWeight.bold)
              : null,
        ),
        focusColor: Theme.of(context).colorScheme.primary,
        selected: sortOrder == order,
        trailing: sortOrder == order ? const Icon(Icons.check) : null,
        onTap: () {
          DependencyInjector.todoListBloc.setSortOrder(order);
          Navigator.of(context).pop();
        },
      ),
    );
  }

  Widget _buildTitle(BuildContext context) {
    return Text(
      AppLocalizations.of(context)!.sortBy,
      style: const TextStyle(fontSize: 20),
    );
  }
}
