import 'package:flutter/material.dart';

import '../../dependency_injection/dependency_injector.dart';
import '../../generic_widgets/platform_aware/platform_icon_button.dart';
import '../../generic_widgets/platform_aware/platform_sliver_app_bar.dart';
import '../../generic_widgets/responsive_padding.dart';
import '../../generic_widgets/separated_tile.dart';
import '../../generic_widgets/sliver_bottom_safe_area.dart';
import '../../generic_widgets/spinner.dart';
import '../../l10n/generated/app_localizations.dart';
import 'models/preferred_todo_list.dart';
import 'todo_list_bloc.dart';

class TodoListCustomizeScreen extends StatelessWidget {
  const TodoListCustomizeScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final bloc = DependencyInjector.todoListBloc;
    final localizations = AppLocalizations.of(context)!;

    return Scaffold(
      body: Scrollbar(
        child: CustomScrollView(
          slivers: [
            _buildAppBar(localizations, bloc),
            _buildLists(bloc),
            const SliverBottomSafeArea(),
          ],
        ),
      ),
    );
  }

  Widget _buildLists(TodoListBloc bloc) {
    return StreamBuilder<List<PreferredTodoList>>(
      stream: bloc.listPreferences,
      builder: (context, snapshot) {
        final lists = snapshot.data;
        if (lists == null) {
          return const SliverFillRemaining(
            child: Center(
              child: Spinner(),
            ),
          );
        }

        return ResponsiveSliverPadding(
          context: context,
          sliver: SliverReorderableList(
            itemCount: lists.length,
            onReorder: bloc.reorderListPreference,
            itemBuilder: (context, index) {
              final preference = lists[index];
              return TodoListPreferenceTile(
                key: ValueKey(preference),
                preference: preference,
                bloc: bloc,
              );
            },
          ),
        );
      },
    );
  }

  Widget _buildAppBar(AppLocalizations localizations, TodoListBloc bloc) {
    return PlatformSliverAppBar(
      title: localizations.customize,
      action: PlatformIconButton(
        svgAsset: 'assets/images/clear_icon.svg',
        semanticLabel: localizations.clear,
        onTapped: bloc.resetPreferredLists,
      ),
    );
  }
}

class TodoListPreferenceTile extends StatelessWidget {
  const TodoListPreferenceTile({
    super.key,
    required this.preference,
    required this.bloc,
  });

  final PreferredTodoList preference;
  final TodoListBloc bloc;

  @override
  Widget build(BuildContext context) {
    return Material(
      color: Colors.transparent,
      child: SeparatedTile(
        child: ListTile(
          onTap: () => bloc.toggleVisibility(preference),
          leading: Checkbox(
            checkColor: Colors.white,
            activeColor: Theme.of(context).colorScheme.primary,
            value: preference.enabled,
            onChanged: (_) => bloc.toggleVisibility(preference),
          ),
          title: Text(
            preference.list.name,
          ),
          trailing: ReorderableDragStartListener(
            index: preference.ordinal,
            child: const Icon(Icons.menu),
          ),
        ),
      ),
    );
  }
}
