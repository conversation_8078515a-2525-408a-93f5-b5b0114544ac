import 'dart:async';

import 'package:cached_network_image/cached_network_image.dart';
import 'package:collection/collection.dart';
import 'package:flutter/material.dart' hide SearchBar;
import 'package:flutter_svg/svg.dart';
import 'package:sliver_tools/sliver_tools.dart';

import '../../dependency_injection/dependency_injector.dart';
import '../../generic_widgets/filter_button.dart';
import '../../generic_widgets/missing_item_sliver_tile.dart';
import '../../generic_widgets/navigation_extensions.dart';
import '../../generic_widgets/pinned_selection_toggle.dart';
import '../../generic_widgets/platform_aware/platform_arrow.dart';
import '../../generic_widgets/platform_aware/platform_icon_button.dart';
import '../../generic_widgets/platform_aware/platform_sliver_app_bar.dart';
import '../../generic_widgets/responsive_padding.dart';
import '../../generic_widgets/search_bar.dart';
import '../../generic_widgets/separated_tile.dart';
import '../../generic_widgets/sliver_bottom_safe_area.dart';
import '../../generic_widgets/sliver_sticky_bar.dart';
import '../../generic_widgets/spinner.dart';
import '../../generic_widgets/tutorial_dialog.dart';
import '../../helpers/margin.dart';
import '../../helpers/url_dispatcher.dart';
import '../../l10n/generated/app_localizations.dart';
import '../../models/selection.dart';
import '../ads/ad_manager.dart';
import '../books/book_link_tile.dart';
import '../map/sliver_annotated_map.dart';
import '../sharing/content/list_sharable_content.dart';
import '../sharing/sharing_service.dart';
import 'components/list_stats_tile.dart';
import 'components/sponsored_badge.dart';
import 'components/todo_list_item_tile.dart';
import 'models/sponsor.dart';
import 'models/todo_area_list.dart';
import 'models/todo_list.dart';
import 'models/todo_list_item.dart';
import 'models/todo_list_type.dart';
import 'models/top_one_thousand_list.dart';
import 'todo_list_map_screen.dart';
import 'todo_list_toggle_sheet.dart';

class TodoListDetailsScreen extends StatefulWidget {
  static String routeName(TodoList list) => 'lists/${list.id}';

  const TodoListDetailsScreen({
    super.key,
    required this.list,
    this.selections = const [Selection.been, Selection.want],
    this.type,
    this.showStats = true,
    this.showMap = true,
    this.showSharing = true,
    this.onSelectedOverride,
    this.selectionStreamOverride,
  });

  final TodoList list;
  final List<Selection> selections;
  final TodoListType? type;
  final bool showStats;
  final bool showMap;
  final bool showSharing;
  final void Function(TodoListItem item, Selection selection)?
  onSelectedOverride;
  final Stream<Selection> Function(TodoListItem item)? selectionStreamOverride;

  @override
  State createState() => _TodoListDetailsScreenState();
}

class _TodoListDetailsScreenState extends State<TodoListDetailsScreen> {
  List<TodoListItem>? items;
  List<TodoListItem>? searchResults;
  late Selection selection;
  var showAds = true;
  final _statsRenderKey = GlobalKey();
  var loading = true;
  StreamSubscription? _languageSubscription;
  StreamSubscription? _adsSubscription;

  @override
  void initState() {
    super.initState();
    selection = widget.selections.first;
    WidgetsBinding.instance.addPostFrameCallback((_) => _fetchData());

    _adsSubscription ??= DependencyInjector.iapBloc.status.listen((value) {
      setState(() {
        showAds = !value.hasRemovedAds;
      });
    });

    _languageSubscription ??= DependencyInjector.settingsBloc.language.listen((
      _,
    ) {
      setState(() {
        loading = true;
      });
      items = null;
      searchResults = null;
      _fetchData();
    });
  }

  Stream<List<TodoListItem>> get _currentItems {
    final bloc = DependencyInjector.todoListBloc;
    return bloc.sortOrder.asyncMap((order) async {
      final items = searchResults ?? this.items;
      if (items == null) {
        return [];
      }

      switch (order) {
        case SortOrder.popularity:
          return items.sorted((a, b) => b.popularity.compareTo(a.popularity));

        case SortOrder.selected:
          return items.sorted((a, b) {
            final aSelection = bloc.getSelection(widget.list, a);
            final bSelection = bloc.getSelection(widget.list, b);
            return bSelection.compareTo(aSelection);
          });

        case SortOrder.alphabetical:
          final alphabetical = items.sorted(
            (a, b) => a.name.trim().toLowerCase().compareTo(
              b.name.trim().toLowerCase(),
            ),
          );
          return alphabetical;

        case SortOrder.country:
          final bloc = DependencyInjector.areaBloc;
          final allAreas = {
            for (final area in (await bloc.allAreas())) area.isoCode: area,
          };
          return items.sorted((a, b) {
            final aIso = a.isoCode;
            final bIso = b.isoCode;
            if (aIso == null || bIso == null) {
              return 1;
            }

            final areaA = allAreas[aIso];
            final areaB = allAreas[bIso];

            if (areaA == null || areaB == null) {
              return 1;
            }

            return areaA.name.compareTo(areaB.name);
          });
      }
    });
  }

  void _fetchData() async {
    if (widget.list is TopOneThousandList) {
      setState(() {
        this.items = (widget.list as TopOneThousandList).items
            .map((e) => e.$1)
            .toList();
        loading = false;
      });
      return;
    }

    final items = await DependencyInjector.todoListBloc.fetchItems(
      widget.list,
      filter: widget.type,
    );

    if (!mounted) {
      return;
    }

    setState(() {
      this.items = items;
      loading = false;
    });

    final tutorial = TutorialDialog(
      items: [
        TutorialItem(
          label: AppLocalizations.of(context)!.tutorialItems,
          icon: SvgPicture.asset(
            'assets/images/swipe.svg',
            width: 100,
            colorFilter: ColorFilter.mode(
              Theme.of(context).colorScheme.primary,
              BlendMode.srcIn,
            ),
          ),
        ),
      ],
    );

    tutorial.showOnlyOnce(
      context: context,
      hasShownKey: 'com.visited.hasShownTodoListTutorial',
    );
  }

  @override
  Widget build(BuildContext context) {
    final book = DependencyInjector.todoListBloc.getBook(widget.list);

    final shouldBuildMap =
        widget.showMap &&
        items?.firstWhereOrNull(
              (item) =>
                  item.coordinate.latitude != 0 &&
                  item.coordinate.longitude != 0,
            ) !=
            null;

    return Scaffold(
      body: Scrollbar(
        child: CustomScrollView(
          slivers: [
            _buildAppBar(),
            if (widget.showStats) _buildStatsSection(context),
            if (book != null)
              SliverToBoxAdapter(child: BookLinkTile(book: book)),
            if (shouldBuildMap) _buildMap(),
            if (items != null) _buildSearchAndSortBar(context),
            if (loading)
              const SliverFillRemaining(child: Center(child: Spinner())),
            if (items != null) ...[
              if (widget.selections.length > 1)
                PinnedSelectionToggle(
                  active: selection,
                  selections: widget.selections,
                  onChanged: (value) {
                    setState(() {
                      selection = value;
                    });
                  },
                ),
              _buildItems(context),
            ],
            MissingItemSliverTile(
              title: AppLocalizations.of(context)!.missingListItem,
              emailTitle: AppLocalizations.of(
                context,
              )!.missingListItemEmailTitle(widget.list.name),
            ),
            const SliverBottomSafeArea(),
          ],
        ),
      ),
    );
  }

  Widget _buildAppBar() {
    return StreamBuilder<String?>(
      stream: _listenToLocalizedListName(),
      builder: (context, snapshot) {
        return PlatformSliverAppBar(
          title: snapshot.data ?? widget.list.name,
          action: _buildShareButton(context),
        );
      },
    );
  }

  Stream<String?> _listenToLocalizedListName() {
    return DependencyInjector.settingsBloc.language.asyncMap((_) async {
      if (widget.list is TodoAreaList) {
        final localizedArea = await DependencyInjector.areaBloc.areaByIsoCode(
          (widget.list as TodoAreaList).area.isoCode,
        );
        return localizedArea?.name ?? widget.list.name;
      }

      final lists = await DependencyInjector.todoListBloc.fetchLists();
      return lists.firstWhereOrNull((list) => list.id == widget.list.id)?.name;
    });
  }

  Widget _buildMap() {
    return SliverAnnotatedMap(
      coordinates: [
        for (final item in items ?? [])
          if (item.coordinate.latitude != 0 && item.coordinate.longitude != 0)
            item.coordinate,
      ],
      onTapped: () => Navigator.of(context).pushMaterialRoute(
        name: '${widget.list.id}/map',
        builder: (context) => TodoListMapScreen(
          list: widget.list,
          items: items!,
          availableSelections: widget.selections,
        ),
      ),
    );
  }

  Widget _buildSearchAndSortBar(BuildContext context) {
    final items = this.items;
    if (items == null) {
      return const SizedBox();
    }

    return SliverStickyBar(
      child: Row(
        children: [
          const SizedBox(width: Margin.small),
          Expanded(
            child: SearchBar<TodoListItem>(
              items: items,
              onQuery: (results) => setState(() {
                searchResults = results;
              }),
            ),
          ),
          FilterButton(onTapped: () => _showFilterSheet(context)),
        ],
      ),
    );
  }

  void _showFilterSheet(BuildContext context) {
    showModalBottomSheet(
      isDismissible: false,
      backgroundColor: Theme.of(context).cardColor,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(16)),
      ),
      context: context,
      builder: (_) => TodoListFilterSheet(list: widget.list),
    );
  }

  Widget? _buildShareButton(BuildContext context) {
    if (!widget.showSharing) {
      return null;
    }

    // Only share in English for now
    if (Localizations.localeOf(
          context,
        ).languageCode.toLowerCase().contains('en') ==
        false) {
      return null;
    }

    return StreamBuilder<bool>(
      stream: DependencyInjector.todoListBloc.canShare(widget.list),
      initialData: false,
      builder: (context, snapshot) {
        if (snapshot.data != true) {
          return const SizedBox();
        }

        return PlatformIconButton(
          icon: Icons.adaptive.share,
          onTapped: _onShareTapped,
        );
      },
    );
  }

  Widget _buildStatsSection(BuildContext context) {
    return SliverPadding(
      padding: const EdgeInsets.symmetric(vertical: 16.0),
      sliver: SliverList(
        delegate: SliverChildListDelegate([
          Hero(
            tag: widget.list.id,
            child: CachedNetworkImage(
              width: double.infinity,
              fit: BoxFit.fitWidth,
              imageUrl: widget.list.thumbnailUrl,
            ),
          ),
          if (widget.list.isSponsored)
            _buildSponsor(context, widget.list.sponsor!),
          ListStatsTile(key: _statsRenderKey, list: widget.list),
        ]),
      ),
    );
  }

  Widget _buildItems(BuildContext context) {
    return StreamBuilder<List<TodoListItem>>(
      stream: _currentItems,
      builder: (context, snapshot) {
        final items = snapshot.data;
        if (items == null) {
          return SliverFillRemaining(
            child: Center(
              child: Text(AppLocalizations.of(context)!.noListsError),
            ),
          );
        }

        return ResponsiveSliverPadding(
          context: context,
          fillToEdgeOnPhone: true,
          sliver: showAds
              ? _buildItemsWithAds(context, items)
              : SliverGrid.builder(
                  gridDelegate: _buildItemGridDelegate(),
                  itemCount: items.length,
                  itemBuilder: (context, index) {
                    final item = items[index];
                    return _buildListItem(item);
                  },
                ),
        );
      },
    );
  }

  Widget _buildItemsWithAds(BuildContext context, List<TodoListItem> items) {
    final itemBlocks = items.chunked(20);
    return MultiSliver(
      children: [
        for (final chunk in itemBlocks) ...[
          SliverGrid.builder(
            gridDelegate: _buildItemGridDelegate(),
            itemCount: chunk.length,
            itemBuilder: (context, index) {
              final item = chunk[index];
              return _buildListItem(item);
            },
          ),
          if (itemBlocks.last != chunk)
            SliverToBoxAdapter(
              key: ValueKey(chunk),
              child: AdView(location: AdLocation.listScreen),
            ),
        ],
      ],
    );
  }

  Widget _buildListItem(TodoListItem item) {
    return TodoListItemTile(
      list: widget.list,
      item: item,
      forceFlagToBeRendered: widget.list is TopOneThousandList,
      activeSelection: _buildSelectionStream(item),
      onTapped: widget.onSelectedOverride != null
          ? () => widget.onSelectedOverride?.call(item, selection)
          : () => DependencyInjector.todoListBloc.select(
              widget.list,
              item,
              selection,
            ),
    );
  }

  SliverGridDelegate _buildItemGridDelegate() {
    return const SliverGridDelegateWithMaxCrossAxisExtent(
      maxCrossAxisExtent: 260,
      crossAxisSpacing: 8,
      mainAxisSpacing: 8,
      childAspectRatio: 1.2,
    );
  }

  Stream<Selection> _buildSelectionStream(TodoListItem item) {
    if (widget.selectionStreamOverride != null) {
      return widget.selectionStreamOverride!.call(item);
    }

    if (widget.list is TopOneThousandList) {
      return DependencyInjector.todoListBloc.topOneThousand.map((event) {
        final record = event.items.firstWhereOrNull((e) => e.$1 == item);
        return record?.$2 ?? Selection.clear;
      });
    }

    return DependencyInjector.todoListBloc.selection(widget.list, item);
  }

  Widget _buildSponsor(BuildContext context, Sponsor sponsor) {
    return SeparatedTile(
      child: ListTile(
        onTap: () => UrlDispatcher.launch(sponsor.link),
        leading: const SponsoredBadge(),
        title: Text(sponsor.name),
        trailing: const PlatformArrow(),
      ),
    );
  }

  void _onShareTapped() async {
    final sharer = SharingService(
      context,
      content: TodoListSharableContent(widget.list),
    );
    sharer.export();
  }

  @override
  void dispose() {
    _languageSubscription?.cancel();
    _adsSubscription?.cancel();
    super.dispose();
  }
}

extension ListChunker<E> on List<E> {
  List<List<E>> chunked(int chunkSize) {
    return List.generate(
      (length / chunkSize).ceil(),
      (index) => sublist(
        index * chunkSize,
        (index + 1) * chunkSize > length ? length : (index + 1) * chunkSize,
      ),
    );
  }
}
