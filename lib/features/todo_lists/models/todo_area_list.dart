import '../../../models/geo_area.dart';
import 'todo_list.dart';

class TodoAreaList extends TodoList {
  TodoAreaList.fromJson(super.json, this.area) : super.fromJson();

  final GeoArea area;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      super == other &&
          other is TodoAreaList &&
          runtimeType == other.runtimeType &&
          area == other.area;

  @override
  int get hashCode => super.hashCode ^ area.hashCode;
}
