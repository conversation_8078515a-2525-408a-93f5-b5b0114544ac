import 'package:flutter/material.dart';

import '../../../models/selection.dart';
import '../components/todo_list_tile.dart';
import '../todo_list_bloc.dart';
import 'todo_list.dart';
import 'todo_list_item.dart';
import 'todo_list_tag.dart';
import 'top_one_thousand_list.dart';

sealed class TodoListViewModel {
  List<TodoList> get lists;
}

class TopOneThousandViewModel implements TodoListViewModel {
  const TopOneThousandViewModel({
    required this.items,
  });

  final List<(TodoAreaListItem, Selection?)> items;

  @override
  List<TodoList> get lists => [
        TopOneThousandList(
          name: 'Top 1,000',
          thumbnailUrl:
              'https://d2p8afmxmwigex.cloudfront.net/lists/2.0x/PLACES.jpg', // Todo Make dynamic
          items: items,
        )
      ];
}

class TodoListTileFlatListViewModel implements TodoListViewModel {
  const TodoListTileFlatListViewModel({
    required this.lists,
  });

  @override
  final List<TodoList> lists;
}

class TodoListTileCategoryViewModel implements TodoListViewModel {
  const TodoListTileCategoryViewModel({
    required this.tag,
    required this.lists,
    required this.mode,
  });

  @override
  final List<TodoList> lists;
  final TodoListTag tag;
  final NestedTodoTileRenderMode mode;
}

class CollapsibleListCategoryViewModel implements TodoListViewModel {
  CollapsibleListCategoryViewModel({
    required this.name,
    required this.lists,
  });

  final String name;

  @override
  final List<TodoList> lists;

  final listKey = GlobalKey<SliverAnimatedListState>();
  bool _collapsed = true;

  bool get collapsed => _collapsed;
  bool get hideStickyHeader =>
      name == TodoListBloc.kSponsoredTag || name == TodoListBloc.kNoHeadersTag;

  void toggle() {
    _collapsed ? _addAllItems() : _removeAllItems();
    _collapsed = !_collapsed;
  }

  void _addAllItems() {
    for (var i = 0; i < lists.length; i++) {
      listKey.currentState?.insertItem(i);
    }
  }

  void _removeAllItems() {
    for (var i = lists.length - 1; i >= 0; i--) {
      listKey.currentState?.removeItem(
        i,
        (context, anim) => _removeItem(context, lists[i], anim),
      );
    }
  }

  Widget _removeItem(
      BuildContext context, TodoList list, Animation<double> anim) {
    return SizeTransition(
      sizeFactor: anim,
      child: TodoListTile(list: list),
    );
  }
}
