import '../../../models/selection.dart';
import 'todo_list.dart';
import 'todo_list_item.dart';
import 'todo_list_type.dart';

class TopOneThousandList extends TodoList {
  TopOneThousandList({
    required super.name,
    required super.thumbnailUrl,
    required this.items,
  }) : super(
          id: 0,
          itemCount: items.length,
          type: TodoListType.ignore,
        );

  final List<(TodoAreaListItem, Selection?)> items;

  double get percentageComplete {
    final selected = items
        .where((item) =>
            item.$2 == Selection.been ||
            item.$2 == Selection.live ||
            item.$2 == Selection.lived)
        .length;
    return selected / items.length;
  }
}
