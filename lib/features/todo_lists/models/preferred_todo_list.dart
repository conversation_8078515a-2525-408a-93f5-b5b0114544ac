import 'todo_list.dart';

class PreferredTodoList implements Comparable<PreferredTodoList> {
  final TodoList list;
  final int ordinal;
  final bool enabled;

  const PreferredTodoList({
    required this.list,
    required this.ordinal,
    this.enabled = true,
  });

  PreferredTodoList copyWith({
    int? ordinal,
    bool? enabled,
  }) =>
      PreferredTodoList(
        list: list,
        ordinal: ordinal ?? this.ordinal,
        enabled: enabled ?? this.enabled,
      );

  @override
  int compareTo(PreferredTodoList other) => ordinal.compareTo(other.ordinal);

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is PreferredTodoList &&
          runtimeType == other.runtimeType &&
          list == other.list;

  @override
  int get hashCode => list.hashCode;

  @override
  String toString() {
    return 'PreferredTodoList{list: $list, ordinal: $ordinal, enabled: $enabled}';
  }
}
