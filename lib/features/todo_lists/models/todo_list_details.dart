import '../../books/book_link.dart';
import 'todo_list.dart';

import 'todo_list_item.dart';

class TodoListDetails {
  const TodoListDetails({
    required this.list,
    required this.items,
    this.bookLink,
  });

  TodoListDetails.fromJson(Map json)
      : list = TodoList.fromJson(json['list']),
        items = json['items']
            .map<TodoListItem>((j) => TodoListItem.fromJson(j))
            .toList(growable: false),
        bookLink = json.containsKey('bookLink')
            ? BookLink.fromJson(json['bookLink'])
            : null;

  final TodoList list;
  final List<TodoListItem> items;
  final BookLink? bookLink;
}
