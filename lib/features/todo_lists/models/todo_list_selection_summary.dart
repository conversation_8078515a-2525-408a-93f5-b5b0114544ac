import 'todo_list.dart';

class TodoListSelectionSummary {
  final int listId;
  final int selected;

  TodoListSelectionSummary.fromJson(Map json)
      : listId = json['listId'],
        selected = json['count'];

  double percentageComplete(TodoList list) {
    assert(listId == list.id);
    return selected / list.itemCount;
  }

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is TodoListSelectionSummary &&
          runtimeType == other.runtimeType &&
          listId == other.listId &&
          selected == other.selected;

  @override
  int get hashCode => listId.hashCode ^ selected.hashCode;
}
