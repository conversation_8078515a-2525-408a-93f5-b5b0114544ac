import 'package:flutter/material.dart';
import 'package:flutter_map/flutter_map.dart';

import '../../dependency_injection/dependency_injector.dart';
import '../../generic_widgets/alert.dart';
import '../../generic_widgets/platform_aware/platform_app_bar.dart';
import '../../models/selection.dart';
import '../cities/map_annotation.dart';
import '../map/map_helper.dart';
import '../map/tiles/internal/coordinate_extension.dart';
import '../map/tiles/visited_tile_layer.dart';
import '../selection/selection_dialog.dart';
import 'components/todo_list_item_tile.dart';
import 'models/todo_list.dart';
import 'models/todo_list_item.dart';

class TodoListMapScreen extends StatefulWidget {
  const TodoListMapScreen({
    required this.list,
    required this.items,
    required this.availableSelections,
    super.key,
  });

  final TodoList list;
  final List<TodoListItem> items;
  final List<Selection> availableSelections;

  @override
  State<TodoListMapScreen> createState() => _TodoListMapScreenState();
}

class _TodoListMapScreenState extends State<TodoListMapScreen> with MapHelper {
  late final List<TodoListItem> _usableTodItems = [
    for (final item in widget.items)
      if (item.coordinate.latitude != 0 && item.coordinate.longitude != 0) item,
  ];

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: PlatformAppBar(title: widget.list.name),
      body: FlutterMap(
        options: buildMapOptions(
          Polygon(
            points: _usableTodItems
                .map((item) => item.coordinate.toLatLng())
                .toList(),
          ).boundingBox,
        ),
        children: [
          const VisitedTileLayer(active: false),
          markerLayerBuilder(
            items: _usableTodItems,
            context: context,
            markerBuilder: _buildMarker,
          ),
        ],
      ),
    );
  }

  Marker _buildMarker(BuildContext context, TodoListItem item) {
    return Marker(
      point: item.coordinate.toLatLng(),
      child: MapAnnotation(
        selection: DependencyInjector.todoListBloc.currentSelection(
          widget.list,
          item,
        ),
        onTapped: () async {
          final dialog = TodoItemSelectionDialog(
            list: widget.list,
            item: item,
            availableSelections: widget.availableSelections,
          );
          await dialog.show(context);
          setState(() {});
        },
      ),
    );
  }
}

class TodoItemSelectionDialog extends StatelessWidget with Presentable<void> {
  const TodoItemSelectionDialog({
    super.key,
    required this.list,
    required this.item,
    required this.availableSelections,
  });

  final TodoList list;
  final TodoListItem item;
  final List<Selection> availableSelections;

  @override
  Widget build(BuildContext context) {
    final bloc = DependencyInjector.todoListBloc;

    return SelectionDialog(
      title: item.userFacingName(),
      selection: bloc.selection(list, item),
      availableSelections: [...availableSelections, Selection.clear],
      centerContent: item.thumbnailUrl != null
          ? Padding(
              padding: const EdgeInsets.symmetric(vertical: 16, horizontal: 32),
              child: TodoListItemThumbnail(
                item: item,
                selection: Selection.clear,
                showActiveSelection: false,
              ),
            )
          : null,
      onChanged: (value) async => bloc.select(list, item, value),
    );
  }
}
