import 'package:flutter/material.dart';
import 'package:sliver_tools/sliver_tools.dart';

import '../../dependency_injection/dependency_injector.dart';
import '../../generic_widgets/cupertino_blurred_background.dart';
import '../../generic_widgets/platform_aware/platform_icon_button.dart';
import '../../generic_widgets/platform_aware/platform_sliver_app_bar.dart';
import '../../generic_widgets/sliver_bottom_safe_area.dart';
import '../../generic_widgets/visited_toggle_bar.dart';
import '../../helpers/string_extensions.dart';
import '../../l10n/generated/app_localizations.dart';
import '../cities/paywall_dialog.dart';
import '../in_app_purchase/iap_product.dart';
import 'components/todo_list_by_area_view.dart';
import 'components/todo_list_by_topic_view.dart';
import 'todo_list_bloc.dart';
import 'todo_list_customize_screen.dart';

class TodoListScreen extends StatelessWidget {
  static const routeName = 'lists';

  const TodoListScreen({
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Scrollbar(
        child: CustomScrollView(
          keyboardDismissBehavior: ScrollViewKeyboardDismissBehavior.onDrag,
          slivers: [
            _buildAppBar(context),
            _buildModeToggle(context),
            _buildCurrentContent(context),
            const SliverBottomSafeArea(),
          ],
        ),
      ),
    );
  }

  Widget _buildCurrentContent(BuildContext context) {
    return StreamBuilder<TodoListMode>(
      stream: DependencyInjector.todoListBloc.mode,
      initialData: TodoListMode.topics,
      builder: (context, snapshot) {
        final child = snapshot.data == TodoListMode.topics
            ? const TodoListsByTopicView()
            : const TodoListByCountryView();
        return SliverAnimatedSwitcher(
          duration: const Duration(milliseconds: 150),
          child: child,
        );
      },
    );
  }

  Widget _buildModeToggle(BuildContext context) {
    final bloc = DependencyInjector.todoListBloc;

    return StreamBuilder<TodoListMode>(
      stream: bloc.mode,
      initialData: TodoListMode.topics,
      builder: (context, snapshot) {
        final localizations = AppLocalizations.of(context)!;
        return SliverPinnedHeader(
          child: CupertinoBlurredBackground(
            child: Center(
              child: Padding(
                padding: const EdgeInsets.all(8.0),
                child: VisitedToggleBar(
                  items: [
                    ToggleItem(
                      value: TodoListMode.topics,
                      label: localizations.interests,
                    ),
                    ToggleItem(
                      value: TodoListMode.countries,
                      label: localizations.countries.capitalized,
                    ),
                  ],
                  onSelected: (mode) => _onListModeSelected(context, mode),
                  selected: snapshot.requireData,
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  PlatformSliverAppBar _buildAppBar(BuildContext context) {
    return PlatformSliverAppBar(
      title: AppLocalizations.of(context)!.lists,
      action: PlatformIconButton(
        icon: Icons.edit,
        onTapped: () => Navigator.of(context).push(
          MaterialPageRoute(
            builder: (_) => const TodoListCustomizeScreen(),
            fullscreenDialog: true,
          ),
        ),
      ),
    );
  }

  void _onListModeSelected(BuildContext context, TodoListMode mode) {
    if (mode == TodoListMode.countries &&
        !DependencyInjector.iapBloc.hasUnlockedItineraries) {
      _showItinerariesPaywall(context);
      return;
    }

    DependencyInjector.todoListBloc.setMode(mode);
  }

  void _showItinerariesPaywall(BuildContext context) async {
    final purchased = await PaywallDialog(feature: IAPFeature.unlockItineraries)
        .show(context);

    if (context.mounted && purchased == true) {
      DependencyInjector.todoListBloc.setMode(TodoListMode.countries);
    }
  }
}
