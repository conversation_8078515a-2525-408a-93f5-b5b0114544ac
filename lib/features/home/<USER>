import 'dart:async';
import 'dart:io';

import 'package:firebase_analytics/firebase_analytics.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_svg/flutter_svg.dart';

import '../../dependency_injection/dependency_injector.dart';
import '../../generic_widgets/navigation_extensions.dart';
import '../../generic_widgets/platform_aware/platform_bottom_navigation_bar.dart';
import '../../generic_widgets/status_bar_tinter.dart';
import '../../l10n/generated/app_localizations.dart';
import '../../state_safety.dart';
import '../authentication/login_background.dart';
import '../dashboard/dashboard_screen.dart';
import '../first_time/live_city_updated_picker.dart';
import '../in_app_purchase/buy_visited_pro_soft_sell.dart';
import '../login_wall/login_wall.dart';
import '../map/map_screen.dart';
import '../more/more_screen.dart';
import '../places/places_screen.dart';
import '../poster_printing/order_poster_landing_screen.dart';
import '../privacy_agreement/privacy_screen.dart';
import '../rating/rating_prompter.dart';
import 'home_tab.dart';
import 'navigate_to_tab_notification.dart';

class HomeScreen extends StatefulWidget {
  const HomeScreen({super.key, required this.analytics});

  final FirebaseAnalytics analytics;

  @override
  State createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> {
  var activeTab = HomeTab.dashboard;
  late final _tabController = CupertinoTabController(
    initialIndex: activeTab.index,
  );
  Timer? _showLoginModelTimer;

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final areaBloc = DependencyInjector.areaSelectionBloc;
      areaBloc.onLiveUpdated = _onLiveUpdate;

      areaBloc.load().then((value) {
        setState(() {});

        _showPrivacyOrRating();
        _scheduleSignInReminder();
        _showUpgradeToProDialog();
      });
    });
  }

  @override
  Widget build(BuildContext context) {
    if (DependencyInjector.areaSelectionBloc.loaded == false) {
      return const LoginBackground();
    }

    return NotificationListener<NavigateToTabNotification>(
      onNotification: (note) {
        setState(() {
          activeTab = note.tab;
          _tabController.index = note.tab.index;
        });
        return true;
      },
      child: StatusBarTinter(
        brightness: Theme.of(context).brightness == Brightness.light
            ? SystemUiOverlayStyle.dark
            : SystemUiOverlayStyle.light,
        child: Platform.isIOS
            ? _buildCupertinoTabView()
            : _buildMaterialTabBar(context),
      ),
    );
  }

  CupertinoTabScaffold _buildCupertinoTabView() {
    return CupertinoTabScaffold(
      controller: _tabController,
      tabBar: CupertinoTabBar(items: navigationItems, onTap: _onTabTapped),
      tabBuilder: (BuildContext context, int index) {
        final tab = HomeTab.values[index];

        return CupertinoTabView(
          builder: (context) {
            return CupertinoPageScaffold(child: _buildTab(tab));
          },
        );
      },
    );
  }

  Widget _buildMaterialTabBar(BuildContext context) {
    return Scaffold(
      body: _buildTabSwitcher(child: _buildTab(activeTab)),
      bottomNavigationBar: _buildTabBar(context),
    );
  }

  AnimatedSwitcher _buildTabSwitcher({required Widget child}) {
    return AnimatedSwitcher(
      transitionBuilder: (child, animation) => SlideTransition(
        position: animation.drive(
          Tween(end: Offset.zero, begin: const Offset(1.0, 0)),
        ),
        child: FadeTransition(opacity: animation, child: child),
      ),
      duration: const Duration(milliseconds: 250),
      child: child,
    );
  }

  void _onTabTapped(int index) {
    final tab = HomeTab.values[index];

    if (!DependencyInjector.sessionBloc.isAuthenticated &&
        !tab.isAllowedUnauthenticatedAccess) {
      _presentLoginWall();
      return;
    }

    widget.analytics.logScreenView(screenName: tab.toString().split('.').last);
    setState(() {
      activeTab = HomeTab.values[index];
    });
  }

  List<BottomNavigationBarItem> get navigationItems => [
    for (final tab in HomeTab.values)
      BottomNavigationBarItem(
        icon: SvgPicture.asset(
          tab.iconPath,
          width: 25,
          colorFilter: ColorFilter.mode(
            activeTab == tab
                ? Theme.of(context).primaryColor
                : Theme.of(context).disabledColor,
            BlendMode.srcIn,
          ),
        ),
        label: tab.localizedLabel(AppLocalizations.of(context)!),
      ),
  ];

  Widget _buildTabBar(BuildContext context) {
    return PlatformBottomNavigationBar(
      currentIndex: activeTab.index,
      onTap: _onTabTapped,
      items: navigationItems,
    );
  }

  Widget _buildTab(HomeTab tab) => switch (tab) {
    HomeTab.dashboard => const DashboardScreen(),
    HomeTab.map => const MapScreen(),
    HomeTab.poster => const OrderPosterLandingScreen(),
    HomeTab.lists => const ListsScreen(),
    HomeTab.more => const MoreScreen(),
  };

  void _showPrivacyOrRating() async {
    final showingPrivacy = await _handlePrivacyAgreement();
    if (!showingPrivacy) {
      _askToRateApp();
    }
  }

  Future<bool> _handlePrivacyAgreement() async {
    try {
      final agreement = await DependencyInjector.sessionBloc
          .fetchPrivacyAgreement();
      if (agreement.accepted) {
        return false;
      }

      maybeNavigator?.pushMaterialRoute(
        name: PrivacyScreen.routeName,
        builder: (_) => PrivacyScreen(agreement: agreement),
        fullscreen: true,
      );
      return true;
    } catch (e) {
      return false;
    }
  }

  void _askToRateApp() {
    const prompter = RatingPrompter();
    prompter.prompt();
  }

  void _scheduleSignInReminder() async {
    final bloc = DependencyInjector.sessionBloc;
    if (bloc.isAuthenticated) {
      return;
    }

    _showLoginModelTimer ??= Timer(
      const Duration(minutes: 1),
      _presentLoginWall,
    );
  }

  void _presentLoginWall() {
    _showLoginModelTimer?.cancel();
    _showLoginModelTimer = null;

    if (DependencyInjector.sessionBloc.isAuthenticated) {
      return;
    }

    Navigator.of(context).pushMaterialRoute(
      name: LoginWall.routeName,
      builder: (_) => const LoginWall(),
      fullscreen: true,
    );
  }

  // void _listenForHierarchyUpdates() {
  //   hierarchyListener ??=
  //       DependencyInjector.sessionBloc.hierarchyUpdatesRequired.listen(
  //     (status) async {
  //       if (_isShowingHeirachyUpdates) {
  //         return;
  //       }
  //
  //       final requiresUpdate = switch (status) {
  //         SessionStatus.requiresHierarchySelection => true,
  //         SessionStatus.requiresLiveCity => true,
  //         SessionStatus.requiresLiveCountry => true,
  //         SessionStatus.requiresLiveSubdivision => true,
  //         _ => false,
  //       };
  //
  //       if (!requiresUpdate) {
  //         return;
  //       }
  //
  //       _isShowingHeirachyUpdates = true;
  //       final nav = Navigator.of(context);
  //       try {
  //         await nav.pushMaterialRoute(
  //           name: '/hierarchyUpdate',
  //           builder: (_) => FirstTimeScreen(
  //             initialPage: status,
  //             enforceBeen: false,
  //             onFinished: nav.pop,
  //           ),
  //           fullscreen: true,
  //         );
  //       } finally {
  //         _isShowingHeirachyUpdates = false;
  //       }
  //     },
  //   );
  // }

  void _onLiveUpdate() async {
    final nav = Navigator.of(context);
    final liveCountry = await DependencyInjector.areaSelectionBloc
        .currentCountryUserLivesIn();
    if (liveCountry == null) {
      return;
    }

    nav.pushMaterialRoute(
      name: LiveCityUpdatedPicker.routeName,
      fullscreen: true,
      builder: (_) => LiveCityUpdatedPicker(area: liveCountry),
    );
  }

  void _showUpgradeToProDialog() async {
    final bloc = DependencyInjector.sessionBloc;
    if (!bloc.isAuthenticated) {
      return;
    }

    if (bloc.isFirstTimeUsingTheApp) {
      return;
    }

    final iap = DependencyInjector.iapBloc;

    if (iap.hasUnlockedPro || iap.hasUnlockedCities || iap.hasUnlockedRegions) {
      return;
    }

    await Future.delayed(const Duration(seconds: 3));

    if (!mounted) {
      return;
    }

    Navigator.of(context).pushMaterialRoute(
      builder: (_) => const BuyVisitedProSoftSell(),
      name: BuyVisitedProSoftSell.routeName,
      fullscreen: true,
    );
  }

  @override
  void dispose() {
    _showLoginModelTimer?.cancel();
    super.dispose();
  }
}
