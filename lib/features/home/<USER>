import '../../l10n/generated/app_localizations.dart';

enum HomeTab {
  dashboard(iconPath: 'assets/icons/progress.svg'),
  map(iconPath: 'assets/images/i-map-outlined.svg'),
  poster(iconPath: 'assets/icons/poster.svg'),
  lists(iconPath: 'assets/icons/lists.svg'),
  more(iconPath: 'assets/icons/more.svg');

  const HomeTab({required this.iconPath});
  final String iconPath;

  String localizedLabel(AppLocalizations localizations) {
    switch (this) {
      case HomeTab.dashboard:
        return localizations.progress;
      case HomeTab.map:
        return localizations.map;
      case HomeTab.poster:
        return localizations.orderPoster;
      case HomeTab.lists:
        return localizations.places;
      case HomeTab.more:
        return localizations.more;
    }
  }

  bool get isAllowedUnauthenticatedAccess {
    return this == HomeTab.map || this == HomeTab.more;
  }
}
