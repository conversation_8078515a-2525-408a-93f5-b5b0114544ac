import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

import '../../dependency_injection/dependency_injector.dart';
import '../../generic_widgets/platform_aware/platform_filled_button.dart';
import '../../generic_widgets/spinner.dart';
import '../../generic_widgets/status_bar_tinter.dart';
import '../../l10n/generated/app_localizations.dart';

class DeleteConfirmationDialog extends StatelessWidget {
  static const routeName = 'deleteConfirmation';

  const DeleteConfirmationDialog({super.key});

  @override
  Widget build(BuildContext context) {
    return StatusBarTinter(
      brightness: SystemUiOverlayStyle.light,
      child: Dialog(
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 24.0, vertical: 16.0),
          child: _buildContent(context),
        ),
      ),
    );
  }

  Widget _buildContent(BuildContext context) {
    final localizations = AppLocalizations.of(context)!;
    return Column(
      mainAxisSize: MainAxisSize.min,
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Text(
          '${localizations.deleteAccount}?',
          style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                fontWeight: FontWeight.w700,
                fontSize: 29,
                color: Colors.red[900],
              ),
        ),
        Padding(
          padding: const EdgeInsets.symmetric(vertical: 16.0),
          child: Image.asset('assets/images/delete_icon.png'),
        ),
        ConstrainedBox(
          constraints: const BoxConstraints(maxWidth: 500),
          child: Text(
            localizations.deleteAccountWarning,
            textAlign: TextAlign.center,
            style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                  fontSize: 17,
                  fontWeight: FontWeight.w400,
                ),
          ),
        ),
        _buildButtons(context)
      ],
    );
  }

  Widget _buildButtons(BuildContext context) {
    final localizations = AppLocalizations.of(context)!;
    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Expanded(
            child: PlatformFilledButton(
              title: localizations.cancel,
              onTapped: () => Navigator.of(context).pop(),
            ),
          ),
          const SizedBox(width: 8),
          Expanded(
            child: PlatformFilledButton(
              title: localizations.delete,
              color: Colors.red[900],
              textColor: Colors.white,
              onTapped: () {
                SpinnerDialog.showDuringLongProcess(
                  context,
                  job: () => DependencyInjector.sessionBloc.deleteAccount(),
                );
              },
            ),
          )
        ],
      ),
    );
  }
}
