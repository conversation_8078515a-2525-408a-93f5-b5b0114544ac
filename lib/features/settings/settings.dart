import 'package:collection/collection.dart';
import 'package:flutter_map/flutter_map.dart';

import '../../bridges/environment_fetcher.dart';
import '../localization/supported_language.dart';

class Settings {
  final bool onlySovereign;
  final bool countUkCountries;
  final bool showLegend;
  final bool showLivedPin;
  final bool useCustomPalette;
  final bool showRegions;
  final Crs projection;
  final SupportedLanguage? language;
  final bool showSelectionDialogOnPickerLists;

  const Settings({
    this.onlySovereign = false,
    this.countUkCountries = false,
    this.showLegend = true,
    this.showLivedPin = true,
    this.useCustomPalette = false,
    this.showRegions = true,
    this.projection = const Epsg3857(),
    this.language,
    this.showSelectionDialogOnPickerLists = true,
  });

  Settings.fromJson(Map json)
      : onlySovereign = json['onlySovereign'] ?? false,
        countUkCountries = json['countUkCountries'] ?? false,
        showLegend = json['showLegend'] ?? true,
        showLivedPin = json['showLivedPin'] ?? true,
        useCustomPalette = json['useMyColours'] ?? false,
        showRegions = json['showRegions'] ?? true,
        projection = CrsBuilder.fromCode(json['projection']),
        showSelectionDialogOnPickerLists =
            json['showSelectionDialogOnPickerLists'] ?? true,
        language = (json['language'] != null
            ? SupportedLanguage.values.firstWhereOrNull(
                (element) => element.localizationKey == json['language'])
            : SupportedLanguage.fromLocale(EnvironmentFetcher.systemLocale)) ?? SupportedLanguage.americanEnglish;

  Settings copyWith({
    bool? onlySovereign,
    bool? countUkCountries,
    bool? showLegend,
    bool? showLivedPin,
    bool? useCustomPalette,
    bool? clusterPins,
    bool? showRegions,
    Crs? projection,
    SupportedLanguage? language,
    bool? showSelectionDialogOnPickerLists,
  }) =>
      Settings(
        onlySovereign: onlySovereign ?? this.onlySovereign,
        countUkCountries: countUkCountries ?? this.countUkCountries,
        showLegend: showLegend ?? this.showLegend,
        showLivedPin: showLivedPin ?? this.showLivedPin,
        useCustomPalette: useCustomPalette ?? this.useCustomPalette,
        showRegions: showRegions ?? this.showRegions,
        projection: projection ?? this.projection,
        language: language ?? this.language,
        showSelectionDialogOnPickerLists:
            showSelectionDialogOnPickerLists ?? this.showSelectionDialogOnPickerLists,
      );

  Map toJson() => {
        'onlySovereign': onlySovereign,
        'countUkCountries': countUkCountries,
        'showLegend': showLegend,
        'showLivedPin': showLivedPin,
        'useMyColours': useCustomPalette,
        'showRegions': showRegions,
        'projection': projection.code,
        if (language != null) 'language': language?.localizationKey,
        'showSelectionDialogOnPickerLists': showSelectionDialogOnPickerLists,
      };

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
          other is Settings && runtimeType == other.runtimeType &&
              onlySovereign == other.onlySovereign &&
              countUkCountries == other.countUkCountries &&
              showLegend == other.showLegend &&
              showLivedPin == other.showLivedPin &&
              useCustomPalette == other.useCustomPalette &&
              showRegions == other.showRegions &&
              projection == other.projection && language == other.language &&
              showSelectionDialogOnPickerLists ==
                  other.showSelectionDialogOnPickerLists;

  @override
  int get hashCode =>
      Object.hash(
          onlySovereign,
          countUkCountries,
          showLegend,
          showLivedPin,
          useCustomPalette,
          showRegions,
          projection,
          language,
          showSelectionDialogOnPickerLists);


}

extension CrsBuilder on Crs {
  static const mercator = Epsg3857();
  static const equirectangular = Epsg4326();

  static Crs fromCode(String? code) => const [Epsg3857(), Epsg4326()]
      .firstWhere((crs) => (code ?? const Epsg3857().code) == crs.code);
}
