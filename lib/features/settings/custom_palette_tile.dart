import 'package:flutter/material.dart';
import '../../generic_widgets/separated_tile.dart';

import 'colour_picker_bottom_sheet.dart';

class CustomPaletteTile extends StatelessWidget {
  const CustomPaletteTile({
    super.key,
    required this.color,
    required this.title,
    required this.onColorUpdated,
  });

  final String title;
  final Stream<Color> color;
  final ValueChanged<Color> onColorUpdated;

  @override
  Widget build(BuildContext context) {
    return SeparatedTile(
      child: ListTile(
        leading: _buildCurrentColour(),
        title: Text(title),
        trailing: const Icon(Icons.edit_outlined),
        onTap: () async {
          final scaffold = Scaffold.of(context);
          final current = await color.first;
          scaffold.showBottomSheet(
              (context) => ColorPickerBottomSheet(
                    color: current,
                    onColorUpdated: onColorUpdated,
                  ),
              elevation: 10);
        },
      ),
    );
  }

  Widget _buildCurrentColour() {
    return StreamBuilder<Color>(
        stream: color,
        initialData: Colors.transparent,
        builder: (context, snapshot) {
          return Container(
            decoration: BoxDecoration(
              color: snapshot.data,
              borderRadius: BorderRadius.circular(8),
            ),
            width: 50,
            height: 35,
          );
        });
  }
}
