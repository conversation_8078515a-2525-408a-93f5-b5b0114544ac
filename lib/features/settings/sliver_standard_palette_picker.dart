import 'dart:async';

import 'package:flutter/material.dart';

import '../../l10n/generated/app_localizations.dart';
import '../../models/palette.dart';
import 'palette_tile.dart';
import 'sliver_subheading_tile.dart';

class SliverStandardPalettePicker extends StatefulWidget {
  const SliverStandardPalettePicker({
    super.key,
    required this.onPaletteSelected,
    required this.activePalette,
  });

  final ValueChanged<Palette> onPaletteSelected;
  final Stream<Palette> activePalette;

  @override
  State createState() => _SliverStandardPalettePickerState();
}

class _SliverStandardPalettePickerState
    extends State<SliverStandardPalettePicker> {
  List<Palette>? palettes;
  Palette? _selectedPalette;
  StreamSubscription? _paletteSubscription;

  @override
  void initState() {
    super.initState();
    _fetchData();
  }

  void _fetchData() {
    palettes = Palette.stockPalettes;
    _paletteSubscription = widget.activePalette.listen((event) {
      setState(() {
        _selectedPalette = event;
      });
    });

    setState(() {});
  }

  @override
  Widget build(BuildContext context) {
    final palettes = this.palettes;
    if (palettes == null) {
      return const SliverToBoxAdapter(child: SizedBox());
    }

    return SliverSubHeadingTile(
      title: AppLocalizations.of(context)!.mapColors,
      sliver: SliverList(
        delegate: SliverChildBuilderDelegate(
          (context, index) {
            final palette = palettes[index];

            return PaletteTile(
              palette: palette,
              selected: _selectedPalette == palette,
              onTapped: () => widget.onPaletteSelected(palette),
            );
          },
          childCount: palettes.length,
        ),
      ),
    );
  }

  @override
  void dispose() {
    _paletteSubscription?.cancel();
    super.dispose();
  }
}
