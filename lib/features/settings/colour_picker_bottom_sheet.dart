import 'package:flutter/material.dart';
import 'package:flutter_colorpicker/flutter_colorpicker.dart';

import '../../generic_widgets/platform_aware/platform_text_button.dart';
import '../../helpers/string_extensions.dart';
import '../../l10n/generated/app_localizations.dart';

class ColorPickerBottomSheet extends StatefulWidget {
  const ColorPickerBottomSheet({
    super.key,
    required this.color,
    required this.onColorUpdated,
  });

  final Color color;
  final ValueChanged<Color> onColorUpdated;

  @override
  State<ColorPickerBottomSheet> createState() => _ColorPickerBottomSheetState();
}

class _ColorPickerBottomSheetState extends State<ColorPickerBottomSheet> {
  late Color color;

  @override
  void initState() {
    super.initState();
    color = widget.color;
  }

  @override
  Widget build(BuildContext context) {
    final localizations = AppLocalizations.of(context)!;
    return Container(
      decoration: BoxDecoration(
        boxShadow: const [BoxShadow(blurRadius: 20, color: Colors.black45)],
        color: Theme.of(context).cardColor,
        borderRadius: const BorderRadius.vertical(
          top: Radius.circular(15),
        ),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          _buildNotch(context),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              PlatformTextButton(
                title: localizations.cancel,
                onTapped: Navigator.of(context).pop,
              ),
              PlatformTextButton(
                  onTapped: () {
                    widget.onColorUpdated(color);
                    Navigator.of(context).pop();
                  },
                  title: MaterialLocalizations.of(context)
                      .saveButtonLabel
                      .capitalized)
            ],
          ),
          SafeArea(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: ColorPicker(
                  pickerAreaHeightPercent: 0.75,
                  pickerColor: color,
                  labelTypes: const [],
                  hexInputBar: true,
                  enableAlpha: false,
                  paletteType: PaletteType.hueWheel,
                  onColorChanged: (updatedColor) {
                    color = updatedColor;
                  }),
            ),
          ),
          if (Theme.of(context).platform == TargetPlatform.iOS)
            const SizedBox(height: 80)
        ],
      ),
    );
  }

  Widget _buildNotch(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.only(top: 16),
      child: GestureDetector(
        onTap: () => Navigator.of(context).pop(),
        child: Container(
          width: 36,
          height: 4,
          decoration: BoxDecoration(
              color: Theme.of(context).unselectedWidgetColor,
              borderRadius: BorderRadius.circular(2)),
        ),
      ),
    );
  }
}
