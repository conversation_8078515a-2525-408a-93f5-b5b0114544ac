import 'package:flutter/material.dart';

import '../../generic_widgets/platform_aware/platform_switch.dart';
import '../../generic_widgets/separated_tile.dart';

class SettingToggleTile extends StatelessWidget {
  const SettingToggleTile({
    super.key,
    required this.title,
    required this.checked,
    required this.onChanged,
    this.enabled = true,
  });

  final bool enabled;
  final String title;
  final Stream<bool> checked;
  final ValueChanged<bool> onChanged;

  @override
  Widget build(BuildContext context) {
    final platform = Theme.of(context).platform;
    return SeparatedTile(
      child: ListTile(
        title: Text(title),
        onTap: () async {
          final value = await checked.first;
          onChanged(!value);
        },
        leading: platform != TargetPlatform.iOS ? _buildSwitch() : null,
        trailing: platform == TargetPlatform.iOS ? _buildSwitch() : null,
      ),
    );
  }

  Widget _buildSwitch() {
    return StreamBuilder<bool>(
      stream: checked,
      initialData: false,
      builder: (context, snapshot) {
        return PlatformSwitch(
          onChanged: enabled ? onChanged : null,
          checked: snapshot.requireData,
        );
      },
    );
  }
}
