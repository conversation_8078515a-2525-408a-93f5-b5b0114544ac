import 'dart:io';
import 'dart:ui';

import 'package:flutter_map/flutter_map.dart';
import 'package:rxdart/subjects.dart';

import '../../bridges/environment_fetcher.dart';
import '../../caching/storage.dart';
import '../../dependency_injection/dependency_injector.dart';
import '../../generic_widgets/bloc.dart';
import '../../models/palette.dart';
import '../localization/supported_language.dart';
import 'settings.dart';

const _kSettingsKey = 'settings';
const _kSavedPalette = 'palette';
const _kCustomPalette = 'custom.palette';

const _kLastSystemLanguage = 'last.system.language';

class SettingsBloc implements Bloc {
  final _paletteController = BehaviorSubject<Palette>();
  final _customPaletteController = BehaviorSubject<Palette>();
  final _settingsController = BehaviorSubject<Settings>();

  // Individual property controllers
  final _countSovereignController = BehaviorSubject<bool>();
  final _countUkCountriesController = BehaviorSubject<bool>();
  final _showLegendController = BehaviorSubject<bool>();
  final _showLivedPinController = BehaviorSubject<bool>();
  final _useMyColoursController = BehaviorSubject<bool>();
  final _showRegionsController = BehaviorSubject<bool>();
  late final _languageController = BehaviorSubject<SupportedLanguage?>()
    ..onListen = _fetchUserSelectedLanguage;

  Storage get _storage => DependencyInjector.sharedPrefsStorage;

  SettingsBloc(
    Brightness brightness,
  ) {
    _checkIfSystemLanguageChanged();

    Future.wait([
      _loadCustomPalette(),
      _loadPalette(brightness),
      _loadSettings(),
    ]).whenComplete(() {
      if (_settingsController.valueOrNull?.useCustomPalette ?? false) {
        _paletteController.add(_customPaletteController.value);
      }
    });
  }

  Settings get currentSettings => _settingsController.value;

  Stream<Palette> get palette => _paletteController.stream;
  Stream<Palette> get customPalette => _customPaletteController.stream;
  Stream<Settings> get settings => _settingsController.stream;

  // Public getters that return the individual property streams
  Stream<bool> get countSovereign => _countSovereignController.stream;
  Stream<bool> get countUkCountries => _countUkCountriesController.stream;
  Stream<bool> get showLegend => _showLegendController.stream;
  Stream<bool> get showLivedPin => _showLivedPinController.stream;
  Stream<bool> get useMyColours => _useMyColoursController.stream;
  Stream<bool> get showRegions => _showRegionsController.stream;
  Stream<SupportedLanguage?> get language => _languageController.stream;

  Palette get currentPalette =>
      _paletteController.valueOrNull ?? Palette.standard;
  Palette get currentCustomPalette =>
      _customPaletteController.valueOrNull ?? Palette.standard;

  SupportedLanguage get currentLanguage =>
      currentSettings.language ??
      SupportedLanguage.fromLocale(EnvironmentFetcher.systemLocale);

  void updateLanguage(SupportedLanguage language) {
    final value = _settingsController.value.copyWith(language: language);
    _updateSettings(value);
  }

  void updateSovereign(bool selected) {
    final value = _settingsController.value.copyWith(onlySovereign: selected);
    _updateSettings(value);
  }

  void updatedCountUk(bool selected) {
    final value = _settingsController.value.copyWith(
      countUkCountries: selected,
    );
    _updateSettings(value);
  }

  void updatedShowLegend(bool selected) {
    final value = _settingsController.value.copyWith(showLegend: selected);
    _updateSettings(value);
  }

  void updateShowLivedPin(bool selected) {
    final value = _settingsController.value.copyWith(showLivedPin: selected);
    _updateSettings(value);
  }

  void updateClusterPins(bool cluster) {
    final value = _settingsController.value.copyWith(clusterPins: cluster);
    _updateSettings(value);
  }

  void _updateSettings(Settings settings) {
    // Update main settings controller
    _settingsController.add(settings);

    // Update individual property controllers
    // Only emit if the value has changed
    if (_countSovereignController.valueOrNull != settings.onlySovereign) {
      _countSovereignController.add(settings.onlySovereign);
    }

    if (_countUkCountriesController.valueOrNull != settings.countUkCountries) {
      _countUkCountriesController.add(settings.countUkCountries);
    }

    if (_showLegendController.valueOrNull != settings.showLegend) {
      _showLegendController.add(settings.showLegend);
    }

    if (_showLivedPinController.valueOrNull != settings.showLivedPin) {
      _showLivedPinController.add(settings.showLivedPin);
    }

    if (_useMyColoursController.valueOrNull != settings.useCustomPalette) {
      _useMyColoursController.add(settings.useCustomPalette);
    }

    if (_showRegionsController.valueOrNull != settings.showRegions) {
      _showRegionsController.add(settings.showRegions);
    }

    if (_languageController.valueOrNull != settings.language) {
      _languageController.add(settings.language);
    }

    _saveSettings();
  }

  void updateUseMyColours(bool selected) {
    final value = _settingsController.value.copyWith(
      useCustomPalette: selected,
    );
    _updateSettings(value);

    if (selected) {
      _paletteController.sink.add(_customPaletteController.value);
    }
  }

  void toggleShowRegions(bool selected) {
    final value = _settingsController.value.copyWith(showRegions: selected);
    DependencyInjector.tileRenderingService.showRegions(selected);
    _updateSettings(value);
  }

  Future<void> updateProjection(Crs projection) async {
    if (currentSettings.projection == projection) {
      return;
    }

    final value = _settingsController.value.copyWith(projection: projection);
    _updateSettings(value);
    DependencyInjector.tileRepository.clear();
    final renderer = DependencyInjector.tileRenderingService;
    await renderer.clearAllTiles();
    renderer.refresh();
  }

  Future<Settings> _loadSettings() async {
    if (_settingsController.hasValue) {
      return _settingsController.value;
    }

    final json = await _storage.getJson(_kSettingsKey);
    Settings settings;

    if (json == null) {
      settings = const Settings();
    } else {
      settings = Settings.fromJson(json);
    }

    if (settings.language == null) {
      final systemLocale = EnvironmentFetcher.systemLocale;
      settings = settings.copyWith(
        language: SupportedLanguage.fromLocale(systemLocale),
      );
    }

    // Add to main settings controller
    _settingsController.add(settings);

    // Initialize individual property controllers
    _countSovereignController.add(settings.onlySovereign);
    _countUkCountriesController.add(settings.countUkCountries);
    _showLegendController.add(settings.showLegend);
    _showLivedPinController.add(settings.showLivedPin);
    _useMyColoursController.add(settings.useCustomPalette);
    _showRegionsController.add(settings.showRegions);
    _languageController.add(settings.language);

    return settings;
  }

  void _saveSettings() {
    final settings = _settingsController.valueOrNull;
    if (settings == null) {
      return;
    }

    _storage.putJson(_kSettingsKey, settings);
  }

  void updatePalette(Palette palette) {
    _paletteController.add(palette);
    _storage.putJson(_kSavedPalette, palette);
    updateUseMyColours(false);
  }

  Future<void> _loadPalette(Brightness brightness) async {
    final json = await _storage.getJson(_kSavedPalette);
    if (json != null) {
      final palette = Palette.fromJson(json);
      _paletteController.sink.add(palette);
      return;
    }

    const palettes = Palette.stockPalettes;
    final initial = palettes.firstWhere((palette) {
      if (brightness == Brightness.light && palette.defaultLight) {
        return true;
      } else if (brightness == Brightness.dark && palette.defaultDark) {
        return true;
      } else {
        return false;
      }
    }, orElse: () => Palette.standard);

    _paletteController.sink.add(initial);
  }

  void updateCustomPalette(Palette palette) {
    _customPaletteController.add(palette);
    updateUseMyColours(true);
    _storage.putJson(_kCustomPalette, palette);
  }

  Future<void> _loadCustomPalette() async {
    final json = await _storage.getJson(_kCustomPalette);
    if (json == null) {
      _customPaletteController.sink.add(
        Palette.standard.copyWith(id: 'custom'),
      );
      return;
    }

    final palette = Palette.fromJson(json);
    _customPaletteController.sink.add(palette);
  }

  @override
  void clear() {
    _storage.delete(_kSettingsKey);
    _storage.delete(_kSavedPalette);

    // Reset to default settings (except for language);
    final defaultSettings = Settings(language: currentLanguage);
    _updateSettings(defaultSettings);
  }

  @override
  void dispose() {
    _settingsController.close();
    _paletteController.close();
    _customPaletteController.close();

    // Close the individual property controllers
    _countSovereignController.close();
    _countUkCountriesController.close();
    _showLegendController.close();
    _showLivedPinController.close();
    _useMyColoursController.close();
    _showRegionsController.close();
    _languageController.close();
  }

  void _checkIfSystemLanguageChanged() async {
    final currentLocale = Platform.localeName;
    final lastLocale = await _storage.get(_kLastSystemLanguage);

    // No change
    if (lastLocale == currentLocale) {
      return;
    }

    // Save the current local and override any user custom language
    _storage.put(_kLastSystemLanguage, currentLocale);

    var languageCode = currentLocale;
    String? scriptCode;
    String? countryCode;

    if (currentLocale.contains('_')) {
      final elements = currentLocale.split('_');
      if (elements.length == 2) {
        languageCode = elements.first;
        countryCode = elements.last;
      } else {
        languageCode = elements.first;
        scriptCode = elements[1];
        countryCode = elements.last;
      }
    }

    final locale = Locale.fromSubtags(
      languageCode: languageCode,
      scriptCode: scriptCode,
      countryCode: countryCode,
    );

    final supportedLanguage = SupportedLanguage.fromLocale(locale);

    updateLanguage(supportedLanguage);
  }

  void _fetchUserSelectedLanguage() async {
    if (_languageController.hasValue) {
      return;
    }

    _loadSettings();
  }
}
