import 'package:flutter/material.dart';

import '../../l10n/generated/app_localizations.dart';
import '../../models/palette.dart';
import 'custom_palette_tile.dart';
import 'sliver_subheading_tile.dart';

class SliverCustomPalettePicker extends StatelessWidget {
  const SliverCustomPalettePicker({
    super.key,
    required this.palette,
    required this.onPaletteChanged,
    this.showLabels = true,
  });

  final Stream<Palette> palette;
  final ValueChanged<Palette> onPaletteChanged;
  final bool showLabels;

  @override
  Widget build(BuildContext context) {
    final localizations = AppLocalizations.of(context)!;
    return SliverSubHeadingTile(
      title: AppLocalizations.of(context)!.myColors,
      sliver: SliverList(
        delegate: SliverChildListDelegate(
          [
            _buildCustomPaletteTile(
              context: context,
              title: localizations.water,
              colorMapper: (palette) => palette.water,
              paletteMapper: (palette, color) => palette.copyWith(water: color),
            ),
            _buildCustomPaletteTile(
              context: context,
              title: localizations.land,
              colorMapper: (palette) => palette.land,
              paletteMapper: (palette, color) => palette.copyWith(land: color),
            ),
            _buildCustomPaletteTile(
              context: context,
              title: localizations.live,
              colorMapper: (palette) => palette.live,
              paletteMapper: (palette, color) => palette.copyWith(live: color),
            ),
            _buildCustomPaletteTile(
              context: context,
              title: localizations.lived,
              colorMapper: (palette) => palette.lived,
              paletteMapper: (palette, color) => palette.copyWith(lived: color),
            ),
            _buildCustomPaletteTile(
              context: context,
              title: localizations.been,
              colorMapper: (palette) => palette.been,
              paletteMapper: (palette, color) => palette.copyWith(been: color),
            ),
            _buildCustomPaletteTile(
              context: context,
              title: localizations.want,
              colorMapper: (palette) => palette.want,
              paletteMapper: (palette, color) => palette.copyWith(want: color),
            ),
            _buildCustomPaletteTile(
              context: context,
              title: localizations.borders,
              colorMapper: (palette) => palette.border,
              paletteMapper: (palette, color) =>
                  palette.copyWith(border: color),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildCustomPaletteTile({
    required BuildContext context,
    required String title,
    required Color Function(Palette palette) colorMapper,
    required Palette Function(Palette palette, Color color) paletteMapper,
  }) {
    return CustomPaletteTile(
      color: palette.map(colorMapper),
      title: title,
      onColorUpdated: (color) async {
        final custom = await palette.first;
        final updated = paletteMapper(custom, color);
        onPaletteChanged(updated);
      },
    );
  }
}
