import 'dart:async';

import 'package:flutter/material.dart';
import 'package:flutter_map/flutter_map.dart';
import 'package:sliver_tools/sliver_tools.dart';

import '../../dependency_injection/dependency_injector.dart';
import '../../generic_widgets/alert.dart';
import '../../generic_widgets/animated_checkmark.dart';
import '../../generic_widgets/navigation_extensions.dart';
import '../../generic_widgets/platform_aware/platform_arrow.dart';
import '../../generic_widgets/platform_aware/platform_sliver_app_bar.dart';
import '../../generic_widgets/responsive_padding.dart';
import '../../generic_widgets/separated_tile.dart';
import '../../generic_widgets/sliver_bottom_safe_area.dart';
import '../../l10n/generated/app_localizations.dart';
import '../disputed_territories/disputed_territories_screen.dart';
import '../localization/supported_language.dart';
import 'settings.dart';
import 'settings_toggle_tile.dart';
import 'sliver_custom_palette_picker.dart';
import 'sliver_standard_palette_picker.dart';
import 'sliver_subheading_tile.dart';

class CustomizeScreen extends StatelessWidget {
  const CustomizeScreen({super.key});

  static const routeName = 'customize';

  @override
  Widget build(BuildContext context) {
    final settingsBloc = DependencyInjector.settingsBloc;
    return Scaffold(
      body: Scrollbar(
        child: CustomScrollView(
          slivers: [
            PlatformSliverAppBar(
              title: AppLocalizations.of(context)!.customize,
            ),
            ResponsiveSliverPadding(
              context: context,
              fillToEdgeOnPhone: true,
              sliver: MultiSliver(
                children: [
                  SliverStandardPalettePicker(
                    activePalette: settingsBloc.palette,
                    onPaletteSelected: settingsBloc.updatePalette,
                  ),
                  SliverCustomPalettePicker(
                    palette: settingsBloc.customPalette,
                    onPaletteChanged: settingsBloc.updateCustomPalette,
                  ),
                  _buildToggles(context),
                  _buildProjection(context),
                  _buildLanguage(context),
                ],
              ),
            ),
            const SliverBottomSafeArea(),
          ],
        ),
      ),
    );
  }

  Widget _buildToggles(BuildContext context) {
    final settingsBloc = DependencyInjector.settingsBloc;
    final localizations = AppLocalizations.of(context)!;
    return SliverSubHeadingTile(
      title: '',
      sliver: SliverList.list(
        children: [
          SettingToggleTile(
            title: localizations.onlyCountSovereign,
            checked: settingsBloc.countSovereign,
            onChanged: settingsBloc.updateSovereign,
          ),
          SettingToggleTile(
            title: localizations.countUkSeparately,
            checked: settingsBloc.countUkCountries,
            onChanged: settingsBloc.updatedCountUk,
          ),
          SettingToggleTile(
            title: localizations.toggleRegions,
            checked: settingsBloc.showRegions,
            onChanged: settingsBloc.toggleShowRegions,
          ),
          SettingToggleTile(
            title: localizations.showLegend,
            checked: settingsBloc.showLegend,
            onChanged: settingsBloc.updatedShowLegend,
          ),
          SettingToggleTile(
            title: localizations.showLivedPin,
            checked: settingsBloc.showLivedPin,
            onChanged: settingsBloc.updateShowLivedPin,
          ),
          SettingToggleTile(
            title: localizations.useMyColours,
            checked: settingsBloc.useMyColours,
            onChanged: settingsBloc.updateUseMyColours,
          ),
          const SubscribeToNewsletterToggle(),
          SeparatedTile(
            child: ListTile(
              title: Text(localizations.disputedTerritories),
              trailing: const PlatformArrow(),
              onTap: () => Navigator.of(context).pushMaterialRoute(
                name: DisputedTerritoriesScreen.routeName,
                builder: (_) => const DisputedTerritoriesScreen(),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildProjection(BuildContext context) {
    return StreamBuilder<Settings>(
      stream: DependencyInjector.settingsBloc.settings,
      builder: (context, snapshot) {
        final settings =
            snapshot.data ?? DependencyInjector.settingsBloc.currentSettings;

        final localizations = AppLocalizations.of(context)!;

        return SliverSubHeadingTile(
          title: localizations.mapProjection,
          sliver: SliverList.list(
            children: [
              _buildProjectionTile(
                settings: settings,
                title: localizations.mercator,
                crs: CrsBuilder.mercator,
              ),
              _buildProjectionTile(
                settings: settings,
                title: localizations.equirectangular,
                crs: CrsBuilder.equirectangular,
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildProjectionTile({
    required Settings settings,
    required String title,
    required Crs crs,
  }) {
    return SeparatedTile(
      child: ListTile(
        title: Text(title),
        onTap: () {
          DependencyInjector.settingsBloc.updateProjection(crs);
        },
        trailing: AnimatedCheckmark(checked: settings.projection == crs),
      ),
    );
  }

  Widget _buildLanguage(BuildContext context) {
    return StreamBuilder<SupportedLanguage?>(
      stream: DependencyInjector.settingsBloc.language,
      builder: (context, snapshot) {
        final language =
            snapshot.data ??
            SupportedLanguage.fromLocale(Localizations.localeOf(context));

        return SliverSubHeadingTile(
          title: AppLocalizations.of(context)!.language,
          sliver: SliverList.list(
            children: [
              for (final lang in SupportedLanguage.values)
                _buildLanguageTile(language: lang, selected: language == lang),
            ],
          ),
        );
      },
    );
  }

  Widget _buildLanguageTile({
    required SupportedLanguage language,
    required bool selected,
  }) {
    return SeparatedTile(
      child: ListTile(
        title: Text(language.userFacingName),
        trailing: AnimatedCheckmark(checked: selected),
        onTap: () => DependencyInjector.settingsBloc.updateLanguage(language),
      ),
    );
  }
}

class SubscribeToNewsletterToggle extends StatefulWidget {
  const SubscribeToNewsletterToggle({super.key});

  @override
  State<SubscribeToNewsletterToggle> createState() =>
      _SubscribeToNewsletterToggleState();
}

class _SubscribeToNewsletterToggleState
    extends State<SubscribeToNewsletterToggle> {
  var enabled = true;

  final Stream<bool> _unsubscribedStream = DependencyInjector
      .sessionBloc
      .userStream
      .map((event) {
        return event?.unsubscribed ?? true;
      });

  StreamSubscription? _subscription;

  @override
  void initState() {
    super.initState();
    _subscription ??= _unsubscribedStream.listen((event) {
      if (!mounted) {
        return;
      }

      setState(() {
        enabled = true;
      });
    });
  }

  @override
  Widget build(BuildContext context) {
    return SettingToggleTile(
      title: AppLocalizations.of(context)!.unsubscribe,
      enabled: enabled,
      checked: _unsubscribedStream,
      onChanged: _onToggled,
    );
  }

  void _onToggled(bool unsubscribe) async {
    setState(() {
      enabled = false;
    });

    final sessionBloc = DependencyInjector.sessionBloc;
    if (!unsubscribe) {
      sessionBloc.toggleNewsletterSubscription();
      return;
    }

    final localizations = AppLocalizations.of(context)!;

    final confirmed = await ConfirmDialog(
      title: localizations.areYouSure,
      message: localizations.unsubscribeConfirmMessage,
      confirmText: localizations.unsubscribe,
      confirmDestructive: true,
      cancelText: localizations.cancel,
    ).show(context);

    if (confirmed == true) {
      sessionBloc.toggleNewsletterSubscription();
    } else {
      setState(() {
        enabled = true;
      });
    }
  }

  @override
  void dispose() {
    _subscription?.cancel();
    super.dispose();
  }
}
