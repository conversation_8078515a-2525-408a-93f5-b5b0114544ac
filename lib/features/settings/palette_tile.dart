import 'package:flutter/material.dart';

import '../../generic_widgets/animated_checkmark.dart';
import '../../generic_widgets/separated_tile.dart';
import '../../l10n/generated/app_localizations.dart';
import '../../models/palette.dart';

class PaletteTile extends StatelessWidget {
  const PaletteTile({
    super.key,
    required this.palette,
    this.selected = false,
    this.onTapped,
  });

  final Palette palette;
  final bool selected;
  final VoidCallback? onTapped;

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTapped,
      child: SeparatedTile(
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                _localizedTitle(context),
                style:
                    const TextStyle(fontSize: 15, fontWeight: FontWeight.w600),
              ),
              const SizedBox(height: 8),
              _buildColours(context)
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildColours(BuildContext context) {
    final localizations = AppLocalizations.of(context)!;
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildColor(palette.water, localizations.water),
        _buildColor(palette.land, localizations.land),
        _buildColor(palette.live, localizations.live),
        _buildColor(palette.lived, localizations.lived),
        _buildColor(palette.been, localizations.been),
        _buildColor(palette.want, localizations.want),
        Padding(
          padding: const EdgeInsets.all(8),
          child: AnimatedCheckmark(
            checked: selected,
            width: 20,
          ),
        )
      ],
    );
  }

  Widget _buildColor(Color color, String label) {
    return Expanded(
        child: Padding(
      padding: const EdgeInsets.symmetric(horizontal: 4.0),
      child: Column(
        children: [
          Container(
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(8),
              color: color,
            ),
            height: 40,
          ),
          Text(label),
        ],
      ),
    ));
  }

  String _localizedTitle(BuildContext context) {
    final localizations = AppLocalizations.of(context)!;

    switch (palette.id) {
      case 'traveller':
        return localizations.traveller;
      case 'nightTraveller':
        return localizations.nightTraveller;
      case 'original':
        return localizations.original;
      case 'explorer':
        return localizations.explorer;
      case 'weekender':
        return localizations.weekender;
      case 'naturalist':
        return localizations.naturalist;
      case 'historian':
        return localizations.historian;
      case 'thrill_seeker':
        return localizations.thrillSeeker;
      case 'cultural_buff':
        return localizations.culturalBuff;
      default:
        return palette.id;
    }
  }
}
