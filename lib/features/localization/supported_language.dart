import 'dart:ui';

enum SupportedLanguage {
  catalan('Català', 'ca', 'ES'),
  czech('<PERSON><PERSON><PERSON><PERSON>', 'cs', 'CZ'),
  danish('Dansk', 'da', 'DK'),
  german('<PERSON><PERSON><PERSON>', 'de', 'DE'),
  greek('Ελληνικά', 'el', 'GR'),
  australianEnglish('English (Australia)', 'en', 'AU'),
  canadianEnglish('English (Canada)', 'en', 'CA'),
  britishEnglish('English (United Kingdom)', 'en', 'GB'),
  americanEnglish('English (United States)', 'en', 'US', true),
  spanish('Español (España)', 'es', 'ES', true),
  mexicanSpanish('Español (México)', 'es', 'MX'),
  finnish('Suomi', 'fi', 'FI'),
  frenchCanadian('Français (Canada)', 'fr', 'CA'),
  french('Fr<PERSON><PERSON> (France)', 'fr', 'FR', true),
  croatian('Hrvatski', 'hr', 'HR'),
  hungarian('Magyar', 'hu', 'HU'),
  indonesian('Indonesia', 'id', 'ID'),
  italian('Italiano', 'it', 'IT'),
  japanese('日本語', 'ja', 'JP'),
  korean('한국어', 'ko', 'KR'),
  malay('Melayu', 'ms', 'MY'),
  norwegian('Norsk bokmål', 'nb', 'NO'),
  dutch('Nederlands', 'nl', 'NL'),
  polish('Polski', 'pl', 'PL'),
  brazilianPortuguese('Português (Brasil)', 'pt', 'BR'),
  portuguese('Português (Portugal)', 'pt', 'PT', true),
  romanian('Română', 'ro', 'RO'),
  russian('Русский', 'ru', 'RU'),
  slovak('Slovenčina', 'sk', 'SK'),
  serbian('Српски', 'sr'),
  swedish('Svenska', 'sv', 'SE'),
  thai('ไทย', 'th', 'TH'),
  turkish('Türkçe', 'tr', 'TR'),
  ukrainian('Українська', 'uk', 'UA'),
  vietnamese('Tiếng Việt', 'vi', 'VN'),
  simplifiedChinese('简体中文', 'zh', 'CN', true),
  traditionalChinese('繁體中文', 'zh', 'TW');

  const SupportedLanguage(this.userFacingName, this.languageCode,
      [this.countryCode, this.defaultLanguage = false]);
  final String languageCode;
  final String? countryCode;
  final String userFacingName;
  final bool defaultLanguage;

  Locale get locale => Locale(languageCode, countryCode);
  String get localizationKey =>
      countryCode == null ? languageCode : '$languageCode-$countryCode';

  static SupportedLanguage fromLocale(Locale locale) {
    final languageOptions = SupportedLanguage.values
        .where((lang) => lang.languageCode == locale.languageCode)
        .toList();
    if (languageOptions.isEmpty) {
      return SupportedLanguage.americanEnglish;
    }

    if (languageOptions.length == 1) {
      return languageOptions.first;
    }

    return languageOptions.firstWhere(
        (lang) => lang.countryCode == locale.countryCode,
        orElse: () =>
            languageOptions.where((lang) => lang.defaultLanguage).first);
  }
}
