import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';

import '../../dependency_injection/dependency_injector.dart';
import '../../generic_widgets/navigation_extensions.dart';
import '../../generic_widgets/platform_aware/platform_filled_button.dart';
import '../../generic_widgets/secondary_button.dart';
import '../../generic_widgets/shiny_visited_logo.dart';
import '../../generic_widgets/spinner.dart';
import '../../generic_widgets/stadium_button.dart';
import '../../l10n/generated/app_localizations.dart';
import '../../logger.dart';
import 'buy_visited_pro_soft_sell.dart';
import 'iap_product.dart';
import 'iap_status.dart';

class IAPTile extends StatelessWidget {
  const IAPTile({
    super.key,
    required this.product,
  });

  final IAPProduct product;

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        _buildFraming(
          context: context,
          child: _buildProductInformation(context),
        ),
        _buildFraming(
          context: context,
          padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 3),
          child: _buildPurchasePanel(context),
        )
      ],
    );
  }

  Widget _buildFraming({
    required BuildContext context,
    double? height,
    EdgeInsets? padding,
    required Widget child,
  }) {
    final theme = Theme.of(context);
    final borderColour = theme.secondaryHeaderColor;

    return Container(
      padding: padding ?? const EdgeInsets.all(20),
      height: height,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: borderColour,
        ),
        color: product.feature == IAPFeature.visitedPro ? borderColour : null,
      ),
      child: child,
    );
  }

  Widget _buildProductInformation(BuildContext context) {
    return Row(
      children: [
        Expanded(
          child: _buildProductDescription(context),
        ),
        if (product.feature == IAPFeature.visitedPro)
          const ShinyVisitedLogo(size: 84)
        else
          SvgPicture.asset(
            product.feature.assetPathSvg,
            width: 84,
            height: 100,
            fit: BoxFit.fitHeight,
          )
      ],
    );
  }

  Widget _buildProductDescription(BuildContext context) {
    final textTheme = Theme.of(context).textTheme;
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Text(
          product.title,
          style: textTheme.titleLarge?.copyWith(fontSize: 20),
        ),
        const SizedBox(height: 8),
        Text(
          product.price,
          style: textTheme.titleSmall?.copyWith(fontWeight: FontWeight.bold),
        ),
        const SizedBox(height: 8),
        Text(
          product.subtitle,
          style:
              textTheme.bodyLarge?.copyWith(color: textTheme.titleLarge?.color),
        ),
        if (product.feature == IAPFeature.visitedPro)
          Padding(
            padding: const EdgeInsets.only(top: 8, right: 64),
            child: PlatformFilledButton(
              title: AppLocalizations.of(context)!.moreInfo,
              color: Colors.white,
              textColor: Colors.black,
              // textStyle: const TextStyle(color: Colors.white, fontSize: 12),
              // padding: EdgeInsets.zero,
              onTapped: () => _pushVisitedProSoftSell(context),
            ),
          )
      ],
    );
  }

  void _pushVisitedProSoftSell(BuildContext context) {
    Navigator.of(context).pushMaterialRoute(
      builder: (_) => const BuyVisitedProSoftSell(),
      name: BuyVisitedProSoftSell.routeName,
    );
  }

  Widget _buildPurchasePanel(BuildContext context) {
    final bloc = DependencyInjector.iapBloc;
    final localizations = AppLocalizations.of(context)!;

    return StreamBuilder<IAPStatus>(
      stream: bloc.status,
      initialData: bloc.currentStatus,
      builder: (context, snapshot) {
        if (snapshot.data?.containsFeature(product.feature) ?? false) {
          return Align(
            alignment: Alignment.centerRight,
            child: Padding(
              padding: const EdgeInsets.all(8.0),
              child: Text(
                localizations.purchased,
                style: Theme.of(context).textTheme.titleMedium,
              ),
            ),
          );
        }

        return Row(
          mainAxisAlignment: MainAxisAlignment.end,
          children: [
            SecondaryButton(
              title: localizations.restorePurchases,
              onTapped: () => DependencyInjector.iapBloc.restorePurchase(),
            ),
            Expanded(
              child: StadiumButton(
                materialTextPadding: EdgeInsets.zero,
                cupertinoTextPadding: EdgeInsets.zero,
                title: localizations.buy,
                onTapped: () => _onBuyTapped(context),
              ),
            )
          ],
        );
      },
    );
  }

  void _onBuyTapped(BuildContext context) async {
    const spinner = SpinnerDialog();
    spinner.show(context);
    try {
      await DependencyInjector.iapBloc.purchase(product);
    } catch (e) {
      log(e);
      //TODO Handle Errors
    } finally {
      if (context.mounted) {
        Navigator.of(context).pop();
      }
    }
  }
}
