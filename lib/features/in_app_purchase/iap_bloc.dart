import 'package:collection/collection.dart';
import 'package:flutter/services.dart';
import 'package:in_app_purchase/in_app_purchase.dart';
import 'package:rxdart/rxdart.dart';

import '../../dependency_injection/dependency_injector.dart';
import '../../generic_widgets/bloc.dart';
import '../../models/geo_area.dart';
import '../map/tiles/tile_constants.dart';
import 'iap_product.dart';
import 'iap_service.dart';
import 'iap_status.dart';

class IAPBloc implements Bloc {
  IAPBloc() : _service = IAPService();

  final _controller = BehaviorSubject<IAPStatus>.seeded(IAPStatus.none);
  final IAPService _service;

  Stream<IAPStatus> get status => _controller.stream;
  IAPStatus get currentStatus => _controller.valueOrNull ?? IAPStatus.none;

  bool get hasRemovedAds => currentStatus.hasRemovedAds;
  bool get showAds => !hasRemovedAds;
  bool get hasUnlockedItineraries => currentStatus.hasUnlockedItineraries;
  bool get hasUnlockedRegions => currentStatus.hasUnlockedRegions;
  bool get hasUnlockedInspirations => currentStatus.hasUnlockedInspirations;
  bool get hasUnlockedCities => currentStatus.hasUnlockedCities;
  bool get hasUnlockedPro => currentStatus.purchasedPro;
  bool get canSelectLived => currentStatus.canSelectLived;

  Future<void> initialize() async {
    try {
      await _service.initialize();
    } catch (e) {
      // The usually only happens on simulators
    }

    final bundleIds = await _service.fetchPurchases();
    final features = bundleIds.map(IAPFeature.fromBundleId).nonNulls.toSet();
    _controller.add(IAPStatus(features));
  }

  void submitPurchases(Set<String> bundleIds) {
    _processBundleIds(bundleIds);
  }

  Future<void> restorePurchase() async {
    _service.productRestoredCallback = _processBundleIds;
    return _service.restorePurchases();
  }

  Future<List<IAPProduct>?> fetchProducts([Set<IAPFeature>? features]) async {
    var products = await _service.fetchProducts();
    if (features != null) {
      products = products
          ?.where((product) => features.contains(product.feature))
          .toList();
    }

    products?.sort();
    return products;
  }

  Future<IAPProduct?> fetchProduct(IAPFeature feature) async {
    final products = await _service.fetchProducts();
    final product = products?.firstWhereOrNull(
      (product) => product.feature == feature,
    );
    return product;
  }

  Future<void> purchase(IAPProduct product) async {
    // No Point in buying anything if you've already bought 'pro'
    // if (_controller.valueOrNull?.purchasedPro ?? false) {
    //   return Future.value();
    // }

    final success = await _service.purchase(product);

    if (!success) {
      return;
    }

    _processBundleIds({product.bundleId});

    if (hasUnlockedRegions) {
      DependencyInjector.tileRenderingService.showRegions(true);
    }
  }

  void _processBundleIds(Set<String> bundleIds) {
    final purchases = bundleIds
        .map<IAPFeature?>(
          (bundleId) => IAPFeature.values.firstWhereOrNull(
            (feature) => feature.bundleId == bundleId,
          ),
        )
        .nonNulls
        .toSet();

    final status =
        _controller.valueOrNull?.copyWith(updatedPurchases: purchases) ??
        IAPStatus(purchases);

    _controller.add(status);
  }

  bool canUnlockRegions(GeoArea area) {
    if (!area.hasSubdivisions) {
      return false;
    }

    return !(hasUnlockedRegions);
  }

  bool canAccessSubdivisions(GeoArea area) {
    if (!area.hasSubdivisions) {
      return false;
    }

    if (hasUnlockedRegions) {
      return true;
    }

    return TileConstants.freeParentSubdivisionIds.contains(area.id);
  }

  Future<bool> canPurchase(IAPFeature feature) async {
    final product = await fetchProduct(feature);
    return product != null;
  }

  @override
  void clear() {
    _controller.add(IAPStatus.none);
  }

  @override
  void dispose() {
    _controller.close();
  }

  Future<List<PurchaseDetails>> fetchRawPurchases() {
    return _service.fetchRawPurchases();
  }

  static const _refundChannel = MethodChannel('com.highheels.visited/refunds');

  Future<void> requestRefund(PurchaseDetails purchase) async {
    final message = {
      'productId': purchase.productID,
      'purchaseId': purchase.purchaseID,
      'verificationData': purchase.verificationData.localVerificationData,
    };
    return _refundChannel.invokeMethod('requestRefund', message);
  }
}
