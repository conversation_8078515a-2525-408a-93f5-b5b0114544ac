import 'iap_product.dart';

class IAPStatus {
  static IAPStatus get none => const IAPStatus({});

  final Set<IAPFeature> _purchases;

  const IAPStatus(Set<IAPFeature> purchases) : _purchases = purchases;

  bool get purchasedPro {
    // if (kDebugMode) return true;
    return _purchases.intersection(IAPFeature.visitedProOptions).isNotEmpty;
  }

  bool get hasRemovedAds {
    if (purchasedPro) return true;
    return _purchases.contains(IAPFeature.removeAds);
  }

  bool get canSelectLived {
    if (purchasedPro) return true;
    return _purchases.contains(IAPFeature.unlockRegions) ||
        _purchases.contains(IAPFeature.unlockCities);
  }

  bool get hasUnlockedRegions {
    if (purchasedPro) return true;
    return _purchases.contains(IAPFeature.unlockRegions);
  }

  bool get hasUnlockedInspirations {
    if (purchasedPro) return true;
    return _purchases.contains(IAPFeature.unlockInspirations);
  }

  bool get hasUnlockedCities {
    if (purchasedPro) return true;
    return _purchases.contains(IAPFeature.unlockCities);
  }

  bool get hasUnlockedItineraries {
    if (purchasedPro) return true;
    return _purchases.contains(IAPFeature.unlockItineraries);
  }

  bool containsFeature(IAPFeature feature) {
    if (purchasedPro) return true;
    return _purchases.contains(feature);
  }

  IAPStatus copyWith({
    required Set<IAPFeature> updatedPurchases,
  }) {
    final update = {...updatedPurchases, ..._purchases};
    return IAPStatus(update);
  }

  @override
  String toString() {
    return 'IAPStatus{_purchases: $_purchases}';
  }
}
