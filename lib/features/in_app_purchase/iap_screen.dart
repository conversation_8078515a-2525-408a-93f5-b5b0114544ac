import 'dart:io';

import 'package:flutter/material.dart';

import '../../dependency_injection/dependency_injector.dart';
import '../../generic_widgets/navigation_extensions.dart';
import '../../generic_widgets/platform_aware/platform_sliver_app_bar.dart';
import '../../generic_widgets/responsive_padding.dart';
import '../../generic_widgets/separated_tile.dart';
import '../../generic_widgets/sliver_bottom_safe_area.dart';
import '../../generic_widgets/spinner.dart';
import '../../l10n/generated/app_localizations.dart';
import 'iap_product.dart';
import 'iap_refund_screen.dart';
import 'iap_tile.dart';

class IAPScreen extends StatelessWidget {
  const IAPScreen({super.key});

  static const routeName = 'iapScreen';

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: FutureBuilder<List<IAPProduct>?>(
        future: DependencyInjector.iapBloc.fetchProducts(
          IAPFeature.standardNonConsumableProducts,
        ),
        builder: (context, snapshot) {
          if (!snapshot.hasData) {
            return _buildNoProducts(context, snapshot.connectionState);
          }

          final products = snapshot.data;
          return _buildScrollView(context, products!);
        },
      ),
    );
  }

  Widget _buildNoProducts(BuildContext context, ConnectionState state) {
    return Stack(
      children: [
        CustomScrollView(
          shrinkWrap: true,
          slivers: [
            PlatformSliverAppBar(
              title: AppLocalizations.of(context)!.unlockPremiumFeatures,
            ),
          ],
        ),
        SizedBox.fromSize(
          size: MediaQuery.of(context).size,
          child: Align(
            alignment: const Alignment(0, 0.1),
            child: state == ConnectionState.waiting
                ? const Spinner()
                : Text(
                    AppLocalizations.of(context)!.noProductsAvailable,
                    style: Theme.of(context).textTheme.headlineSmall,
                  ),
          ),
        ),
      ],
    );
  }

  Widget _buildScrollView(BuildContext context, List<IAPProduct> products) {
    return CustomScrollView(
      slivers: [
        PlatformSliverAppBar(
          title: AppLocalizations.of(context)!.unlockPremiumFeatures,
        ),
        SliverToBoxAdapter(
          child: Padding(
            padding: const EdgeInsets.only(top: 8.0),
            child: Center(
              child: Text(
                AppLocalizations.of(context)!.oneTimePurchase,
                style: Theme.of(context).textTheme.titleSmall,
              ),
            ),
          ),
        ),
        SliverPadding(
          padding: const EdgeInsets.symmetric(horizontal: 8.0),
          sliver: SliverList(
            delegate: SliverChildBuilderDelegate((context, i) {
              final item = products[i];
              return Padding(
                padding: const EdgeInsets.symmetric(
                  vertical: 12,
                ),
                child: IAPTile(product: item),
              );
            }, childCount: products.length),
          ),
        ),
        if (Platform.isIOS) _buildRefundTile(context),
        const SliverBottomSafeArea(),
      ],
    );
  }

  Widget _buildRefundTile(BuildContext context) {
    return ResponsiveSliverPadding(
      context: context,
      fillToEdgeOnPhone: true,
      sliver: SliverList.list(
        children: [
          SeparatedTile(
            divideTop: true,
            divideBottom: true,
            child: ListTile(
              title: Text(AppLocalizations.of(context)!.requestARefund),
              trailing: Icon(Icons.adaptive.arrow_forward),
              onTap: () => Navigator.of(context).pushMaterialRoute(
                name: '$routeName/refund',
                builder: (BuildContext context) => const IAPRefundScreen(),
              ),
            ),
          ),
          const SizedBox(height: 8)
        ],
      ),
    );
  }
}
