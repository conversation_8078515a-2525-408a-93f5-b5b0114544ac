import 'package:collection/collection.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';

import '../../dependency_injection/dependency_injector.dart';
import '../../generic_widgets/platform_aware/platform_filled_button.dart';
import '../../generic_widgets/platform_aware/platform_sliver_app_bar.dart';
import '../../generic_widgets/platform_aware/platform_text_button.dart';
import '../../generic_widgets/responsive_padding.dart';
import '../../generic_widgets/spinner.dart';
import '../../l10n/generated/app_localizations.dart';
import '../authentication/login_screen.dart';
import 'iap_product.dart';
import 'iap_status.dart';

class VisitedSubscriptionSoftSell extends StatefulWidget {
  const VisitedSubscriptionSoftSell({super.key});

  @override
  State<VisitedSubscriptionSoftSell> createState() =>
      _VisitedSubscriptionSoftSellState();
}

class _VisitedSubscriptionSoftSellState
    extends State<VisitedSubscriptionSoftSell> {
  List<IAPProduct>? _visitedProOptions;
  IAPProduct? _selectedSubscription;

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();

    if (_visitedProOptions != null) {
      return;
    }

    DependencyInjector.iapBloc
        .fetchProducts(IAPFeature.visitedProOptions)
        .then((products) {
          if (products == null) {
            if (mounted) {
              Navigator.maybePop(context);
            }
            return;
          }

          final selectedProduct =
              _findSelectedProProduct(products) ??
              products.firstWhereOrNull((product) {
                return product.feature == IAPFeature.subscriptionProAnnual;
              });

          setState(() {
            _visitedProOptions = products;
            _selectedSubscription = selectedProduct;
          });
        })
        .catchError((error, stack) {
          if (mounted) {
            Navigator.of(context).pop();
          }
        });
  }

  IAPProduct? _findSelectedProProduct(List<IAPProduct> products) {
    for (final proFeature in IAPFeature.visitedProOptions) {
      if (DependencyInjector.iapBloc.currentStatus.containsFeature(
        proFeature,
      )) {
        final product = products.firstWhereOrNull(
          (product) => product.feature == proFeature,
        );

        if (product != null) {
          return product;
        }
      }
    }

    return null;
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: _visitedProOptions == null
          ? const Center(child: Spinner())
          : _buildProOptions(_visitedProOptions!),
    );
  }

  bool get isModal =>
      (ModalRoute.of(context) as MaterialPageRoute?)?.fullscreenDialog ?? false;

  Widget _buildProOptions(List<IAPProduct> visitedProProducts) {
    return Column(
      children: [
        _buildSubscriptionDescription(visitedProProducts),
        _buildProductPicker(),
        const SizedBox(height: 4),
        _buildPurchaseAndRestoreButtons(),
        const SizedBox(height: 16),
      ],
    );
  }

  Widget _buildProductPicker() {
    return Column(
      children: [
        Divider(color: Theme.of(context).cardColor, height: 12),
        Text(
          AppLocalizations.of(context)!.chooseYourPlan,
          style: const TextStyle(fontWeight: FontWeight.bold),
        ),
        const SizedBox(height: 8),
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 8),
          child: IntrinsicHeight(
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              spacing: 16,
              children: [
                for (final feature in [
                  IAPFeature.subscriptionProMonthly,
                  IAPFeature.subscriptionProAnnual,
                  IAPFeature.visitedPro,
                ])
                  _buildSelectableProduct(feature),
              ],
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildPurchaseAndRestoreButtons() {
    return StreamBuilder<IAPStatus>(
      stream: DependencyInjector.iapBloc.status,
      builder: (context, snapshot) {
        final subscribed = snapshot.data?.purchasedPro ?? false;

        return Padding(
          padding: const EdgeInsets.all(4.0),
          child: Column(
            children: [
              _buildPurchaseButton(context, subscribed),
              _buildRestorePurchases(context),
            ],
          ),
        );
      },
    );
  }

  Widget _buildPurchaseButton(BuildContext context, bool subscribed) {
    final localizations = AppLocalizations.of(context)!;
    return PlatformFilledButton(
      title: subscribed ? localizations.purchased : localizations.buyNow,
      onTapped: subscribed || _selectedSubscription == null
          ? null
          : () async {
              await SpinnerDialog.showDuringLongProcess(
                context,
                job: () =>
                    DependencyInjector.iapBloc.purchase(_selectedSubscription!),
              );

              if (!context.mounted) {
                return;
              }

              if (isModal) {
                Navigator.of(context).pop();
              }
            },
    );
  }

  Widget _buildRestorePurchases(BuildContext context) {
    return PlatformTextButton(
      title: AppLocalizations.of(context)!.restorePurchases,
      onTapped: () async {
        await SpinnerDialog.showDuringLongProcess(
          context,
          job: DependencyInjector.iapBloc.restorePurchase,
        );

        final selected = _findSelectedProProduct(_visitedProOptions!);
        setState(() {
          _selectedSubscription = selected;
        });
      },
    );
  }

  Widget _buildSubscriptionDescription(List<IAPProduct> visitedProProducts) {
    return Expanded(
      child: Scrollbar(
        child: CustomScrollView(
          slivers: [
            PlatformSliverAppBar(
              title: AppLocalizations.of(context)!.unlockPremiumFeatures,
            ),
            ResponsiveSliverPadding(
              context: context,
              fillToEdgeOnPhone: false,
              sliver: SliverList.list(
                children: [
                  Text(
                    visitedProProducts
                        .firstWhere(
                          (product) => product.feature == IAPFeature.visitedPro,
                        )
                        .subtitle,
                    textAlign: TextAlign.center,
                    style: Theme.of(context).textTheme.titleLarge,
                  ),
                  const SizedBox(height: 24),
                ],
              ),
            ),
            ResponsiveSliverPadding(
              context: context,
              fillToEdgeOnPhone: false,
              sliver: DecoratedSliver(
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(8.0),
                  color: Theme.of(context).disabledColor.withAlpha(16),
                ),
                sliver: SliverPadding(
                  padding: const EdgeInsets.all(8.0),
                  sliver: SliverList.list(children: _buildFeatureTiles()),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSelectableProduct(IAPFeature feature) {
    final product = _visitedProOptions?.firstWhereOrNull(
      (product) => product.feature == feature,
    );
    if (product == null) {
      return const SizedBox();
    }

    final selected = _selectedSubscription?.feature == feature;
    final textColour = selected
        ? Colors.black
        : Theme.of(context).textTheme.bodyMedium?.color;

    return Expanded(
      child: GestureDetector(
        onTap: () {
          setState(() {
            _selectedSubscription = product;
          });
        },
        child: AnimatedContainer(
          decoration: BoxDecoration(
            color: selected
                ? LoginScreen.confirmButtonColour
                : Theme.of(context).disabledColor.withAlpha(32),
            borderRadius: BorderRadius.circular(8),
            boxShadow: [
              if (selected)
                const BoxShadow(
                  offset: Offset(0, 0),
                  blurRadius: 10,
                  spreadRadius: 3,
                  color: Colors.white,
                  blurStyle: BlurStyle.inner,
                ),
            ],
          ),
          padding: const EdgeInsets.all(16),
          duration: const Duration(milliseconds: 150),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            crossAxisAlignment: CrossAxisAlignment.center,

            children: [
              Text(
                feature == IAPFeature.visitedPro
                    ? AppLocalizations.of(context)!.lifetime
                    : product.title,
                textAlign: TextAlign.center,
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.w700,
                  color: textColour,
                ),
              ),
              Text(
                _buildPrice(product),
                textAlign: TextAlign.center,
                style: Theme.of(
                  context,
                ).textTheme.bodySmall?.copyWith(color: textColour),
              ),
            ],
          ),
        ),
      ),
    );
  }

  String _buildPrice(IAPProduct product) {
    final buffer = StringBuffer();
    buffer.write(product.price);

    final timeScale = product.subscription?.period.timeScale.name;
    if (timeScale != null) {
      buffer.write('/$timeScale');
    }
    return buffer.toString();
  }

  List<Widget> _buildFeatureTiles() {
    return [
      Row(
        children: [
          for (final feature in [
            IAPFeature.unlockCities,
            IAPFeature.unlockRegions,
            IAPFeature.unlockItineraries,
          ])
            Expanded(child: _buildFeature(feature)),
        ],
      ),
      const SizedBox(height: 16),
      Row(
        children: [
          Expanded(
            child: _buildFeatureTile(
              title: AppLocalizations.of(context)!.unlockLived,
              svgPath: 'assets/images/lived_feature.svg',
            ),
          ),
          for (final feature in [
            IAPFeature.unlockInspirations,
            IAPFeature.removeAds,
          ])
            Expanded(child: _buildFeature(feature)),
        ],
      ),
      const SizedBox(height: 16),
      _buildFeatureTile(
        svgPath: 'assets/images/future_features.svg',
        title: AppLocalizations.of(context)!.futureFeaturesDescription,
      ),
    ];
  }

  Widget _buildFeature(IAPFeature feature) {
    return FutureBuilder<IAPProduct?>(
      future: DependencyInjector.iapBloc.fetchProduct(feature),
      builder: (context, snapshot) {
        final product = snapshot.data;
        if (product == null) {
          return const SizedBox();
        }

        return _buildFeatureTile(
          svgPath: feature.assetPathSvg,
          title: product.title,
        );
      },
    );
  }

  Widget _buildFeatureTile({required String svgPath, required String title}) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        SvgPicture.asset(svgPath, height: 75),
        Text(title, textAlign: TextAlign.center),
      ],
    );
  }
}
