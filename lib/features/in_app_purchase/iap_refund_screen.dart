import 'package:collection/collection.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:in_app_purchase/in_app_purchase.dart';

import '../../dependency_injection/dependency_injector.dart';
import '../../generic_widgets/platform_aware/platform_sliver_app_bar.dart';
import '../../generic_widgets/responsive_padding.dart';
import '../../generic_widgets/shiny_visited_logo.dart';
import '../../generic_widgets/spinner.dart';
import '../../l10n/generated/app_localizations.dart';
import 'iap_product.dart';

class IAPRefundScreen extends StatelessWidget {
  const IAPRefundScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: CustomScrollView(
        slivers: [
          PlatformSliverAppBar(
            title: AppLocalizations.of(context)!.requestARefund,
          ),
          FutureBuilder<List<PurchaseDetails>>(
            future: DependencyInjector.iapBloc.fetchRawPurchases(),
            builder: (context, snapshot) {
              if (snapshot.connectionState == ConnectionState.waiting) {
                return _buildWaiting();
              }

              final purchases = snapshot.data;
              if (purchases == null) {
                return _buildNoPurchases(context);
              }

              return _buildPurchases(purchases);
            },
          ),
        ],
      ),
    );
  }

  Widget _buildPurchases(List<PurchaseDetails> purchases) {
    return FutureBuilder<List<IAPProduct>?>(
      future: DependencyInjector.iapBloc.fetchProducts(),
      builder: (context, snapshot) {
        final products = snapshot.data;
        if (products == null) {
          return const SliverToBoxAdapter(child: SizedBox());
        }

        return ResponsiveSliverPadding(
          context: context,
          sliver: SliverList.separated(
            itemCount: purchases.length,
            separatorBuilder: (_, _) =>
                Divider(color: Theme.of(context).colorScheme.primary),
            itemBuilder: (context, index) {
              final purchase = purchases[index];
              final feature = IAPFeature.fromBundleId(purchase.productID);
              final product = products.firstWhereOrNull(
                (product) => product.feature == feature,
              );
              if (product == null || feature == null) {
                return const SizedBox();
              }

              return _buildPurchaseTile(feature, product, purchase);
            },
          ),
        );
      },
    );
  }

  Widget _buildPurchaseTile(
    IAPFeature feature,
    IAPProduct product,
    PurchaseDetails purchase,
  ) {
    return ListTile(
      onTap: () => DependencyInjector.iapBloc.requestRefund(purchase),
      leading: feature == IAPFeature.visitedPro
          ? const ShinyVisitedLogo(size: 40)
          : SvgPicture.asset(
              feature.assetPathSvg,
              width: 40,
              height: 40,
              fit: BoxFit.fitHeight,
            ),
      title: Text(product.title),
      trailing: Icon(Icons.adaptive.arrow_forward),
    );
  }

  Widget _buildNoPurchases(BuildContext context) {
    return SliverFillRemaining(
      child: Center(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Padding(
              padding: const EdgeInsets.all(16.0),
              child: Icon(
                Icons.error_outline_rounded,
                color: Theme.of(context).colorScheme.primary,
              ),
            ),
            Text(
              AppLocalizations.of(context)!.noPurchasesFound,
              style: Theme.of(context).textTheme.titleMedium,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildWaiting() {
    return const SliverFillRemaining(child: Center(child: Spinner()));
  }
}
