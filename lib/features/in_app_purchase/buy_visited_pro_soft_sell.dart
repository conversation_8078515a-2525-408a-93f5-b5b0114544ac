import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';

import '../../dependency_injection/dependency_injector.dart';
import '../../generic_widgets/navigation_extensions.dart';
import '../../generic_widgets/platform_aware/platform_app_bar.dart';
import '../../generic_widgets/platform_aware/platform_filled_button.dart';
import '../../generic_widgets/platform_aware/platform_text_button.dart';
import '../../generic_widgets/responsive_padding.dart';
import '../../generic_widgets/shiny_visited_logo.dart';
import '../../generic_widgets/spinner.dart';
import '../../l10n/generated/app_localizations.dart';
import '../../models/device.dart';
import 'iap_product.dart';
import 'iap_status.dart';
import 'visted_subscription_soft_sell.dart';

class BuyVisitedProSoftSell extends StatelessWidget {
  const BuyVisitedProSoftSell({super.key});

  static const routeName = '/proSoftSell';

  static Future<bool?> present(BuildContext context) {
    return Navigator.of(context).pushMaterialRoute<bool>(
        name: routeName,
        fullscreen: true,
        builder: (_) => const BuyVisitedProSoftSell());
  }

  @override
  Widget build(BuildContext context) {
    final isModal =
        (ModalRoute.of(context) as MaterialPageRoute?)?.fullscreenDialog ??
            false;

    return Scaffold(
      body: FutureBuilder<bool>(
          future: DependencyInjector.featureFlags.enableSubscriptions(),
          builder: (context, snapshot) {
            if (snapshot.data == true) {
              return const VisitedSubscriptionSoftSell();
            }

            return _buildNonConsumableSoftSell(isModal);
          }),
    );
  }

  FutureBuilder<IAPProduct?> _buildNonConsumableSoftSell(bool isModal) {
    return FutureBuilder<IAPProduct?>(
      future: DependencyInjector.iapBloc.fetchProduct(IAPFeature.visitedPro),
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.waiting) {
          return const Center(child: Spinner());
        }

        final visitedPro = snapshot.data;

        if (visitedPro == null) {
          return _buildError(context);
        }

        return Scaffold(
          appBar: _buildAppBar(
            context: context,
            visitedPro: visitedPro,
            isModal: isModal,
          ),
          body: SafeArea(
            bottom: false,
            child: _buildProductDetails(
              context,
              visitedPro,
            ),
          ),
        );
      },
    );
  }

  PreferredSizeWidget _buildAppBar({
    required BuildContext context,
    required IAPProduct visitedPro,
    required bool isModal,
  }) {
    return PlatformAppBar(
      leading: isModal ? const SizedBox() : null,
      title: visitedPro.title,
      backgroundColour:
          isModal ? Theme.of(context).scaffoldBackgroundColor : null,
      action: isModal
          ? CloseButton(
              onPressed: Navigator.of(context).pop,
              style: const ButtonStyle(
                foregroundColor: WidgetStatePropertyAll(Colors.white),
                backgroundColor: WidgetStatePropertyAll(Colors.black),
              ),
            )
          : null,
    );
  }

  Widget _buildProductDetails(BuildContext context, IAPProduct visitedPro) {
    return ResponsivePadding(
      context: context,
      fillToEdgeOnPhone: false,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          _buildPromotionalCopy(context, visitedPro),
          _buildFooter(visitedPro),
          const SizedBox(height: 4),
        ],
      ),
    );
  }

  Widget _buildPromotionalCopy(BuildContext context, IAPProduct visitedPro) {
    return Expanded(
      child: Center(
        child: SizedBox(
          width: 250,
          child: ListView(
            children: [
              if (Device.isTablet(context))
                const SizedBox(height: 120)
              else
                const SizedBox(height: 16),
              const ShinyVisitedLogo(),
              _buildDescription(visitedPro, context),
              _buildBulletPoints(context),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildFooter(IAPProduct visitedPro) {
    return Padding(
      padding: const EdgeInsets.all(6.0),
      child: StreamBuilder<IAPStatus>(
        stream: DependencyInjector.iapBloc.status,
        builder: (context, snapshot) {
          final status = snapshot.data;
          if (status == null) {
            return const SizedBox();
          }

          if (status.purchasedPro) {
            return PlatformFilledButton(
              title: AppLocalizations.of(context)!.purchased,
            );
          }

          return _buildPurchaseButton(context, visitedPro);
        },
      ),
    );
  }

  Widget _buildPurchaseButton(BuildContext context, IAPProduct visitedPro) {
    return Column(
      children: [
        PlatformFilledButton(
          title: '${AppLocalizations.of(context)!.buy} - ${visitedPro.price}',
          onTapped: () async {
            await SpinnerDialog.showDuringLongProcess(
              context,
              job: () => DependencyInjector.iapBloc.purchase(visitedPro),
            );
            if (context.mounted) {
              Navigator.of(context)
                  .pop(DependencyInjector.iapBloc.hasUnlockedPro);
            }
          },
        ),
        PlatformTextButton(
          title: AppLocalizations.of(context)!.restorePurchases,
          onTapped: () => SpinnerDialog.showDuringLongProcess(
            context,
            job: DependencyInjector.iapBloc.restorePurchase,
          ),
        ),
      ],
    );
  }

  Widget _buildDescription(IAPProduct visitedPro, BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: Text(
        visitedPro.subtitle,
        textAlign: TextAlign.center,
        style: Theme.of(context)
            .textTheme
            .titleMedium
            ?.copyWith(fontWeight: FontWeight.w800),
      ),
    );
  }

  Widget _buildBulletPoints(BuildContext context) {
    final localizations = AppLocalizations.of(context)!;
    return Column(
      mainAxisAlignment: MainAxisAlignment.start,
      children: [
        _buildBulletPoint(
          context,
          svgPath: 'assets/images/lived_feature.svg',
          title: localizations.unlockLived,
          subtitle: localizations.unlockLivedDescription,
        ),
        for (final feature in IAPFeature.standardNonConsumableProducts)
          if (feature != IAPFeature.visitedPro)
            _buildFeatureDescription(feature: feature),
        _buildBulletPoint(
          context,
          svgPath: 'assets/images/future_features.svg',
          title: localizations.futureFeaturesDescription,
          subtitle: localizations.futureFeaturesDescription,
        ),
      ],
    );
  }

  Widget _buildFeatureDescription({
    required IAPFeature feature,
  }) {
    return FutureBuilder<IAPProduct?>(
        future: DependencyInjector.iapBloc.fetchProduct(feature),
        builder: (context, snapshot) {
          final product = snapshot.data;
          if (product == null) {
            return const SizedBox();
          }

          return _buildBulletPoint(
            context,
            svgPath: product.feature.assetPathSvg,
            title: product.title,
            subtitle: product.subtitle,
          );
        });
  }

  Widget _buildBulletPoint(
    BuildContext context, {
    required String svgPath,
    String? title,
    String? subtitle,
  }) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 6.0),
      child: Row(
        children: [
          if (svgPath.isNotEmpty)
            SizedBox(
              width: 50,
              child: SvgPicture.asset(
                svgPath,
                height: 50,
              ),
            ),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title ?? '',
                  style: Theme.of(context)
                      .textTheme
                      .titleMedium
                      ?.copyWith(fontWeight: FontWeight.w700),
                ),
                if (title != subtitle)
                  Text(
                    subtitle ?? '',
                    style: Theme.of(context).textTheme.bodyLarge,
                  ),
              ],
            ),
          )
        ],
      ),
    );
  }

  Widget _buildError(BuildContext context) {
    return Center(
      child: Column(
        mainAxisSize: MainAxisSize.min,
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          Text(AppLocalizations.of(context)!.errorTitle),
          PlatformFilledButton(
            title: AppLocalizations.of(context)!.cancel,
            onTapped: Navigator.of(context).pop,
          )
        ],
      ),
    );
  }
}
