class IAPException implements Exception {
  static const IAPException purchaseInProgress = IAPException._(
    'iap.purchaseInProgress',
  );
  static const IAPException unableToPurchase = IAPException._(
    'iap.unableToPurchase',
  );
  static const IAPException noProductsAvailable = IAPException._(
    'iap.noProductsAvailable',
  );

  final String id;
  const IAPException._(this.id);

  @override
  String toString() {
    return 'IAPException{id: $id}';
  }
}
