import 'dart:async';
import 'dart:io';

import 'package:flutter/foundation.dart';
import 'package:in_app_purchase/in_app_purchase.dart';
import 'package:in_app_purchase_android/in_app_purchase_android.dart'
    show GooglePlayPurchaseDetails;
import 'package:in_app_purchase_storekit/in_app_purchase_storekit.dart'
    show SK2PurchaseDetails;
import 'package:in_app_purchase_storekit/store_kit_wrappers.dart';

import '../../dependency_injection/dependency_injector.dart';
import '../../logger.dart';
import 'iap_exception.dart';
import 'iap_product.dart';
import 'iap_receipt.dart';
import 'iap_subscription.dart';

export 'iap_exception.dart';

class IAPService {
  late final client = DependencyInjector.client;

  InAppPurchase get _iap => InAppPurchase.instance;
  late StreamSubscription<List<PurchaseDetails>> _purchaseSubscription;

  List<IAPProduct>? _products;
  final _purchaseCompleters = <IAPFeature, Completer<bool>>{};

  bool _initialized = false;
  void Function(Set<String> bundleIds)? productRestoredCallback;

  Completer<List<PurchaseDetails>>? _rawProductsCompleter;

  Future<void> initialize() async {
    if (_initialized) {
      return;
    }

    _purchaseSubscription = _iap.purchaseStream.listen(_onPurchaseUpdated);

    try {
      await checkStoreConnection();
      _initialized = true;
      await cancelActiveTransactions();
    } catch (e, stacktrace) {
      log(e);
      log(stacktrace);
    }
  }

  Future<void> checkStoreConnection() async {
    try {
      final canBuy = await _iap.isAvailable();
      if (!canBuy) {
        final store = Platform.isIOS ? 'App Store' : 'Play Store';
        throw Exception(
          'Cannot reach the $store.  Please check your internet connection.',
        );
      }
    } catch (e) {
      log(e);
      _initialized = true;
      rethrow;
    }
  }

  Future<void> cancelActiveTransactions() async {
    if (!Platform.isIOS) {
      return;
    }

    // Clean up any left over transactions that can happen if the
    // the app gets into a weird state
    try {
      final transactions = await SKPaymentQueueWrapper().transactions();
      for (final transaction in transactions) {
        await SKPaymentQueueWrapper().finishTransaction(transaction);
      }
    } catch (e) {
      if (kDebugMode) {
        print(e);
      }
      rethrow;
    }
  }

  FutureOr<List<IAPProduct>?> fetchProducts() async {
    if (_products != null) {
      return _products;
    }

    if (!_initialized) {
      await initialize();
    }

    final knownBundleIds = IAPFeature.values.map((e) => e.bundleId).toSet();

    final response = await _iap.queryProductDetails(knownBundleIds);
    if (response.error != null) {
      log(response.error?.message ?? '');
      return null;
    }

    if (response.productDetails.isEmpty) {
      throw IAPException.noProductsAvailable;
    }

    _products ??= [];
    for (final details in response.productDetails) {
      final feature = IAPFeature.fromBundleId(details.id);
      if (feature == null) {
        continue;
      }

      // Avoid Duplicate products, which randomly happens sometimes...
      if (_products
              ?.where((element) => element.feature == feature)
              .isNotEmpty ??
          false) {
        continue;
      }

      _products?.add(
        IAPProduct(
          feature: feature,
          title: details.title
              .replaceAll(RegExp(r'\([^)]*\)'), '')
              .replaceAll(')', '')
              .trim(), // Remove extra brackets that can be added by some stores
          subtitle: details.description,
          bundleId: details.id,
          price: details.price,
          subscription: IAPSubscription.fromProductDetails(details),
          underlyingProduct: details,
        ),
      );
    }

    return _products;
  }

  Future<bool> purchase(IAPProduct product) async {
    var completer = _purchaseCompleters[product.feature];
    if (completer != null) {
      throw IAPException.purchaseInProgress;
    }

    completer = Completer();
    _purchaseCompleters[product.feature] = completer;
    _processPurchase(product);
    return completer.future;
  }

  void _processPurchase(IAPProduct product) async {
    final completer = _purchaseCompleters[product.feature];
    assert(completer != null);
    try {
      // The process doesn't actually finish here. Look at the delegate method
      // to find where the IAP purchase actually completes
      final success = await _iap.buyNonConsumable(
        purchaseParam: PurchaseParam(productDetails: product.underlyingProduct),
      );

      if (!success) {
        completer?.completeError(IAPException.unableToPurchase);
        _purchaseCompleters.remove(product.feature);
      }
    } catch (error, stacktrace) {
      completer?.completeError(error, stacktrace);
      _purchaseCompleters.remove(product.feature);
    }
  }

  Future<void> restorePurchases() async {
    try {
      _iap.restorePurchases();
    } catch (e, stacktrace) {
      log(e);
      log(stacktrace);
      rethrow;
    }
  }

  Future<List<PurchaseDetails>> fetchRawPurchases() {
    if (_rawProductsCompleter != null) {
      throw Exception('Fetching Raw Products in progress');
    }

    final completer = Completer<List<PurchaseDetails>>();
    _rawProductsCompleter = completer;

    try {
      _iap.restorePurchases();
      return completer.future;
    } catch (e, stacktrace) {
      log(e);
      log(stacktrace);
      _rawProductsCompleter = null;
      rethrow;
    }
  }

  void _onPurchaseUpdated(List<PurchaseDetails> details) async {
    if (_rawProductsCompleter != null) {
      _rawProductsCompleter?.complete(details);
      _rawProductsCompleter = null;
    }

    final restoredPurchases = <String>{};

    for (final purchase in details) {
      if (purchase.status == PurchaseStatus.pending) {
        continue;
      }

      final feature = _findIAPFeature(purchase);
      if (feature == null) {
        continue;
      }

      if (purchase.status == PurchaseStatus.restored) {
        restoredPurchases.add(purchase.productID);
        // Continue with the regular flow, since StoreKit2 doesn't
        // differentiate between restored and original purchases
      }

      final completer = _purchaseCompleters[feature];

      if (purchase.status == PurchaseStatus.error ||
          purchase.status == PurchaseStatus.canceled) {
        _handleFailedPurchase(purchase, feature, completer);
      }

      if (purchase.status == PurchaseStatus.purchased ||
          purchase is SK2PurchaseDetails &&
              purchase.status == PurchaseStatus.restored) {
        await _handleSuccessfulPurchase(purchase, completer);
      }

      _purchaseCompleters.remove(feature);
    }

    if (restoredPurchases.isNotEmpty) {
      productRestoredCallback?.call(restoredPurchases);
    }
  }

  Future<void> _handleSuccessfulPurchase(
    PurchaseDetails purchase,
    Completer<bool>? completer,
  ) async {
    final receipt = _buildReceipt(purchase);
    await _updateBackend(receipt);

    if (purchase is GooglePlayPurchaseDetails) {
      // This is weirdly causing exceptions...
      await _iap.completePurchase(purchase);
    } else {
      await _iap.completePurchase(purchase);
    }

    if (completer?.isCompleted == false) {
      completer?.complete(true);
    }
  }

  void _handleFailedPurchase(
    PurchaseDetails purchase,
    IAPFeature feature,
    Completer<bool>? completer,
  ) {
    try {
      _iap.completePurchase(purchase);
    } finally {
      completer?.completeError(IAPException.unableToPurchase);
      _purchaseCompleters.remove(feature);
    }
  }

  IAPFeature? _findIAPFeature(PurchaseDetails details) {
    final feature = IAPFeature.fromBundleId(details.productID);
    if (feature != null) {
      return feature;
    }

    // An invalid product id was returned.  Try to find an active purchase instead.
    if (_purchaseCompleters.isEmpty) {
      return null;
    }

    return _purchaseCompleters.keys.first;
  }

  IAPReceipt _buildReceipt(PurchaseDetails purchase) {
    final receipt = IAPReceipt(
      id: purchase.purchaseID ?? '',
      productId: purchase.productID,
      timeStamp: purchase.transactionDate ?? '',
      localVerificationData: purchase.verificationData.localVerificationData,
      serverVerificationData: purchase.verificationData.serverVerificationData,
      source: purchase.verificationData.source.toString().split('.').last,
    );
    return receipt;
  }

  Future<void> _updateBackend(IAPReceipt receipt) async {
    //TODO: This will fail for android if we start validating receipts server side...
    const path = 'receiptValidation/apple';
    final body = {
      'productId': receipt.productId,
      'purchaseId': receipt.id,
      'token': receipt.serverVerificationData,
      'source': receipt.source,
    };

    final result = await client.post(path, body: body);
    return result;
  }

  Future<List<String>> fetchPurchases() async {
    try {
      final result = await client.get(
        'receiptValidation/purchases',
        cacheable: false,
      );
      return (result as List).cast<String>();
    } catch (e) {
      return [];
    }
  }

  void dispose() {
    _purchaseSubscription.cancel();
  }
}
