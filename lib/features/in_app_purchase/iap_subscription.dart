import 'dart:math';

import 'package:in_app_purchase/in_app_purchase.dart';
import 'package:in_app_purchase_android/billing_client_wrappers.dart';
import 'package:in_app_purchase_android/in_app_purchase_android.dart';
import 'package:in_app_purchase_storekit/in_app_purchase_storekit.dart';
import 'package:in_app_purchase_storekit/store_kit_wrappers.dart';

class IAPSubscription {
  final SubscriptionPeriod period;
  final SubscriptionPeriod? trial;

  const IAPSubscription({
    required this.period,
    this.trial,
  });

  bool get hasTrial => trial != null;

  static IAPSubscription? fromProductDetails(ProductDetails details) {
    return switch (details) {
      final AppStoreProductDetails details => _buildAppleSubscription(details),
      final GooglePlayProductDetails details =>
        _buildGoogleSubscription(details),
      _ => null
    };
  }

  static IAPSubscription? _buildAppleSubscription(
      AppStoreProductDetails details) {
    final skProduct = details.skProduct;
    final skSubscriptionPeriod = skProduct.subscriptionPeriod;
    if (skSubscriptionPeriod == null) {
      return null;
    }
    final period = SubscriptionPeriod.fromSKPeriod(skSubscriptionPeriod);

    final trial = details.skProduct.introductoryPrice;
    if (trial == null) {
      return IAPSubscription(period: period);
    }

    final discountPeriod =
        SubscriptionPeriod.fromSKPeriod(trial.subscriptionPeriod);

    return IAPSubscription(
      period: period,
      trial: discountPeriod,
    );
  }

  static IAPSubscription? _buildGoogleSubscription(
    GooglePlayProductDetails googleProduct,
  ) {
    // If you are wondering why this code is so confusing then take a look at the note
    // in 'iap_service.dart' on finding the subscription price.
    // Google organizes Subscription IAP products as a container instead of
    // using iOS's subscription group concept
    // Subscriptions have multiple offers, which intern have multiple Pricing Phases
    // An offer with multiple phases is likely the one that has the introductory offer,
    // but is is more a fragile convention rather than something strictly enforced by the api

    final details = googleProduct.productDetails;
    if (details.productType != ProductType.subs) {
      return null;
    }

    final plans = details.subscriptionOfferDetails
        ?.map((plan) => plan.pricingPhases
            .map((e) => SubscriptionPeriod.fromPricingWrapper(e))
            .toList())
        .toList();

    if (plans == null || plans.isEmpty) {
      return null;
    }

    final largestIndex = plans.map((e) => e.length).toList().largestIndex;
    final periods = plans[largestIndex];
    late SubscriptionPeriod actualCost;
    SubscriptionPeriod? trial;

    for (final period in periods) {
      if (period.units == 1) {
        trial = period;
      } else {
        actualCost = period;
      }
    }

    return IAPSubscription(
      period: actualCost,
      trial: trial,
    );
  }
}

class SubscriptionPeriod {
  final int units;
  final TimeScale timeScale;

  SubscriptionPeriod(this.units, this.timeScale);

  SubscriptionPeriod.fromSKPeriod(SKProductSubscriptionPeriodWrapper period)
      : units = period.numberOfUnits,
        timeScale = TimeScale.fromPeriodUnit(period.unit);

  String get inDays {
    if (units == 1 && timeScale == TimeScale.week) {
      return '7-day';
    }

    return '$units-${timeScale.name}';
  }

  SubscriptionPeriod.fromPricingWrapper(PricingPhaseWrapper pricingPlan)
      : units = pricingPlan.billingCycleCount,
        timeScale = TimeScale.fromBillingPeriod(pricingPlan.billingPeriod);
}

enum TimeScale {
  day,
  week,
  month,
  year;

  static TimeScale fromPeriodUnit(SKSubscriptionPeriodUnit unit) {
    return switch (unit) {
      SKSubscriptionPeriodUnit.day => day,
      SKSubscriptionPeriodUnit.week => week,
      SKSubscriptionPeriodUnit.month => month,
      SKSubscriptionPeriodUnit.year => year,
    };
  }

  static TimeScale fromBillingPeriod(String billingPeriod) {
    if (billingPeriod.endsWith('D')) {
      return day;
    } else if (billingPeriod.endsWith('W')) {
      return week;
    } else if (billingPeriod.endsWith('M')) {
      return month;
    } else if (billingPeriod.endsWith('Y')) {
      return year;
    } else {
      throw Exception('Unknown Billing Period format: $billingPeriod');
    }
  }
}

extension on List<int> {
  int get largest => reduce(max);
  int get largestIndex {
    final max = largest;
    for (var i = 0; i <= length; i++) {
      if (this[i] == max) {
        return i;
      }
    }

    return -1;
  }
}
