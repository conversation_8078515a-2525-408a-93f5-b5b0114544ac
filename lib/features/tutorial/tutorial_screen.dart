import 'dart:ui';

import 'package:flutter/material.dart';
import 'package:video_player/video_player.dart';

import '../../generic_widgets/platform_aware/platform_app_bar.dart';
import '../../generic_widgets/spinner.dart';
import '../../l10n/generated/app_localizations.dart';
import '../authentication/login_background.dart';

class TutorialScreen extends StatefulWidget {
  static const routeName = 'tutorial_screen';

  const TutorialScreen({super.key});

  @override
  State<TutorialScreen> createState() => _TutorialScreenState();
}

class _TutorialScreenState extends State<TutorialScreen>
    with WidgetsBindingObserver {
  VideoPlayerController? _controller;

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addObserver(this);
    _loadVideo();
  }

  void _loadVideo() async {
    _controller ??= VideoPlayerController.asset(
      'assets/tutorial.mp4',
      videoPlayerOptions: VideoPlayerOptions(allowBackgroundPlayback: false),
    );
    await _controller?.initialize();
    await _controller?.play();

    _controller?.addListener(() {
      final video = _controller?.value;
      if (video == null) {
        return;
      }
      if (video.position >= video.duration) {
        Navigator.of(context).pop();
      }
    });

    setState(() {});
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.black,
      appBar: PlatformAppBar(
        title: AppLocalizations.of(context)!.getStarted,
      ),
      body: Stack(
        children: [
          ImageFiltered(
            imageFilter: ImageFilter.blur(
              sigmaX: 10,
              sigmaY: 10,
              tileMode: TileMode.decal,
            ),
            child: const LoginBackground(),
          ),
          _buildVideo(),
          _buildCloseButton(context)
        ],
      ),
    );
  }

  Widget _buildVideo() {
    return _controller == null
        ? const Center(child: Spinner())
        : Padding(
            padding: const EdgeInsets.all(40.0),
            child: SafeArea(
              child: Center(
                child: AspectRatio(
                  aspectRatio: _controller!.value.aspectRatio,
                  child: DecoratedBox(
                    decoration: const BoxDecoration(
                      boxShadow: [
                        BoxShadow(blurRadius: 8, offset: Offset(0, 4))
                      ],
                    ),
                    child: GestureDetector(
                      onTap: () {
                        final playing = _controller?.value.isPlaying ?? true;
                        playing ? _controller?.pause() : _controller?.play();
                      },
                      child: VideoPlayer(_controller!),
                    ),
                  ),
                ),
              ),
            ),
          );
  }

  Widget _buildCloseButton(BuildContext context) {
    return Positioned(
      top: 16,
      right: 16,
      child: SafeArea(
        child: CloseButton(
          style: const ButtonStyle(
              backgroundColor: WidgetStatePropertyAll(Colors.black26),
              foregroundColor: WidgetStatePropertyAll(Colors.white),
              shape: WidgetStatePropertyAll(CircleBorder())),
          onPressed: Navigator.of(context).pop,
        ),
      ),
    );
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    state == AppLifecycleState.resumed
        ? _controller?.play()
        : _controller?.pause();
  }

  @override
  void dispose() {
    _controller?.dispose();
    WidgetsBinding.instance.removeObserver(this);
    super.dispose();
  }
}
