import 'package:in_app_review/in_app_review.dart';

import '../../dependency_injection/dependency_injector.dart';

class RatingPrompter {
  static const _kLastRequestedDay = 'com.visited.lastPromptDay';
  static const _kMinPromptDays = 7;

  const RatingPrompter();

  void prompt() async {
    if (await _checkIfShouldPrompt()) {
      _showRatingDialog();
    }
  }

  Future<bool> _checkIfShouldPrompt() async {
    if (await InAppReview.instance.isAvailable() == false) {
      return false;
    }

    final dateString = await DependencyInjector.sharedPrefsStorage.get(
      _kLastRequestedDay,
    );
    if (dateString == null) {
      _saveCurrentDate();
      return false;
    }

    DateTime date;
    try {
      date = DateTime.parse(dateString);
    } catch (_) {
      _saveCurrentDate();
      return false;
    }

    final daysSinceLastCheck = DateTime.now().difference(date).inDays;

    if (daysSinceLastCheck < _kMinPromptDays) {
      return false;
    }

    return true;
  }

  void _showRatingDialog() {
    InAppReview.instance.requestReview();
  }

  void _saveCurrentDate() {
    final dateString = DateTime.now().toIso8601String();
    DependencyInjector.sharedPrefsStorage.put(_kLastRequestedDay, dateString);
  }
}
