import 'dart:io';

import 'package:flutter/material.dart';
import 'package:google_mobile_ads/google_mobile_ads.dart';

import '../../logger.dart';

class AdManager {
  static void init() async {
    try {
      final googleAds = MobileAds.instance;
      await googleAds.initialize();
    } catch (e) {
      log(e);
    }
  }
}

class AdLocation {
  final String _adId;

  const AdLocation._private(this._adId);

  static const testAd = AdLocation._private(
    'ca-app-pub-3940256099942544/3986624511',
  );

  static AdLocation get areaDetails => Platform.isIOS
      ? const AdLocation._private('ca-app-pub-9385283247860729/3772426531')
      : const AdLocation._private('ca-app-pub-9385283247860729/4872048923');

  static AdLocation get dashboard => Platform.isIOS
      ? const AdLocation._private('ca-app-pub-9385283247860729/4885008284')
      : const AdLocation._private('ca-app-pub-9385283247860729/8928172994');

  static AdLocation get experienceDashboard => Platform.isIOS
      ? const AdLocation._private('ca-app-pub-9385283247860729/3089900257')
      : const AdLocation._private('ca-app-pub-9385283247860729/7993036282');

  static AdLocation get experiences => Platform.isIOS
      ? const AdLocation._private('ca-app-pub-9385283247860729/1038017213')
      : const AdLocation._private('ca-app-pub-9385283247860729/6185130596');

  static AdLocation get listScreen => Platform.isIOS
      ? const AdLocation._private('ca-app-pub-9385283247860729/5907635288')
      : const AdLocation._private('ca-app-pub-9385283247860729/8297617601');
}

class AdView extends StatefulWidget {
  final AdLocation location;

  const AdView({super.key, required this.location});

  @override
  State createState() => _AdViewState();
}

class _AdViewState extends State<AdView> {
  bool _loaded = false;
  bool tryToShowAd = true;
  AdWithView? _ad;

  // Keeping if in case we need to test ads
  // final _testAd = '/21775744923/example/native';

  @override
  Widget build(BuildContext context) {
    if (!tryToShowAd) {
      return const SizedBox();
    }

    return LayoutBuilder(
      builder: (context, constraints) {
        if (!_loaded) {
          _ad = buildAd(Size(constraints.biggest.width, 360));
          _ad?.load();
        }

        return Container(
          height: 360,
          color: Theme.of(context).disabledColor.withValues(alpha: 0.2),
          child: _loaded ? AdWidget(ad: _ad!) : null,
        );
      },
    );
  }

  AdWithView buildAd(Size size) {
    return NativeAd(
      adUnitId: widget.location._adId,
      listener: NativeAdListener(
        onAdLoaded: _onAdLoaded,
        onAdFailedToLoad: _onAdFailedToLoad,
      ),
      request: const AdRequest(),
      nativeTemplateStyle: NativeTemplateStyle(
        // Required: Choose a template.
        templateType: TemplateType.medium,
        // Optional: Customize the ad's style.
        mainBackgroundColor: Theme.of(context).cardColor,
        cornerRadius: 10.0,
        callToActionTextStyle: NativeTemplateTextStyle(
          textColor: Theme.of(context).textTheme.bodyMedium!.color,
          // backgroundColor: Colors.red,
          // style: NativeTemplateFontStyle.monospace,
          size: 16.0,
        ),
        primaryTextStyle: NativeTemplateTextStyle(
          textColor: Theme.of(context).textTheme.bodyMedium!.color,
          size: 15.0,
        ),
        secondaryTextStyle: NativeTemplateTextStyle(
          textColor: Theme.of(context).textTheme.bodyMedium!.color,
          style: NativeTemplateFontStyle.bold,
          size: 13.0,
        ),
        tertiaryTextStyle: NativeTemplateTextStyle(
          textColor: Theme.of(context).textTheme.bodyMedium!.color,
          size: 13.0,
        ),
      ),
      customOptions: {'width': size.width, 'height': size.height},
    );
  }

  void _onAdLoaded(Ad ad) {
    setState(() {
      _loaded = true;
    });
  }

  void _onAdFailedToLoad(Ad ad, LoadAdError error) {
    log(error);

    setState(() {
      tryToShowAd = false;
    });
  }

  @override
  void dispose() {
    _ad?.dispose();
    super.dispose();
  }
}
