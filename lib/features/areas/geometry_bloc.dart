import 'dart:async';
import 'dart:math';
import 'dart:ui';

import 'package:collection/collection.dart';
import 'package:latlong2/latlong.dart';

import '../../caching/database.dart';
import '../../dependency_injection/dependency_injector.dart';
import '../../generic_widgets/bloc.dart';
import '../../models/coordinate.dart';
import '../../models/geo_area.dart';
import '../../models/geo_bounds.dart';
import '../../models/polygon.dart';
import '../disputed_territories/disputed_territories_service.dart';
import '../map/coordinate_transformer.dart';
import '../map/projection.dart';
import '../map/tiles/internal/geometry_repository.dart';
import '../map/tiles/tile_constants.dart';
import 'area_bloc.dart';
import 'enclosed_area_lookup.dart';

class GeometryBloc implements Bloc {
  GeometryBloc();

  late final _geometryRepo = DependencyInjector.geometryRepository;
  late final AreaBloc _areaBloc = DependencyInjector.areaBloc;

  VisitedDatabase get database => DependencyInjector.database;

  Set<EnclosedAreaLookup>? _enclosedAreas;

  DisputedTerritoriesService get _disputedTerritoriesService =>
      DependencyInjector.disputedTerritoriesService;

  Future<PolygonLookup> fetchCountryPolygons({bool lowRes = false}) async {
    await _fetchWhiteList();
    await _disputedTerritoriesService.fetchDisputedTerritories();
    final countryPolygons =
        await _geometryRepo.allCountryPolygons(lowRes: lowRes);
    final currentPolygons =
        await _updateCountriesWithDisputedPreferences(countryPolygons);

    return currentPolygons;
  }

  Future<PolygonLookup> _updateCountriesWithDisputedPreferences(
    PolygonLookup areas,
  ) async {
    final updatedLookup = {...areas};
    final disputedAdjustments =
        await _disputedTerritoriesService.topLevelDisputedPreferences();

    updatedLookup.removeWhere(
      (isoCode, _) => disputedAdjustments.removeIsoCodes.contains(isoCode),
    );

    for (final addition in disputedAdjustments.add) {
      final area = await _areaBloc.areaByIsoCode(addition.isoCode);
      if (area == null) {
        continue;
      }
      final parent = await _areaBloc.fetchParent(area);
      if (parent == null) {
        continue;
      }
      final allPolygons = await fetchPolygonsForArea(parent);
      final polygons = allPolygons.$1[area.isoCode];

      if (polygons == null) {
        continue;
      }

      updatedLookup[area.isoCode] = polygons;
    }

    final alternatives =
        await _disputedTerritoriesService.requestedAlternativeGeometry();
    if (alternatives.isNotEmpty) {
      final isoCodes = alternatives.map((e) => e.isoCode).toList();
      final geometry =
          await _geometryRepo.loadNestedPolygonsWithSemaphore('disputed');
      for (final iso in isoCodes) {
        final lookup = geometry[iso];
        if (lookup == null) {
          continue;
        }

        updatedLookup.addAll(lookup);
      }
    }

    return updatedLookup;
  }

  Future<(PolygonLookup, GeoBounds?)> fetchPolygonsForArea(
    GeoArea area,
  ) async {
    GeoBounds? customBounds;

    if (area.hasSubdivisions) {
      final polygons = await _geometryRepo.allSubdivisionPolygons(area);
      if (polygons == null) {
        throw Exception('Cannot find polygons for [$area]');
      }

      final areas = await _areaBloc.fetchSubdivisions(area);

      // User has altered this country's geography
      if (areas.length != polygons.length) {
        await _applyDisputedTerritorySelectionsForSubdivisions(polygons, areas);
        customBounds = polygons.values.expand((e) => e).calculateBounds();
      }
      return (polygons, customBounds);
    }

    final allPolygons = await fetchCountryPolygons();

    final polygons = allPolygons[area.isoCode];
    if (polygons == null) {
      throw Exception('No polygon for $area');
    }

    return (
      {area.isoCode: polygons},
      customBounds,
    );
  }

  Future<void> _applyDisputedTerritorySelectionsForSubdivisions(
      PolygonLookup polygons, List<GeoArea> areas) async {
    polygons.removeWhere((isoCode, polygons) =>
        !areas.map((area) => area.isoCode).contains(isoCode));

    // Additions
    final additions = areas.toSet().difference(polygons.keys.toSet());
    for (final addition in additions) {
      if (addition.parentId != null) {
        final parent = await _areaBloc.fetchParent(addition);
        if (parent == null) {
          continue;
        }

        final subdivisions =
            await _geometryRepo.loadNestedPolygonsWithSemaphore('subdivisions');
        final parentPolygons = subdivisions[parent.isoCode];

        if (parentPolygons == null) {
          continue;
        }

        final replacement = parentPolygons[addition.isoCode];
        if (replacement == null) {
          continue;
        }

        polygons[addition.isoCode] = replacement;
      }
    }
  }

  Future<GeoArea?> geocodeWithCoordinate(
    LatLng latLng,
    int zoomLevel,
  ) async {
    final polygons = await _geometryRepo.allGeometry(
      includeSubdivisions:
          DependencyInjector.settingsBloc.currentSettings.showRegions &&
              zoomLevel >= TileConstants.subdivisionLevelOneThreshold,
    );

    final coordinate = Coordinate(
      latitude: latLng.latitude,
      longitude: latLng.longitude,
    );

    final foundAreas = await _findMatchingAreas(
      polygons,
      coordinate,
    );

    if (foundAreas.isEmpty) {
      return null;
    }

    if (!DependencyInjector.iapBloc.hasUnlockedRegions) {
      _removeNonFreeSubdivisions(foundAreas);
    }

    if (foundAreas.isEmpty) {
      return null;
    }

    if (foundAreas.length == 1) {
      return _applyDisputedTerritoryLogicToGeocodedArea(foundAreas.first);
    }

    // Find the correct subdivision level
    // Sort the results in hierarchical order
    foundAreas.sort(
      (a, b) => (a.parentId ?? -1).compareTo(b.parentId ?? -1),
    );

    if (zoomLevel < TileConstants.subdivisionLevelOneThreshold) {
      return foundAreas.first;
    } else if (zoomLevel < TileConstants.subdivisionLevelTwoThreshold) {
      return foundAreas[1];
    } else {
      return foundAreas.last;
    }
  }

  void _removeNonFreeSubdivisions(List<GeoArea> areas) {
    areas.removeWhere((area) {
      final parentId = area.parentId;
      if (parentId == null) {
        return false;
      }

      return !TileConstants.freeParentSubdivisionIds.contains(parentId);
    });
  }

  Future<GeoArea> _applyDisputedTerritoryLogicToGeocodedArea(
      GeoArea area) async {
    // Check to see if the country has been demoted and we are looking at topLevel
    if (area.parentId != null) {
      return area;
    }

    final disputedBloc = DependencyInjector.disputedTerritoriesBloc;
    await disputedBloc.initialize();
    final selections = disputedBloc.currentSelections;
    final selection = selections?.entries
        .firstWhereOrNull((entry) => entry.key.area.isoCode == area.isoCode);

    if (selection == null) {
      return area;
    }

    final selected = selection.value;

    if (selected.area.isoCode == area.isoCode) {
      return area;
    }

    final alternativeArea =
        await _areaBloc.areaByIsoCode(selected.area.isoCode);
    return alternativeArea ?? area;
  }

  Future<List<GeoArea>> _findMatchingAreas(
      PolygonLookup polygons, Coordinate coordinate) async {
    final foundAreas = <GeoArea>[];
    // Final all the areas that could match this point
    for (final entry in polygons.entries) {
      if (_isInArea(entry.value, coordinate)) {
        final potentialArea = await _areaBloc.areaByIsoCode(entry.key);

        if (potentialArea == null) {
          continue;
        }

        final area = await _checkIfLandlockedArea(potentialArea, coordinate);

        foundAreas.add(area);
      }
    }
    return foundAreas;
  }

  /// Only works with the old style map. Not the TiledMap
  Future<GeoArea?> geocode({
    required Point<num> position,
    required Size mapSize,
    GeoBounds bounds = GeoBounds.wholePlanet,
    PolygonLookup? searchableAreas,
  }) async {
    assert(_enclosedAreas != null,
        'Enclosed Whitelist missing.  Call _fetchWhiteList() first');

    final transformer = CoordinateTransformer(
      bounds: bounds,
      size: mapSize,
      projection: EquirectangularProjection(mapSize.width),
    );
    final coordinate = transformer.denormalize(position);

    // Very naive geo-coding algorithm, but it seems fast enough for now
    // Optimize this one if performance is lagging (especially on older devices)
    var areas = searchableAreas;
    areas ??= await fetchCountryPolygons();

    final polygons = areas.entries;

    for (final entry in polygons) {
      if (_isInArea(entry.value, coordinate)) {
        final area = await _areaBloc.areaByIsoCode(entry.key);
        if (area == null) {
          continue;
        }
        return _checkIfLandlockedArea(area, coordinate);
      }
    }

    return null;
  }

  bool _isInArea(List<Polygon> polygons, Coordinate coordinate) {
    for (final polygon in polygons) {
      if (polygon.contains(coordinate)) {
        return true;
      }
    }

    return false;
  }

  Future<void> _fetchWhiteList() async {
    if (_enclosedAreas != null) {
      return Future.value();
    }

    _enclosedAreas = await _areaBloc.fetchWhitelist();
  }

  Future<GeoArea> _checkIfLandlockedArea(
      GeoArea parent, Coordinate coordinate) async {
    if (_enclosedAreas == null) {
      await _fetchWhiteList();
    }

    return _checkIfLandlockedAreaSync(parent, coordinate);
  }

  GeoArea _checkIfLandlockedAreaSync(GeoArea parent, Coordinate coordinate) {
    assert(_enclosedAreas != null);

    try {
      final lookup = _enclosedAreas!.firstWhere(
          (element) => element.surroundedByIsoCode == parent.isoCode);
      final polygons = _geometryRepo.countryPolygonsSync(lookup.isoCode);
      if (polygons == null) {
        return parent;
      }

      if (_isInArea(polygons, coordinate)) {
        return _areaBloc.areaByIsoCodeSync(lookup.isoCode)!;
      }

      return parent;
    } catch (_) {
      return parent;
    }
  }

  @override
  void dispose() {}

  @override
  void clear() {}
}
