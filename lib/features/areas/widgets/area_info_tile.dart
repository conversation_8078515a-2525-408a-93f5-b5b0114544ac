import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';

class AreaInfoTile extends StatelessWidget {
  final String iconPath;
  final String label;
  final String value;

  const AreaInfoTile({
    super.key,
    required this.iconPath,
    required this.label,
    required this.value,
  });

  @override
  Widget build(BuildContext context) {
    final color = Theme.of(context).colorScheme.primary;
    return Column(
      children: [
        Padding(
          padding: const EdgeInsets.only(bottom: 8.0, top: 10),
          child: Row(
            children: [
              SvgPicture.asset(
                iconPath,
                colorFilter: ColorFilter.mode(color, BlendMode.srcIn),
              ),
              const SizedBox(width: 16),
              Expanded(
                  child: Text(
                label,
                style: TextStyle(
                    fontSize: 17, color: color.withValues(alpha: 0.75)),
              )),
              Text(
                value,
                style: TextStyle(fontSize: 17, color: color),
              ),
            ],
          ),
        ),
        const Divider()
      ],
    );
  }
}
