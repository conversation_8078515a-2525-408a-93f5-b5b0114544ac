import 'dart:async';

import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';

import '../../../dependency_injection/dependency_injector.dart';
import '../../../generic_widgets/navigation_extensions.dart';
import '../../../generic_widgets/responsive_padding.dart';
import '../../../generic_widgets/separated_tile.dart';
import '../../../l10n/generated/app_localizations.dart';
import '../../../models/device.dart';
import '../../../models/geo_area.dart';
import '../../../models/geo_bounds.dart';
import '../../../models/palette.dart';
import '../../../models/selection.dart';
import '../../dashboard/graphs/percent_graph.dart';
import '../../in_app_purchase/iap_product.dart';
import '../../in_app_purchase/iap_tile.dart';
import '../../map/geometry_painter.dart';
import '../../map/map_screen.dart';
import '../../map/projection.dart';
import '../../map/tiles/internal/geometry_repository.dart';
import 'area_details_header.dart';

class RegionSliver extends StatefulWidget {
  const RegionSliver({
    super.key,
    required this.area,
    this.customTitle,
    this.allowNavigationToRegionMap = true,
    this.fillToEdgeOnPhone = false,
    this.headerTextStyle,
  });

  final GeoArea area;
  final String? customTitle;
  final TextStyle? headerTextStyle;
  final bool allowNavigationToRegionMap;
  final bool fillToEdgeOnPhone;

  @override
  State<RegionSliver> createState() => _RegionSliverState();
}

class _RegionSliverState extends State<RegionSliver>
    with AutomaticKeepAliveClientMixin {
  @override
  Widget build(BuildContext context) {
    super.build(context);

    final iap = DependencyInjector.iapBloc;
    return StreamBuilder<bool>(
      stream: iap.status.map((event) => iap.canAccessSubdivisions(widget.area)),
      initialData: iap.canAccessSubdivisions(widget.area),
      builder: (context, snapshot) {
        return ResponsiveSliverPadding(
          fillToEdgeOnPhone: widget.fillToEdgeOnPhone,
          context: context,
          sliver: snapshot.requireData
              ? _buildRegionContent(context)
              : _buildBuyRegionsTile(context),
        );
      },
    );
  }

  Widget _buildBuyRegionsTile(BuildContext context) {
    return SliverToBoxAdapter(
      child: FutureBuilder<IAPProduct?>(
        future:
            DependencyInjector.iapBloc.fetchProduct(IAPFeature.unlockRegions),
        builder: (context, snapshot) {
          if (snapshot.hasData == false) {
            return const SizedBox();
          }

          return IAPTile(product: snapshot.data!);
        },
      ),
    );
  }

  Widget _buildRegionContent(BuildContext context) {
    return SliverList(
      delegate: SliverChildListDelegate([
        _buildCoverageGraph(context),
        RegionMap(
          area: widget.area,
          showHeader: widget.customTitle == null,
          onTapped: widget.allowNavigationToRegionMap
              ? () => _onRegionMapTapped(context)
              : null,
        ),
        if (widget.allowNavigationToRegionMap) _buildShowRegions(context)
      ]),
    );
  }

  Widget _buildShowRegions(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.only(top: 8.0).add(Device.isTablet(context)
          ? const EdgeInsets.symmetric(horizontal: Device.tabletMargin)
          : EdgeInsets.zero),
      child: SeparatedTile(
        color: Theme.of(context).scaffoldBackgroundColor,
        divideTop: true,
        child: ListTile(
          title: Text(
            AppLocalizations.of(context)!.regions,
            style: const TextStyle(fontSize: 16.0),
          ),
          trailing: const Icon(CupertinoIcons.forward),
          onTap: () => _onRegionMapTapped(context),
        ),
      ),
    );
  }

  Widget _buildCoverageGraph(BuildContext context) {
    final localizations = AppLocalizations.of(context)!;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        AreaDetailsHeader(
          text: widget.customTitle ?? localizations.coverage,
          textStyle: widget.headerTextStyle,
        ),
        if (widget.customTitle == null)
          Text(
            localizations.percentOfCountryVisited,
            style: Theme.of(context).textTheme.titleMedium,
          ),
        PercentCountrySeenGraph(area: widget.area)
      ],
    );
  }

  void _onRegionMapTapped(BuildContext context) {
    Navigator.of(context).pushMaterialRoute(
      name: MapScreen.routeName(widget.area),
      builder: (_) => MapScreen(
        area: widget.area,
        useLegacy: true,
      ),
    );
  }

  @override
  bool get wantKeepAlive => true;
}

class PercentCountrySeenGraph extends StatelessWidget {
  const PercentCountrySeenGraph({
    super.key,
    required this.area,
  });

  final GeoArea area;

  @override
  Widget build(BuildContext context) {
    return StreamBuilder<(int, int)>(
      stream: DependencyInjector.areaSelectionBloc.fractionOfCountrySeen(area),
      initialData: (0, 0),
      builder: (context, snapshot) {
        final (numerator, denominator) = snapshot.requireData;
        final percent = denominator == 0 ? 0.0 : numerator / denominator;

        return Padding(
          padding: const EdgeInsets.symmetric(vertical: 24.0),
          child: SizedBox(
            height: 140,
            child: Center(
              child: Stack(
                alignment: Alignment.center,
                children: [
                  PercentGraph(lineWidth: 4, percentage: percent),
                  Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Text(
                        '${(percent * 100).toInt()}%',
                        style: TextStyle(
                          fontSize: 42,
                          color: Theme.of(context).colorScheme.primary,
                        ),
                      ),
                      Text(
                        AppLocalizations.of(context)!.visited,
                        style: Theme.of(context).textTheme.titleMedium,
                      ),
                      if (denominator > 0)
                        Text(
                          '$numerator/$denominator',
                          style: Theme.of(context).textTheme.titleMedium,
                        )
                    ],
                  )
                ],
              ),
            ),
          ),
        );
      },
    );
  }
}

class RegionMap extends StatefulWidget {
  const RegionMap({
    super.key,
    required this.area,
    required this.onTapped,
    this.showHeader = true,
  });

  final bool showHeader;
  final GeoArea area;
  final VoidCallback? onTapped;

  @override
  State<RegionMap> createState() => _RegionMapState();
}

class _RegionMapState extends State<RegionMap>
    with AutomaticKeepAliveClientMixin {
  late Size size;
  (PolygonLookup, GeoBounds?)? polygons;
  Map<GeoArea, Selection>? selections;
  StreamSubscription? _subscription;

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();

    final deviceSize = MediaQuery.of(context).size;
    size = Size(
      deviceSize.width,
      deviceSize.width / widget.area.renderingBounds.calculateRatio(),
    );

    if (polygons == null) {
      DependencyInjector.geometryBloc
          .fetchPolygonsForArea(widget.area)
          .then((value) {
        if (mounted) {
          setState(() {
            polygons = value;
          });
        }
      });
    }

    _subscription ??=
        DependencyInjector.areaSelectionBloc.selections.listen((selections) {
      setState(() {
        this.selections = selections;
      });
    });
  }

  @override
  Widget build(BuildContext context) {
    super.build(context);
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisSize: MainAxisSize.min,
      children: [
        if (widget.showHeader)
          AreaDetailsHeader(text: AppLocalizations.of(context)!.map),
        if (polygons == null)
          SizedBox.fromSize(size: size)
        else
          _buildMap(polygons!)
      ],
    );
  }

  Widget _buildMap((PolygonLookup, GeoBounds?) polygons) {
    final geometry = polygons.$1;
    final bounds = polygons.$2 ?? widget.area.renderingBounds;

    final selections = this.selections;
    if (selections == null) {
      return SizedBox.fromSize(size: size);
    }

    return SizedBox.fromSize(
      size: size,
      child: GestureDetector(
        onTap: widget.onTapped,
        behavior: HitTestBehavior.opaque,
        child: RepaintBoundary(
          child: CustomPaint(
            size: size,
            isComplex: true,
            painter: GeometryPainter(
              geometry: geometry,
              projection: EquirectangularProjection(size.width),
              selectionGetter: (area) => selections[area] ?? Selection.clear,
              palette: Palette.outlined(context),
              borderWidth: 1,
              bounds: bounds,
              renderWater: false,
            ),
          ),
        ),
      ),
    );
  }

  @override
  bool get wantKeepAlive => true;

  @override
  void dispose() {
    _subscription?.cancel();
    super.dispose();
  }
}
