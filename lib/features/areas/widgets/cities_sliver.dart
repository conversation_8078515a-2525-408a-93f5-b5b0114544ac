import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';

import '../../../dependency_injection/dependency_injector.dart';
import '../../../generic_widgets/navigation_extensions.dart';
import '../../../generic_widgets/responsive_padding.dart';
import '../../../generic_widgets/separated_tile.dart';
import '../../../l10n/generated/app_localizations.dart';
import '../../../models/geo_area.dart';
import '../../cities/city_picker.dart';
import '../../in_app_purchase/iap_product.dart';
import '../../in_app_purchase/iap_tile.dart';

class CitiesSliver extends StatelessWidget {
  const CitiesSliver({
    super.key,
    required this.area,
  });

  final GeoArea area;

  @override
  Widget build(BuildContext context) {
    return ResponsiveSliverPadding(
      context: context,
      sliver: SliverToBoxAdapter(
        child: StreamBuilder<bool>(
          stream: DependencyInjector.iapBloc.status
              .map((event) => event.hasUnlockedCities),
          initialData: false,
          builder: (context, snapshot) {
            return snapshot.data ?? false
                ? _buildShowCitiesTile(context)
                : _buildPurchaseTile(context);
          },
        ),
      ),
    );
  }

  Widget _buildPurchaseTile(BuildContext context) {
    return FutureBuilder<IAPProduct?>(
      future: DependencyInjector.iapBloc.fetchProduct(IAPFeature.unlockCities),
      builder: (context, snapshot) {
        if (snapshot.hasData == false) {
          return const SizedBox();
        }

        return Padding(
          padding: const EdgeInsets.all(16.0),
          child: IAPTile(product: snapshot.data!),
        );
      },
    );
  }

  Widget _buildShowCitiesTile(BuildContext context) {
    return SeparatedTile(
      child: ListTile(
        title: Text(
          AppLocalizations.of(context)!.cities,
          style: const TextStyle(fontSize: 16.0),
        ),
        trailing: const Icon(CupertinoIcons.forward),
        onTap: () => Navigator.of(context).pushMaterialRoute(
          name: CityPicker.routeName(area),
          builder: (_) => CityPicker(area: area),
          fullscreen: true,
        ),
      ),
    );
  }
}
