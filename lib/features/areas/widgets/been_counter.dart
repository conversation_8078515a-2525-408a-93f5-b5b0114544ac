import 'package:debounce_throttle/debounce_throttle.dart';
import 'package:flutter/material.dart';

import '../../../dependency_injection/dependency_injector.dart';
import '../../../generic_widgets/platform_aware/platform_icon_button.dart';
import '../../../l10n/generated/app_localizations.dart';
import '../../../models/geo_area.dart';

class BeenCounter extends StatefulWidget {
  const BeenCounter({super.key, required this.area, required this.controller});

  final ScrollController controller;
  final GeoArea area;

  @override
  State createState() => _BeenCounterState();
}

class _BeenCounterState extends State<BeenCounter> {
  var _count = 1;
  int? _lastSavedCount;
  final _countController = TextEditingController();
  final focus = FocusNode();
  final textKey = GlobalKey();
  final _debouncer = Debouncer<int>(
    const Duration(milliseconds: 200),
    initialValue: 1,
  );

  @override
  void initState() {
    super.initState();
    _fetchCount();

    _debouncer.values.listen(_synchronizeBeenCounter);

    focus.addListener(() {
      setState(() {});

      Future.delayed(const Duration(milliseconds: 200)).then((value) {
        widget.controller.animateTo(
          150,
          duration: const Duration(seconds: 1),
          curve: Curves.easeIn,
        );
      });
    });
  }

  void _synchronizeBeenCounter(int updatedCount) {
    if (updatedCount == _lastSavedCount) {
      return;
    }

    DependencyInjector.areaSelectionBloc.updateBeenCounter(
      area: widget.area,
      count: updatedCount,
    );
    _lastSavedCount = updatedCount;
  }

  void _fetchCount() async {
    if (!mounted) {
      return;
    }

    final count = await DependencyInjector.areaSelectionBloc.beenCount(
      widget.area,
    );

    if (mounted) {
      setState(() {
        _count = count;
        _countController.text = _count.toString();
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return PopScope(
      onPopInvokedWithResult: (_, _) => _synchronizeBeenCounter(_count),
      child: Padding(
        padding: const EdgeInsets.all(8.0),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            _buildLabel(context),
            _buildCounter(context),
            AnimatedSwitcher(
              duration: const Duration(milliseconds: 100),
              child: focus.hasFocus
                  ? FittedBox(
                      fit: BoxFit.scaleDown,
                      child: PlatformIconButton(
                        icon: Icons.close,
                        semanticLabel: AppLocalizations.of(context)!.done,
                        onTapped: focus.unfocus,
                      ),
                    )
                  : const SizedBox(),
            ),
            const SizedBox(width: 16),
            _buildDecrementButton(context),
            const SizedBox(width: 1),
            _buildIncrementButton(context),
          ],
        ),
      ),
    );
  }

  Widget _buildLabel(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Text(
        '${AppLocalizations.of(context)!.been} #:',
        style: const TextStyle(fontSize: 18, fontWeight: FontWeight.w500),
      ),
    );
  }

  Widget _buildCounter(BuildContext context) {
    return Expanded(
      child: Container(
        color: Theme.of(context).cardColor.withValues(alpha: 0.5),
        child: TextField(
          key: textKey,
          focusNode: focus,
          maxLines: 1,
          minLines: 1,
          expands: false,
          controller: _countController,
          textAlign: TextAlign.center,
          keyboardType: TextInputType.number,
          style: Theme.of(context).textTheme.titleLarge,
          decoration: const InputDecoration(
            border: InputBorder.none,
            isCollapsed: true,
            contentPadding: EdgeInsets.zero,
          ),
          onChanged: (value) {
            setState(() {
              _count = int.tryParse(value) ?? 1;
            });

            _debouncer.value = _count;
          },
        ),
      ),
    );
  }

  Widget _buildIncrementButton(BuildContext context) {
    return PlatformIconButton(
      borderRadius: const BorderRadius.horizontal(right: Radius.circular(40)),
      color: Theme.of(context).cardColor,
      onTapped: () {
        setState(() {
          _count++;
          _countController.text = _count.toString();
          _debouncer.value = _count;
        });
      },
      icon: Icons.arrow_right,
    );
  }

  Widget _buildDecrementButton(BuildContext context) {
    return PlatformIconButton(
      borderRadius: const BorderRadius.horizontal(left: Radius.circular(40)),
      color: Theme.of(context).cardColor,
      onTapped: () {
        if (_count <= 1) {
          return;
        }

        setState(() {
          _count--;
          _countController.text = _count.toString();
          _debouncer.value = _count;
        });
      },
      icon: Icons.arrow_left,
    );
  }

  @override
  void dispose() {
    _countController.dispose();
    focus.dispose();
    _debouncer.cancel();
    super.dispose();
  }
}
