import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:intl/intl.dart';

import '../../../dependency_injection/dependency_injector.dart';
import '../../../generic_widgets/platform_aware/platform_icon_button.dart';
import '../../../generic_widgets/platform_aware/platform_sliver_app_bar.dart';
import '../../../generic_widgets/responsive_padding.dart';
import '../../../generic_widgets/separated_tile.dart';
import '../../../generic_widgets/sliver_bottom_safe_area.dart';
import '../../../generic_widgets/spinner.dart';
import '../../../l10n/generated/app_localizations.dart';
import '../../../models/device.dart';
import '../../../models/geo_area.dart';
import '../../../models/ordinal_number_formatter.dart';
import '../../../models/selection.dart';
import '../../ads/ad_manager.dart';
import '../../books/book_link_tile.dart';
import '../../in_app_purchase/iap_status.dart';
import '../../map/selection_bar.dart';
import '../../notes/notes_tile.dart';
import '../../sharing/content/country_sharable_content.dart';
import '../../sharing/sharing_service.dart';
import '../area_details.dart';
import 'area_details_header.dart';
import 'area_info_tile.dart';
import 'been_counter.dart';
import 'cities_sliver.dart';
import 'region_sliver.dart';

class AreaDetailsScreen extends StatefulWidget {
  static String routeName(GeoArea area) => 'areaDetails/${area.isoCode}';

  final GeoArea area;

  const AreaDetailsScreen({super.key, required this.area});

  @override
  State createState() => _AreaDetailsScreenState();
}

class _AreaDetailsScreenState extends State<AreaDetailsScreen> {
  final _scrollController = ScrollController();

  bool loaded = false;
  late final AreaDetails areaDetails;

  @override
  void initState() {
    super.initState();
    _loadDetails();
  }

  void _loadDetails() async {
    if (loaded) {
      return;
    }

    final details = await DependencyInjector.areaBloc.fetchDetails(widget.area);
    if (!mounted) {
      return;
    }

    setState(() {
      loaded = true;
      areaDetails = details;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      resizeToAvoidBottomInset: true,
      body: loaded ? _buildContent() : const Center(child: Spinner()),
    );
  }

  Widget _buildContent() {
    return Scrollbar(
      controller: _scrollController,
      child: CustomScrollView(
        controller: _scrollController,
        keyboardDismissBehavior: ScrollViewKeyboardDismissBehavior.onDrag,
        slivers: [
          PlatformSliverAppBar(
            title: widget.area.name,
            action: PlatformIconButton(
              icon: Icons.adaptive.share,
              onTapped: () {
                final sharer = SharingService(
                  context,
                  content: CountrySharableContent(widget.area),
                );
                sharer.export();
              },
            ),
          ),
          _buildImageSection(),
          _buildSelectionSection(),
          if (areaDetails.bookLink != null) _buildBookLinkSection(),
          _buildAd(),
          if (DependencyInjector.iapBloc.hasUnlockedCities)
            CitiesSliver(area: widget.area),
          if (widget.area.hasSubdivisions) RegionSliver(area: widget.area),
          ResponsiveSliverPadding(
            context: context,
            fillToEdgeOnPhone: false,
            sliver: SliverList(
              delegate: SliverChildListDelegate([
                _buildNotes(),
                ..._buildInfoSection(),
              ]),
            ),
          ),
          const SliverBottomSafeArea(),
        ],
      ),
    );
  }

  Widget _buildAd() {
    return StreamBuilder<IAPStatus>(
      stream: DependencyInjector.iapBloc.status,
      builder: (context, snapshot) {
        if (snapshot.data?.hasRemovedAds ?? true) {
          return const SliverToBoxAdapter(child: SizedBox());
        }

        return ResponsiveSliverPadding(
          fillToEdgeOnPhone: false,
          context: context,
          sliver: SliverToBoxAdapter(
            child: Padding(
              padding: const EdgeInsets.only(top: 16.0),
              child: AdView(location: AdLocation.areaDetails),
            ),
          ),
        );
      },
    );
  }

  Widget _buildNotes() {
    return NotesTile(
      key: ValueKey(widget.area.id),
      scrollController: _scrollController,
      area: widget.area,
    );
  }

  Iterable<Widget> _buildInfoSection() {
    final localizations = AppLocalizations.of(context)!;
    return [
      AreaDetailsHeader(text: localizations.about),
      AreaInfoTile(
        iconPath: 'assets/images/population.svg',
        label: localizations.population,
        value: NumberFormat.decimalPattern().format(areaDetails.population),
      ),
      AreaInfoTile(
        iconPath: 'assets/images/size.svg',
        label: localizations.size,
        value:
            '${NumberFormat.decimalPattern().format(areaDetails.size)} ${localizations.kmSquared}',
      ),
    ];
  }

  Widget _buildImageSection() {
    return SliverList(
      delegate: SliverChildListDelegate([
        CachedNetworkImage(
          imageUrl: areaDetails.thumbnailUrl,
          fit: BoxFit.fitWidth,
          progressIndicatorBuilder: (context, url, progress) {
            return AspectRatio(
              aspectRatio: 2048 / 1304,
              child: Container(
                color: Theme.of(
                  context,
                ).dividerColor.withValues(alpha: progress.progress ?? 0),
              ),
            );
          },
        ),
        _buildPopularity(),
      ]),
    );
  }

  Widget _buildBookLinkSection() {
    return SliverList(
      delegate: SliverChildListDelegate([
        SeparatedTile(child: BookLinkTile(book: areaDetails.bookLink!)),
      ]),
    );
  }

  Widget _buildPopularity() {
    return ColoredBox(
      color: Theme.of(context).dividerColor,
      child: Padding(
        padding: Device.isTablet(context)
            ? const EdgeInsets.symmetric(horizontal: Device.tabletMargin)
            : EdgeInsets.zero,
        child: ListTile(
          title: Text(
            AppLocalizations.of(context)!.popularity,
            style: const TextStyle(fontSize: 16),
          ),
          trailing: Text(
            OrdinalNumberFormatter().localizedFormat(
              context,
              areaDetails.popularity,
            ),
            style: Theme.of(context).textTheme.titleLarge,
          ),
        ),
      ),
    );
  }

  Widget _buildSelectionSection() {
    final bloc = DependencyInjector.areaSelectionBloc;
    return ResponsiveSliverPadding(
      fillToEdgeOnPhone: false,
      context: context,
      sliver: SliverList(
        delegate: SliverChildListDelegate([
          const SizedBox(height: 24),
          Center(
            child: SelectionBar(
              selection: bloc.listenToSelection(widget.area),
              onSelected: (selection) => bloc.select(widget.area, selection),
            ),
          ),
          _buildBeenCounter(),
        ]),
      ),
    );
  }

  Widget _buildBeenCounter() {
    return StreamBuilder<bool>(
      stream: DependencyInjector.areaSelectionBloc
          .listenToSelection(widget.area)
          .map((selection) => selection == Selection.been),
      builder: (context, snapshot) {
        final showCounter = snapshot.data == true;

        return AnimatedSwitcher(
          duration: const Duration(milliseconds: 600),
          transitionBuilder: (child, anim) => FadeTransition(
            opacity: anim,
            child: SlideTransition(
              position: Tween(
                begin: const Offset(0, -1),
                end: Offset.zero,
              ).animate(anim),
              child: child,
            ),
          ),
          child: showCounter
              ? BeenCounter(controller: _scrollController, area: widget.area)
              : const SizedBox(key: ValueKey(false)),
        );
      },
    );
  }

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }
}
