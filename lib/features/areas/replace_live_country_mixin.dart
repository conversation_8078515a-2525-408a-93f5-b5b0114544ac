import 'package:flutter/cupertino.dart';

import '../../dependency_injection/dependency_injector.dart';
import '../../generic_widgets/alert.dart';
import '../../generic_widgets/navigation_extensions.dart';
import '../../l10n/generated/app_localizations.dart';
import '../../models/geo_area.dart';
import '../first_time/first_time_city_search_screen.dart';

mixin ReplaceLiveCountryMixin {
  Future<bool> replaceLiveCountry({
    required GeoArea area,
    required BuildContext context,
  }) async {
    final localizations = AppLocalizations.of(context)!;

    final dialog = ConfirmDialog(
      title: localizations.updateLive,
      message: localizations.updateLiveDescription,
      confirmText: localizations.update,
      cancelText: localizations.cancel,
      confirmDestructive: true,
    );
    final confirmed = await dialog.show(context);

    if (context.mounted == false) {
      return false;
    }

    if (confirmed != true) {
      return false;
    }

    final nav = Navigator.of(context);
    await nav.pushMaterialRoute(
      name: 'updatelivecity',
      builder: (context) => const FirstTimeCitySearchScreen(),
      fullscreen: true,
    );

    final updatedLive = await DependencyInjector.areaSelectionBloc
        .currentCountryUserLivesIn();
    if (updatedLive != area) {
      await DependencyInjector.tileRenderingService.updateAreas([
        area,
        updatedLive!,
      ]);
      DependencyInjector.tileRenderingService.refresh();
    }

    return true;
  }
}
