import '../../dependency_injection/dependency_injector.dart';
import '../../models/geo_area.dart';
import 'area_details.dart';
import 'enclosed_area_lookup.dart';

class AreaService {
  late final _client = DependencyInjector.client;

  Future<Map> fetchGeometryUpdates([DateTime? lastUpdate]) async {
    const path = 'areas/updated';
    final query =
        lastUpdate != null ? {'since': lastUpdate.toIso8601String()} : null;
    final json = await _client.get(
      path,
      parameters: query,
      requiredAuthentication: false,
    );

    return json;
  }

  // TODO: Store a locally cached version, this never really changes
  Future<Set<EnclosedAreaLookup>> fetchEnclosedAreaWhitelist() async {
    const path = 'geometry/whitelist';
    final json = await _client.get(
      path,
      requiredAuthentication: false,
    );
    final lookup = json
        .map<EnclosedAreaLookup>((e) => EnclosedAreaLookup.fromJson(e))
        .toSet();
    return lookup;
  }

  Future<AreaDetails> fetchDetails(
    GeoArea area, {
    double resolution = 1,
  }) async {
    final path = 'areas/${area.isoCode}/details';
    final response = await _client.get(
      path,
      parameters: {'resolution': resolution},
    );
    return AreaDetails.fromJson(response);
  }
}
