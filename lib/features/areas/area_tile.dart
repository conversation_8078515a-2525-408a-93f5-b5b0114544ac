import 'package:flutter/material.dart';

import '../../generic_widgets/area_flag.dart';
import '../../generic_widgets/navigation_extensions.dart';
import '../../generic_widgets/platform_aware/platform_arrow.dart';
import '../../generic_widgets/separated_tile.dart';
import '../../models/geo_area.dart';
import 'widgets/area_details_screen.dart';

class AreaTile extends StatelessWidget {
  const AreaTile({
    super.key,
    required this.area,
    this.onTapped,
  });

  final GeoArea area;
  final VoidCallback? onTapped;

  @override
  Widget build(BuildContext context) {
    return SeparatedTile(
      child: ListTile(
        leading: AreaFlag(
          area: area,
        ),
        title: Text(area.name),
        trailing: area.hasDetails ? const PlatformArrow() : null,
        onTap: onTapped ??
            (area.hasDetails
                ? () => Navigator.of(context).pushMaterialRoute(
                      name: AreaDetailsScreen.routeName(area),
                      builder: (_) => AreaDetailsScreen(area: area),
                    )
                : null),
      ),
    );
  }
}
