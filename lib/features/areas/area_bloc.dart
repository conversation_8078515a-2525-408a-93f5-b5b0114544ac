import '../../dependency_injection/dependency_injector.dart';
import '../../generic_widgets/bloc.dart';
import '../../models/continent.dart';
import '../../models/geo_area.dart';
import '../disputed_territories/disputed_territories_service.dart';
import '../disputed_territories/disputed_territory.dart';
import 'area_details.dart';
import 'area_repository.dart';
import 'area_services.dart';
import 'enclosed_area_lookup.dart';

class AreaBloc implements Bloc {
  late final AreaService _services = DependencyInjector.areaService;
  late final AreaRepository _repository = DependencyInjector.areaRepository;
  DisputedTerritoriesService get _disputedTerritoriesServices =>
      DependencyInjector.disputedTerritoriesService;

  Stream<int> get totalNumberOfCountries => DependencyInjector
      .settingsBloc
      .countSovereign
      .asyncMap((countSovereign) async {
        final all = await _repository.fetchAll();
        return all
            .where(
              (area) => area.isCountry && (!countSovereign || area.sovereign),
            )
            .length;
      });

  Future<void> initialize() async {
    // TODO: this should be the only place that we initialize the area repo
    await _repository.initialize();
  }

  void fetchUpdates() async {
    final lastModified = await _repository.fetchLastModifiedDate();
    final updates = await _services.fetchGeometryUpdates(lastModified);
    _repository.cacheChanges(updates);
  }

  Stream<List<Continent>> fetchContinents() {
    return _repository.fetchContinents();
  }

  Stream<List<GeoArea>> listenToCountriesInContinent(Continent continent) {
    return DependencyInjector.settingsBloc.countSovereign.asyncMap((
      countSovereign,
    ) async {
      final all = await _repository.fetchAreasFromContinent(continent);
      return all
          .where(
            (area) => area.isCountry && (!countSovereign || area.sovereign),
          )
          .toList();
    });
  }

  Future<GeoArea?> areaByIsoCode(String isoCode) {
    return _repository.fetchByIsoCode(isoCode);
  }

  Future<Set<EnclosedAreaLookup>> fetchWhitelist() {
    return _services.fetchEnclosedAreaWhitelist();
  }

  Future<List<GeoArea>> areasWithSubdivisions() async {
    final all = await _repository.fetchAll();
    return all.where((area) => area.hasSubdivisions).toList();
  }

  Future<List<GeoArea>> fetchSubdivisions(GeoArea area) async {
    final subdivisions = await _repository.fetchSubdivisions(area);
    return subdivisions.toList(growable: false)
      ..sort((a, b) => a.name.compareTo(b.name));
  }

  Future<List<GeoArea>> allCountries() async {
    final all = await _repository.fetchAll();
    final countries = all.where((area) => area.isCountry).toList();

    final adjustments = await _disputedTerritoriesServices
        .topLevelDisputedPreferences();
    countries.removeWhere(
      (area) => adjustments.removeIsoCodes.contains(area.isoCode),
    );

    for (final areaToAdd in adjustments.add) {
      final area = await _repository.fetchByIsoCode(areaToAdd.isoCode);
      if (area != null && !countries.contains(area)) {
        countries.add(area);
      }
    }

    if (DependencyInjector.settingsBloc.currentSettings.countUkCountries) {
      final uk = all.firstWhere((area) => area.id == 178);
      final ukCountries = await fetchSubdivisions(uk);
      countries.addAll(ukCountries);
    }

    countries.sort((a, b) => a.name.compareTo(b.name));
    return countries;
  }

  Future<Set<GeoArea>> allAreas() {
    return _repository.fetchAll();
  }

  Future<List<GeoArea>> areasByIsoCodes(List<String> codes) =>
      _repository.fetchByIsoCodes(codes);

  Future<AreaDetails> fetchDetails(GeoArea area) {
    final density = DependencyInjector.environment.pixelDensity;
    return _services.fetchDetails(area, resolution: density);
  }

  Future<GeoArea?> fetchParent(GeoArea area) => _repository.fetchParent(area);

  Future<List<DisputedTerritory>> fetchDisputedTerritories() async =>
      _disputedTerritoriesServices.fetchDisputedTerritories();

  // Future<DisputedTerritoryAdjustments> topLevelDisputedTerritoryAdjustments(
  //         Iterable<GeoArea> countries) =>
  //     _disputedTerritoriesServices.topLevelDisputedPreferences(countries);
  //
  // Future<Set<SimpleGeoArea>> alternativeCountryGeographySelections() =>
  //     _disputedTerritoriesServices.requestedAlternativeGeometry();

  GeoArea? areaByIsoCodeSync(String iso) => _repository.fetchByIsoCodeSync(iso);

  @override
  void clear() {}

  @override
  void dispose() {}

  List<GeoArea> allCountriesSync() {
    return _repository.fetchAllSync().where((area) => area.isCountry).toList();
  }

  List<GeoArea> allAreasSync() => _repository.fetchAllSync().toList();
}
