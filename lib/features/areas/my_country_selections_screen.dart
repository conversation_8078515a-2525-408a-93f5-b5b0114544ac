import 'package:flutter/material.dart';

import '../../dependency_injection/dependency_injector.dart';
import '../../generic_widgets/navigation_extensions.dart';
import '../../l10n/generated/app_localizations.dart';
import 'area_list_picker.dart';
import 'selected_areas_screen.dart';

class MyCountrySelectionsScreen extends StatelessWidget {
  static const routeName = '/countrySelections';

  const MyCountrySelectionsScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return SelectedAreasScreen(
      title: AppLocalizations.of(context)!.myCountrySelections,
      sortedSelectionStream: DependencyInjector.areaSelectionBloc
          .sortedCountrySelections(),
      onEditTapped: () => _pushEditScreen(context),
    );
  }

  void _pushEditScreen(BuildContext context) {
    Navigator.of(context).pushMaterialRoute(
      name: 'areaListPicker',
      builder: (_) => const AreaListPicker(),
    );
  }
}
