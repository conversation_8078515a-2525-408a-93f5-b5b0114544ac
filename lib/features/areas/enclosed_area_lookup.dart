class EnclosedAreaLookup {
  final String isoCode;
  final String surroundedByIsoCode;

  EnclosedAreaLookup.fromJson(Map json)
      : isoCode = json['areaKey'],
        surroundedByIsoCode = json['surroundingKey'];

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is EnclosedAreaLookup &&
          runtimeType == other.runtimeType &&
          isoCode == other.isoCode &&
          surroundedByIsoCode == other.surroundedByIsoCode;

  @override
  int get hashCode => isoCode.hashCode ^ surroundedByIsoCode.hashCode;
}
