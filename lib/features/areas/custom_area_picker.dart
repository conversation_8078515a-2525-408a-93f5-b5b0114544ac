import 'dart:async';

import 'package:flutter/material.dart';

import '../../dependency_injection/dependency_injector.dart';
import '../../generic_widgets/pinned_selection_toggle.dart';
import '../../generic_widgets/platform_aware/platform_sliver_app_bar.dart';
import '../../generic_widgets/responsive_padding.dart';
import '../../generic_widgets/sliver_sticky_search_bar.dart';
import '../../generic_widgets/spinner.dart';
import '../../generic_widgets/streaming_selectable_tile.dart';
import '../../models/geo_area.dart';
import '../../models/selection.dart';

class CustomAreaPicker extends StatefulWidget {
  const CustomAreaPicker({
    super.key,
    required this.titleFetcher,
    required this.areaFetcher,
    required this.selectionStream,
    required this.onSelected,
    required this.availableSelections,
  });

  final Future<String> Function() titleFetcher;
  final Future<List<GeoArea>> Function() areaFetcher;
  final Stream<Selection> Function(GeoArea area) selectionStream;
  final void Function(GeoArea area, Selection selection) onSelected;
  final List<Selection> availableSelections;

  @override
  State<CustomAreaPicker> createState() => _CustomAreaPickerState();
}

class _CustomAreaPickerState extends State<CustomAreaPicker> {
  var selection = Selection.been;
  List<GeoArea>? areas;
  List<GeoArea>? searchResults;
  String title = '';

  StreamSubscription? _languageSubscription;
  final _textController = TextEditingController();

  @override
  void initState() {
    super.initState();

    _languageSubscription ??= DependencyInjector.settingsBloc.language.listen((
      _,
    ) {
      _textController.clear();
      searchResults = null;
      _fetchAreas();
    });

    _fetchAreas();
  }

  void _fetchAreas() async {
    title = await widget.titleFetcher();
    final results = await widget.areaFetcher();
    setState(() {
      areas = results;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Scrollbar(
        child: CustomScrollView(
          keyboardDismissBehavior: ScrollViewKeyboardDismissBehavior.onDrag,
          slivers: [
            PlatformSliverAppBar(title: title),
            if (areas != null)
              SliverStickySearchBar<GeoArea>(
                searchController: _textController,
                items: areas!,
                onQuery: (results) => setState(() => searchResults = results),
              ),
            PinnedSelectionToggle(
              active: selection,
              selections: widget.availableSelections,
              expand: widget.availableSelections.length > 3,
              onChanged: (selection) {
                setState(() {
                  this.selection = selection;
                });
              },
            ),
            ResponsiveSliverPadding(
              context: context,
              sliver: _buildAreaList(context),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildAreaList(BuildContext context) {
    if (this.areas == null) {
      return const SliverFillRemaining(child: Center(child: Spinner()));
    }

    final areas = searchResults?.isNotEmpty ?? false
        ? searchResults!
        : this.areas!;

    return SliverList.builder(
      itemCount: areas.length,
      itemBuilder: (context, index) {
        final area = areas[index];
        return StreamingSelectableTile(
          selectionStream: widget.selectionStream(area),
          item: area,
          onTapped: () => widget.onSelected(area, selection),
        );
      },
    );
  }

  @override
  void dispose() {
    _languageSubscription?.cancel();
    _textController.dispose();
    super.dispose();
  }
}
