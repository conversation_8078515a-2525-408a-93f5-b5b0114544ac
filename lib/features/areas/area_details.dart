import '../books/book_link.dart';

class AreaDetails {
  const AreaDetails({
    required this.id,
    required this.isoCode,
    required this.name,
    required this.popularity,
    required this.size,
    required this.population,
    required this.thumbnailUrl,
    this.bookLink,
  });

  AreaDetails.fromJson(Map json)
      : id = json['id'],
        isoCode = json['isoCode'],
        name = json['name'],
        popularity = json['popularity'],
        size = json['size'],
        population = json['population'],
        thumbnailUrl = json['thumbnailUrl'],
        bookLink = json.containsKey('bookLink')
            ? BookLink.fromJson(json['bookLink'])
            : null;

  final int id;
  final String isoCode;
  final String name;
  final int popularity;
  final double size;
  final int population;
  final String thumbnailUrl;
  final BookLink? bookLink;
}
