import 'package:flutter/material.dart';
import 'package:flutter_sticky_header/flutter_sticky_header.dart';
import 'package:sliver_tools/sliver_tools.dart';

import '../../generic_widgets/list_view_header.dart';
import '../../generic_widgets/platform_aware/platform_sliver_app_bar.dart';
import '../../generic_widgets/platform_aware/platform_text_button.dart';
import '../../generic_widgets/responsive_padding.dart';
import '../../generic_widgets/sliver_bottom_safe_area.dart';
import '../../generic_widgets/spinner.dart';
import '../../l10n/generated/app_localizations.dart';
import '../../models/geo_area.dart';
import '../../models/selection.dart';
import 'area_tile.dart';

class SelectedAreasScreen extends StatelessWidget {
  const SelectedAreasScreen({
    super.key,
    required this.sortedSelectionStream,
    required this.title,
    required this.onEditTapped,
    this.header,
  });

  final Stream<Map<Selection, List<GeoArea>>> sortedSelectionStream;
  final String title;
  final VoidCallback onEditTapped;
  final Widget? header;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      floatingActionButton: _buildFloatingActionButton(context),
      body: StreamBuilder<Map<Selection, List<GeoArea>>>(
        stream: sortedSelectionStream,
        builder: (context, snapshot) {
          if (snapshot.connectionState == ConnectionState.waiting) {
            return const Center(child: Spinner());
          }

          final selections = snapshot.data;

          return Scrollbar(
            child: CustomScrollView(
              slivers: [
                _buildAppBar(context),
                ResponsiveSliverPadding(
                  context: context,
                  sliver: selections == null || selections.isEmpty
                      ? _buildNoSelections(context)
                      : _buildSelections(context, snapshot.requireData),
                ),
                const SliverBottomSafeArea(),
              ],
            ),
          );
        },
      ),
    );
  }

  // TODO: LOCALIZE!
  Widget _buildNoSelections(BuildContext context) {
    final textTheme = Theme.of(context).textTheme;
    return SliverFillRemaining(
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            Icon(
              Icons.no_luggage_outlined,
              size: 50,
              color: textTheme.titleLarge?.color,
            ),
            Text('No Selections', style: textTheme.titleLarge),
            Text(
              'Tap the update button to track your travels',
              style: textTheme.titleSmall,
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget? _buildFloatingActionButton(BuildContext context) {
    return Theme.of(context).platform != TargetPlatform.iOS
        ? FloatingActionButton(
            onPressed: onEditTapped,
            child: const Icon(Icons.add),
          )
        : null;
  }

  Widget _buildSelections(
    BuildContext context,
    Map<Selection, List<GeoArea>> selections,
  ) {
    final localizations = AppLocalizations.of(context)!;
    return MultiSliver(
      children: [
        if (header != null) SliverToBoxAdapter(child: header!),
        for (final entry in selections.entries)
          SliverStickyHeader(
            header: _buildHeaderContent(entry, localizations),
            sliver: _buildAreas(entry),
          ),
      ],
    );
  }

  SliverList _buildAreas(MapEntry<Selection, List<GeoArea>> entry) {
    return SliverList(
      delegate: SliverChildBuilderDelegate((context, index) {
        final area = entry.value[index];
        return AreaTile(area: area);
      }, childCount: entry.value.length),
    );
  }

  Widget _buildHeaderContent(
    MapEntry<Selection, List<GeoArea>> entry,
    AppLocalizations localizations,
  ) {
    return ListViewHeader(title: entry.key.localized(localizations));
  }

  Widget _buildAppBar(BuildContext context) {
    return PlatformSliverAppBar(
      title: title,
      action: Theme.of(context).platform == TargetPlatform.iOS
          ? PlatformTextButton(
              padding: EdgeInsets.zero,
              title: AppLocalizations.of(context)!.update,
              onTapped: onEditTapped,
            )
          : null,
    );
  }
}
