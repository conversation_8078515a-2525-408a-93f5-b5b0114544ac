import 'dart:async';

import 'package:flutter/material.dart';

import '../../dependency_injection/dependency_injector.dart';
import '../../generic_widgets/pinned_selection_toggle.dart';
import '../../generic_widgets/platform_aware/platform_sliver_app_bar.dart';
import '../../generic_widgets/sliver_bottom_safe_area.dart';
import '../../generic_widgets/sliver_sticky_search_bar.dart';
import '../../generic_widgets/spinner.dart';
import '../../generic_widgets/streaming_selectable_tile.dart';
import '../../l10n/generated/app_localizations.dart';
import '../../models/geo_area.dart';
import '../../models/selection.dart';
import '../in_app_purchase/iap_status.dart';
import '../map/selection_bar.dart';
import 'replace_live_country_mixin.dart';

class AreaListPicker extends StatelessWidget {
  final GeoArea? area;
  final bool showAppBar;

  static const routeName = '/countrySelections';

  const AreaListPicker({super.key, this.area, this.showAppBar = true});

  @override
  Widget build(BuildContext context) {
    return GenericAreaListPicker(
      title: showAppBar ? _titleFetcher(context) : null,
      areaStream: _areaFetcher(),
      showAppBar: showAppBar,
    );
  }

  Stream<String> _titleFetcher(BuildContext context) {
    return DependencyInjector.settingsBloc.language.asyncMap((_) async {
      if (area == null && context.mounted) {
        return AppLocalizations.of(context)!.countries;
      }

      if (area != null) {
        final localizedArea = await DependencyInjector.areaBloc.areaByIsoCode(
          area!.isoCode,
        );
        return localizedArea?.name ?? area!.name;
      }

      return '';
    });
  }

  Stream<List<GeoArea>> _areaFetcher() async* {
    final bloc = DependencyInjector.areaBloc;
    final areas = await (area == null
        ? bloc.allCountries()
        : bloc.fetchSubdivisions(area!));
    yield areas;
  }
}

class GenericAreaListPicker extends StatefulWidget {
  final Stream<String>? title;
  final Stream<List<GeoArea>> areaStream;
  final bool showAppBar;

  const GenericAreaListPicker({
    super.key,
    required this.title,
    required this.areaStream,
    required this.showAppBar,
  });

  @override
  State createState() => _GenericAreaListPickerState();
}

class _GenericAreaListPickerState extends State<GenericAreaListPicker>
    with ReplaceLiveCountryMixin {
  bool loading = true;
  late List<GeoArea> areas;
  List<GeoArea>? queriedAreas;
  var currentSelection = Selection.been;
  var availableSelections = SelectionBar.defaultSelections;
  StreamSubscription<IAPStatus>? _livedListener;
  StreamSubscription? _areaListener;

  @override
  void initState() {
    super.initState();
    _loadData();

    _livedListener ??= DependencyInjector.iapBloc.status.listen((value) {
      setState(() {
        availableSelections = [
          Selection.live,
          if (value.canSelectLived) Selection.lived,
          Selection.been,
          Selection.want,
          Selection.clear,
        ];
      });
    });
  }

  void _loadData() async {
    _areaListener ??= widget.areaStream.listen((areas) {
      setState(() {
        loading = false;
        this.areas = areas;
      });
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: loading ? const Center(child: Spinner()) : _buildContent(),
    );
  }

  Widget _buildContent() {
    return Scrollbar(
      child: CustomScrollView(
        keyboardDismissBehavior: ScrollViewKeyboardDismissBehavior.onDrag,
        slivers: [
          if (widget.showAppBar && widget.title != null)
            StreamBuilder<String>(
              stream: widget.title,
              builder: (context, snapshot) {
                return PlatformSliverAppBar(title: snapshot.data ?? '');
              },
            ),
          SliverStickySearchBar<GeoArea>(
            items: areas,
            onQuery: (results) {
              setState(() {
                queriedAreas = results;
              });
            },
          ),
          PinnedSelectionToggle(
            expand: true,
            active: currentSelection,
            selections: availableSelections,
            onChanged: (value) {
              setState(() {
                currentSelection = value;
              });
            },
          ),
          _buildAreaList(),
          const SliverBottomSafeArea(height: 44),
        ],
      ),
    );
  }

  Widget _buildAreaList() {
    final areas = (queriedAreas?.isNotEmpty ?? false)
        ? queriedAreas!
        : this.areas;
    return SliverList.builder(
      itemCount: areas.length,
      itemBuilder: (context, index) {
        final area = areas[index];
        return StreamingSelectableTile(
          selectionStream: DependencyInjector.areaSelectionBloc
              .listenToSelection(area),
          item: area,
          onTapped: () async {
            final existingSelection = await DependencyInjector.areaSelectionBloc
                .fetchSelection(area);

            if (existingSelection == Selection.live &&
                currentSelection != Selection.live &&
                context.mounted) {
              final replaced = await replaceLiveCountry(
                area: area,
                context: context,
              );
              if (replaced == false) {
                return;
              }
            }

            DependencyInjector.areaSelectionBloc.select(area, currentSelection);
          },
        );
      },
    );
  }

  @override
  void dispose() {
    _areaListener?.cancel();
    _livedListener?.cancel();
    super.dispose();
  }
}
