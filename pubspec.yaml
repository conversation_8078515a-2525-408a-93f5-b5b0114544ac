name: visited
version: 4.3.15+4000185
publish_to: none
environment:
  sdk: ">=3.8.0 <4.0.0"

description: Visited 4.0

dependencies:
  address: ^0.1.0+2
  async: ^2.13.0
  bubble_chart: ^0.5.0
  cached_network_image: ^3.4.1
  collection: ^1.19.1
  connectivity_plus: ^6.1.4
  cupertino_icons: ^1.0.8
  debounce_throttle: ^2.0.0
  device_info_plus: ^11.5.0
  drift: ^2.28.0
  email_validator: ^3.0.0
  firebase_analytics: ^11.5.2
  firebase_core: ^3.15.1
  firebase_in_app_messaging: ^0.8.1+9
  firebase_remote_config: ^5.4.7
  flutter:
    sdk: flutter
  flutter_colorpicker: ^1.1.0
  flutter_isolate: ^2.1.0
  flutter_localizations:
    sdk: flutter
  flutter_map: ^6.2.1
  flutter_map_marker_cluster: ^1.3.6
  flutter_markdown: ^0.7.7+1
  flutter_sticky_header: ^0.8.0
  flutter_stripe: ^11.5.0
  flutter_svg: ^2.2.0
  flutter_tindercard:
    git:
      url: "https://github.com/bkayfitz/flutter_tindercard.git"
      ref: "null_safety"
  fuzzy: ^0.5.1
  get_it: ^8.0.3
  google_mobile_ads: ^6.0.0
  http: ^1.4.0
  in_app_purchase: ^3.2.3
  in_app_purchase_storekit: ^0.4.3
  in_app_review: ^2.0.10
  intl: any
  keyboard_actions: ^4.2.0
  latlong2: ^0.9.1
  lottie: ^3.3.1
  native_exif: ^0.6.2
  open_mail_app_plus: ^0.0.2
  path: ^1.9.1
  path_provider: ^2.1.5
  rxdart: ^0.28.0
  sentry_flutter: ^9.4.1
  share_plus: ^11.0.0
  shared_preferences: ^2.5.3
  sliver_tools: ^0.2.12
  sqlite3_flutter_libs: ^0.5.34
  smooth_page_indicator: ^1.2.1
  url_launcher: ^6.3.1
  video_player: ^2.10.0
  package_info_plus: ^8.3.0
  in_app_purchase_android: ^0.4.0+2

dev_dependencies:
  build_resolvers: ^3.0.0
  build_runner: ^2.6.0
  drift_dev: ^2.28.0
  flutter_lints: ^6.0.0
  flutter_test:
    sdk: flutter
  pubspec_dependency_sorter: ^1.0.5
  remove_unused_localizations_keys: ^0.0.40

dependency_overrides:
  stripe_android: ^11.5.0
  flutter_lints: ^6.0.0
  intl: ^0.20.2

flutter:
  uses-material-design: true
  generate: true
  assets:
    - assets/
    - assets/data/
    - assets/images/
    - assets/flags/
    - assets/anim/
    - assets/icons/
    - assets/fonts/
    - assets/poster/
    - assets/onboarding/

  fonts:
    - family: Arimo
      fonts:
        - asset: assets/fonts/Arimo-BoldItalic.ttf
          weight: 700
          style: italic

        - asset: assets/fonts/Arimo-Bold.ttf
          weight: 700

        - asset: assets/fonts/Arimo-SemiBoldItalic.ttf
          weight: 600
          style: italic

        - asset: assets/fonts/Arimo-SemiBold.ttf
          weight: 600

        - asset: assets/fonts/Arimo-MediumItalic.ttf
          weight: 500
          style: italic

        - asset: assets/fonts/Arimo-Medium.ttf
          weight: 500

        - asset: assets/fonts/Arimo-Regular.ttf
          weight: 400
