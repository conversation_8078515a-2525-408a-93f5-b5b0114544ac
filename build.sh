#!/bin/bash
set -e

flutter clean
rm pubspec.lock
rm ios/Podfile.lock
perl -i -pe 's/^(version:\s+\d+\.\d+\.\d+\+)(\d+)$/$1.($2+1)/e' pubspec.yaml

version=$(grep 'version: ' pubspec.yaml | sed 's/version: //')
echo Setting version number to "$version"

# Android to Firebase
flutter build apk \
  --flavor prod \
  --dart-define-from-file environment_prod.json

firebase appdistribution:distribute build/app/outputs/flutter-apk/app-prod-release.apk \
 --app 1:122594309874:android:89fc74a3f76ee240 \
 --release-notes "Beta Build" \
 --token "$FIREBASE_TOKEN_BKAYFITZ" \
 --testers "<EMAIL>, <EMAIL>"

flutter build appbundle \
  --flavor prod \
  --dart-define-from-file environment_prod.json

# iOS

PLIST='{"compileBitcode":false,"method":"app-store", "teamID": "79375UBF5L", "provisioningProfiles": {"com.HighHeels.Travelist": "Visited App Store", "com.HighHeels.Travelist.Visited-Stickers": "Visited Stickers App Store"}}'
EXPORT_PLIST=/tmp/${PROJECT}.plist

echo "$PLIST" | plutil -convert xml1 -o "$EXPORT_PLIST" -
flutter build ipa \
  --flavor prod \
  --export-options-plist="$EXPORT_PLIST" \
  --dart-define-from-file environment_prod.json

xcrun altool \
  --upload-app \
  --type ios \
  -f build/ios/ipa/visited.ipa \
  -u "$VISITED_APP_STORE_USER" \
  -p "$VISITED_APP_STORE_PASSWORD"

git add pubspec.yaml
git commit -m "automated build $version"
git push origin develop
