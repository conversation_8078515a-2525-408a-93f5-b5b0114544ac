import 'package:flutter_test/flutter_test.dart';
import 'package:visited/networking/network_request.dart';
import 'package:visited/networking/network_request_queue.dart';

void main() {
  test(
      'When the same request comes in multiple times before the first one is finished, its on executed once',
      () async {
    final hub = NetworkRequestQueue(
      requestProcessor: (uri) => Future.delayed(const Duration(seconds: 2))
          .then((_) => DateTime.now().millisecondsSinceEpoch),
    );

    const mockRequest = NetworkRequest('https://fake.not.a.url');
    final results = await Future.wait([
      hub.add(mockRequest),
      Future.delayed(const Duration(milliseconds: 0))
          .then((_) => hub.add(mockRequest))
    ]);

    expect(results.first, equals(results.last));
  });
}
