import 'package:flutter_test/flutter_test.dart';
import 'package:visited/caching/resettable_behaviour_subject.dart';

void main() {
  setUp(() => TestWidgetsFlutterBinding.ensureInitialized());

  test('onListen callbacks are maintained after resetting', () {
    final controller = ResettableBehaviorSubject();
    controller.onListen = () {};

    controller.reset();

    expect(controller.onListen, isNotNull);
  });

  test('Controllers can be reset', () {
    final controller = ResettableBehaviorSubject<int>();
    controller.add(5);
    expect(controller.valueOrNull, equals(5));

    controller.reset();
    expect(controller.valueOrNull, isNull);
  });
}
