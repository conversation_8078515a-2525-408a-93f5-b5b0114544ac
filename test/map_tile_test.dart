import 'package:flutter_test/flutter_test.dart';
import 'package:visited/features/map/tiles/internal/map_tile.dart';

void main() {
  test('Map Tile can be reconstructed by their ids', () {
    const tile = MapTile(x: 888, y: 999, z: 777);
    final id = tile.id;

    final recreated = MapTile.fromId(id);
    expect(tile, equals(recreated));
  });

  test(
      'The correct four children are returned when going one level deeper down the tile tree',
      () {
    const parent = MapTile(x: 0, y: 0, z: 1);

    final children = parent.children;
    expect(children.length, equals(4));

    expect(children.contains(const MapTile(x: 0, y: 0, z: 2)), isTrue);
    expect(children.contains(const MapTile(x: 1, y: 0, z: 2)), isTrue);
    expect(children.contains(const MapTile(x: 0, y: 1, z: 2)), isTrue);
    expect(children.contains(const MapTile(x: 1, y: 1, z: 2)), isTrue);

    const second = MapTile(x: 1, y: 1, z: 1);
    final secondChild = second.children;
    expect(secondChild.contains(const MapTile(x: 2, y: 2, z: 2)), isTrue);
    expect(secondChild.contains(const MapTile(x: 2, y: 3, z: 2)), isTrue);
    expect(secondChild.contains(const MapTile(x: 3, y: 2, z: 2)), isTrue);
    expect(secondChild.contains(const MapTile(x: 3, y: 3, z: 2)), isTrue);

    const third = MapTile(x: 2, y: 2, z: 1);
    final thirdChild = third.children;
    expect(thirdChild.contains(const MapTile(x: 4, y: 4, z: 2)), isTrue);
    expect(thirdChild.contains(const MapTile(x: 4, y: 5, z: 2)), isTrue);
    expect(thirdChild.contains(const MapTile(x: 5, y: 4, z: 2)), isTrue);
    expect(thirdChild.contains(const MapTile(x: 5, y: 5, z: 2)), isTrue);
  });
}
