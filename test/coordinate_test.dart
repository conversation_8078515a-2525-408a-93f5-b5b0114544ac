import 'dart:math';
import 'dart:ui';

import 'package:flutter_test/flutter_test.dart';
import 'package:visited/features/map/coordinate_transformer.dart';
import 'package:visited/features/map/projection.dart';
import 'package:visited/models/coordinate.dart';
import 'package:visited/models/geo_bounds.dart';

void main() {
  test('Coordinate is transformed correctly to fix on a constrained canvas',
      () {
    const coordinate = Coordinate(latitude: 0, longitude: 0);
    final bounds = GeoBounds(
        northWest: GeoBounds.wholePlanet.northWest, southEast: coordinate);
    const size = Size(100, 50);
    final transformer = CoordinateTransformer(
      bounds: bounds,
      size: size,
      projection: EquirectangularProjection(size.width),
    );
    final point = transformer.normalize(coordinate);

    expect(point, const Point(100, 50));
  });

  test('Original Coordinate can be restored', () {
    // Basic Test
    const point = Point(50, 25);
    const bounds = GeoBounds.wholePlanet;
    const size = Size(100, 50);
    final transformer = CoordinateTransformer(
      bounds: bounds,
      size: size,
      projection: EquirectangularProjection(size.width),
    );

    final coordinate = transformer.denormalize(point);
    expect(coordinate, const Coordinate(latitude: 0, longitude: 0));

    // Slightly More Advanced
    const centre = Coordinate(latitude: 0, longitude: 0);
    final subset = GeoBounds(
        northWest: GeoBounds.wholePlanet.northWest, southEast: centre);
    final secondTransformer = CoordinateTransformer(
      bounds: subset,
      size: size,
      projection: EquirectangularProjection(size.width),
    );

    final restored = secondTransformer.denormalize(const Point(100, 50));
    expect(restored, centre);
  });

  test('A random point can be normalized then denormalized', () {
    final random = Random();
    final coordinate = Coordinate(
      latitude: lerpDouble(
        Coordinate.min.latitude,
        Coordinate.max.latitude,
        random.nextDouble(),
      )!,
      longitude: lerpDouble(
        Coordinate.min.longitude,
        Coordinate.max.longitude,
        random.nextDouble(),
      )!,
    );

    const size = Size(1024, 512);
    const bounds = GeoBounds.wholePlanet;
    final transformer = CoordinateTransformer(
      bounds: bounds,
      size: size,
      projection: EquirectangularProjection(size.width),
    );

    final normalized = transformer.normalize(coordinate);
    final restored = transformer.denormalize(normalized);

    expect(restored, coordinate);
  });
}
