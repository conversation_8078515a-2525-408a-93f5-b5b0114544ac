import 'dart:io';

void main() {
  final dir = Directory.current;
  print(dir.toString());

  var count = 0;
  for (final flag in files) {
    final name = flag['flag_file_name'];
    final path = File('assets/flags/$name.png');

    if (path.existsSync() == false) {
      print(flag);
      count++;
    }
  }

  print('');
  print('$count missing flags');
}

const files = [
  {
    "id": 255,
    "iso_key": "AU-NT",
    "name": "Northern Territory",
    "flag_file_name": "flag_aunt"
  },
  {
    "id": 1744,
    "iso_key": "BES_2_1",
    "name": "Saba",
    "flag_file_name": "bes_2_1"
  },
  {"id": 190, "iso_key": "NO", "name": "Norway", "flag_file_name": "flag_no"},
  {
    "id": 315,
    "iso_key": "SC",
    "name": "Seychelles",
    "flag_file_name": "flag_sc"
  },
  {"id": 188, "iso_key": "ES", "name": "Spain", "flag_file_name": "flag_es"},
  {"id": 217, "iso_key": "RU", "name": "Russia", "flag_file_name": "flag_ru"},
  {"id": 191, "iso_key": "PT", "name": "Portugal", "flag_file_name": "flag_pt"},
  {
    "id": 97,
    "iso_key": "DO",
    "name": "Dominican Republic",
    "flag_file_name": "flag_do"
  },
  {
    "id": 304,
    "iso_key": "CF",
    "name": "Central African Republic",
    "flag_file_name": "flag_cf"
  },
  {
    "id": 1745,
    "iso_key": "BES_3_1",
    "name": "Sint Eustatius",
    "flag_file_name": "bes_3_1"
  },
  {
    "id": 91,
    "iso_key": "CA-ON",
    "name": "Ontario",
    "flag_file_name": "flag_caon"
  },
  {"id": 294, "iso_key": "RW", "name": "Rwanda", "flag_file_name": "flag_rw"},
  {
    "id": 22,
    "iso_key": "US-TN",
    "name": "Tennessee",
    "flag_file_name": "flag_ustn"
  },
  {
    "id": 45,
    "iso_key": "US-WI",
    "name": "Wisconsin",
    "flag_file_name": "flag_uswi"
  },
  {
    "id": 300,
    "iso_key": "MZ",
    "name": "Mozambique",
    "flag_file_name": "flag_mz"
  },
  {"id": 289, "iso_key": "MW", "name": "Malawi", "flag_file_name": "flag_mw"},
  {
    "id": 287,
    "iso_key": "MU",
    "name": "Mauritius",
    "flag_file_name": "flag_mu"
  },
  {"id": 282, "iso_key": "NA", "name": "Namibia", "flag_file_name": "flag_na"},
  {
    "id": 13,
    "iso_key": "US-PA",
    "name": "Pennsylvania",
    "flag_file_name": "flag_uspa"
  },
  {
    "id": 252,
    "iso_key": "AU-NSW",
    "name": "New South Wales",
    "flag_file_name": "flag_aunsw"
  },
  {"id": 298, "iso_key": "YT", "name": "Mayotte", "flag_file_name": "flag_yt"},
  {
    "id": 1234,
    "iso_key": "CO.AT",
    "name": "Atlántico",
    "flag_file_name": "co_at"
  },
  {
    "id": 1235,
    "iso_key": "CO.BL",
    "name": "Bolívar",
    "flag_file_name": "co_bl"
  },
  {"id": 1236, "iso_key": "CO.BY", "name": "Boyacá", "flag_file_name": "co_by"},
  {"id": 1237, "iso_key": "CO.CL", "name": "Caldas", "flag_file_name": "co_cl"},
  {
    "id": 43,
    "iso_key": "US-WA",
    "name": "Washington",
    "flag_file_name": "flag_uswa"
  },
  {
    "id": 54,
    "iso_key": "US-VT",
    "name": "Vermont",
    "flag_file_name": "flag_usvt"
  },
  {"id": 47, "iso_key": "US-UT", "name": "Utah", "flag_file_name": "flag_usut"},
  {
    "id": 56,
    "iso_key": "US-SC",
    "name": "South Carolina",
    "flag_file_name": "flag_ussc"
  },
  {
    "id": 49,
    "iso_key": "US-OR",
    "name": "Oregon",
    "flag_file_name": "flag_usor"
  },
  {
    "id": 24,
    "iso_key": "US-OK",
    "name": "Oklahoma",
    "flag_file_name": "flag_usok"
  },
  {
    "id": 44,
    "iso_key": "US-NM",
    "name": "New Mexico",
    "flag_file_name": "flag_usnm"
  },
  {
    "id": 48,
    "iso_key": "US-NH",
    "name": "New Hampshire",
    "flag_file_name": "flag_usnh"
  },
  {
    "id": 23,
    "iso_key": "US-NE",
    "name": "Nebraska",
    "flag_file_name": "flag_usne"
  },
  {
    "id": 40,
    "iso_key": "US-ND",
    "name": "North Dakota",
    "flag_file_name": "flag_usnd"
  },
  {
    "id": 46,
    "iso_key": "US-NC",
    "name": "North Carolina",
    "flag_file_name": "flag_usnc"
  },
  {
    "id": 41,
    "iso_key": "US-MT",
    "name": "Montana",
    "flag_file_name": "flag_usmt"
  },
  {
    "id": 26,
    "iso_key": "US-MS",
    "name": "Mississippi",
    "flag_file_name": "flag_usms"
  },
  {
    "id": 39,
    "iso_key": "US-ME",
    "name": "Maine",
    "flag_file_name": "flag_usme"
  },
  {
    "id": 60,
    "iso_key": "US-LA",
    "name": "Louisiana",
    "flag_file_name": "flag_usla"
  },
  {
    "id": 59,
    "iso_key": "US-NY",
    "name": "New York",
    "flag_file_name": "flag_usny"
  },
  {
    "id": 21,
    "iso_key": "US-KS",
    "name": "Kansas",
    "flag_file_name": "flag_usks"
  },
  {
    "id": 58,
    "iso_key": "US-IN",
    "name": "Indiana",
    "flag_file_name": "flag_usin"
  },
  {
    "id": 33,
    "iso_key": "US-IL",
    "name": "Illinois",
    "flag_file_name": "flag_usil"
  },
  {"id": 32, "iso_key": "US-IA", "name": "Iowa", "flag_file_name": "flag_usia"},
  {
    "id": 31,
    "iso_key": "US-HI",
    "name": "Hawaii",
    "flag_file_name": "flag_ushi"
  },
  {
    "id": 30,
    "iso_key": "US-DE",
    "name": "Delaware",
    "flag_file_name": "flag_usde"
  },
  {
    "id": 523,
    "iso_key": "AR-TM",
    "name": "Province de Tucumán",
    "flag_file_name": "AR-TM"
  },
  {
    "id": 51,
    "iso_key": "US-FL",
    "name": "Florida",
    "flag_file_name": "flag_usfl"
  },
  {"id": 25, "iso_key": "US-OH", "name": "Ohio", "flag_file_name": "flag_usoh"},
  {
    "id": 1238,
    "iso_key": "CO.CQ",
    "name": "Caquetá",
    "flag_file_name": "co_cq"
  },
  {
    "id": 52,
    "iso_key": "US-ID",
    "name": "Idaho",
    "flag_file_name": "flag_usid"
  },
  {
    "id": 28,
    "iso_key": "US-SD",
    "name": "South Dakota",
    "flag_file_name": "flag_ussd"
  },
  {
    "id": 29,
    "iso_key": "US-MA",
    "name": "Massachusetts",
    "flag_file_name": "flag_usma"
  },
  {
    "id": 36,
    "iso_key": "US-GA",
    "name": "Georgia",
    "flag_file_name": "flag_usga"
  },
  {
    "id": 16,
    "iso_key": "US-VA",
    "name": "Virginia",
    "flag_file_name": "flag_usva"
  },
  {
    "id": 35,
    "iso_key": "US-NJ",
    "name": "New Jersey",
    "flag_file_name": "flag_usnj"
  },
  {
    "id": 55,
    "iso_key": "US-MD",
    "name": "Maryland",
    "flag_file_name": "flag_usmd"
  },
  {
    "id": 19,
    "iso_key": "US-MN",
    "name": "Minnesota",
    "flag_file_name": "flag_usmn"
  },
  {
    "id": 12,
    "iso_key": "US-TX",
    "name": "Texas",
    "flag_file_name": "flag_ustx"
  },
  {
    "id": 17,
    "iso_key": "US-RI",
    "name": "Rhode Island",
    "flag_file_name": "flag_usri"
  },
  {
    "id": 20,
    "iso_key": "US-DC",
    "name": "District of Columbia",
    "flag_file_name": "flag_usdc"
  },
  {
    "id": 10,
    "iso_key": "US-CO",
    "name": "Colorado",
    "flag_file_name": "flag_usco"
  },
  {
    "id": 34,
    "iso_key": "US-AZ",
    "name": "Arizona",
    "flag_file_name": "flag_usaz"
  },
  {
    "id": 50,
    "iso_key": "US-AR",
    "name": "Arkansas",
    "flag_file_name": "flag_usar"
  },
  {
    "id": 53,
    "iso_key": "US-KY",
    "name": "Kentucky",
    "flag_file_name": "flag_usky"
  },
  {
    "id": 14,
    "iso_key": "US-AL",
    "name": "Alabama",
    "flag_file_name": "flag_usal"
  },
  {
    "id": 11,
    "iso_key": "US-WV",
    "name": "West Virginia",
    "flag_file_name": "flag_uswv"
  },
  {
    "id": 15,
    "iso_key": "US-CA",
    "name": "California",
    "flag_file_name": "flag_usca"
  },
  {
    "id": 1239,
    "iso_key": "CO.CS",
    "name": "Casanare",
    "flag_file_name": "co_cs"
  },
  {"id": 1240, "iso_key": "CO.CA", "name": "Cauca", "flag_file_name": "co_ca"},
  {"id": 1241, "iso_key": "CO.CE", "name": "Cesar", "flag_file_name": "co_ce"},
  {"id": 1242, "iso_key": "CO.CH", "name": "Chocó", "flag_file_name": "co_ch"},
  {
    "id": 1243,
    "iso_key": "CO.CO",
    "name": "Córdoba",
    "flag_file_name": "co_co"
  },
  {
    "id": 1244,
    "iso_key": "CO.CU",
    "name": "Cundinamarca",
    "flag_file_name": "co_cu"
  },
  {
    "id": 1245,
    "iso_key": "CO.GN",
    "name": "Guainía",
    "flag_file_name": "co_gn"
  },
  {
    "id": 1246,
    "iso_key": "CO.GV",
    "name": "Guaviare",
    "flag_file_name": "co_gv"
  },
  {"id": 1247, "iso_key": "CO.HU", "name": "Huila", "flag_file_name": "co_hu"},
  {
    "id": 18,
    "iso_key": "US-MI",
    "name": "Michigan",
    "flag_file_name": "flag_usmi"
  },
  {"id": 317, "iso_key": "TO", "name": "Tonga", "flag_file_name": "flag_to"},
  {
    "id": 266,
    "iso_key": "SL",
    "name": "Sierra Leone",
    "flag_file_name": "flag_sl"
  },
  {"id": 260, "iso_key": "SD", "name": "Sudan", "flag_file_name": "flag_sd"},
  {"id": 324, "iso_key": "SJ", "name": "Svalbard", "flag_file_name": "flag_sj"},
  {
    "id": 306,
    "iso_key": "SS",
    "name": "South Sudan",
    "flag_file_name": "flag_ss"
  },
  {"id": 301, "iso_key": "SN", "name": "Senegal", "flag_file_name": "flag_sn"},
  {
    "id": 296,
    "iso_key": "SH",
    "name": "Saint Helena",
    "flag_file_name": "flag_sh"
  },
  {"id": 286, "iso_key": "SO", "name": "Somalia", "flag_file_name": "flag_so"},
  {
    "id": 281,
    "iso_key": "ST",
    "name": "Sao Tome and Principe",
    "flag_file_name": "flag_st"
  },
  {
    "id": 259,
    "iso_key": "SOL",
    "name": "Somaliland",
    "flag_file_name": "flag_sol"
  },
  {
    "id": 244,
    "iso_key": "SB",
    "name": "Solomon Islands",
    "flag_file_name": "flag_sb"
  },
  {"id": 223, "iso_key": "SK", "name": "Slovakia", "flag_file_name": "flag_sk"},
  {
    "id": 1248,
    "iso_key": "CO.LG",
    "name": "La Guajira",
    "flag_file_name": "co_lg"
  },
  {
    "id": 1249,
    "iso_key": "CO.MA",
    "name": "Magdalena",
    "flag_file_name": "co_ma"
  },
  {"id": 1250, "iso_key": "CO.ME", "name": "Meta", "flag_file_name": "co_me"},
  {"id": 1251, "iso_key": "CO.NA", "name": "Nariño", "flag_file_name": "co_na"},
  {
    "id": 1252,
    "iso_key": "CO.NS",
    "name": "Norte de Santander",
    "flag_file_name": "co_ns"
  },
  {
    "id": 1253,
    "iso_key": "CO.PU",
    "name": "Putumayo",
    "flag_file_name": "co_pu"
  },
  {
    "id": 1254,
    "iso_key": "CO.QD",
    "name": "Quindío",
    "flag_file_name": "co_qd"
  },
  {
    "id": 1255,
    "iso_key": "CO.RI",
    "name": "Risaralda",
    "flag_file_name": "co_ri"
  },
  {
    "id": 1256,
    "iso_key": "CO.SA",
    "name": "San Andrés",
    "flag_file_name": "co_sa"
  },
  {
    "id": 1257,
    "iso_key": "CO.ST",
    "name": "Santander",
    "flag_file_name": "co_st"
  },
  {"id": 1258, "iso_key": "CO.SU", "name": "Sucre", "flag_file_name": "co_su"},
  {"id": 1259, "iso_key": "CO.TO", "name": "Tolima", "flag_file_name": "co_to"},
  {
    "id": 1260,
    "iso_key": "CO.VC",
    "name": "Valle del Cauca",
    "flag_file_name": "co_vc"
  },
  {"id": 1261, "iso_key": "CO.VP", "name": "Vaupés", "flag_file_name": "co_vp"},
  {
    "id": 1262,
    "iso_key": "CO.VD",
    "name": "Vichada",
    "flag_file_name": "co_vd"
  },
  {
    "id": 1263,
    "iso_key": "HR.BB",
    "name": "Bjelovarska-Bilogorska",
    "flag_file_name": "hr_bb"
  },
  {
    "id": 1264,
    "iso_key": "HR.SP",
    "name": "Brodsko-Posavska",
    "flag_file_name": "hr_sp"
  },
  {
    "id": 1265,
    "iso_key": "HR.DN",
    "name": "Dubrovacko-Neretvanska",
    "flag_file_name": "hr_dn"
  },
  {
    "id": 1266,
    "iso_key": "HR.GZ",
    "name": "Grad Zagreb",
    "flag_file_name": "hr_gz"
  },
  {
    "id": 1267,
    "iso_key": "HR.IS",
    "name": "Istarska",
    "flag_file_name": "hr_is"
  },
  {
    "id": 1268,
    "iso_key": "HR.KA",
    "name": "Karlovacka",
    "flag_file_name": "hr_ka"
  },
  {"id": 290, "iso_key": "NE", "name": "Niger", "flag_file_name": "flag_ne"},
  {
    "id": 1269,
    "iso_key": "HR.KK",
    "name": "Koprivnica-Križevci ",
    "flag_file_name": "hr_kk"
  },
  {
    "id": 1270,
    "iso_key": "HR.KZ",
    "name": "Krapinsko-Zagorska",
    "flag_file_name": "hr_kz"
  },
  {
    "id": 1271,
    "iso_key": "HR.LS",
    "name": "Licko-Senjska",
    "flag_file_name": "hr_ls"
  },
  {
    "id": 1272,
    "iso_key": "HR.ME",
    "name": "Medimurska",
    "flag_file_name": "hr_me"
  },
  {
    "id": 1273,
    "iso_key": "HR.OB",
    "name": "Osijek-Baranja",
    "flag_file_name": "hr_ob"
  },
  {
    "id": 1274,
    "iso_key": "HR.PS",
    "name": "Požega-Slavonia",
    "flag_file_name": "hr_ps"
  },
  {
    "id": 1275,
    "iso_key": "HR.PG",
    "name": "Primorsko-Goranska",
    "flag_file_name": "hr_pg"
  },
  {
    "id": 1276,
    "iso_key": "HR.SB",
    "name": "Šibenik-Knin",
    "flag_file_name": "hr_sb"
  },
  {
    "id": 1277,
    "iso_key": "HR.SM",
    "name": "Sisacko-Moslavacka",
    "flag_file_name": "hr_sm"
  },
  {
    "id": 1278,
    "iso_key": "HR.SD",
    "name": "Splitsko-Dalmatinska",
    "flag_file_name": "hr_sd"
  },
  {
    "id": 1279,
    "iso_key": "HR.VA",
    "name": "Varaždin",
    "flag_file_name": "hr_va"
  },
  {
    "id": 1280,
    "iso_key": "HR.VP",
    "name": "Viroviticko-Podravska",
    "flag_file_name": "hr_vp"
  },
  {
    "id": 251,
    "iso_key": "AU-VIC",
    "name": "Victoria",
    "flag_file_name": "flag_auvic"
  },
  {
    "id": 256,
    "iso_key": "AU-TAS",
    "name": "Tasmania",
    "flag_file_name": "flag_autas"
  },
  {
    "id": 1281,
    "iso_key": "HR.VS",
    "name": "Vukovarsko-Srijemska",
    "flag_file_name": "hr_vs"
  },
  {
    "id": 1282,
    "iso_key": "HR.ZD",
    "name": "Zadarska",
    "flag_file_name": "hr_zd"
  },
  {"id": 1283, "iso_key": "HR.ZG", "name": "Zagreb", "flag_file_name": "hr_zg"},
  {
    "id": 1284,
    "iso_key": "EG.DQ",
    "name": "Ad Daqahliyah",
    "flag_file_name": "eg_dq"
  },
  {
    "id": 27,
    "iso_key": "US-AK",
    "name": "Alaska",
    "flag_file_name": "flag_usak"
  },
  {
    "id": 1746,
    "iso_key": "AE-FU",
    "name": "Al Fujayrah",
    "flag_file_name": "AEFU"
  },
  {
    "id": 1747,
    "iso_key": "AE-SH",
    "name": "Ash Shariqah",
    "flag_file_name": "AESH"
  },
  {
    "id": 1748,
    "iso_key": "AE-RK",
    "name": "Ra's al Khaymah",
    "flag_file_name": "AERK"
  },
  {"id": 1749, "iso_key": "AE-DU", "name": "Dubayy", "flag_file_name": "AEDU"},
  {
    "id": 1750,
    "iso_key": "AE-AZ",
    "name": "Abu Zaby",
    "flag_file_name": "AEAZ"
  },
  {
    "id": 1751,
    "iso_key": "AE-UQ",
    "name": "Umm al Qaywayn",
    "flag_file_name": "AEUQ"
  },
  {"id": 1752, "iso_key": "AE-AJ", "name": "Ajman", "flag_file_name": "Ajman"},
  {
    "id": 1753,
    "iso_key": "CL-AP",
    "name": "Region de Arica y Parinacota",
    "flag_file_name": "CLAP"
  },
  {
    "id": 1754,
    "iso_key": "CL-TA",
    "name": "Region de Tarapaca",
    "flag_file_name": "CLTA"
  },
  {
    "id": 1755,
    "iso_key": "CL-AN",
    "name": "Region de Antofagasta",
    "flag_file_name": "CLAN"
  },
  {
    "id": 1756,
    "iso_key": "CL-AT",
    "name": "Region de Atacama",
    "flag_file_name": "CLAT"
  },
  {
    "id": 1285,
    "iso_key": "EG.BA",
    "name": "Al Bahr al Ahmar",
    "flag_file_name": "eg_ba"
  },
  {
    "id": 1286,
    "iso_key": "EG.BH",
    "name": "Al Buhayrah",
    "flag_file_name": "eg_bh"
  },
  {
    "id": 1287,
    "iso_key": "EG.FY",
    "name": "Al Fayyum",
    "flag_file_name": "eg_fy"
  },
  {
    "id": 1288,
    "iso_key": "EG.GH",
    "name": "Al Gharbiyah",
    "flag_file_name": "eg_gh"
  },
  {
    "id": 1289,
    "iso_key": "EG.IK",
    "name": "Al Iskandariyah",
    "flag_file_name": "eg_ik"
  },
  {
    "id": 1757,
    "iso_key": "CL-CO",
    "name": "Region de Coquimbo",
    "flag_file_name": "CLCO"
  },
  {
    "id": 1758,
    "iso_key": "CL-RM",
    "name": "Region Metropolitana de Santiago",
    "flag_file_name": "CLRM"
  },
  {
    "id": 1759,
    "iso_key": "CL-VS",
    "name": "Region de Valparaiso",
    "flag_file_name": "CLVS"
  },
  {
    "id": 1760,
    "iso_key": "CL-ML",
    "name": "Region del Maule",
    "flag_file_name": "CLML"
  },
  {
    "id": 1761,
    "iso_key": "CL-LI",
    "name": "Region del Libertador General Bernardo O'Higgins",
    "flag_file_name": "CLLI"
  },
  {
    "id": 221,
    "iso_key": "IM",
    "name": "Isle of Man",
    "flag_file_name": "flag_im"
  },
  {
    "id": 1290,
    "iso_key": "EG.IS",
    "name": "Al Isma`iliyah",
    "flag_file_name": "eg_is"
  },
  {
    "id": 1291,
    "iso_key": "EG.JZ",
    "name": "Al Jizah",
    "flag_file_name": "eg_jz"
  },
  {
    "id": 1292,
    "iso_key": "EG.MF",
    "name": "Al Minufiyah",
    "flag_file_name": "eg_mf"
  },
  {
    "id": 1293,
    "iso_key": "EG.MN",
    "name": "Al Minya",
    "flag_file_name": "eg_mn"
  },
  {
    "id": 1294,
    "iso_key": "EG.QH",
    "name": "Al Qahirah",
    "flag_file_name": "eg_qh"
  },
  {
    "id": 1295,
    "iso_key": "EG.QL",
    "name": "Al Qalyubiyah",
    "flag_file_name": "eg_ql"
  },
  {
    "id": 1296,
    "iso_key": "EG.UQ",
    "name": "Al Uqsur",
    "flag_file_name": "eg_uq"
  },
  {
    "id": 1297,
    "iso_key": "EG.WJ",
    "name": "Al Wadi al Jadid",
    "flag_file_name": "eg_wj"
  },
  {
    "id": 1298,
    "iso_key": "EG.SW",
    "name": "As Suways",
    "flag_file_name": "eg_sw"
  },
  {
    "id": 1299,
    "iso_key": "EG.SQ",
    "name": "Ash Sharqiyah",
    "flag_file_name": "eg_sq"
  },
  {"id": 1300, "iso_key": "EG.AN", "name": "Aswan", "flag_file_name": "eg_an"},
  {"id": 1301, "iso_key": "EG.AT", "name": "Asyut", "flag_file_name": "eg_at"},
  {
    "id": 1302,
    "iso_key": "EG.BN",
    "name": "Bani Suwayf",
    "flag_file_name": "eg_bn"
  },
  {
    "id": 1303,
    "iso_key": "EG.BS",
    "name": "Bur Sa`id",
    "flag_file_name": "eg_bs"
  },
  {
    "id": 1762,
    "iso_key": "CL-BI",
    "name": "Region del Biobio",
    "flag_file_name": "CLBI"
  },
  {
    "id": 327,
    "iso_key": "GS",
    "name": "South Georgia and the South Sandwich Islands",
    "flag_file_name": "flag_gs"
  },
  {"id": 1304, "iso_key": "EG.DT", "name": "Dumyat", "flag_file_name": "eg_dt"},
  {
    "id": 1305,
    "iso_key": "EG.JS",
    "name": "South Sinai",
    "flag_file_name": "eg_js"
  },
  {
    "id": 1306,
    "iso_key": "EG.KS",
    "name": "Kafr El Sheikh",
    "flag_file_name": "eg_ks"
  },
  {
    "id": 1307,
    "iso_key": "EG.MT",
    "name": "Matrouh",
    "flag_file_name": "eg_mt"
  },
  {"id": 1308, "iso_key": "EG.QN", "name": "Qina", "flag_file_name": "eg_qn"},
  {
    "id": 1309,
    "iso_key": "EG.SS",
    "name": "Shamal Sina'",
    "flag_file_name": "eg_ss"
  },
  {"id": 1310, "iso_key": "EG.SJ", "name": "Suhaj", "flag_file_name": "eg_sj"},
  {
    "id": 1763,
    "iso_key": "CL-AR",
    "name": "Region de la Araucania",
    "flag_file_name": "CLAR"
  },
  {
    "id": 1311,
    "iso_key": "FIN.1.1_1",
    "name": "North Karelia",
    "flag_file_name": "fin_1_1_1"
  },
  {
    "id": 1764,
    "iso_key": "CL-LR",
    "name": "Region de Los Rios",
    "flag_file_name": "CLLR"
  },
  {
    "id": 1312,
    "iso_key": "FIN.1.2_1",
    "name": "North Savonia",
    "flag_file_name": "fin_1_2_1"
  },
  {
    "id": 1313,
    "iso_key": "FIN.1.3_1",
    "name": "Päijänne-Tavastland",
    "flag_file_name": "fin_1_3_1"
  },
  {
    "id": 1314,
    "iso_key": "FIN.1.4_1",
    "name": "Southern Savonia",
    "flag_file_name": "fin_1_4_1"
  },
  {
    "id": 1315,
    "iso_key": "FIN.2.1_1",
    "name": "Lapland",
    "flag_file_name": "fin_2_1_1"
  },
  {
    "id": 1316,
    "iso_key": "FIN.3.1_1",
    "name": "Kainuu",
    "flag_file_name": "fin_3_1_1"
  },
  {
    "id": 1317,
    "iso_key": "FIN.3.2_1",
    "name": "Northern Ostrobothnia",
    "flag_file_name": "fin_3_2_1"
  },
  {
    "id": 1318,
    "iso_key": "FIN.4.1_1",
    "name": "Itä-Uusimaa",
    "flag_file_name": "fin_4_1_1"
  },
  {
    "id": 1319,
    "iso_key": "FIN.4.2_1",
    "name": "Kymenlaakso",
    "flag_file_name": "fin_4_2_1"
  },
  {
    "id": 1320,
    "iso_key": "FIN.4.3_1",
    "name": "Päijänne-Tavastland",
    "flag_file_name": "fin_4_3_1"
  },
  {
    "id": 1321,
    "iso_key": "FIN.4.4_1",
    "name": "South Karelia",
    "flag_file_name": "fin_4_4_1"
  },
  {
    "id": 1322,
    "iso_key": "FIN.4.5_1",
    "name": "Uusimaa",
    "flag_file_name": "fin_4_5_1"
  },
  {
    "id": 1323,
    "iso_key": "FIN.5.1_1",
    "name": "Central Finland",
    "flag_file_name": "fin_5_1_1"
  },
  {
    "id": 1324,
    "iso_key": "FIN.5.2_1",
    "name": "Central Ostrobothnia",
    "flag_file_name": "fin_5_2_1"
  },
  {"id": 273, "iso_key": "GM", "name": "Gambia", "flag_file_name": "flag_gm"},
  {
    "id": 1325,
    "iso_key": "FIN.5.3_1",
    "name": "Finland Proper",
    "flag_file_name": "fin_5_3_1"
  },
  {
    "id": 1326,
    "iso_key": "FIN.5.4_1",
    "name": "Ostrobothnia",
    "flag_file_name": "fin_5_4_1"
  },
  {
    "id": 1327,
    "iso_key": "FIN.5.5_1",
    "name": "Päijänne-Tavastland",
    "flag_file_name": "fin_5_5_1"
  },
  {
    "id": 1765,
    "iso_key": "CL-LL",
    "name": "Region de Los Lagos",
    "flag_file_name": "CLLL"
  },
  {
    "id": 1766,
    "iso_key": "CL-AI",
    "name": "Region Aisen del General Carlos Ibanez del Campo",
    "flag_file_name": "CLAI"
  },
  {
    "id": 1767,
    "iso_key": "CL-MA",
    "name": "Region de Magallanes y de la Antartica Chilena",
    "flag_file_name": "CLMA"
  },
  {
    "id": 1328,
    "iso_key": "FIN.5.6_1",
    "name": "Pirkanmaa",
    "flag_file_name": "fin_5_6_1"
  },
  {
    "id": 1329,
    "iso_key": "FIN.5.7_1",
    "name": "Satakunta",
    "flag_file_name": "fin_5_7_1"
  },
  {
    "id": 1330,
    "iso_key": "FIN.5.8_1",
    "name": "Southern Ostrobothnia",
    "flag_file_name": "fin_5_8_1"
  },
  {
    "id": 1331,
    "iso_key": "FIN.5.9_1",
    "name": "Tavastia Proper",
    "flag_file_name": "fin_5_9_1"
  },
  {"id": 1352, "iso_key": "ID.AC", "name": "Aceh", "flag_file_name": "id_ac"},
  {"id": 1353, "iso_key": "ID.BA", "name": "Bali", "flag_file_name": "id_ba"},
  {
    "id": 1354,
    "iso_key": "ID.BB",
    "name": "Bangka Belitung",
    "flag_file_name": "id_bb"
  },
  {"id": 1355, "iso_key": "ID.BT", "name": "Banten", "flag_file_name": "id_bt"},
  {
    "id": 1356,
    "iso_key": "ID.BE",
    "name": "Bengkulu",
    "flag_file_name": "id_be"
  },
  {
    "id": 1357,
    "iso_key": "ID.GO",
    "name": "Gorontalo",
    "flag_file_name": "id_go"
  },
  {
    "id": 1358,
    "iso_key": "ID.JK",
    "name": "Jakarta Raya",
    "flag_file_name": "id_jk"
  },
  {"id": 1359, "iso_key": "ID.JA", "name": "Jambi", "flag_file_name": "id_ja"},
  {
    "id": 1360,
    "iso_key": "ID.JR",
    "name": "Jawa Barat",
    "flag_file_name": "id_jr"
  },
  {
    "id": 1361,
    "iso_key": "ID.JT",
    "name": "Jawa Tengah",
    "flag_file_name": "id_jt"
  },
  {
    "id": 1362,
    "iso_key": "ID.JI",
    "name": "Jawa Timur",
    "flag_file_name": "id_ji"
  },
  {
    "id": 1363,
    "iso_key": "ID.KB",
    "name": "Kalimantan Barat",
    "flag_file_name": "id_kb"
  },
  {
    "id": 1364,
    "iso_key": "ID.KS",
    "name": "Kalimantan Selatan",
    "flag_file_name": "id_ks"
  },
  {
    "id": 1365,
    "iso_key": "ID.KT",
    "name": "Kalimantan Tengah",
    "flag_file_name": "id_kt"
  },
  {
    "id": 1366,
    "iso_key": "ID.KM",
    "name": "Kalimantan Timur",
    "flag_file_name": "id_km"
  },
  {
    "id": 1367,
    "iso_key": "ID.KR",
    "name": "Kepulauan Riau",
    "flag_file_name": "id_kr"
  },
  {
    "id": 319,
    "iso_key": "CV",
    "name": "Cape Verde",
    "flag_file_name": "flag_cv"
  },
  {"id": 311, "iso_key": "DZ", "name": "Algeria", "flag_file_name": "flag_dz"},
  {
    "id": 1368,
    "iso_key": "ID.LA",
    "name": "Lampung",
    "flag_file_name": "id_la"
  },
  {
    "id": 88,
    "iso_key": "CA-YT",
    "name": "Yukon",
    "flag_file_name": "flag_cayt"
  },
  {
    "id": 85,
    "iso_key": "CA-SK",
    "name": "Saskatchewan",
    "flag_file_name": "flag_cask"
  },
  {
    "id": 92,
    "iso_key": "CA-QC",
    "name": "Quebec",
    "flag_file_name": "flag_caqc"
  },
  {
    "id": 95,
    "iso_key": "CA-PE",
    "name": "Prince Edward Island",
    "flag_file_name": "flag_cape"
  },
  {
    "id": 84,
    "iso_key": "CA-NU",
    "name": "Nunavut",
    "flag_file_name": "flag_canu"
  },
  {
    "id": 93,
    "iso_key": "CA-NT",
    "name": "Northwest Territories",
    "flag_file_name": "flag_cant"
  },
  {
    "id": 94,
    "iso_key": "CA-NS",
    "name": "Nova Scotia",
    "flag_file_name": "flag_cans"
  },
  {
    "id": 87,
    "iso_key": "CA-NL",
    "name": "Newfoundland and Labrador",
    "flag_file_name": "flag_canl"
  },
  {
    "id": 86,
    "iso_key": "CA-NB",
    "name": "New Brunswick",
    "flag_file_name": "flag_canb"
  },
  {
    "id": 83,
    "iso_key": "CA-MB",
    "name": "Manitoba",
    "flag_file_name": "flag_camb"
  },
  {
    "id": 89,
    "iso_key": "CA-AB",
    "name": "Alberta",
    "flag_file_name": "flag_caab"
  },
  {"id": 1369, "iso_key": "ID.MA", "name": "Maluku", "flag_file_name": "id_ma"},
  {
    "id": 1370,
    "iso_key": "ID.MU",
    "name": "Maluku Utara",
    "flag_file_name": "id_mu"
  },
  {
    "id": 1371,
    "iso_key": "ID.NB",
    "name": "Nusa Tenggara Barat",
    "flag_file_name": "id_nb"
  },
  {
    "id": 1372,
    "iso_key": "ID.NT",
    "name": "Nusa Tenggara Timur",
    "flag_file_name": "id_nt"
  },
  {"id": 1373, "iso_key": "ID.PA", "name": "Papua", "flag_file_name": "id_pa"},
  {
    "id": 1374,
    "iso_key": "ID.IB",
    "name": "Papua Barat",
    "flag_file_name": "id_ib"
  },
  {"id": 1375, "iso_key": "ID.RI", "name": "Riau", "flag_file_name": "id_ri"},
  {
    "id": 1376,
    "iso_key": "ID.SR",
    "name": "Sulawesi Barat",
    "flag_file_name": "id_sr"
  },
  {
    "id": 1377,
    "iso_key": "ID.SE",
    "name": "Sulawesi Selatan",
    "flag_file_name": "id_se"
  },
  {
    "id": 326,
    "iso_key": "BV",
    "name": "Bouvet Island",
    "flag_file_name": "flag_bv"
  },
  {
    "id": 1378,
    "iso_key": "ID.ST",
    "name": "Sulawesi Tengah",
    "flag_file_name": "id_st"
  },
  {
    "id": 1379,
    "iso_key": "ID.SG",
    "name": "Sulawesi Tenggara",
    "flag_file_name": "id_sg"
  },
  {
    "id": 1380,
    "iso_key": "ID.SW",
    "name": "Sulawesi Utara",
    "flag_file_name": "id_sw"
  },
  {
    "id": 1381,
    "iso_key": "ID.SB",
    "name": "Sumatera Barat",
    "flag_file_name": "id_sb"
  },
  {
    "id": 90,
    "iso_key": "CA-BC",
    "name": "British Columbia",
    "flag_file_name": "flag_cabc"
  },
  {"id": 313, "iso_key": "BJ", "name": "Benin", "flag_file_name": "flag_bj"},
  {
    "id": 250,
    "iso_key": "AU-SA",
    "name": "South Australia",
    "flag_file_name": "flag_ausa"
  },
  {
    "id": 1382,
    "iso_key": "ID.SL",
    "name": "Sumatera Selatan",
    "flag_file_name": "id_sl"
  },
  {
    "id": 248,
    "iso_key": "AU-JB",
    "name": "Jervis Bay Territory",
    "flag_file_name": "flag_aujb"
  },
  {
    "id": 249,
    "iso_key": "AU-ACT",
    "name": "Australian Capital Territory",
    "flag_file_name": "flag_auact"
  },
  {
    "id": 1383,
    "iso_key": "ID.SU",
    "name": "Sumatera Utara",
    "flag_file_name": "id_su"
  },
  {
    "id": 1384,
    "iso_key": "ID.YO",
    "name": "Yogyakarta",
    "flag_file_name": "id_yo"
  },
  {"id": 1385, "iso_key": "MY.JH", "name": "Johor", "flag_file_name": "my_jh"},
  {"id": 1386, "iso_key": "MY.KH", "name": "Kedah", "flag_file_name": "my_kh"},
  {
    "id": 1387,
    "iso_key": "MY.KN",
    "name": "Kelantan",
    "flag_file_name": "my_kn"
  },
  {
    "id": 1388,
    "iso_key": "MY.KL",
    "name": "Kuala Lumpur",
    "flag_file_name": "my_kl"
  },
  {
    "id": 253,
    "iso_key": "AU-WA",
    "name": "Western Australia",
    "flag_file_name": "flag_auwa"
  },
  {"id": 1389, "iso_key": "MY.LA", "name": "Labuan", "flag_file_name": "my_la"},
  {
    "id": 254,
    "iso_key": "AU-QLD",
    "name": "Queensland",
    "flag_file_name": "flag_auqld"
  },
  {"id": 1390, "iso_key": "MY.ME", "name": "Melaka", "flag_file_name": "my_me"},
  {
    "id": 1391,
    "iso_key": "MY.NS",
    "name": "Negeri Sembilan",
    "flag_file_name": "my_ns"
  },
  {"id": 1392, "iso_key": "MY.PH", "name": "Pahang", "flag_file_name": "my_ph"},
  {"id": 1393, "iso_key": "MY.PK", "name": "Perak", "flag_file_name": "my_pk"},
  {"id": 1394, "iso_key": "MY.PL", "name": "Perlis", "flag_file_name": "my_pl"},
  {
    "id": 678,
    "iso_key": "UA-9",
    "name": "Khmelnytskyi Oblast",
    "flag_file_name": "UA-9"
  },
  {
    "id": 677,
    "iso_key": "UA-8",
    "name": "Kherson Oblast",
    "flag_file_name": "UA-8"
  },
  {
    "id": 676,
    "iso_key": "UA-7",
    "name": "Kharkiv Oblast",
    "flag_file_name": "UA-7"
  },
  {
    "id": 675,
    "iso_key": "UA-6",
    "name": "Ivano-Frankivsk Oblast",
    "flag_file_name": "UA-6"
  },
  {
    "id": 674,
    "iso_key": "UA-5",
    "name": "Donetsk Oblast",
    "flag_file_name": "UA-5"
  },
  {
    "id": 673,
    "iso_key": "UA-4",
    "name": "Dnipropetrovsk Oblast",
    "flag_file_name": "UA-4"
  },
  {
    "id": 672,
    "iso_key": "UA-3",
    "name": "Chernivtsi Oblast",
    "flag_file_name": "UA-3"
  },
  {"id": 1027, "iso_key": "UA-25", "name": "Kiev", "flag_file_name": "UA-25"},
  {
    "id": 693,
    "iso_key": "UA-24",
    "name": "Zhytomyr Oblast",
    "flag_file_name": "UA-24"
  },
  {
    "id": 692,
    "iso_key": "UA-23",
    "name": "Zaporizhia Oblast",
    "flag_file_name": "UA-23"
  },
  {
    "id": 691,
    "iso_key": "UA-22",
    "name": "Zakarpattia Oblast",
    "flag_file_name": "UA-22"
  },
  {
    "id": 690,
    "iso_key": "UA-21",
    "name": "Volyn Oblast",
    "flag_file_name": "UA-21"
  },
  {
    "id": 689,
    "iso_key": "UA-20",
    "name": "Vinnytsia Oblast",
    "flag_file_name": "UA-20"
  },
  {
    "id": 671,
    "iso_key": "UA-2",
    "name": "Chernihiv Oblast",
    "flag_file_name": "UA-2"
  },
  {
    "id": 688,
    "iso_key": "UA-19",
    "name": "Ternopil Oblast",
    "flag_file_name": "UA-19"
  },
  {
    "id": 687,
    "iso_key": "UA-18",
    "name": "Sumy Oblast",
    "flag_file_name": "UA-18"
  },
  {
    "id": 686,
    "iso_key": "UA-17",
    "name": "Rivne Oblast",
    "flag_file_name": "UA-17"
  },
  {
    "id": 685,
    "iso_key": "UA-16",
    "name": "Poltava Oblast",
    "flag_file_name": "UA-16"
  },
  {
    "id": 684,
    "iso_key": "UA-15",
    "name": "Odessa Oblast",
    "flag_file_name": "UA-15"
  },
  {
    "id": 683,
    "iso_key": "UA-14",
    "name": "Mykolaiv Oblast",
    "flag_file_name": "UA-14"
  },
  {
    "id": 681,
    "iso_key": "UA-12",
    "name": "Luhansk Oblast",
    "flag_file_name": "UA-12"
  },
  {
    "id": 680,
    "iso_key": "UA-11",
    "name": "Kirovohrad Oblast",
    "flag_file_name": "UA-11"
  },
  {
    "id": 679,
    "iso_key": "UA-10",
    "name": "Kiev Oblast",
    "flag_file_name": "UA-10"
  },
  {
    "id": 670,
    "iso_key": "UA-1",
    "name": "Cherkasy Oblast",
    "flag_file_name": "UA-1"
  },
  {
    "id": 410,
    "iso_key": "RU-92",
    "name": "Zabaykalsky Krai",
    "flag_file_name": "RU-92"
  },
  {
    "id": 409,
    "iso_key": "RU-91",
    "name": "Kamchatka Krai",
    "flag_file_name": "RU-91"
  },
  {
    "id": 336,
    "iso_key": "RU-9",
    "name": "Karachay-Cherkess",
    "flag_file_name": "RU-9"
  },
  {
    "id": 407,
    "iso_key": "RU-89",
    "name": "Yamalo-Nenets Autonomous Okrug",
    "flag_file_name": "RU-89"
  },
  {
    "id": 406,
    "iso_key": "RU-87",
    "name": "Chukotka Autonomous Okrug",
    "flag_file_name": "RU-87"
  },
  {
    "id": 133,
    "iso_key": "AF",
    "name": "Afghanistan",
    "flag_file_name": "flag_af"
  },
  {
    "id": 1395,
    "iso_key": "MY.PG",
    "name": "Pulau Pinang",
    "flag_file_name": "my_pg"
  },
  {
    "id": 1396,
    "iso_key": "MY.PJ",
    "name": "Putrajaya",
    "flag_file_name": "my_pj"
  },
  {"id": 1397, "iso_key": "MY.SA", "name": "Sabah", "flag_file_name": "my_sa"},
  {
    "id": 682,
    "iso_key": "UA-13",
    "name": "Lviv Oblast",
    "flag_file_name": "UA-13"
  },
  {
    "id": 405,
    "iso_key": "RU-86",
    "name": "Khanty–Mansi Autonomous Okrug – Yugra",
    "flag_file_name": "RU-86"
  },
  {
    "id": 404,
    "iso_key": "RU-83",
    "name": "Nenets Autonomous Okrug",
    "flag_file_name": "RU-83"
  },
  {"id": 335, "iso_key": "RU-8", "name": "Kalmykia", "flag_file_name": "RU-8"},
  {
    "id": 403,
    "iso_key": "RU-79",
    "name": "Jewish Autonomous Oblast",
    "flag_file_name": "RU-79"
  },
  {
    "id": 402,
    "iso_key": "RU-78",
    "name": "Saint Petersburg",
    "flag_file_name": "RU-78"
  },
  {"id": 401, "iso_key": "RU-77", "name": "Moscow", "flag_file_name": "RU-77"},
  {
    "id": 400,
    "iso_key": "RU-76",
    "name": "Yaroslavl Oblast",
    "flag_file_name": "RU-76"
  },
  {
    "id": 397,
    "iso_key": "RU-72",
    "name": "Tyumen Oblast",
    "flag_file_name": "RU-72"
  },
  {
    "id": 396,
    "iso_key": "RU-71",
    "name": "Tula Oblast",
    "flag_file_name": "RU-71"
  },
  {
    "id": 395,
    "iso_key": "RU-70",
    "name": "Tomsk Oblast",
    "flag_file_name": "RU-70"
  },
  {
    "id": 334,
    "iso_key": "RU-7",
    "name": "Kabardino-Balkar",
    "flag_file_name": "RU-7"
  },
  {
    "id": 394,
    "iso_key": "RU-69",
    "name": "Tver Oblast",
    "flag_file_name": "RU-69"
  },
  {
    "id": 393,
    "iso_key": "RU-68",
    "name": "Tambov Oblast",
    "flag_file_name": "RU-68"
  },
  {
    "id": 392,
    "iso_key": "RU-67",
    "name": "Smolensk Oblast",
    "flag_file_name": "RU-67"
  },
  {
    "id": 391,
    "iso_key": "RU-66",
    "name": "Sverdlovsk Oblast",
    "flag_file_name": "RU-66"
  },
  {
    "id": 390,
    "iso_key": "RU-65",
    "name": "Sakhalin Oblast",
    "flag_file_name": "RU-65"
  },
  {
    "id": 389,
    "iso_key": "RU-64",
    "name": "Saratov Oblast",
    "flag_file_name": "RU-64"
  },
  {
    "id": 388,
    "iso_key": "RU-63",
    "name": "Samara Oblast",
    "flag_file_name": "RU-63"
  },
  {
    "id": 387,
    "iso_key": "RU-62",
    "name": "Ryazan Oblast",
    "flag_file_name": "RU-62"
  },
  {
    "id": 386,
    "iso_key": "RU-61",
    "name": "Rostov Oblast",
    "flag_file_name": "RU-61"
  },
  {
    "id": 385,
    "iso_key": "RU-60",
    "name": "Pskov Oblast",
    "flag_file_name": "RU-60"
  },
  {
    "id": 333,
    "iso_key": "RU-6",
    "name": "Ingushetia",
    "flag_file_name": "RU-6"
  },
  {
    "id": 384,
    "iso_key": "RU-58",
    "name": "Penza Oblast",
    "flag_file_name": "RU-58"
  },
  {
    "id": 383,
    "iso_key": "RU-57",
    "name": "Oryol Oblast",
    "flag_file_name": "RU-57"
  },
  {
    "id": 382,
    "iso_key": "RU-56",
    "name": "Orenburg Oblast",
    "flag_file_name": "RU-56"
  },
  {
    "id": 381,
    "iso_key": "RU-55",
    "name": "Omsk Oblast",
    "flag_file_name": "RU-55"
  },
  {
    "id": 380,
    "iso_key": "RU-54",
    "name": "Novosibirsk Oblast",
    "flag_file_name": "RU-54"
  },
  {
    "id": 379,
    "iso_key": "RU-53",
    "name": "Novgorod Oblast",
    "flag_file_name": "RU-53"
  },
  {
    "id": 378,
    "iso_key": "RU-52",
    "name": "Nizhny Novgorod Oblast",
    "flag_file_name": "RU-52"
  },
  {
    "id": 377,
    "iso_key": "RU-51",
    "name": "Murmansk Oblast",
    "flag_file_name": "RU-51"
  },
  {
    "id": 376,
    "iso_key": "RU-50",
    "name": "Moscow Oblast",
    "flag_file_name": "RU-50"
  },
  {"id": 332, "iso_key": "RU-5", "name": "Dagestan", "flag_file_name": "RU-5"},
  {
    "id": 375,
    "iso_key": "RU-49",
    "name": "Magadan Oblast",
    "flag_file_name": "RU-49"
  },
  {
    "id": 1398,
    "iso_key": "MY.SK",
    "name": "Sarawak",
    "flag_file_name": "my_sk"
  },
  {
    "id": 1399,
    "iso_key": "MY.SL",
    "name": "Selangor",
    "flag_file_name": "my_sl"
  },
  {
    "id": 1400,
    "iso_key": "MY.TE",
    "name": "Trengganu",
    "flag_file_name": "my_te"
  },
  {
    "id": 1435,
    "iso_key": "NO.AK",
    "name": "Akershus",
    "flag_file_name": "no_ak"
  },
  {
    "id": 1436,
    "iso_key": "NO.OF",
    "name": "Østfold",
    "flag_file_name": "no_of"
  },
  {
    "id": 1437,
    "iso_key": "NO.AA",
    "name": "Aust-Agder",
    "flag_file_name": "no_aa"
  },
  {
    "id": 373,
    "iso_key": "RU-47",
    "name": "Leningrad Oblast",
    "flag_file_name": "RU-47"
  },
  {
    "id": 372,
    "iso_key": "RU-46",
    "name": "Kursk Oblast",
    "flag_file_name": "RU-46"
  },
  {
    "id": 371,
    "iso_key": "RU-45",
    "name": "Kurgan Oblast",
    "flag_file_name": "RU-45"
  },
  {
    "id": 370,
    "iso_key": "RU-44",
    "name": "Kostroma Oblast",
    "flag_file_name": "RU-44"
  },
  {
    "id": 369,
    "iso_key": "RU-43",
    "name": "Kirov Oblast",
    "flag_file_name": "RU-43"
  },
  {
    "id": 368,
    "iso_key": "RU-42",
    "name": "Kemerovo Oblast",
    "flag_file_name": "RU-42"
  },
  {
    "id": 367,
    "iso_key": "RU-40",
    "name": "Kaluga Oblast",
    "flag_file_name": "RU-40"
  },
  {"id": 331, "iso_key": "RU-4", "name": "Altai", "flag_file_name": "RU-4"},
  {
    "id": 366,
    "iso_key": "RU-39",
    "name": "Kaliningrad Oblast",
    "flag_file_name": "RU-39"
  },
  {
    "id": 365,
    "iso_key": "RU-38",
    "name": "Irkutsk Oblast",
    "flag_file_name": "RU-38"
  },
  {
    "id": 364,
    "iso_key": "RU-37",
    "name": "Ivanovo Oblast",
    "flag_file_name": "RU-37"
  },
  {"id": 330, "iso_key": "RU-3", "name": "Buryatia", "flag_file_name": "RU-3"},
  {
    "id": 356,
    "iso_key": "RU-29",
    "name": "Arkhangelsk Oblast",
    "flag_file_name": "RU-29"
  },
  {
    "id": 355,
    "iso_key": "RU-28",
    "name": "Amur Oblast",
    "flag_file_name": "RU-28"
  },
  {
    "id": 354,
    "iso_key": "RU-27",
    "name": "Khabarovsk Krai",
    "flag_file_name": "RU-27"
  },
  {
    "id": 353,
    "iso_key": "RU-26",
    "name": "Stavropol Krai",
    "flag_file_name": "RU-26"
  },
  {
    "id": 352,
    "iso_key": "RU-25",
    "name": "Primorsky Krai",
    "flag_file_name": "RU-25"
  },
  {
    "id": 351,
    "iso_key": "RU-24",
    "name": "Krasnoyarsk Krai",
    "flag_file_name": "RU-24"
  },
  {
    "id": 350,
    "iso_key": "RU-23",
    "name": "Krasnodar Krai",
    "flag_file_name": "RU-23"
  },
  {
    "id": 349,
    "iso_key": "RU-22",
    "name": "Altai Krai",
    "flag_file_name": "RU-22"
  },
  {
    "id": 1438,
    "iso_key": "NO.BU",
    "name": "Buskerud",
    "flag_file_name": "no_bu"
  },
  {
    "id": 1439,
    "iso_key": "NO.FI",
    "name": "Finnmark",
    "flag_file_name": "no_fi"
  },
  {
    "id": 1440,
    "iso_key": "NO.HE",
    "name": "Hedmark",
    "flag_file_name": "no_he"
  },
  {
    "id": 1441,
    "iso_key": "NO.HO",
    "name": "Hordaland",
    "flag_file_name": "no_ho"
  },
  {
    "id": 1442,
    "iso_key": "NO.MR",
    "name": "Møre og Romsdal",
    "flag_file_name": "no_mr"
  },
  {
    "id": 1443,
    "iso_key": "NO.NT",
    "name": "Nord-Trøndelag",
    "flag_file_name": "no_nt"
  },
  {
    "id": 1444,
    "iso_key": "NO.NO",
    "name": "Nordland",
    "flag_file_name": "no_no"
  },
  {
    "id": 1445,
    "iso_key": "NO.OP",
    "name": "Oppland",
    "flag_file_name": "no_op"
  },
  {"id": 1446, "iso_key": "NO.OS", "name": "Oslo", "flag_file_name": "no_os"},
  {
    "id": 1447,
    "iso_key": "NO.RO",
    "name": "Rogaland",
    "flag_file_name": "no_ro"
  },
  {
    "id": 1448,
    "iso_key": "NO.SF",
    "name": "Sogn og Fjordane",
    "flag_file_name": "no_sf"
  },
  {
    "id": 1449,
    "iso_key": "NO.ST",
    "name": "Sør-Trøndelag",
    "flag_file_name": "no_st"
  },
  {
    "id": 1450,
    "iso_key": "NO.TE",
    "name": "Telemark",
    "flag_file_name": "no_te"
  },
  {"id": 1451, "iso_key": "NO.TR", "name": "Troms", "flag_file_name": "no_tr"},
  {
    "id": 1452,
    "iso_key": "NO.VA",
    "name": "Vest-Agder",
    "flag_file_name": "no_va"
  },
  {
    "id": 1453,
    "iso_key": "NO.VF",
    "name": "Vestfold",
    "flag_file_name": "no_vf"
  },
  {
    "id": 1454,
    "iso_key": "POL.1_1",
    "name": "Dolnośląskie",
    "flag_file_name": "pol_1_1"
  },
  {
    "id": 1455,
    "iso_key": "POL.2_1",
    "name": "kujawsko-pomorskie",
    "flag_file_name": "pol_2_1"
  },
  {
    "id": 1456,
    "iso_key": "POL.3_1",
    "name": "Łódzkie",
    "flag_file_name": "pol_3_1"
  },
  {
    "id": 1457,
    "iso_key": "POL.4_1",
    "name": "Lubelskie",
    "flag_file_name": "pol_4_1"
  },
  {
    "id": 1458,
    "iso_key": "POL.5_1",
    "name": "Lubuskie",
    "flag_file_name": "pol_5_1"
  },
  {
    "id": 1459,
    "iso_key": "POL.6_1",
    "name": "Małopolskie",
    "flag_file_name": "pol_6_1"
  },
  {
    "id": 1460,
    "iso_key": "POL.7_1",
    "name": "Mazowieckie",
    "flag_file_name": "pol_7_1"
  },
  {
    "id": 1461,
    "iso_key": "POL.8_1",
    "name": "Opolskie",
    "flag_file_name": "pol_8_1"
  },
  {
    "id": 1462,
    "iso_key": "POL.9_1",
    "name": "Podkarpackie",
    "flag_file_name": "pol_9_1"
  },
  {
    "id": 1463,
    "iso_key": "POL.10_1",
    "name": "Podlaskie",
    "flag_file_name": "pol_10_1"
  },
  {
    "id": 374,
    "iso_key": "RU-48",
    "name": "Lipetsk Oblast",
    "flag_file_name": "RU-48"
  },
  {
    "id": 359,
    "iso_key": "RU-32",
    "name": "Bryansk Oblast",
    "flag_file_name": "RU-32"
  },
  {
    "id": 358,
    "iso_key": "RU-31",
    "name": "Belgorod Oblast",
    "flag_file_name": "RU-31"
  },
  {
    "id": 357,
    "iso_key": "RU-30",
    "name": "Astrakhan Oblast",
    "flag_file_name": "RU-30"
  },
  {
    "id": 1464,
    "iso_key": "POL.11_1",
    "name": "Pomorskie",
    "flag_file_name": "pol_11_1"
  },
  {
    "id": 1465,
    "iso_key": "POL.12_1",
    "name": "Śląskie",
    "flag_file_name": "pol_12_1"
  },
  {
    "id": 1466,
    "iso_key": "POL.13_1",
    "name": "Świętokrzyskie",
    "flag_file_name": "pol_13_1"
  },
  {
    "id": 1467,
    "iso_key": "POL.14_1",
    "name": "Warmińsko-Mazurskie",
    "flag_file_name": "pol_14_1"
  },
  {
    "id": 1468,
    "iso_key": "POL.15_1",
    "name": "Wielkopolskie",
    "flag_file_name": "pol_15_1"
  },
  {
    "id": 1469,
    "iso_key": "POL.16_1",
    "name": "Zachodniopomorskie",
    "flag_file_name": "pol_16_1"
  },
  {"id": 1470, "iso_key": "RO.AB", "name": "Alba", "flag_file_name": "ro_ab"},
  {"id": 1471, "iso_key": "RO.AR", "name": "Arad", "flag_file_name": "ro_ar"},
  {"id": 1472, "iso_key": "RO.AG", "name": "Argeș", "flag_file_name": "ro_ag"},
  {"id": 1473, "iso_key": "RO.BC", "name": "Bacău", "flag_file_name": "ro_bc"},
  {"id": 1474, "iso_key": "RO.BH", "name": "Bihor", "flag_file_name": "ro_bh"},
  {
    "id": 1475,
    "iso_key": "RO.BN",
    "name": "Bistrița-Năsăud",
    "flag_file_name": "ro_bn"
  },
  {
    "id": 1476,
    "iso_key": "RO.BT",
    "name": "Botoșani",
    "flag_file_name": "ro_bt"
  },
  {"id": 1477, "iso_key": "RO.BV", "name": "Brașov", "flag_file_name": "ro_bv"},
  {"id": 1478, "iso_key": "RO.BR", "name": "Brăila", "flag_file_name": "ro_br"},
  {
    "id": 1479,
    "iso_key": "RO.BI",
    "name": "București",
    "flag_file_name": "ro_bi"
  },
  {"id": 1480, "iso_key": "RO.BZ", "name": "Buzău", "flag_file_name": "ro_bz"},
  {
    "id": 1481,
    "iso_key": "RO.CL",
    "name": "Călărași",
    "flag_file_name": "ro_cl"
  },
  {
    "id": 1482,
    "iso_key": "RO.CS",
    "name": "Caraș-Severin",
    "flag_file_name": "ro_cs"
  },
  {"id": 1483, "iso_key": "RO.CJ", "name": "Cluj", "flag_file_name": "ro_cj"},
  {
    "id": 1484,
    "iso_key": "RO.CT",
    "name": "Constanța",
    "flag_file_name": "ro_ct"
  },
  {
    "id": 1485,
    "iso_key": "RO.CV",
    "name": "Covasna",
    "flag_file_name": "ro_cv"
  },
  {
    "id": 1486,
    "iso_key": "RO.DB",
    "name": "Dâmbovița",
    "flag_file_name": "ro_db"
  },
  {"id": 1487, "iso_key": "RO.DJ", "name": "Dolj", "flag_file_name": "ro_dj"},
  {"id": 1488, "iso_key": "RO.GL", "name": "Galați", "flag_file_name": "ro_gl"},
  {
    "id": 1489,
    "iso_key": "RO.GR",
    "name": "Giurgiu",
    "flag_file_name": "ro_gr"
  },
  {"id": 1490, "iso_key": "RO.GJ", "name": "Gorj", "flag_file_name": "ro_gj"},
  {
    "id": 1491,
    "iso_key": "RO.HR",
    "name": "Harghita",
    "flag_file_name": "ro_hr"
  },
  {
    "id": 1492,
    "iso_key": "RO.HD",
    "name": "Hunedoara",
    "flag_file_name": "ro_hd"
  },
  {
    "id": 38,
    "iso_key": "US-MO",
    "name": "Missouri",
    "flag_file_name": "flag_usmo"
  },
  {"id": 1493, "iso_key": "RO.IS", "name": "Iași", "flag_file_name": "ro_is"},
  {
    "id": 348,
    "iso_key": "RU-21",
    "name": "Chuvashia",
    "flag_file_name": "RU-21"
  },
  {"id": 347, "iso_key": "RU-20", "name": "Chechen", "flag_file_name": "RU-20"},
  {
    "id": 329,
    "iso_key": "RU-2",
    "name": "Bashkortostan",
    "flag_file_name": "RU-2"
  },
  {
    "id": 346,
    "iso_key": "RU-19",
    "name": "Khakassia",
    "flag_file_name": "RU-19"
  },
  {"id": 345, "iso_key": "RU-18", "name": "Udmurt", "flag_file_name": "RU-18"},
  {"id": 344, "iso_key": "RU-17", "name": "Tuva", "flag_file_name": "RU-17"},
  {
    "id": 343,
    "iso_key": "RU-16",
    "name": "Tatarstan",
    "flag_file_name": "RU-16"
  },
  {
    "id": 342,
    "iso_key": "RU-15",
    "name": "North Ossetia-Alania",
    "flag_file_name": "RU-15"
  },
  {
    "id": 341,
    "iso_key": "RU-14",
    "name": "Sakha (Yakutia)",
    "flag_file_name": "RU-14"
  },
  {
    "id": 340,
    "iso_key": "RU-13",
    "name": "Mordovia",
    "flag_file_name": "RU-13"
  },
  {"id": 339, "iso_key": "RU-12", "name": "Mari El", "flag_file_name": "RU-12"},
  {"id": 338, "iso_key": "RU-11", "name": "Komi", "flag_file_name": "RU-11"},
  {"id": 337, "iso_key": "RU-10", "name": "Karelia", "flag_file_name": "RU-10"},
  {"id": 328, "iso_key": "RU-1", "name": "Adygea", "flag_file_name": "RU-1"},
  {"id": 532, "iso_key": "MX-9", "name": "Colima", "flag_file_name": "MX-9"},
  {"id": 531, "iso_key": "MX-8", "name": "Coahuila", "flag_file_name": "MX-8"},
  {"id": 530, "iso_key": "MX-7", "name": "Chihuahua", "flag_file_name": "MX-7"},
  {
    "id": 529,
    "iso_key": "MX-6",
    "name": "District fédéral",
    "flag_file_name": "MX-6"
  },
  {"id": 528, "iso_key": "MX-5", "name": "Chiapas", "flag_file_name": "MX-5"},
  {"id": 527, "iso_key": "MX-4", "name": "Campeche", "flag_file_name": "MX-4"},
  {
    "id": 555,
    "iso_key": "MX-32",
    "name": "Zacatecas",
    "flag_file_name": "MX-32"
  },
  {"id": 554, "iso_key": "MX-31", "name": "Yucatán", "flag_file_name": "MX-31"},
  {
    "id": 553,
    "iso_key": "MX-30",
    "name": "Veracruz",
    "flag_file_name": "MX-30"
  },
  {
    "id": 526,
    "iso_key": "MX-3",
    "name": "Baja California Sur",
    "flag_file_name": "MX-3"
  },
  {
    "id": 552,
    "iso_key": "MX-29",
    "name": "Tlaxcala",
    "flag_file_name": "MX-29"
  },
  {
    "id": 551,
    "iso_key": "MX-28",
    "name": "Tamaulipas",
    "flag_file_name": "MX-28"
  },
  {"id": 550, "iso_key": "MX-27", "name": "Tabasco", "flag_file_name": "MX-27"},
  {"id": 549, "iso_key": "MX-26", "name": "Sonora", "flag_file_name": "MX-26"},
  {"id": 548, "iso_key": "MX-25", "name": "Sinaloa", "flag_file_name": "MX-25"},
  {
    "id": 547,
    "iso_key": "MX-24",
    "name": "San Luis Potosí",
    "flag_file_name": "MX-24"
  },
  {
    "id": 546,
    "iso_key": "MX-23",
    "name": "Quintana Roo",
    "flag_file_name": "MX-23"
  },
  {
    "id": 545,
    "iso_key": "MX-22",
    "name": "Querétaro",
    "flag_file_name": "MX-22"
  },
  {
    "id": 1494,
    "iso_key": "RO.IL",
    "name": "Ialomița",
    "flag_file_name": "ro_il"
  },
  {"id": 1495, "iso_key": "RO.IF", "name": "Ilfov", "flag_file_name": "ro_if"},
  {
    "id": 1496,
    "iso_key": "RO.MM",
    "name": "Maramureș",
    "flag_file_name": "ro_mm"
  },
  {"id": 544, "iso_key": "MX-21", "name": "Puebla", "flag_file_name": "MX-21"},
  {"id": 543, "iso_key": "MX-20", "name": "Oaxaca", "flag_file_name": "MX-20"},
  {
    "id": 525,
    "iso_key": "MX-2",
    "name": "Baja California",
    "flag_file_name": "MX-2"
  },
  {
    "id": 542,
    "iso_key": "MX-19",
    "name": "Nuevo León",
    "flag_file_name": "MX-19"
  },
  {"id": 541, "iso_key": "MX-18", "name": "Nayarit", "flag_file_name": "MX-18"},
  {"id": 540, "iso_key": "MX-17", "name": "Morelos", "flag_file_name": "MX-17"},
  {
    "id": 539,
    "iso_key": "MX-16",
    "name": "Michoacán",
    "flag_file_name": "MX-16"
  },
  {
    "id": 538,
    "iso_key": "MX-15",
    "name": "México State",
    "flag_file_name": "MX-15"
  },
  {"id": 537, "iso_key": "MX-14", "name": "Jalisco", "flag_file_name": "MX-14"},
  {"id": 536, "iso_key": "MX-13", "name": "Hidalgo", "flag_file_name": "MX-13"},
  {
    "id": 535,
    "iso_key": "MX-12",
    "name": "Guerrero",
    "flag_file_name": "MX-12"
  },
  {
    "id": 534,
    "iso_key": "MX-11",
    "name": "Guanajuato",
    "flag_file_name": "MX-11"
  },
  {"id": 533, "iso_key": "MX-10", "name": "Durango", "flag_file_name": "MX-10"},
  {
    "id": 524,
    "iso_key": "MX-1",
    "name": "Aguascalientes",
    "flag_file_name": "MX-1"
  },
  {"id": 784, "iso_key": "JP-9", "name": "Gifu", "flag_file_name": "JP-9"},
  {"id": 783, "iso_key": "JP-8", "name": "Fukushima", "flag_file_name": "JP-8"},
  {"id": 782, "iso_key": "JP-7", "name": "Fukuoka", "flag_file_name": "JP-7"},
  {"id": 781, "iso_key": "JP-6", "name": "Fukui", "flag_file_name": "JP-6"},
  {"id": 780, "iso_key": "JP-5", "name": "Ehime", "flag_file_name": "JP-5"},
  {
    "id": 822,
    "iso_key": "JP-47",
    "name": "Yamanashi",
    "flag_file_name": "JP-47"
  },
  {
    "id": 821,
    "iso_key": "JP-46",
    "name": "Yamaguchi",
    "flag_file_name": "JP-46"
  },
  {
    "id": 820,
    "iso_key": "JP-45",
    "name": "Yamagata",
    "flag_file_name": "JP-45"
  },
  {
    "id": 819,
    "iso_key": "JP-44",
    "name": "Wakayama",
    "flag_file_name": "JP-44"
  },
  {"id": 818, "iso_key": "JP-43", "name": "Toyama", "flag_file_name": "JP-43"},
  {"id": 817, "iso_key": "JP-42", "name": "Tottori", "flag_file_name": "JP-42"},
  {"id": 816, "iso_key": "JP-41", "name": "Tokyo", "flag_file_name": "JP-41"},
  {
    "id": 815,
    "iso_key": "JP-40",
    "name": "Tokushima",
    "flag_file_name": "JP-40"
  },
  {"id": 779, "iso_key": "JP-4", "name": "Chiba", "flag_file_name": "JP-4"},
  {"id": 814, "iso_key": "JP-39", "name": "Tochigi", "flag_file_name": "JP-39"},
  {
    "id": 813,
    "iso_key": "JP-38",
    "name": "Shizuoka",
    "flag_file_name": "JP-38"
  },
  {"id": 812, "iso_key": "JP-37", "name": "Shimane", "flag_file_name": "JP-37"},
  {"id": 811, "iso_key": "JP-36", "name": "Shiga", "flag_file_name": "JP-36"},
  {"id": 810, "iso_key": "JP-35", "name": "Saitama", "flag_file_name": "JP-35"},
  {"id": 809, "iso_key": "JP-34", "name": "Saga", "flag_file_name": "JP-34"},
  {
    "id": 1497,
    "iso_key": "RO.MH",
    "name": "Mehedinți",
    "flag_file_name": "ro_mh"
  },
  {"id": 1498, "iso_key": "RO.MS", "name": "Mureș", "flag_file_name": "ro_ms"},
  {"id": 1499, "iso_key": "RO.NT", "name": "Neamț", "flag_file_name": "ro_nt"},
  {
    "id": 399,
    "iso_key": "RU-74",
    "name": "Chelyabinsk Oblast",
    "flag_file_name": "RU-74"
  },
  {
    "id": 398,
    "iso_key": "RU-73",
    "name": "Ulyanovsk Oblast",
    "flag_file_name": "RU-73"
  },
  {
    "id": 363,
    "iso_key": "RU-36",
    "name": "Voronezh Oblast",
    "flag_file_name": "RU-36"
  },
  {
    "id": 362,
    "iso_key": "RU-35",
    "name": "Vologda Oblast",
    "flag_file_name": "RU-35"
  },
  {
    "id": 361,
    "iso_key": "RU-34",
    "name": "Volgograd Oblast",
    "flag_file_name": "RU-34"
  },
  {
    "id": 360,
    "iso_key": "RU-33",
    "name": "Vladimir Oblast",
    "flag_file_name": "RU-33"
  },
  {"id": 808, "iso_key": "JP-33", "name": "Osaka", "flag_file_name": "JP-33"},
  {"id": 807, "iso_key": "JP-32", "name": "Okinawa", "flag_file_name": "JP-32"},
  {"id": 806, "iso_key": "JP-31", "name": "Okayama", "flag_file_name": "JP-31"},
  {"id": 805, "iso_key": "JP-30", "name": "Ōita", "flag_file_name": "JP-30"},
  {"id": 778, "iso_key": "JP-3", "name": "Aomori", "flag_file_name": "JP-3"},
  {"id": 804, "iso_key": "JP-29", "name": "Niigata", "flag_file_name": "JP-29"},
  {"id": 803, "iso_key": "JP-28", "name": "Nara", "flag_file_name": "JP-28"},
  {
    "id": 802,
    "iso_key": "JP-27",
    "name": "Nagasaki",
    "flag_file_name": "JP-27"
  },
  {"id": 801, "iso_key": "JP-26", "name": "Nagano", "flag_file_name": "JP-26"},
  {
    "id": 800,
    "iso_key": "JP-25",
    "name": "Miyazaki",
    "flag_file_name": "JP-25"
  },
  {"id": 799, "iso_key": "JP-24", "name": "Miyagi", "flag_file_name": "JP-24"},
  {"id": 798, "iso_key": "JP-23", "name": "Mie", "flag_file_name": "JP-23"},
  {"id": 797, "iso_key": "JP-22", "name": "Kyoto", "flag_file_name": "JP-22"},
  {
    "id": 796,
    "iso_key": "JP-21",
    "name": "Kumamoto",
    "flag_file_name": "JP-21"
  },
  {"id": 795, "iso_key": "JP-20", "name": "Kōchi", "flag_file_name": "JP-20"},
  {"id": 777, "iso_key": "JP-2", "name": "Akita", "flag_file_name": "JP-2"},
  {
    "id": 794,
    "iso_key": "JP-19",
    "name": "Kanagawa",
    "flag_file_name": "JP-19"
  },
  {"id": 1500, "iso_key": "RO.OT", "name": "Olt", "flag_file_name": "ro_ot"},
  {
    "id": 1501,
    "iso_key": "RO.PH",
    "name": "Prahova",
    "flag_file_name": "ro_ph"
  },
  {"id": 1502, "iso_key": "RO.SJ", "name": "Sălaj", "flag_file_name": "ro_sj"},
  {
    "id": 1503,
    "iso_key": "RO.SM",
    "name": "Satu Mare",
    "flag_file_name": "ro_sm"
  },
  {"id": 1504, "iso_key": "RO.SB", "name": "Sibiu", "flag_file_name": "ro_sb"},
  {
    "id": 1505,
    "iso_key": "RO.SV",
    "name": "Suceava",
    "flag_file_name": "ro_sv"
  },
  {
    "id": 1506,
    "iso_key": "RO.TR",
    "name": "Teleorman",
    "flag_file_name": "ro_tr"
  },
  {"id": 1507, "iso_key": "RO.TM", "name": "Timiș", "flag_file_name": "ro_tm"},
  {"id": 1508, "iso_key": "RO.TL", "name": "Tulcea", "flag_file_name": "ro_tl"},
  {"id": 1509, "iso_key": "RO.VL", "name": "Vâlcea", "flag_file_name": "ro_vl"},
  {"id": 1510, "iso_key": "RO.VS", "name": "Vaslui", "flag_file_name": "ro_vs"},
  {
    "id": 1511,
    "iso_key": "RO.VN",
    "name": "Vrancea",
    "flag_file_name": "ro_vn"
  },
  {
    "id": 1525,
    "iso_key": "ZA.EC",
    "name": "Eastern Cape",
    "flag_file_name": "za_ec"
  },
  {
    "id": 1526,
    "iso_key": "ZA.FS",
    "name": "Free State",
    "flag_file_name": "za_fs"
  },
  {
    "id": 1527,
    "iso_key": "ZA.GT",
    "name": "Gauteng",
    "flag_file_name": "za_gt"
  },
  {
    "id": 1528,
    "iso_key": "ZA.NL",
    "name": "KwaZulu-Natal",
    "flag_file_name": "za_nl"
  },
  {
    "id": 1529,
    "iso_key": "ZA.NP",
    "name": "Limpopo",
    "flag_file_name": "za_np"
  },
  {
    "id": 1530,
    "iso_key": "ZA.MP",
    "name": "Mpumalanga",
    "flag_file_name": "za_mp"
  },
  {
    "id": 1531,
    "iso_key": "ZA.NW",
    "name": "North West",
    "flag_file_name": "za_nw"
  },
  {
    "id": 1532,
    "iso_key": "ZA.NC",
    "name": "Northern Cape",
    "flag_file_name": "za_nc"
  },
  {
    "id": 1533,
    "iso_key": "ZA.WC",
    "name": "Western Cape",
    "flag_file_name": "za_wc"
  },
  {"id": 1534, "iso_key": "KR.PU", "name": "Busan", "flag_file_name": "kr_pu"},
  {
    "id": 1535,
    "iso_key": "KR.GB",
    "name": "Chungcheongbuk-do",
    "flag_file_name": "kr_gb"
  },
  {
    "id": 1536,
    "iso_key": "KR.GN",
    "name": "Chungcheongnam-do",
    "flag_file_name": "kr_gn"
  },
  {"id": 1537, "iso_key": "KR.TG", "name": "Daegu", "flag_file_name": "kr_tg"},
  {
    "id": 1538,
    "iso_key": "KR.TJ",
    "name": "Daejeon",
    "flag_file_name": "kr_tj"
  },
  {
    "id": 1539,
    "iso_key": "KR.KW",
    "name": "Gangwon-do",
    "flag_file_name": "kr_kw"
  },
  {
    "id": 1540,
    "iso_key": "KR.KJ",
    "name": "Gwangju",
    "flag_file_name": "kr_kj"
  },
  {
    "id": 1541,
    "iso_key": "KR.KG",
    "name": "Gyeonggi-do",
    "flag_file_name": "kr_kg"
  },
  {
    "id": 1542,
    "iso_key": "KR.KB",
    "name": "Gyeongsangbuk-do",
    "flag_file_name": "kr_kb"
  },
  {
    "id": 1543,
    "iso_key": "KR.KN",
    "name": "Gyeongsangnam-do",
    "flag_file_name": "kr_kn"
  },
  {
    "id": 1544,
    "iso_key": "KR.IN",
    "name": "Incheon",
    "flag_file_name": "kr_in"
  },
  {"id": 1545, "iso_key": "KR.CJ", "name": "Jeju", "flag_file_name": "kr_cj"},
  {
    "id": 1546,
    "iso_key": "KR.CB",
    "name": "Jeollabuk-do",
    "flag_file_name": "kr_cb"
  },
  {
    "id": 1547,
    "iso_key": "KR.CN",
    "name": "Jeollanam-do",
    "flag_file_name": "kr_cn"
  },
  {"id": 1548, "iso_key": "KR.SJ", "name": "Sejong", "flag_file_name": "kr_sj"},
  {"id": 1549, "iso_key": "KR.SO", "name": "Seoul", "flag_file_name": "kr_so"},
  {"id": 1550, "iso_key": "KR.UL", "name": "Ulsan", "flag_file_name": "kr_ul"},
  {
    "id": 1551,
    "iso_key": "SE.BL",
    "name": "Blekinge",
    "flag_file_name": "se_bl"
  },
  {
    "id": 1552,
    "iso_key": "SE.KO",
    "name": "Dalarna",
    "flag_file_name": "se_ko"
  },
  {
    "id": 1553,
    "iso_key": "SE.GV",
    "name": "Gävleborgs",
    "flag_file_name": "se_gv"
  },
  {
    "id": 1554,
    "iso_key": "SE.GT",
    "name": "Gotland",
    "flag_file_name": "se_gt"
  },
  {
    "id": 1555,
    "iso_key": "SE.HA",
    "name": "Halland",
    "flag_file_name": "se_ha"
  },
  {
    "id": 1556,
    "iso_key": "SE.JA",
    "name": "Jämtland",
    "flag_file_name": "se_ja"
  },
  {
    "id": 1557,
    "iso_key": "SE.JO",
    "name": "Jönköpings",
    "flag_file_name": "se_jo"
  },
  {"id": 1558, "iso_key": "SE.KA", "name": "Kalmar", "flag_file_name": "se_ka"},
  {
    "id": 1559,
    "iso_key": "SE.KR",
    "name": "Kronoberg",
    "flag_file_name": "se_kr"
  },
  {
    "id": 1560,
    "iso_key": "SE.NB",
    "name": "Norrbotten",
    "flag_file_name": "se_nb"
  },
  {"id": 1561, "iso_key": "SE.OR", "name": "Orebro", "flag_file_name": "se_or"},
  {
    "id": 1562,
    "iso_key": "SE.OG",
    "name": "Östergötland",
    "flag_file_name": "se_og"
  },
  {"id": 1563, "iso_key": "SE.SN", "name": "Skåne", "flag_file_name": "se_sn"},
  {
    "id": 1564,
    "iso_key": "SE.SD",
    "name": "Södermanland   ",
    "flag_file_name": "se_sd"
  },
  {
    "id": 1565,
    "iso_key": "SE.ST",
    "name": "Stockholm",
    "flag_file_name": "se_st"
  },
  {
    "id": 1566,
    "iso_key": "SE.UP",
    "name": "Uppsala",
    "flag_file_name": "se_up"
  },
  {
    "id": 1567,
    "iso_key": "SE.VR",
    "name": "Värmland",
    "flag_file_name": "se_vr"
  },
  {
    "id": 1568,
    "iso_key": "SE.VB",
    "name": "Västerbotten",
    "flag_file_name": "se_vb"
  },
  {
    "id": 1569,
    "iso_key": "SE.VN",
    "name": "Västernorrlands",
    "flag_file_name": "se_vn"
  },
  {
    "id": 1570,
    "iso_key": "SE.VM",
    "name": "Västmanland",
    "flag_file_name": "se_vm"
  },
  {
    "id": 1571,
    "iso_key": "SE.VG",
    "name": "Västra Götalands",
    "flag_file_name": "se_vg"
  },
  {
    "id": 1572,
    "iso_key": "TH.AC",
    "name": "Amnat Charoen",
    "flag_file_name": "th_ac"
  },
  {
    "id": 1573,
    "iso_key": "TH.AT",
    "name": "Ang Thong",
    "flag_file_name": "th_at"
  },
  {
    "id": 1574,
    "iso_key": "TH.BM",
    "name": "Bangkok Metropolis",
    "flag_file_name": "th_bm"
  },
  {
    "id": 1575,
    "iso_key": "TH.BK",
    "name": "Bueng Kan",
    "flag_file_name": "th_bk"
  },
  {
    "id": 1576,
    "iso_key": "TH.BR",
    "name": "Buri Ram",
    "flag_file_name": "th_br"
  },
  {
    "id": 1232,
    "iso_key": "CO.AN",
    "name": "Antioquia",
    "flag_file_name": "co_an"
  },
  {"id": 1233, "iso_key": "CO.AR", "name": "Arauca", "flag_file_name": "co_ar"},
  {
    "id": 1231,
    "iso_key": "CO.AM",
    "name": "Amazonas",
    "flag_file_name": "co_am"
  },
  {
    "id": 42,
    "iso_key": "US-NV",
    "name": "Nevada",
    "flag_file_name": "flag_usnv"
  },
  {
    "id": 1577,
    "iso_key": "TH.CC",
    "name": "Chachoengsao",
    "flag_file_name": "th_cc"
  },
  {
    "id": 1578,
    "iso_key": "TH.CN",
    "name": "Chai Nat",
    "flag_file_name": "th_cn"
  },
  {
    "id": 1579,
    "iso_key": "TH.CY",
    "name": "Chaiyaphum",
    "flag_file_name": "th_cy"
  },
  {
    "id": 1580,
    "iso_key": "TH.CT",
    "name": "Chanthaburi",
    "flag_file_name": "th_ct"
  },
  {
    "id": 1581,
    "iso_key": "TH.CM",
    "name": "Chiang Mai",
    "flag_file_name": "th_cm"
  },
  {
    "id": 1582,
    "iso_key": "TH.CR",
    "name": "Chiang Rai",
    "flag_file_name": "th_cr"
  },
  {
    "id": 1583,
    "iso_key": "TH.CB",
    "name": "Chon Buri",
    "flag_file_name": "th_cb"
  },
  {
    "id": 1584,
    "iso_key": "TH.CP",
    "name": "Chumphon",
    "flag_file_name": "th_cp"
  },
  {
    "id": 1585,
    "iso_key": "TH.KL",
    "name": "Kalasin",
    "flag_file_name": "th_kl"
  },
  {
    "id": 1586,
    "iso_key": "TH.KP",
    "name": "Kamphaeng Phet",
    "flag_file_name": "th_kp"
  },
  {
    "id": 1587,
    "iso_key": "TH.KN",
    "name": "Kanchanaburi",
    "flag_file_name": "th_kn"
  },
  {
    "id": 1588,
    "iso_key": "TH.KK",
    "name": "Khon Kaen",
    "flag_file_name": "th_kk"
  },
  {"id": 1589, "iso_key": "TH.KR", "name": "Krabi", "flag_file_name": "th_kr"},
  {
    "id": 1590,
    "iso_key": "TH.LG",
    "name": "Lampang",
    "flag_file_name": "th_lg"
  },
  {
    "id": 1591,
    "iso_key": "TH.LN",
    "name": "Lamphun",
    "flag_file_name": "th_ln"
  },
  {"id": 1592, "iso_key": "TH.LE", "name": "Loei", "flag_file_name": "th_le"},
  {
    "id": 1593,
    "iso_key": "TH.LB",
    "name": "Lop Buri",
    "flag_file_name": "th_lb"
  },
  {
    "id": 1594,
    "iso_key": "TH.MH",
    "name": "Mae Hong Son",
    "flag_file_name": "th_mh"
  },
  {
    "id": 1595,
    "iso_key": "TH.MS",
    "name": "Maha Sarakham",
    "flag_file_name": "th_ms"
  },
  {
    "id": 1596,
    "iso_key": "TH.MD",
    "name": "Mukdahan",
    "flag_file_name": "th_md"
  },
  {
    "id": 1597,
    "iso_key": "TH.NN",
    "name": "Nakhon Nayok",
    "flag_file_name": "th_nn"
  },
  {
    "id": 1598,
    "iso_key": "TH.NP",
    "name": "Nakhon Pathom",
    "flag_file_name": "th_np"
  },
  {
    "id": 1599,
    "iso_key": "TH.NF",
    "name": "Nakhon Phanom",
    "flag_file_name": "th_nf"
  },
  {
    "id": 1600,
    "iso_key": "TH.NR",
    "name": "Nakhon Ratchasima",
    "flag_file_name": "th_nr"
  },
  {
    "id": 1601,
    "iso_key": "TH.NS",
    "name": "Nakhon Sawan",
    "flag_file_name": "th_ns"
  },
  {
    "id": 1602,
    "iso_key": "TH.NT",
    "name": "Nakhon Si Thammarat",
    "flag_file_name": "th_nt"
  },
  {"id": 1603, "iso_key": "TH.NA", "name": "Nan", "flag_file_name": "th_na"},
  {
    "id": 1604,
    "iso_key": "TH.NW",
    "name": "Narathiwat",
    "flag_file_name": "th_nw"
  },
  {
    "id": 1605,
    "iso_key": "TH.NB",
    "name": "Nong Bua Lam Phu",
    "flag_file_name": "th_nb"
  },
  {
    "id": 1606,
    "iso_key": "TH.NK",
    "name": "Nong Khai",
    "flag_file_name": "th_nk"
  },
  {
    "id": 1607,
    "iso_key": "TH.NO",
    "name": "Nonthaburi",
    "flag_file_name": "th_no"
  },
  {
    "id": 1608,
    "iso_key": "TH.PT",
    "name": "Pathum Thani",
    "flag_file_name": "th_pt"
  },
  {
    "id": 1609,
    "iso_key": "TH.PI",
    "name": "Pattani",
    "flag_file_name": "th_pi"
  },
  {
    "id": 1610,
    "iso_key": "TH.PG",
    "name": "Phangnga",
    "flag_file_name": "th_pg"
  },
  {
    "id": 37,
    "iso_key": "US-WY",
    "name": "Wyoming",
    "flag_file_name": "flag_uswy"
  },
  {
    "id": 1611,
    "iso_key": "TH.PL",
    "name": "Phatthalung",
    "flag_file_name": "th_pl"
  },
  {"id": 1612, "iso_key": "TH.PY", "name": "Phayao", "flag_file_name": "th_py"},
  {
    "id": 1613,
    "iso_key": "TH.PH",
    "name": "Phetchabun",
    "flag_file_name": "th_ph"
  },
  {
    "id": 1614,
    "iso_key": "TH.PE",
    "name": "Phetchaburi",
    "flag_file_name": "th_pe"
  },
  {
    "id": 1615,
    "iso_key": "TH.PC",
    "name": "Phichit",
    "flag_file_name": "th_pc"
  },
  {
    "id": 1616,
    "iso_key": "TH.PS",
    "name": "Phitsanulok",
    "flag_file_name": "th_ps"
  },
  {
    "id": 1617,
    "iso_key": "TH.PA",
    "name": "Phra Nakhon Si Ayutthaya",
    "flag_file_name": "th_pa"
  },
  {"id": 1618, "iso_key": "TH.PR", "name": "Phrae", "flag_file_name": "th_pr"},
  {"id": 1619, "iso_key": "TH.PU", "name": "Phuket", "flag_file_name": "th_pu"},
  {
    "id": 1620,
    "iso_key": "TH.PB",
    "name": "Prachin Buri",
    "flag_file_name": "th_pb"
  },
  {
    "id": 1621,
    "iso_key": "TH.PK",
    "name": "Prachuap Khiri Khan",
    "flag_file_name": "th_pk"
  },
  {"id": 1622, "iso_key": "TH.RN", "name": "Ranong", "flag_file_name": "th_rn"},
  {
    "id": 1623,
    "iso_key": "TH.RT",
    "name": "Ratchaburi",
    "flag_file_name": "th_rt"
  },
  {"id": 1624, "iso_key": "TH.RY", "name": "Rayong", "flag_file_name": "th_ry"},
  {"id": 1625, "iso_key": "TH.RE", "name": "Roi Et", "flag_file_name": "th_re"},
  {
    "id": 1626,
    "iso_key": "TH.SK",
    "name": "Sa Kaeo",
    "flag_file_name": "th_sk"
  },
  {
    "id": 1627,
    "iso_key": "TH.SN",
    "name": "Sakon Nakhon",
    "flag_file_name": "th_sn"
  },
  {"id": 224, "iso_key": "MD", "name": "Moldova", "flag_file_name": "flag_md"},
  {"id": 147, "iso_key": "LA", "name": "Laos", "flag_file_name": "flag_la"},
  {
    "id": 8,
    "iso_key": "LC",
    "name": "Saint Lucia",
    "flag_file_name": "flag_lc"
  },
  {
    "id": 269,
    "iso_key": "GQ",
    "name": "Equatorial Guinea",
    "flag_file_name": "flag_gq"
  },
  {
    "id": 325,
    "iso_key": "HM",
    "name": "Heard Island and McDonald Islands",
    "flag_file_name": "flag_hm"
  },
  {
    "id": 318,
    "iso_key": "MR",
    "name": "Mauritania",
    "flag_file_name": "flag_mr"
  },
  {
    "id": 316,
    "iso_key": "MG",
    "name": "Madagascar",
    "flag_file_name": "flag_mg"
  },
  {"id": 314, "iso_key": "AO", "name": "Angola", "flag_file_name": "flag_ao"},
  {"id": 312, "iso_key": "ML", "name": "Mali", "flag_file_name": "flag_ml"},
  {"id": 310, "iso_key": "CM", "name": "Cameroon", "flag_file_name": "flag_cm"},
  {"id": 309, "iso_key": "GH", "name": "Ghana", "flag_file_name": "flag_gh"},
  {"id": 308, "iso_key": "ET", "name": "Ethiopia", "flag_file_name": "flag_et"},
  {"id": 307, "iso_key": "ER", "name": "Eritrea", "flag_file_name": "flag_er"},
  {
    "id": 302,
    "iso_key": "BF",
    "name": "Burkina Faso",
    "flag_file_name": "flag_bf"
  },
  {"id": 299, "iso_key": "KE", "name": "Kenya", "flag_file_name": "flag_ke"},
  {"id": 295, "iso_key": "UG", "name": "Uganda", "flag_file_name": "flag_ug"},
  {
    "id": 293,
    "iso_key": "IO",
    "name": "British Indian Ocean Territory",
    "flag_file_name": "flag_io"
  },
  {"id": 288, "iso_key": "BI", "name": "Burundi", "flag_file_name": "flag_bi"},
  {
    "id": 285,
    "iso_key": "CD",
    "name": "Democratic Republic of the Congo",
    "flag_file_name": "flag_cd"
  },
  {
    "id": 283,
    "iso_key": "CI",
    "name": "Cote d'Ivoire",
    "flag_file_name": "flag_ci"
  },
  {"id": 280, "iso_key": "KM", "name": "Comoros", "flag_file_name": "flag_km"},
  {"id": 279, "iso_key": "DJ", "name": "Djibouti", "flag_file_name": "flag_dj"},
  {"id": 278, "iso_key": "GA", "name": "Gabon", "flag_file_name": "flag_ga"},
  {"id": 277, "iso_key": "BW", "name": "Botswana", "flag_file_name": "flag_bw"},
  {"id": 276, "iso_key": "NG", "name": "Nigeria", "flag_file_name": "flag_ng"},
  {"id": 275, "iso_key": "TG", "name": "Togo", "flag_file_name": "flag_tg"},
  {"id": 274, "iso_key": "RE", "name": "Reunion", "flag_file_name": "flag_re"},
  {"id": 272, "iso_key": "TD", "name": "Chad", "flag_file_name": "flag_td"},
  {"id": 271, "iso_key": "TZ", "name": "Tanzania", "flag_file_name": "flag_tz"},
  {"id": 270, "iso_key": "GN", "name": "Guinea", "flag_file_name": "flag_gn"},
  {
    "id": 265,
    "iso_key": "GW",
    "name": "Guinea Bissau",
    "flag_file_name": "flag_gw"
  },
  {"id": 263, "iso_key": "CG", "name": "Congo", "flag_file_name": "flag_cg"},
  {
    "id": 262,
    "iso_key": "EH",
    "name": "Western Sahara",
    "flag_file_name": "flag_eh"
  },
  {"id": 261, "iso_key": "TN", "name": "Tunisia", "flag_file_name": "flag_tn"},
  {"id": 258, "iso_key": "VU", "name": "Vanuatu", "flag_file_name": "flag_vu"},
  {"id": 297, "iso_key": "SZ", "name": "Eswatini", "flag_file_name": "flag_sz"},
  {
    "id": 247,
    "iso_key": "AU",
    "name": "Australia",
    "flag_file_name": "flag_au"
  },
  {
    "id": 257,
    "iso_key": "AS",
    "name": "American Samoa",
    "flag_file_name": "flag_as"
  },
  {
    "id": 246,
    "iso_key": "MH",
    "name": "Marshall Islands",
    "flag_file_name": "flag_mh"
  },
  {
    "id": 245,
    "iso_key": "CC",
    "name": "Cocos (Keeling) Islands",
    "flag_file_name": "flag_cc"
  },
  {"id": 243, "iso_key": "NR", "name": "Nauru", "flag_file_name": "flag_nr"},
  {"id": 242, "iso_key": "WS", "name": "Samoa", "flag_file_name": "flag_ws"},
  {"id": 240, "iso_key": "TV", "name": "Tuvalu", "flag_file_name": "flag_tv"},
  {"id": 239, "iso_key": "KI", "name": "Kiribati", "flag_file_name": "flag_ki"},
  {
    "id": 238,
    "iso_key": "PG",
    "name": "Papua New Guinea",
    "flag_file_name": "flag_pg"
  },
  {
    "id": 237,
    "iso_key": "NC",
    "name": "New Caledonia",
    "flag_file_name": "flag_nc"
  },
  {
    "id": 236,
    "iso_key": "CX",
    "name": "Christmas Island",
    "flag_file_name": "flag_cx"
  },
  {
    "id": 234,
    "iso_key": "NF",
    "name": "Norfolk Island",
    "flag_file_name": "flag_nf"
  },
  {
    "id": 233,
    "iso_key": "PN",
    "name": "Pitcairn Islands",
    "flag_file_name": "flag_pn"
  },
  {
    "id": 232,
    "iso_key": "WF",
    "name": "Wallis and Futuna",
    "flag_file_name": "flag_wf"
  },
  {
    "id": 231,
    "iso_key": "PF",
    "name": "French Polynesia",
    "flag_file_name": "flag_pf"
  },
  {"id": 230, "iso_key": "FJ", "name": "Fiji", "flag_file_name": "flag_fj"},
  {"id": 229, "iso_key": "NU", "name": "Niue", "flag_file_name": "flag_nu"},
  {
    "id": 228,
    "iso_key": "CK",
    "name": "Cook Islands",
    "flag_file_name": "flag_ck"
  },
  {
    "id": 227,
    "iso_key": "AQ",
    "name": "Antarctica",
    "flag_file_name": "flag_aq"
  },
  {
    "id": 226,
    "iso_key": "TF",
    "name": "French Southern and Antarctic Lands",
    "flag_file_name": "flag_tf"
  },
  {
    "id": 225,
    "iso_key": "FO",
    "name": "Faroe Islands",
    "flag_file_name": "flag_fo"
  },
  {"id": 222, "iso_key": "GG", "name": "Guernsey", "flag_file_name": "flag_gg"},
  {
    "id": 218,
    "iso_key": "AZ",
    "name": "Azerbaijan",
    "flag_file_name": "flag_az"
  },
  {
    "id": 216,
    "iso_key": "LU",
    "name": "Luxembourg",
    "flag_file_name": "flag_lu"
  },
  {"id": 215, "iso_key": "IE", "name": "Ireland", "flag_file_name": "flag_ie"},
  {"id": 214, "iso_key": "HU", "name": "Hungary", "flag_file_name": "flag_hu"},
  {
    "id": 213,
    "iso_key": "GI",
    "name": "Gibraltar",
    "flag_file_name": "flag_gi"
  },
  {"id": 212, "iso_key": "BY", "name": "Belarus", "flag_file_name": "flag_by"},
  {"id": 211, "iso_key": "LV", "name": "Latvia", "flag_file_name": "flag_lv"},
  {
    "id": 219,
    "iso_key": "CZ",
    "name": "Czech Republic",
    "flag_file_name": "flag_cz"
  },
  {
    "id": 210,
    "iso_key": "ME",
    "name": "Montenegro",
    "flag_file_name": "flag_me"
  },
  {"id": 209, "iso_key": "SI", "name": "Slovenia", "flag_file_name": "flag_si"},
  {"id": 208, "iso_key": "CY", "name": "Cyprus", "flag_file_name": "flag_cy"},
  {"id": 206, "iso_key": "IS", "name": "Iceland", "flag_file_name": "flag_is"},
  {"id": 205, "iso_key": "EE", "name": "Estonia", "flag_file_name": "flag_ee"},
  {"id": 203, "iso_key": "AL", "name": "Albania", "flag_file_name": "flag_al"},
  {"id": 201, "iso_key": "GE", "name": "Georgia", "flag_file_name": "flag_ge"},
  {"id": 200, "iso_key": "RS", "name": "Serbia", "flag_file_name": "flag_rs"},
  {"id": 198, "iso_key": "BG", "name": "Bulgaria", "flag_file_name": "flag_bg"},
  {"id": 197, "iso_key": "GR", "name": "Greece", "flag_file_name": "flag_gr"},
  {
    "id": 196,
    "iso_key": "CH",
    "name": "Switzerland",
    "flag_file_name": "flag_ch"
  },
  {"id": 194, "iso_key": "VA", "name": "Vatican", "flag_file_name": "flag_va"},
  {
    "id": 193,
    "iso_key": "GL",
    "name": "Greenland",
    "flag_file_name": "flag_gl"
  },
  {"id": 189, "iso_key": "AD", "name": "Andorra", "flag_file_name": "flag_ad"},
  {"id": 187, "iso_key": "RO", "name": "Romania", "flag_file_name": "flag_ro"},
  {"id": 184, "iso_key": "FR", "name": "France", "flag_file_name": "flag_fr"},
  {"id": 186, "iso_key": "DE", "name": "Germany", "flag_file_name": "flag_de"},
  {"id": 185, "iso_key": "UA", "name": "Ukraine", "flag_file_name": "flag_ua"},
  {
    "id": 202,
    "iso_key": "BA",
    "name": "Bosnia and Herzegovina",
    "flag_file_name": "flag_ba"
  },
  {
    "id": 204,
    "iso_key": "XK",
    "name": "Kosovo (Disputed)",
    "flag_file_name": "flag_xk"
  },
  {
    "id": 195,
    "iso_key": "MK",
    "name": "North Macedonia",
    "flag_file_name": "flag_mk"
  },
  {"id": 268, "iso_key": "ZW", "name": "Zimbabwe", "flag_file_name": "flag_zw"},
  {"id": 165, "iso_key": "CL", "name": "Chile", "flag_file_name": "flag_cl"},
  {
    "id": 171,
    "iso_key": "LT",
    "name": "Lithuania",
    "flag_file_name": "flag_lt"
  },
  {"id": 267, "iso_key": "ZM", "name": "Zambia", "flag_file_name": "flag_zm"},
  {
    "id": 183,
    "iso_key": "NL",
    "name": "Netherlands",
    "flag_file_name": "flag_nl"
  },
  {"id": 182, "iso_key": "HR", "name": "Croatia", "flag_file_name": "flag_hr"},
  {"id": 181, "iso_key": "MT", "name": "Malta", "flag_file_name": "flag_mt"},
  {"id": 180, "iso_key": "AT", "name": "Austria", "flag_file_name": "flag_at"},
  {"id": 179, "iso_key": "FI", "name": "Finland", "flag_file_name": "flag_fi"},
  {"id": 177, "iso_key": "AM", "name": "Armenia", "flag_file_name": "flag_am"},
  {
    "id": 175,
    "iso_key": "SM",
    "name": "San Marino",
    "flag_file_name": "flag_sm"
  },
  {"id": 174, "iso_key": "BE", "name": "Belgium", "flag_file_name": "flag_be"},
  {"id": 173, "iso_key": "JE", "name": "Jersey", "flag_file_name": "flag_je"},
  {"id": 172, "iso_key": "MC", "name": "Monaco", "flag_file_name": "flag_mc"},
  {"id": 170, "iso_key": "AX", "name": "Aland", "flag_file_name": "flag_ax"},
  {"id": 168, "iso_key": "GY", "name": "Guyana", "flag_file_name": "flag_gy"},
  {"id": 167, "iso_key": "EC", "name": "Ecuador", "flag_file_name": "flag_ec"},
  {"id": 166, "iso_key": "SR", "name": "Suriname", "flag_file_name": "flag_sr"},
  {
    "id": 164,
    "iso_key": "FK",
    "name": "Falkland Islands",
    "flag_file_name": "flag_fk"
  },
  {
    "id": 163,
    "iso_key": "VE",
    "name": "Venezuela",
    "flag_file_name": "flag_ve"
  },
  {"id": 162, "iso_key": "CO", "name": "Colombia", "flag_file_name": "flag_co"},
  {"id": 161, "iso_key": "UY", "name": "Uruguay", "flag_file_name": "flag_uy"},
  {"id": 176, "iso_key": "PL", "name": "Poland", "flag_file_name": "flag_pl"},
  {"id": 160, "iso_key": "BO", "name": "Bolivia", "flag_file_name": "flag_bo"},
  {
    "id": 159,
    "iso_key": "AR",
    "name": "Argentina",
    "flag_file_name": "flag_ar"
  },
  {"id": 158, "iso_key": "PY", "name": "Paraguay", "flag_file_name": "flag_py"},
  {"id": 157, "iso_key": "PE", "name": "Peru", "flag_file_name": "flag_pe"},
  {
    "id": 156,
    "iso_key": "GF",
    "name": "French Guiana",
    "flag_file_name": "flag_gf"
  },
  {"id": 155, "iso_key": "PK", "name": "Pakistan", "flag_file_name": "flag_pk"},
  {"id": 154, "iso_key": "IN", "name": "India", "flag_file_name": "flag_in"},
  {
    "id": 153,
    "iso_key": "MO",
    "name": "Macau S.A.R",
    "flag_file_name": "flag_mo"
  },
  {
    "id": 178,
    "iso_key": "GB",
    "name": "United Kingdom",
    "flag_file_name": "flag_gb"
  },
  {
    "id": 103,
    "iso_key": "GT",
    "name": "Guatemala",
    "flag_file_name": "flag_gt"
  },
  {"id": 132, "iso_key": "TR", "name": "Turkey", "flag_file_name": "flag_tr"},
  {"id": 169, "iso_key": "BR", "name": "Brazil", "flag_file_name": "flag_br"},
  {"id": 152, "iso_key": "JO", "name": "Jordan", "flag_file_name": "flag_jo"},
  {
    "id": 151,
    "iso_key": "SG",
    "name": "Singapore",
    "flag_file_name": "flag_sg"
  },
  {
    "id": 150,
    "iso_key": "FM",
    "name": "Federated States of Micronesia",
    "flag_file_name": "flag_fm"
  },
  {
    "id": 149,
    "iso_key": "HK",
    "name": "Hong Kong S.A.R.",
    "flag_file_name": "flag_hk"
  },
  {"id": 146, "iso_key": "MV", "name": "Maldives", "flag_file_name": "flag_mv"},
  {
    "id": 145,
    "iso_key": "KG",
    "name": "Kyrgyzstan",
    "flag_file_name": "flag_kg"
  },
  {
    "id": 144,
    "iso_key": "TM",
    "name": "Turkmenistan",
    "flag_file_name": "flag_tm"
  },
  {"id": 143, "iso_key": "BH", "name": "Bahrain", "flag_file_name": "flag_bh"},
  {"id": 142, "iso_key": "NP", "name": "Nepal", "flag_file_name": "flag_np"},
  {
    "id": 141,
    "iso_key": "PS",
    "name": "Palestine",
    "flag_file_name": "flag_ps"
  },
  {"id": 140, "iso_key": "MM", "name": "Myanmar", "flag_file_name": "flag_mm"},
  {"id": 139, "iso_key": "MY", "name": "Malaysia", "flag_file_name": "flag_my"},
  {
    "id": 137,
    "iso_key": "SA",
    "name": "Saudi Arabia",
    "flag_file_name": "flag_sa"
  },
  {"id": 135, "iso_key": "BN", "name": "Brunei", "flag_file_name": "flag_bn"},
  {"id": 134, "iso_key": "IL", "name": "Israel", "flag_file_name": "flag_il"},
  {"id": 131, "iso_key": "IR", "name": "Iran", "flag_file_name": "flag_ir"},
  {
    "id": 130,
    "iso_key": "KP",
    "name": "North Korea",
    "flag_file_name": "flag_kp"
  },
  {
    "id": 129,
    "iso_key": "PH",
    "name": "Philippines",
    "flag_file_name": "flag_ph"
  },
  {"id": 128, "iso_key": "VN", "name": "Vietnam", "flag_file_name": "flag_vn"},
  {"id": 126, "iso_key": "TL", "name": "Timor", "flag_file_name": "flag_tl"},
  {"id": 125, "iso_key": "BT", "name": "Bhutan", "flag_file_name": "flag_bt"},
  {
    "id": 124,
    "iso_key": "TJ",
    "name": "Tajikistan",
    "flag_file_name": "flag_tj"
  },
  {"id": 123, "iso_key": "YE", "name": "Yemen", "flag_file_name": "flag_ye"},
  {"id": 122, "iso_key": "TW", "name": "Taiwan", "flag_file_name": "flag_tw"},
  {
    "id": 121,
    "iso_key": "UZ",
    "name": "Uzbekistan",
    "flag_file_name": "flag_uz"
  },
  {
    "id": 120,
    "iso_key": "AE",
    "name": "United Arab Emirates",
    "flag_file_name": "flag_ae"
  },
  {
    "id": 74,
    "iso_key": "CR",
    "name": "Costa Rica",
    "flag_file_name": "flag_cr"
  },
  {"id": 127, "iso_key": "OM", "name": "Oman", "flag_file_name": "flag_om"},
  {"id": 119, "iso_key": "PW", "name": "Palau", "flag_file_name": "flag_pw"},
  {"id": 118, "iso_key": "KW", "name": "Kuwait", "flag_file_name": "flag_kw"},
  {"id": 117, "iso_key": "TH", "name": "Thailand", "flag_file_name": "flag_th"},
  {"id": 116, "iso_key": "MN", "name": "Mongolia", "flag_file_name": "flag_mn"},
  {"id": 115, "iso_key": "IQ", "name": "Iraq", "flag_file_name": "flag_iq"},
  {
    "id": 114,
    "iso_key": "ID",
    "name": "Indonesia",
    "flag_file_name": "flag_id"
  },
  {"id": 112, "iso_key": "KH", "name": "Cambodia", "flag_file_name": "flag_kh"},
  {
    "id": 111,
    "iso_key": "BD",
    "name": "Bangladesh",
    "flag_file_name": "flag_bd"
  },
  {"id": 110, "iso_key": "CN", "name": "China", "flag_file_name": "flag_cn"},
  {"id": 107, "iso_key": "SY", "name": "Syria", "flag_file_name": "flag_sy"},
  {
    "id": 106,
    "iso_key": "GP",
    "name": "Guadeloupe",
    "flag_file_name": "flag_gp"
  },
  {"id": 105, "iso_key": "PA", "name": "Panama", "flag_file_name": "flag_pa"},
  {
    "id": 104,
    "iso_key": "BL",
    "name": "Saint Barthelemy",
    "flag_file_name": "flag_bl"
  },
  {"id": 101, "iso_key": "CU", "name": "Cuba", "flag_file_name": "flag_cu"},
  {
    "id": 100,
    "iso_key": "VC",
    "name": "Saint Vincent and the Grenadines",
    "flag_file_name": "flag_vc"
  },
  {
    "id": 99,
    "iso_key": "TC",
    "name": "Turks and Caicos Islands",
    "flag_file_name": "flag_tc"
  },
  {"id": 98, "iso_key": "HN", "name": "Honduras", "flag_file_name": "flag_hn"},
  {"id": 96, "iso_key": "GD", "name": "Grenada", "flag_file_name": "flag_gd"},
  {"id": 82, "iso_key": "CA", "name": "Canada", "flag_file_name": "flag_ca"},
  {"id": 81, "iso_key": "BM", "name": "Bermuda", "flag_file_name": "flag_bm"},
  {
    "id": 80,
    "iso_key": "AG",
    "name": "Antigua and Barbuda",
    "flag_file_name": "flag_ag"
  },
  {"id": 79, "iso_key": "HT", "name": "Haiti", "flag_file_name": "flag_ht"},
  {
    "id": 78,
    "iso_key": "SV",
    "name": "El Salvador",
    "flag_file_name": "flag_sv"
  },
  {
    "id": 77,
    "iso_key": "MS",
    "name": "Montserrat",
    "flag_file_name": "flag_ms"
  },
  {
    "id": 76,
    "iso_key": "MF",
    "name": "Saint Martin",
    "flag_file_name": "flag_mf"
  },
  {
    "id": 75,
    "iso_key": "KN",
    "name": "Saint Kitts and Nevis",
    "flag_file_name": "flag_kn"
  },
  {
    "id": 109,
    "iso_key": "KR",
    "name": "South Korea",
    "flag_file_name": "flag_kr"
  },
  {
    "id": 113,
    "iso_key": "KZ",
    "name": "Kazakhstan",
    "flag_file_name": "flag_kz"
  },
  {"id": 72, "iso_key": "MX", "name": "Mexico", "flag_file_name": "flag_mx"},
  {"id": 108, "iso_key": "JP", "name": "Japan", "flag_file_name": "flag_jp"},
  {
    "id": 57,
    "iso_key": "US-CT",
    "name": "Connecticut",
    "flag_file_name": "flag_usct"
  },
  {
    "id": 71,
    "iso_key": "BS",
    "name": "The Bahamas",
    "flag_file_name": "flag_bs"
  },
  {
    "id": 70,
    "iso_key": "MQ",
    "name": "Martinique",
    "flag_file_name": "flag_mq"
  },
  {
    "id": 69,
    "iso_key": "PM",
    "name": "Saint Pierre and Miquelon",
    "flag_file_name": "flag_pm"
  },
  {"id": 68, "iso_key": "GU", "name": "Guam", "flag_file_name": "flag_gu"},
  {
    "id": 67,
    "iso_key": "VG",
    "name": "British Virgin Islands",
    "flag_file_name": "flag_vg"
  },
  {
    "id": 66,
    "iso_key": "TT",
    "name": "Trinidad and Tobago",
    "flag_file_name": "flag_tt"
  },
  {
    "id": 65,
    "iso_key": "SX",
    "name": "Sint Maarten",
    "flag_file_name": "flag_sx"
  },
  {
    "id": 64,
    "iso_key": "KY",
    "name": "Cayman Islands",
    "flag_file_name": "flag_ky"
  },
  {"id": 63, "iso_key": "AW", "name": "Aruba", "flag_file_name": "flag_aw"},
  {"id": 61, "iso_key": "AI", "name": "Anguilla", "flag_file_name": "flag_ai"},
  {
    "id": 7,
    "iso_key": "MP",
    "name": "Northern Mariana Islands",
    "flag_file_name": "flag_mp"
  },
  {"id": 6, "iso_key": "BZ", "name": "Belize", "flag_file_name": "flag_bz"},
  {"id": 5, "iso_key": "NI", "name": "Nicaragua", "flag_file_name": "flag_ni"},
  {"id": 4, "iso_key": "CW", "name": "Curaçao", "flag_file_name": "flag_cw"},
  {"id": 3, "iso_key": "BB", "name": "Barbados", "flag_file_name": "flag_bb"},
  {"id": 2, "iso_key": "JM", "name": "Jamaica", "flag_file_name": "flag_jm"},
  {"id": 1, "iso_key": "AN", "name": "Bonaire", "flag_file_name": "flag_an"},
  {
    "id": 9,
    "iso_key": "US",
    "name": "United States of America",
    "flag_file_name": "flag_us"
  },
  {
    "id": 62,
    "iso_key": "PR",
    "name": "Puerto Rico",
    "flag_file_name": "flag_pr"
  },
  {
    "id": 102,
    "iso_key": "VI",
    "name": "United States Virgin Islands",
    "flag_file_name": "flag_vi"
  },
  {
    "id": 1628,
    "iso_key": "TH.SP",
    "name": "Samut Prakan",
    "flag_file_name": "th_sp"
  },
  {
    "id": 1629,
    "iso_key": "TH.SS",
    "name": "Samut Sakhon",
    "flag_file_name": "th_ss"
  },
  {
    "id": 1630,
    "iso_key": "TH.SM",
    "name": "Samut Songkhram",
    "flag_file_name": "th_sm"
  },
  {
    "id": 1631,
    "iso_key": "TH.SR",
    "name": "Saraburi",
    "flag_file_name": "th_sr"
  },
  {"id": 1632, "iso_key": "TH.SA", "name": "Satun", "flag_file_name": "th_sa"},
  {
    "id": 1633,
    "iso_key": "TH.SI",
    "name": "Si Sa Ket",
    "flag_file_name": "th_si"
  },
  {
    "id": 1634,
    "iso_key": "TH.SB",
    "name": "Sing Buri",
    "flag_file_name": "th_sb"
  },
  {
    "id": 1635,
    "iso_key": "TH.SG",
    "name": "Songkhla",
    "flag_file_name": "th_sg"
  },
  {
    "id": 1636,
    "iso_key": "TH.SO",
    "name": "Sukhothai",
    "flag_file_name": "th_so"
  },
  {
    "id": 1637,
    "iso_key": "TH.SH",
    "name": "Suphan Buri",
    "flag_file_name": "th_sh"
  },
  {
    "id": 1638,
    "iso_key": "TH.ST",
    "name": "Surat Thani",
    "flag_file_name": "th_st"
  },
  {"id": 1639, "iso_key": "TH.SU", "name": "Surin", "flag_file_name": "th_su"},
  {"id": 1640, "iso_key": "TH.TK", "name": "Tak", "flag_file_name": "th_tk"},
  {"id": 1641, "iso_key": "TH.TG", "name": "Trang", "flag_file_name": "th_tg"},
  {"id": 1642, "iso_key": "TH.TT", "name": "Trat", "flag_file_name": "th_tt"},
  {
    "id": 1643,
    "iso_key": "TH.UR",
    "name": "Ubon Ratchathani",
    "flag_file_name": "th_ur"
  },
  {
    "id": 1644,
    "iso_key": "TH.UN",
    "name": "Udon Thani",
    "flag_file_name": "th_un"
  },
  {
    "id": 1645,
    "iso_key": "TH.UT",
    "name": "Uthai Thani",
    "flag_file_name": "th_ut"
  },
  {
    "id": 1646,
    "iso_key": "TH.UD",
    "name": "Uttaradit",
    "flag_file_name": "th_ud"
  },
  {"id": 1647, "iso_key": "TH.YL", "name": "Yala", "flag_file_name": "th_yl"},
  {
    "id": 1648,
    "iso_key": "TH.YS",
    "name": "Yasothon",
    "flag_file_name": "th_ys"
  },
  {"id": 1649, "iso_key": "TR.AA", "name": "Adana", "flag_file_name": "tr_aa"},
  {
    "id": 1650,
    "iso_key": "TR.AD",
    "name": "Adiyaman",
    "flag_file_name": "tr_ad"
  },
  {"id": 1651, "iso_key": "TR.AF", "name": "Afyon", "flag_file_name": "tr_af"},
  {"id": 1652, "iso_key": "TR.AG", "name": "Agri", "flag_file_name": "tr_ag"},
  {
    "id": 1653,
    "iso_key": "TR.AK",
    "name": "Aksaray",
    "flag_file_name": "tr_ak"
  },
  {"id": 1654, "iso_key": "TR.AM", "name": "Amasya", "flag_file_name": "tr_am"},
  {"id": 1655, "iso_key": "TR.AN", "name": "Ankara", "flag_file_name": "tr_an"},
  {
    "id": 1656,
    "iso_key": "TR.AL",
    "name": "Antalya",
    "flag_file_name": "tr_al"
  },
  {
    "id": 1657,
    "iso_key": "TR.AR",
    "name": "Ardahan",
    "flag_file_name": "tr_ar"
  },
  {"id": 1658, "iso_key": "TR.AV", "name": "Artvin", "flag_file_name": "tr_av"},
  {"id": 1659, "iso_key": "TR.AY", "name": "Aydin", "flag_file_name": "tr_ay"},
  {
    "id": 1660,
    "iso_key": "TR.BK",
    "name": "Balikesir",
    "flag_file_name": "tr_bk"
  },
  {"id": 1661, "iso_key": "TR.BR", "name": "Bartın", "flag_file_name": "tr_br"},
  {"id": 1662, "iso_key": "TR.BM", "name": "Batman", "flag_file_name": "tr_bm"},
  {
    "id": 1663,
    "iso_key": "TR.BB",
    "name": "Bayburt",
    "flag_file_name": "tr_bb"
  },
  {
    "id": 1664,
    "iso_key": "TR.BC",
    "name": "Bilecik",
    "flag_file_name": "tr_bc"
  },
  {"id": 1665, "iso_key": "TR.BG", "name": "Bingöl", "flag_file_name": "tr_bg"},
  {"id": 1666, "iso_key": "TR.BT", "name": "Bitlis", "flag_file_name": "tr_bt"},
  {"id": 1667, "iso_key": "TR.BL", "name": "Bolu", "flag_file_name": "tr_bl"},
  {"id": 1668, "iso_key": "TR.BD", "name": "Burdur", "flag_file_name": "tr_bd"},
  {"id": 1669, "iso_key": "TR.BU", "name": "Bursa", "flag_file_name": "tr_bu"},
  {
    "id": 1670,
    "iso_key": "TR.CK",
    "name": "Çanakkale",
    "flag_file_name": "tr_ck"
  },
  {
    "id": 1671,
    "iso_key": "TR.CI",
    "name": "Çankırı",
    "flag_file_name": "tr_ci"
  },
  {"id": 1672, "iso_key": "TR.CM", "name": "Çorum", "flag_file_name": "tr_cm"},
  {
    "id": 1673,
    "iso_key": "TR.DN",
    "name": "Denizli",
    "flag_file_name": "tr_dn"
  },
  {
    "id": 1674,
    "iso_key": "TR.DY",
    "name": "Diyarbakir",
    "flag_file_name": "tr_dy"
  },
  {"id": 1675, "iso_key": "TR.DU", "name": "Düzce", "flag_file_name": "tr_du"},
  {"id": 1676, "iso_key": "TR.ED", "name": "Edirne", "flag_file_name": "tr_ed"},
  {"id": 1677, "iso_key": "TR.EG", "name": "Elazı_", "flag_file_name": "tr_eg"},
  {
    "id": 1678,
    "iso_key": "TR.EN",
    "name": "Erzincan",
    "flag_file_name": "tr_en"
  },
  {
    "id": 1679,
    "iso_key": "TR.EM",
    "name": "Erzurum",
    "flag_file_name": "tr_em"
  },
  {
    "id": 1680,
    "iso_key": "TR.ES",
    "name": "Eskisehir",
    "flag_file_name": "tr_es"
  },
  {
    "id": 1681,
    "iso_key": "TR.GA",
    "name": "Gaziantep",
    "flag_file_name": "tr_ga"
  },
  {
    "id": 1682,
    "iso_key": "TR.GI",
    "name": "Giresun",
    "flag_file_name": "tr_gi"
  },
  {
    "id": 1683,
    "iso_key": "TR.GU",
    "name": "Gümüşhane",
    "flag_file_name": "tr_gu"
  },
  {
    "id": 1684,
    "iso_key": "TR.HK",
    "name": "Hakkari",
    "flag_file_name": "tr_hk"
  },
  {"id": 1685, "iso_key": "TR.HT", "name": "Hatay", "flag_file_name": "tr_ht"},
  {"id": 1686, "iso_key": "TR.IG", "name": "Iğdır", "flag_file_name": "tr_ig"},
  {
    "id": 1687,
    "iso_key": "TR.IP",
    "name": "Isparta",
    "flag_file_name": "tr_ip"
  },
  {
    "id": 1688,
    "iso_key": "TR.IB",
    "name": "Istanbul",
    "flag_file_name": "tr_ib"
  },
  {"id": 1689, "iso_key": "TR.IZ", "name": "Izmir", "flag_file_name": "tr_iz"},
  {
    "id": 1690,
    "iso_key": "TR.KM",
    "name": "K. Maras",
    "flag_file_name": "tr_km"
  },
  {
    "id": 1691,
    "iso_key": "TR.KB",
    "name": "Karabük",
    "flag_file_name": "tr_kb"
  },
  {
    "id": 1692,
    "iso_key": "TR.KR",
    "name": "Karaman",
    "flag_file_name": "tr_kr"
  },
  {"id": 1693, "iso_key": "TR.KA", "name": "Kars", "flag_file_name": "tr_ka"},
  {
    "id": 1694,
    "iso_key": "TR.KS",
    "name": "Kastamonu",
    "flag_file_name": "tr_ks"
  },
  {
    "id": 408,
    "iso_key": "RU-90",
    "name": "Perm Krai",
    "flag_file_name": "RU-90"
  },
  {
    "id": 1695,
    "iso_key": "TR.KY",
    "name": "Kayseri",
    "flag_file_name": "tr_ky"
  },
  {"id": 1696, "iso_key": "TR.KI", "name": "Kilis", "flag_file_name": "tr_ki"},
  {
    "id": 1697,
    "iso_key": "TR.KK",
    "name": "Kinkkale",
    "flag_file_name": "tr_kk"
  },
  {
    "id": 1698,
    "iso_key": "TR.KL",
    "name": "Kirklareli",
    "flag_file_name": "tr_kl"
  },
  {
    "id": 1699,
    "iso_key": "TR.KH",
    "name": "Kirsehir",
    "flag_file_name": "tr_kh"
  },
  {
    "id": 1700,
    "iso_key": "TR.KC",
    "name": "Kocaeli",
    "flag_file_name": "tr_kc"
  },
  {"id": 1701, "iso_key": "TR.KO", "name": "Konya", "flag_file_name": "tr_ko"},
  {
    "id": 1702,
    "iso_key": "TR.KU",
    "name": "Kütahya",
    "flag_file_name": "tr_ku"
  },
  {
    "id": 1703,
    "iso_key": "TR.ML",
    "name": "Malatya",
    "flag_file_name": "tr_ml"
  },
  {"id": 1704, "iso_key": "TR.MN", "name": "Manisa", "flag_file_name": "tr_mn"},
  {"id": 1705, "iso_key": "TR.MR", "name": "Mardin", "flag_file_name": "tr_mr"},
  {"id": 1706, "iso_key": "TR.IC", "name": "Mersin", "flag_file_name": "tr_ic"},
  {"id": 1707, "iso_key": "TR.MG", "name": "Mugla", "flag_file_name": "tr_mg"},
  {"id": 1708, "iso_key": "TR.MS", "name": "Mus", "flag_file_name": "tr_ms"},
  {
    "id": 1709,
    "iso_key": "TR.NV",
    "name": "Nevsehir",
    "flag_file_name": "tr_nv"
  },
  {"id": 1710, "iso_key": "TR.NG", "name": "Nigde", "flag_file_name": "tr_ng"},
  {"id": 1711, "iso_key": "TR.OR", "name": "Ordu", "flag_file_name": "tr_or"},
  {
    "id": 1712,
    "iso_key": "TR.OS",
    "name": "Osmaniye",
    "flag_file_name": "tr_os"
  },
  {"id": 1713, "iso_key": "TR.RI", "name": "Rize", "flag_file_name": "tr_ri"},
  {
    "id": 1714,
    "iso_key": "TR.SK",
    "name": "Sakarya",
    "flag_file_name": "tr_sk"
  },
  {"id": 1715, "iso_key": "TR.SS", "name": "Samsun", "flag_file_name": "tr_ss"},
  {
    "id": 1716,
    "iso_key": "TR.SU",
    "name": "Sanliurfa",
    "flag_file_name": "tr_su"
  },
  {"id": 1717, "iso_key": "TR.SI", "name": "Siirt", "flag_file_name": "tr_si"},
  {"id": 1718, "iso_key": "TR.SP", "name": "Sinop", "flag_file_name": "tr_sp"},
  {"id": 1719, "iso_key": "TR.SR", "name": "Sirnak", "flag_file_name": "tr_sr"},
  {"id": 1720, "iso_key": "TR.SV", "name": "Sivas", "flag_file_name": "tr_sv"},
  {
    "id": 1721,
    "iso_key": "TR.TG",
    "name": "Tekirdag",
    "flag_file_name": "tr_tg"
  },
  {"id": 1722, "iso_key": "TR.TT", "name": "Tokat", "flag_file_name": "tr_tt"},
  {
    "id": 1723,
    "iso_key": "TR.TB",
    "name": "Trabzon",
    "flag_file_name": "tr_tb"
  },
  {
    "id": 1724,
    "iso_key": "TR.TC",
    "name": "Tunceli",
    "flag_file_name": "tr_tc"
  },
  {"id": 1725, "iso_key": "TR.US", "name": "Usak", "flag_file_name": "tr_us"},
  {"id": 1726, "iso_key": "TR.VA", "name": "Van", "flag_file_name": "tr_va"},
  {"id": 1727, "iso_key": "TR.YL", "name": "Yalova", "flag_file_name": "tr_yl"},
  {"id": 1728, "iso_key": "TR.YZ", "name": "Yozgat", "flag_file_name": "tr_yz"},
  {
    "id": 1729,
    "iso_key": "TR.ZO",
    "name": "Zinguldak",
    "flag_file_name": "tr_zo"
  },
  {
    "id": 241,
    "iso_key": "NZ",
    "name": "New Zealand",
    "flag_file_name": "flag_nz"
  },
  {"id": 207, "iso_key": "SE", "name": "Sweden", "flag_file_name": "flag_se"},
  {
    "id": 412,
    "iso_key": "RU-94",
    "name": "Sevastopol (Disputed)",
    "flag_file_name": "RU-94"
  },
  {
    "id": 411,
    "iso_key": "RU-93",
    "name": "Crimea (Disputed)",
    "flag_file_name": "RU-93"
  },
  {
    "id": 305,
    "iso_key": "ZA",
    "name": "South Africa",
    "flag_file_name": "flag_za"
  },
  {"id": 264, "iso_key": "EG", "name": "Egypt", "flag_file_name": "flag_eg"},
  {
    "id": 793,
    "iso_key": "JP-18",
    "name": "Kagoshima",
    "flag_file_name": "JP-18"
  },
  {"id": 792, "iso_key": "JP-17", "name": "Kagawa", "flag_file_name": "JP-17"},
  {"id": 791, "iso_key": "JP-16", "name": "Iwate", "flag_file_name": "JP-16"},
  {
    "id": 790,
    "iso_key": "JP-15",
    "name": "Ishikawa",
    "flag_file_name": "JP-15"
  },
  {"id": 789, "iso_key": "JP-14", "name": "Ibaraki", "flag_file_name": "JP-14"},
  {"id": 788, "iso_key": "JP-13", "name": "Hyōgo", "flag_file_name": "JP-13"},
  {
    "id": 787,
    "iso_key": "JP-12",
    "name": "Hokkaido",
    "flag_file_name": "JP-12"
  },
  {
    "id": 786,
    "iso_key": "JP-11",
    "name": "Hiroshima",
    "flag_file_name": "JP-11"
  },
  {"id": 785, "iso_key": "JP-10", "name": "Gunma", "flag_file_name": "JP-10"},
  {"id": 776, "iso_key": "JP-1", "name": "Aichi", "flag_file_name": "JP-1"},
  {"id": 915, "iso_key": "IT-9", "name": "Lazio", "flag_file_name": "IT-9"},
  {
    "id": 914,
    "iso_key": "IT-8",
    "name": "Friuli-Venezia Giulia",
    "flag_file_name": "IT-8"
  },
  {
    "id": 913,
    "iso_key": "IT-7",
    "name": "Emilia-Romagna",
    "flag_file_name": "IT-7"
  },
  {"id": 912, "iso_key": "IT-6", "name": "Campania", "flag_file_name": "IT-6"},
  {"id": 911, "iso_key": "IT-5", "name": "Calabria", "flag_file_name": "IT-5"},
  {
    "id": 910,
    "iso_key": "IT-4",
    "name": "Basilicata",
    "flag_file_name": "IT-4"
  },
  {
    "id": 909,
    "iso_key": "IT-3",
    "name": "Apulia Puglia",
    "flag_file_name": "IT-3"
  },
  {"id": 926, "iso_key": "IT-20", "name": "Veneto", "flag_file_name": "IT-20"},
  {
    "id": 908,
    "iso_key": "IT-2",
    "name": "Aosta Valley",
    "flag_file_name": "IT-2"
  },
  {"id": 925, "iso_key": "IT-19", "name": "Umbria", "flag_file_name": "IT-19"},
  {"id": 924, "iso_key": "IT-18", "name": "Tuscany", "flag_file_name": "IT-18"},
  {
    "id": 923,
    "iso_key": "IT-17",
    "name": "Trentino-South Tyrol",
    "flag_file_name": "IT-17"
  },
  {"id": 922, "iso_key": "IT-16", "name": "Sicily", "flag_file_name": "IT-16"},
  {
    "id": 921,
    "iso_key": "IT-15",
    "name": "Sardinia",
    "flag_file_name": "IT-15"
  },
  {
    "id": 920,
    "iso_key": "IT-14",
    "name": "Piedmont",
    "flag_file_name": "IT-14"
  },
  {"id": 919, "iso_key": "IT-13", "name": "Molise", "flag_file_name": "IT-13"},
  {"id": 918, "iso_key": "IT-12", "name": "Marche", "flag_file_name": "IT-12"},
  {
    "id": 917,
    "iso_key": "IT-11",
    "name": "Lombardy",
    "flag_file_name": "IT-11"
  },
  {"id": 916, "iso_key": "IT-10", "name": "Liguria", "flag_file_name": "IT-10"},
  {"id": 907, "iso_key": "IT-1", "name": "Abruzzo", "flag_file_name": "IT-1"},
  {
    "id": 665,
    "iso_key": "FR-9",
    "name": "Hauts-de-France",
    "flag_file_name": "FR-9"
  },
  {"id": 664, "iso_key": "FR-8", "name": "Occitanie", "flag_file_name": "FR-8"},
  {
    "id": 663,
    "iso_key": "FR-7",
    "name": "Île-de-France",
    "flag_file_name": "FR-7"
  },
  {
    "id": 662,
    "iso_key": "FR-6",
    "name": "Centre-Val de Loire",
    "flag_file_name": "FR-6"
  },
  {"id": 661, "iso_key": "FR-5", "name": "Brittany", "flag_file_name": "FR-5"},
  {
    "id": 660,
    "iso_key": "FR-4",
    "name": "Bourgogne-Franche-Comté",
    "flag_file_name": "FR-4"
  },
  {
    "id": 192,
    "iso_key": "LI",
    "name": "Liechtenstein",
    "flag_file_name": "flag_li"
  },
  {"id": 138, "iso_key": "LB", "name": "Lebanon", "flag_file_name": "flag_lb"},
  {
    "id": 136,
    "iso_key": "LK",
    "name": "Sri Lanka",
    "flag_file_name": "flag_lk"
  },
  {"id": 303, "iso_key": "MA", "name": "Morocco", "flag_file_name": "flag_ma"},
  {
    "id": 321,
    "iso_key": "GB-NIR",
    "name": "Northern Ireland",
    "flag_file_name": "flag_gbnir"
  },
  {"id": 292, "iso_key": "LY", "name": "Libya", "flag_file_name": "flag_ly"},
  {"id": 291, "iso_key": "LR", "name": "Liberia", "flag_file_name": "flag_lr"},
  {"id": 284, "iso_key": "LS", "name": "Lesotho", "flag_file_name": "flag_ls"},
  {
    "id": 322,
    "iso_key": "GB-SCT",
    "name": "Scotland",
    "flag_file_name": "flag_gbsct"
  },
  {
    "id": 320,
    "iso_key": "GB-ENG",
    "name": "England",
    "flag_file_name": "flag_gbeng"
  },
  {
    "id": 323,
    "iso_key": "GB-WLS",
    "name": "Wales",
    "flag_file_name": "flag_gbwls"
  },
  {
    "id": 659,
    "iso_key": "FR-3",
    "name": "Auvergne-Rhône-Alpes",
    "flag_file_name": "FR-3"
  },
  {
    "id": 658,
    "iso_key": "FR-2",
    "name": "Nouvelle-Aquitaine",
    "flag_file_name": "FR-2"
  },
  {"id": 669, "iso_key": "FR-13", "name": "Corsica", "flag_file_name": "FR-13"},
  {
    "id": 668,
    "iso_key": "FR-12",
    "name": "Provence-Alpes-Côte d'Azur",
    "flag_file_name": "FR-12"
  },
  {
    "id": 667,
    "iso_key": "FR-11",
    "name": "Pays de la Loire",
    "flag_file_name": "FR-11"
  },
  {
    "id": 666,
    "iso_key": "FR-10",
    "name": "Normandy",
    "flag_file_name": "FR-10"
  },
  {"id": 657, "iso_key": "FR-1", "name": "Grand Est", "flag_file_name": "FR-1"},
  {"id": 702, "iso_key": "ES-9", "name": "Biscay", "flag_file_name": "ES-9"},
  {"id": 701, "iso_key": "ES-8", "name": "Cádiz", "flag_file_name": "ES-8"},
  {"id": 700, "iso_key": "ES-7", "name": "Murcia", "flag_file_name": "ES-7"},
  {
    "id": 754,
    "iso_key": "ES-62",
    "name": "Lanzarote",
    "flag_file_name": "ES-62"
  },
  {
    "id": 753,
    "iso_key": "ES-61",
    "name": "Fuerteventura",
    "flag_file_name": "ES-61"
  },
  {
    "id": 752,
    "iso_key": "ES-60",
    "name": "Gran Canaria",
    "flag_file_name": "ES-60"
  },
  {"id": 699, "iso_key": "ES-6", "name": "Málaga", "flag_file_name": "ES-6"},
  {
    "id": 751,
    "iso_key": "ES-59",
    "name": "Tenerife",
    "flag_file_name": "ES-59"
  },
  {
    "id": 750,
    "iso_key": "ES-58",
    "name": "La Gomera",
    "flag_file_name": "ES-58"
  },
  {
    "id": 749,
    "iso_key": "ES-57",
    "name": "La Palma",
    "flag_file_name": "ES-57"
  },
  {
    "id": 748,
    "iso_key": "ES-56",
    "name": "El Hierro",
    "flag_file_name": "ES-56"
  },
  {
    "id": 747,
    "iso_key": "ES-55",
    "name": "Formentera",
    "flag_file_name": "ES-55"
  },
  {"id": 746, "iso_key": "ES-54", "name": "Menorca", "flag_file_name": "ES-54"},
  {"id": 745, "iso_key": "ES-53", "name": "Ibiza", "flag_file_name": "ES-53"},
  {"id": 744, "iso_key": "ES-52", "name": "Majorca", "flag_file_name": "ES-52"},
  {"id": 743, "iso_key": "ES-51", "name": "Melilla", "flag_file_name": "ES-51"},
  {"id": 742, "iso_key": "ES-50", "name": "Ceuta", "flag_file_name": "ES-50"},
  {"id": 698, "iso_key": "ES-5", "name": "Seville", "flag_file_name": "ES-5"},
  {"id": 741, "iso_key": "ES-49", "name": "Soria", "flag_file_name": "ES-49"},
  {"id": 740, "iso_key": "ES-48", "name": "Teruel", "flag_file_name": "ES-48"},
  {"id": 739, "iso_key": "ES-47", "name": "Segovia", "flag_file_name": "ES-47"},
  {"id": 738, "iso_key": "ES-46", "name": "Ávila", "flag_file_name": "ES-46"},
  {
    "id": 737,
    "iso_key": "ES-45",
    "name": "Palencia",
    "flag_file_name": "ES-45"
  },
  {"id": 736, "iso_key": "ES-44", "name": "Zamora", "flag_file_name": "ES-44"},
  {"id": 735, "iso_key": "ES-43", "name": "Cuenca", "flag_file_name": "ES-43"},
  {"id": 734, "iso_key": "ES-42", "name": "Huesca", "flag_file_name": "ES-42"},
  {
    "id": 733,
    "iso_key": "ES-41",
    "name": "Guadalajara",
    "flag_file_name": "ES-41"
  },
  {"id": 732, "iso_key": "ES-40", "name": "Álava", "flag_file_name": "ES-40"},
  {"id": 697, "iso_key": "ES-4", "name": "Alicante", "flag_file_name": "ES-4"},
  {"id": 148, "iso_key": "QA", "name": "Qatar", "flag_file_name": "flag_qa"},
  {"id": 73, "iso_key": "DM", "name": "Dominica", "flag_file_name": "flag_dm"},
  {
    "id": 731,
    "iso_key": "ES-39",
    "name": "La Rioja",
    "flag_file_name": "ES-39"
  },
  {"id": 730, "iso_key": "ES-38", "name": "Ourense", "flag_file_name": "ES-38"},
  {
    "id": 729,
    "iso_key": "ES-37",
    "name": "Salamanca",
    "flag_file_name": "ES-37"
  },
  {"id": 728, "iso_key": "ES-36", "name": "Lugo", "flag_file_name": "ES-36"},
  {"id": 727, "iso_key": "ES-35", "name": "Burgos", "flag_file_name": "ES-35"},
  {
    "id": 726,
    "iso_key": "ES-34",
    "name": "Albacete",
    "flag_file_name": "ES-34"
  },
  {"id": 725, "iso_key": "ES-33", "name": "Cáceres", "flag_file_name": "ES-33"},
  {"id": 724, "iso_key": "ES-32", "name": "Lleida", "flag_file_name": "ES-32"},
  {"id": 723, "iso_key": "ES-31", "name": "León", "flag_file_name": "ES-31"},
  {"id": 722, "iso_key": "ES-30", "name": "Huelva", "flag_file_name": "ES-30"},
  {"id": 696, "iso_key": "ES-3", "name": "Valence", "flag_file_name": "ES-3"},
  {
    "id": 721,
    "iso_key": "ES-29",
    "name": "Ciudad Real",
    "flag_file_name": "ES-29"
  },
  {
    "id": 720,
    "iso_key": "ES-28",
    "name": "Valladolid",
    "flag_file_name": "ES-28"
  },
  {
    "id": 719,
    "iso_key": "ES-27",
    "name": "Cantabria",
    "flag_file_name": "ES-27"
  },
  {
    "id": 718,
    "iso_key": "ES-26",
    "name": "Castellón",
    "flag_file_name": "ES-26"
  },
  {"id": 717, "iso_key": "ES-25", "name": "Navarre", "flag_file_name": "ES-25"},
  {"id": 716, "iso_key": "ES-24", "name": "Jaén", "flag_file_name": "ES-24"},
  {"id": 715, "iso_key": "ES-23", "name": "Badajoz", "flag_file_name": "ES-23"},
  {"id": 714, "iso_key": "ES-22", "name": "Almería", "flag_file_name": "ES-22"},
  {"id": 713, "iso_key": "ES-21", "name": "Toledo", "flag_file_name": "ES-21"},
  {
    "id": 712,
    "iso_key": "ES-20",
    "name": "Gipuzkoa",
    "flag_file_name": "ES-20"
  },
  {"id": 695, "iso_key": "ES-2", "name": "Barcelona", "flag_file_name": "ES-2"},
  {"id": 711, "iso_key": "ES-19", "name": "Girona", "flag_file_name": "ES-19"},
  {"id": 710, "iso_key": "ES-18", "name": "Córdoba", "flag_file_name": "ES-18"},
  {
    "id": 709,
    "iso_key": "ES-17",
    "name": "Tarragona",
    "flag_file_name": "ES-17"
  },
  {"id": 708, "iso_key": "ES-16", "name": "Granada", "flag_file_name": "ES-16"},
  {
    "id": 707,
    "iso_key": "ES-15",
    "name": "Pontevedra",
    "flag_file_name": "ES-15"
  },
  {
    "id": 706,
    "iso_key": "ES-14",
    "name": "Zaragoza",
    "flag_file_name": "ES-14"
  },
  {
    "id": 705,
    "iso_key": "ES-12",
    "name": "Asturias",
    "flag_file_name": "ES-12"
  },
  {"id": 703, "iso_key": "ES-10", "name": "Corogne", "flag_file_name": "ES-10"},
  {"id": 694, "iso_key": "ES-1", "name": "Madrid", "flag_file_name": "ES-1"},
  {
    "id": 831,
    "iso_key": "DE-9",
    "name": "Mecklenburg-Vorpommern",
    "flag_file_name": "DE-9"
  },
  {
    "id": 830,
    "iso_key": "DE-8",
    "name": "Lower Saxony",
    "flag_file_name": "DE-8"
  },
  {"id": 829, "iso_key": "DE-7", "name": "Hesse", "flag_file_name": "DE-7"},
  {"id": 828, "iso_key": "DE-6", "name": "Hamburg", "flag_file_name": "DE-6"},
  {"id": 827, "iso_key": "DE-5", "name": "Bremen", "flag_file_name": "DE-5"},
  {
    "id": 825,
    "iso_key": "DE-4",
    "name": "Brandenburg",
    "flag_file_name": "DE-4"
  },
  {"id": 826, "iso_key": "DE-3", "name": "Berlin", "flag_file_name": "DE-3"},
  {"id": 824, "iso_key": "DE-2", "name": "Bavaria", "flag_file_name": "DE-2"},
  {
    "id": 838,
    "iso_key": "DE-16",
    "name": "Thuringia",
    "flag_file_name": "DE-16"
  },
  {
    "id": 837,
    "iso_key": "DE-15",
    "name": "Schleswig-Holstein",
    "flag_file_name": "DE-15"
  },
  {
    "id": 836,
    "iso_key": "DE-14",
    "name": "Saxony-Anhalt",
    "flag_file_name": "DE-14"
  },
  {"id": 835, "iso_key": "DE-13", "name": "Saxony", "flag_file_name": "DE-13"},
  {
    "id": 834,
    "iso_key": "DE-12",
    "name": "Saarland",
    "flag_file_name": "DE-12"
  },
  {
    "id": 833,
    "iso_key": "DE-11",
    "name": "Rhineland-Palatinate",
    "flag_file_name": "DE-11"
  },
  {
    "id": 832,
    "iso_key": "DE-10",
    "name": "North Rhine- Westphalia",
    "flag_file_name": "DE-10"
  },
  {
    "id": 823,
    "iso_key": "DE-1",
    "name": "Baden-Württemberg",
    "flag_file_name": "DE-1"
  },
  {
    "id": 470,
    "iso_key": "BR-TO",
    "name": "Tocantins",
    "flag_file_name": "BR-TO"
  },
  {
    "id": 468,
    "iso_key": "BR-SP",
    "name": "São Paulo",
    "flag_file_name": "BR-SP"
  },
  {"id": 469, "iso_key": "BR-SE", "name": "Sergipe", "flag_file_name": "BR-SE"},
  {
    "id": 467,
    "iso_key": "BR-SC",
    "name": "Santa Catarina",
    "flag_file_name": "BR-SC"
  },
  {
    "id": 464,
    "iso_key": "BR-RS",
    "name": "Rio Grande do Sul",
    "flag_file_name": "BR-RS"
  },
  {"id": 466, "iso_key": "BR-RR", "name": "Roraima", "flag_file_name": "BR-RR"},
  {
    "id": 465,
    "iso_key": "BR-RO",
    "name": "Rondônia",
    "flag_file_name": "BR-RO"
  },
  {
    "id": 463,
    "iso_key": "BR-RN",
    "name": "Rio Grande do Norte",
    "flag_file_name": "BR-RN"
  },
  {
    "id": 462,
    "iso_key": "BR-RJ",
    "name": "Rio de Janeiro",
    "flag_file_name": "BR-RJ"
  },
  {"id": 459, "iso_key": "BR-PR", "name": "Paraná", "flag_file_name": "BR-PR"},
  {"id": 461, "iso_key": "BR-PI", "name": "Piauí", "flag_file_name": "BR-PI"},
  {
    "id": 460,
    "iso_key": "BR-PE",
    "name": "Pernambuco",
    "flag_file_name": "BR-PE"
  },
  {"id": 458, "iso_key": "BR-PB", "name": "Paraíba", "flag_file_name": "BR-PB"},
  {"id": 457, "iso_key": "BR-PA", "name": "Pará", "flag_file_name": "BR-PA"},
  {
    "id": 454,
    "iso_key": "BR-MT",
    "name": "Mato Grosso",
    "flag_file_name": "BR-MT"
  },
  {
    "id": 455,
    "iso_key": "BR-MS",
    "name": "Mato Grosso do Sul",
    "flag_file_name": "BR-MS"
  },
  {
    "id": 456,
    "iso_key": "BR-MG",
    "name": "Minas Gerais",
    "flag_file_name": "BR-MG"
  },
  {
    "id": 453,
    "iso_key": "BR-MA",
    "name": "Maranhão",
    "flag_file_name": "BR-MA"
  },
  {"id": 452, "iso_key": "BR-GO", "name": "Goiás", "flag_file_name": "BR-GO"},
  {
    "id": 451,
    "iso_key": "BR-ES",
    "name": "Espírito Santo",
    "flag_file_name": "BR-ES"
  },
  {
    "id": 450,
    "iso_key": "BR-DF",
    "name": "Distrito Federal",
    "flag_file_name": "BR-DF"
  },
  {"id": 449, "iso_key": "BR-CE", "name": "Ceará", "flag_file_name": "BR-CE"},
  {"id": 220, "iso_key": "DK", "name": "Denmark", "flag_file_name": "flag_dk"},
  {"id": 235, "iso_key": "TK", "name": "Tokelau", "flag_file_name": "flag_tk"},
  {"id": 448, "iso_key": "BR-BA", "name": "Bahia", "flag_file_name": "BR-BA"},
  {"id": 446, "iso_key": "BR-AP", "name": "Amapá", "flag_file_name": "BR-AP"},
  {
    "id": 447,
    "iso_key": "BR-AM",
    "name": "Amazonas",
    "flag_file_name": "BR-AM"
  },
  {"id": 445, "iso_key": "BR-AL", "name": "Alagoas", "flag_file_name": "BR-AL"},
  {"id": 444, "iso_key": "BR-AC", "name": "Acre", "flag_file_name": "BR-AC"},
  {
    "id": 522,
    "iso_key": "AR-TF",
    "name": "Tierra del Fuego",
    "flag_file_name": "AR-TF"
  },
  {
    "id": 518,
    "iso_key": "AR-SL",
    "name": "San Luis",
    "flag_file_name": "AR-SL"
  },
  {
    "id": 517,
    "iso_key": "AR-SJ",
    "name": "San Juan",
    "flag_file_name": "AR-SJ"
  },
  {
    "id": 520,
    "iso_key": "AR-SF",
    "name": "Santa Fe Province",
    "flag_file_name": "AR-SF"
  },
  {
    "id": 521,
    "iso_key": "AR-SE",
    "name": "Santiago del Estero",
    "flag_file_name": "AR-SE"
  },
  {
    "id": 519,
    "iso_key": "AR-SC",
    "name": "Santa Cruz",
    "flag_file_name": "AR-SC"
  },
  {"id": 516, "iso_key": "AR-SA", "name": "Salta", "flag_file_name": "AR-SA"},
  {
    "id": 515,
    "iso_key": "AR-RN",
    "name": "Río Negro",
    "flag_file_name": "AR-RN"
  },
  {"id": 514, "iso_key": "AR-NQ", "name": "Neuquén", "flag_file_name": "AR-NQ"},
  {"id": 512, "iso_key": "AR-MZ", "name": "Mendoza", "flag_file_name": "AR-MZ"},
  {
    "id": 513,
    "iso_key": "AR-MN",
    "name": "Misiones",
    "flag_file_name": "AR-MN"
  },
  {
    "id": 511,
    "iso_key": "AR-LR",
    "name": "La Rioja",
    "flag_file_name": "AR-LR"
  },
  {
    "id": 510,
    "iso_key": "AR-LP",
    "name": "La Pampa",
    "flag_file_name": "AR-LP"
  },
  {"id": 509, "iso_key": "AR-JY", "name": "Jujuy", "flag_file_name": "AR-JY"},
  {"id": 508, "iso_key": "AR-FM", "name": "Formosa", "flag_file_name": "AR-FM"},
  {
    "id": 507,
    "iso_key": "AR-ER",
    "name": "Entre Ríos",
    "flag_file_name": "AR-ER"
  },
  {
    "id": 500,
    "iso_key": "AR-DF",
    "name": "Ciudad de Buenos Aires",
    "flag_file_name": "AR-DF"
  },
  {
    "id": 502,
    "iso_key": "AR-CT",
    "name": "Catamarca",
    "flag_file_name": "AR-CT"
  },
  {
    "id": 506,
    "iso_key": "AR-CN",
    "name": "Corrientes",
    "flag_file_name": "AR-CN"
  },
  {"id": 504, "iso_key": "AR-CH", "name": "Chubut", "flag_file_name": "AR-CH"},
  {"id": 503, "iso_key": "AR-CC", "name": "Chaco", "flag_file_name": "AR-CC"},
  {"id": 505, "iso_key": "AR-CB", "name": "Córdoba", "flag_file_name": "AR-CB"},
  {
    "id": 501,
    "iso_key": "AR-BA",
    "name": "Buenos Aires",
    "flag_file_name": "AR-BA"
  },
  {"id": 199, "iso_key": "IT", "name": "Italy", "flag_file_name": "flag_it"}
];
