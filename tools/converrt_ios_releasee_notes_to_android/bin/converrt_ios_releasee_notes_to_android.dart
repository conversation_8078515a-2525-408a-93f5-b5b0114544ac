import 'dart:io';

void main(List<String> arguments) {
  final metadata = Directory('../../ios/metadata');
  final directories = metadata
      .listSync()
      .whereType<Directory>()
      .where((element) => element.path.contains('review_information') == false)
      .toList()
    ..sort((Directory a, Directory b) => a.path.compareTo(b.path));
  final xmlBuffer = StringBuffer();

  final languageCodeMapping = {
    'cs': 'cs-CZ',
    'da': 'da-DK',
    'el': 'el-GR',
    'fi': 'fi-FI',
    'hu': 'hu-HU',
    'it': 'it-IT',
    'ja': 'ja-JP',
    'ko': 'ko-KR',
    'no': 'no-NO',
    'pl': 'pl-PL',
    'ru': 'ru-RU',
    'sv': 'sv-SE',
    'tr': 'tr-TR',
    'zh-Hans': 'zh-C<PERSON>',
    'zh-Hant': 'zh-TW',
    'es-MX': 'es-419',
  };

  final duplicate = {
    // 'pt-PT': 'pt-BR',
  };

  for (final directory in directories) {
    final iosLanguageCode = Uri.parse(directory.path).pathSegments.last;

    final androidLanguageCode =
        languageCodeMapping[iosLanguageCode] ?? iosLanguageCode;

    _writeLanguage(
      xmlBuffer,
      androidLanguageCode,
      directory,
    );

    final duplicateLanguageCode = duplicate[iosLanguageCode];
    if (duplicateLanguageCode != null) {
      _writeLanguage(
        xmlBuffer,
        duplicateLanguageCode,
        directory,
      );
    }
  }

  final output = xmlBuffer.toString();

  print(output);
  File('android_release_notes.xml').writeAsStringSync(output);
}

void _writeLanguage(
  StringBuffer xmlBuffer,
  String languageCode,
  Directory directory,
) {
  xmlBuffer.writeln('<$languageCode>');

  final releaseNotes =
      File('${directory.path}/release_notes.txt').readAsStringSync();

  xmlBuffer.write(releaseNotes);
  xmlBuffer.writeln('</$languageCode>');
  xmlBuffer.writeln();
}
